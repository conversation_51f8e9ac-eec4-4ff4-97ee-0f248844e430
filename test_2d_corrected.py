#!/usr/bin/env python3
"""
测试修正后的2D TFIM块对角化代码
"""

import numpy as np
from collections import defaultdict

# 从原文件中提取必要的函数
def get_site_value(state: int, site: int) -> int:
    """获取状态state在site位置的自旋值（0或1）"""
    return (state >> site) & 1

def flip_state(state: int, site: int) -> int:
    """翻转状态state在site位置的自旋"""
    return state ^ (1 << site)

def translate_2d(state: int, row: int, col: int, shift_row: int, shift_col: int) -> int:
    """二维格点上的平移操作"""
    N = row * col
    new_state = 0
    
    for i in range(N):
        old_row, old_col = divmod(i, col)
        new_row = (old_row + shift_row) % row
        new_col = (old_col + shift_col) % col
        new_i = new_row * col + new_col
        
        if get_site_value(state, i):
            new_state |= (1 << new_i)
    
    return new_state

def represent(N: int, state: int, row: int, col: int):
    """找到状态的代表元及平移步数"""
    min_state = state
    best_l_row, best_l_col = 0, 0
    
    for l_row in range(row):
        for l_col in range(col):
            translated = translate_2d(state, row, col, l_row, l_col)
            if translated < min_state:
                min_state = translated
                best_l_row, best_l_col = l_row, l_col
    
    return min_state, best_l_row, best_l_col, 0

def orbit_period_2d(state: int, row: int, col: int):
    """计算二维轨道的周期"""
    # 行方向周期
    R_row = 1
    current = translate_2d(state, row, col, 1, 0)
    while current != state and R_row < row:
        current = translate_2d(current, row, col, 1, 0)
        R_row += 1
    
    # 列方向周期
    R_col = 1
    current = translate_2d(state, row, col, 0, 1)
    while current != state and R_col < col:
        current = translate_2d(current, row, col, 0, 1)
        R_col += 1
    
    return R_row, R_col

def is_k_compatible(N: int, R: int, k: int) -> bool:
    """检查动量k与轨道周期R的兼容性"""
    return (k * R) % N == 0

def build_translation_basis_by_k(col: int, row: int):
    """构建二维平移对称性下的基，按动量量子数(k_row, k_col)分块"""
    basis = {}
    seen = set()
    N = col * row
    
    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s, row, col)
        if rep in seen:
            continue
        seen.add(rep)
        
        # 计算轨道周期
        R_row, R_col = orbit_period_2d(rep, row, col)
        
        # 遍历所有可能的动量量子数组合
        for k_row in range(row):
            for k_col in range(col):
                # 检查动量兼容性
                if is_k_compatible(row, R_row, k_row) and is_k_compatible(col, R_col, k_col):
                    k = (k_row, k_col)
                    if k not in basis:
                        basis[k] = {
                            'repr': [], 
                            'peri_row': [], 
                            'peri_col': []
                        }
                    basis[k]['repr'].append(rep)
                    basis[k]['peri_row'].append(R_row)
                    basis[k]['peri_col'].append(R_col)
    
    return basis

def helement_translation_2d_simple(Ra_row: int, Ra_col: int, Rb_row: int, Rb_col: int, 
                                  l_row: int, l_col: int, k_row: int, k_col: int, 
                                  row: int, col: int) -> complex:
    """二维平移对称性下的矩阵元相位因子"""
    return (np.sqrt(Ra_row / Rb_row) * np.sqrt(Ra_col / Rb_col) * 
            np.exp(1j * 2 * np.pi * k_row * l_row / row) * 
            np.exp(1j * 2 * np.pi * k_col * l_col / col))

def build_block_Hamiltonian_translation_2d_corrected(row: int, col: int, 
                                                    reps: list, 
                                                    peri_row: list, peri_col: list,
                                                    k_row: int, k_col: int,
                                                    J: float, h: float) -> np.ndarray:
    """构建二维平移对称性下的块对角哈密顿量（修正版本）"""
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    N = row * col
    
    for ia in range(nrep):
        sa = reps[ia]
        Ra_row = peri_row[ia]
        Ra_col = peri_col[ia]
        
        # 对角项：计算二维格点上的相邻自旋相互作用
        Ez = 0.0
        for i in range(N):
            row_i, col_i = divmod(i, col)
            
            # 行方向相邻
            j_row = (row_i + 1) % row
            j = j_row * col + col_i
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)
            
            # 列方向相邻
            j_col = (col_i + 1) % col
            j = row_i * col + j_col
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)
        
        Hk[ia, ia] += Ez
        
        # 非对角项：横向场引起的自旋翻转
        for i in range(N):
            sb = flip_state(sa, i)
            rep_b, l_row, l_col, _ = represent(N, sb, row, col)
            if rep_b in reps:
                ib = reps.index(rep_b)
                Rb_row = peri_row[ib]
                Rb_col = peri_col[ib]
                elem = h * helement_translation_2d_simple(
                    Ra_row, Ra_col, Rb_row, Rb_col,
                    l_row, l_col, k_row, k_col,
                    row, col
                )
                Hk[ia, ib] += elem
    
    return Hk

def fullspectrum_blocks_direct(col: int, row: int, J: float, h: float):
    """计算二维TFIM的完整能谱（修正版本）"""
    basis = build_translation_basis_by_k(col, row)
    total_dim = sum(len(v['repr']) for v in basis.values())
    N = col * row
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    
    energies = []
    labels = []
    
    for k, data in sorted(basis.items()):
        k_row, k_col = k
        reps = data['repr']
        peri_row = data['peri_row']
        peri_col = data['peri_col']
        
        if len(reps) == 0:
            continue
            
        print(f"处理动量块 k=({k_row},{k_col}), 包含 {len(reps)} 个代表元")
        
        # 使用修正的哈密顿量构建函数
        Hk = build_block_Hamiltonian_translation_2d_corrected(
            row, col, reps, peri_row, peri_col,
            k_row, k_col, J, h
        )
        
        w, _ = np.linalg.eig(Hk)
        print(f"块 k=({k_row},{k_col}) 的本征值数量: {len(w)}")
        
        energies.extend(w.real.tolist())
        labels.extend([k_row * col + k_col] * len(w))
    
    return energies, labels

def test_2d_corrected(row: int = 2, col: int = 2, J: float = 1.0, h: float = 0.5):
    """测试修正后的2D代码"""
    print(f"测试 {row}x{col} 二维格点，J={J}, h={h}")
    print("="*50)
    
    # 计算能谱
    energies, labels = fullspectrum_blocks_direct(col, row, J, h)
    
    print(f"总本征值数量: {len(energies)}")
    print(f"期望本征值数量: {2**(row*col)}")
    
    # 按动量块分组显示结果
    energy_by_k = defaultdict(list)
    for e, k in zip(energies, labels):
        energy_by_k[k].append(e)
    
    print("\n各动量块的本征值:")
    for k in sorted(energy_by_k.keys()):
        k_row, k_col = divmod(k, col)
        energies_k = sorted(energy_by_k[k])
        print(f"k=({k_row},{k_col}): {len(energies_k)} 个本征值")
        print(f"  能量: {[f'{e:.4f}' for e in energies_k[:5]]}" + 
              (f" ... (共{len(energies_k)}个)" if len(energies_k) > 5 else ""))
    
    return energies, labels

if __name__ == "__main__":
    print("测试修正后的2D TFIM块对角化代码")
    print("="*60)
    
    # 测试小系统
    print("1. 测试 2x2 系统:")
    try:
        energies_2x2, labels_2x2 = test_2d_corrected(2, 2, 1.0, 0.5)
        print("✓ 2x2 系统测试成功")
    except Exception as e:
        print(f"✗ 2x2 系统测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n主要修正:")
    print("1. 基构建函数现在正确地按(k_row, k_col)分块")
    print("2. 哈密顿量构建函数使用块的固定动量，而不是每个态的动量")
    print("3. 矩阵元计算遵循1D代码的正确模式")
    print("4. 二维相邻相互作用正确实现（行方向和列方向）")
