{"cells": [{"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy \n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["N = 8\n", "#T=t1+t2\n", "t_1 = 1\n", "t_2 = 1\n", "lam_h = 0.01\n", "lam_J = 0.1\n", "h_x=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J_z=np.pi/4 - lam_J#J*t2# 0.75"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def translate(N, state, steps):\n", "    \"\"\"平移操作（示例：循环左移steps位）\"\"\"\n", "    # 实际实现需与checkstate中的平移逻辑一致\n", "    bits = [(state >> i) & 1 for i in range(N)]\n", "    steps = steps % N\n", "    shifted_bits = bits[steps:] + bits[:steps]\n", "    return sum(bit << i for i, bit in enumerate(shifted_bits))\n", "\n", "def spin_flip_state(N, rep):\n", "    \"\"\"自旋翻转（逐位取反）\"\"\"\n", "    return rep ^ ((1 << N) - 1)\n", "\n", "def reverseBits(state: int, num_spins: int) -> int:\n", "    \"\"\"\n", "    自旋态的空间反演操作（优化版）：将N位自旋的位置完全翻转\n", "    例：N=4时，state=0b1010（第1、3位自旋为1）→ 反演后=0b0101（第0、2位自旋为1）\n", "    \n", "    参数:\n", "        state: 整数编码的自旋态（需确保二进制位数 ≤ num_spins）\n", "        num_spins: 系统自旋总数（即反演的目标位数N）\n", "    返回:\n", "        reversed_state: 反演后的自旋态（整数编码）\n", "    \"\"\"\n", "    # 1. 输入验证：避免无效输入（如state位数超过num_spins）\n", "    if state < 0:\n", "        raise ValueError(\"自旋态state必须为非负整数\")\n", "    if num_spins < 1:\n", "        raise ValueError(\"自旋总数num_spins必须≥1\")\n", "    if state >= (1 << num_spins):\n", "        raise ValueError(f\"state={bin(state)}的位数超过num_spins={num_spins}，请检查输入\")\n", "    \n", "    reversed_state = 0\n", "    remaining_state = state\n", "    \n", "    # 2. 逐位提取并反转位置（仅遍历num_spins位，避免多余计算）\n", "    for i in range(num_spins):\n", "        # 提取当前最低位（第i位，从0开始计数）\n", "        current_bit = remaining_state & 1\n", "        # 将当前位放到反演后的位置：原第i位 → 反演后第(num_spins-1 -i)位\n", "        reversed_state |= current_bit << (num_spins - 1 - i)\n", "        # 移除已处理的最低位\n", "        remaining_state >>= 1\n", "    \n", "    return reversed_state"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def translate(L, state, n_translation_sites):\\n    new_state = 0\\n    for site in range(L):\\n        site_value = get_site_value(state, site)\\n        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\\n    return new_state\\ndef spin_flip_state(N: int, rep: int) -> int:\\n    \"\"\"\\n    对N维系统中的量子态rep执行自旋翻转操作\\n    \\n    参数:\\n        N: 系统维度（自旋数量）\\n        rep: 待翻转的量子态（整数表示，二进制编码）\\n        \\n    返回:\\n        自旋翻转后的量子态（整数表示）\\n        \\n    异常:\\n        ValueError: 当rep超出N维系统的可能状态范围时抛出\\n    \"\"\"\\n    # 验证输入态的有效性\\n    max_state = (1 << N) - 1  # N个自旋的最大可能状态\\n    if rep < 0 or rep > max_state:\\n        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\\n    \\n    # 自旋翻转逻辑：翻转每个比特位（0<->1）\\n    # 构造N位全1的掩码，用于异或操作实现翻转\\n    mask = (1 << N) - 1\\n    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\\n    \\n    return flipped_rep\\ndef reverseBits(s,N):\\n    bin_chars = \"\"\\n    temp = s\\n    for i in range(N):\\n        bin_char = bin(temp % 2)[-1]\\n        temp = temp // 2\\n        bin_chars = bin_char + bin_chars\\n    bits =  bin_chars.upper()\\n    return int(bits[::-1], 2)'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "'''def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def spin_flip_state(N: int, rep: int) -> int:\n", "    \"\"\"\n", "    对N维系统中的量子态rep执行自旋翻转操作\n", "    \n", "    参数:\n", "        N: 系统维度（自旋数量）\n", "        rep: 待翻转的量子态（整数表示，二进制编码）\n", "        \n", "    返回:\n", "        自旋翻转后的量子态（整数表示）\n", "        \n", "    异常:\n", "        ValueError: 当rep超出N维系统的可能状态范围时抛出\n", "    \"\"\"\n", "    # 验证输入态的有效性\n", "    max_state = (1 << N) - 1  # N个自旋的最大可能状态\n", "    if rep < 0 or rep > max_state:\n", "        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\n", "    \n", "    # 自旋翻转逻辑：翻转每个比特位（0<->1）\n", "    # 构造N位全1的掩码，用于异或操作实现翻转\n", "    mask = (1 << N) - 1\n", "    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\n", "    \n", "    return flipped_rep\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)'''"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def ffun(t,s,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    if t == -1:\n", "        if s == -1:\n", "            f = np.cos(kk*l)\n", "        elif s==1:\n", "            f = -np.sin(kk*l)\n", "    elif t ==1:\n", "        if s == -1:\n", "            f = np.sin(kk*l)\n", "        elif s==1:\n", "            f = np.cos(kk*l)\n", "    return f\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    \n", "    return 1+t*np.cos(kk*l)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def block_direct_sum(blocks):\n", "    \"\"\"\n", "    沿对角线构造块直和矩阵（块对角矩阵）\n", "    \n", "    参数:\n", "        blocks: 子矩阵列表，每个元素为numpy数组（方阵）\n", "    返回:\n", "        块直和矩阵（对角线上是输入的子矩阵，其余为0）\n", "    \"\"\"\n", "    # 检查输入是否为空\n", "    if not blocks:\n", "        return np.array([], dtype=float)\n", "    \n", "    # 检查所有子矩阵是否为方阵\n", "    for i, b in enumerate(blocks):\n", "        if b.ndim != 2 or b.shape[0] != b.shape[1]:\n", "            raise ValueError(f\"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）\")\n", "    \n", "    # 初始化结果矩阵（从空矩阵开始）\n", "    result = np.array([], dtype=blocks[0].dtype)\n", "    \n", "    for block in blocks:\n", "        m = block.shape[0]  # 当前块的维度\n", "        if result.size == 0:\n", "            # 第一次拼接：直接用当前块作为初始矩阵\n", "            result = block.copy()\n", "        else:\n", "            # 非第一次拼接：构造新的块对角矩阵\n", "            n = result.shape[0]  # 现有矩阵的维度\n", "            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）\n", "            new_result = np.zeros((n + m, n + m), dtype=result.dtype)\n", "            # 填充左上角为现有矩阵\n", "            new_result[:n, :n] = result\n", "            # 填充右下角为当前块\n", "            new_result[n:, n:] = block\n", "            result = new_result\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["def checkstate(s, k, N):\n", "    R = -1\n", "    tp = -1  # 反演+平移的周期\n", "    tz = -1  # 自旋翻转+平移的周期\n", "    tpz = -1  # 反演+自旋翻转+平移的周期\n", "    smax = 2 **N - 1\n", "\n", "    # 1. 首先检查平移对称性，确定最小平移周期R\n", "    t = s\n", "    for i in range(1, N + 1):\n", "        t = translate(N, t, 1)  # 平移一位\n", "        if t < s:  # 存在更小的代表元，当前态无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到平移周期\n", "            if k % (N // i) == 0:  # 满足动量守恒\n", "                R = i\n", "            else:\n", "                return -1, -1, -1, -1\n", "            break\n", "    if R == -1:  # 无平移对称性\n", "        return -1, -1, -1, -1\n", "\n", "    # 2. 检查反演+平移对称性 (P*T^i)\n", "    t = reverseBits(s, N)  # 先反演\n", "    for i in range(R):  # 只在平移周期内检查\n", "        if t < s:  # 存在更小的代表元，无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到反演+平移的周期\n", "            tp = i\n", "            break\n", "        t = translate(N, t, 1)  # 平移一位\n", "    if t < s:  # 循环结束后仍需检查\n", "        return -1, -1, -1, -1\n", "\n", "    # 3. 检查自旋翻转+平移对称性 (Z*T^i)\n", "    t = smax - s  # 先自旋翻转\n", "    for i in range(R):\n", "        if t < s:  # 存在更小的代表元，无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到自旋翻转+平移的周期\n", "            tz = i\n", "            break\n", "        t = translate(N, t, 1)  # 平移一位\n", "    if t < s:\n", "        return -1, -1, -1, -1\n", "\n", "    # 4. 检查反演+自旋翻转+平移对称性 (PZ*T^i)\n", "    t = reverseBits(smax - s, N)  # 先自旋翻转再反演\n", "    for i in range(R):\n", "        if t < s:  # 存在更小的代表元，无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到复合操作的周期\n", "            tpz = i\n", "            break\n", "        t = translate(N, t, 1)  # 平移一位\n", "    if t < s:\n", "        return -1, -1, -1, -1\n", "\n", "    return R, tp, tz, tpz"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2, 1, 1, 0)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["checkstate(5,2,4)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p, z):\n", "    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数\n", "    repr_list = []\n", "    typee = []# 分类标签\n", "    peri = []# 平移对称性\n", "    mtrf = []# 反演对称性\n", "    ntrf = []# 自旋翻转对称性\n", "    mntrf = []# 联合对称性\n", "    capr = []# 类型\n", "    \n", "   \n", "    for s in range(2 **N):\n", "        for sigma in [-1, 1]:\n", "            # 3. 每次迭代初始化m、n，避免跨状态污染\n", "        # 2. 显式列出sigma值（替代range，更清晰）\n", "            m, n ,mn= None, None, None\n", "            ca = None  # 显式初始化分类标签\n", "            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\n", "            \n", "            # 基础过滤：仅处理有效平移对称性的状态\n", "            if R <= -1:\n", "                continue\n", "            \n", "            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\n", "            if (k == 0 or k == N // 2) and (sigma == -1):\n", "                continue#R = -1  # 标记为无效状态\n", "            \n", "            # 5. 仅处理R仍有效的状态\n", "            if R > 0:\n", "                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\n", "                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\n", "                if tp == -1 and tz == -1 and tpz == -1:\n", "                    ca = 1\n", "                    m, n = None, None  # 明确赋值，避免未定义\n", "                \n", "                # 分支2：tp≠-1、tz=-1（反演-平移对称）\n", "                elif tp != -1 and tz == -1 and tpz == -1:\n", "                    ca = 2\n", "                    m = tp#(tp+1)% R\n", "                    # 7. 浮点数比较改用np.isclose，增强稳健性\n", "                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\n", "                elif tp == -1 and tz != -1 and tpz == -1:\n", "                    ca = 3\n", "                    n = tz#(tz+1)%R\n", "                    #if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\n", "                    #    R = -1\n", "                \n", "                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\n", "                elif tp == -1 and tz == -1 and tpz != -1:\n", "                    ca = 4\n", "                    mn = tpz#(tpz+1)%R\n", "                    if np.isclose(ggun(sigma * p * z, mn, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, mn, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\n", "                elif tp != -1 and tz != -1 and tpz != -1:\n", "                    ca = 5\n", "                    m, n,mn = tp,tz,tpz#(tp+1)%R, (tz+1)%R,(tpz+1)%R\n", "                    '''if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "\n", "                    '''\n", "                    \n", "                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 8. 捕获未覆盖的状态组合（调试用）\n", "                else:\n", "                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\n", "                    continue\n", "                \n", "                # 9. 最终检查：ca必须已定义且R仍有效\n", "                if ca is not None and R > 0:\n", "                    repr_list.append(s)\n", "                    typee.append(sigma)\n", "                    capr.append(ca)\n", "                    peri.append(R)\n", "                    mtrf.append(m)\n", "                    ntrf.append(n)\n", "                    mntrf.append(mn)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 10. 简化打印，避免输出过载\n", "    #print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf,mntrf, capr"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def represent(L, a0):\n", "    sa = a0  # 最小代表态\n", "    la = 0   # 平移步数\n", "    qa = 0   # 反演标志 (0:无反演, 1:有反演)\n", "    ga = 0   # 自旋翻转标志 (0:无翻转, 1:有翻转)\n", "    smax = 2 **L - 1\n", "\n", "    # 1. 检查平移操作 (T^i)\n", "    auxt = a0\n", "    for i in range(1, L):\n", "        auxt = translate(L, auxt, 1)  # 每次平移1步，累计i步\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 0\n", "            ga = 0\n", "\n", "    # 2. 检查反演+平移操作 (P*T^i)\n", "    auxt = reverseBits(a0, L)  # 先反演\n", "    for i in range(L):\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 1\n", "            ga = 0\n", "        auxt = translate(L, auxt, 1)  # 平移1步\n", "\n", "    # 3. 检查自旋翻转+平移操作 (Z*T^i)\n", "    auxt = smax ^ a0  # 自旋翻转 (Bits_Invert)\n", "    for i in range(L):\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 0\n", "            ga = 1\n", "        auxt = translate(L, auxt, 1)  # 平移1步\n", "\n", "    # 4. 检查反演+自旋翻转+平移操作 (PZ*T^i)\n", "    auxt = reverseBits(smax ^ a0, L)  # 先自旋翻转再反演\n", "    for i in range(L):\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 1\n", "            ga = 1\n", "        auxt = translate(L, auxt, 1)  # 平移1步\n", "\n", "    return sa, a0, la, qa, ga"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def represent(L, a0):\\n    \"\"\"\\n    找到状态a0的表示态和达到该表示态所需的对称操作\\n    \\n    参数:\\n        L: 系统大小\\n        a0: 初始状态\\n        \\n    返回:\\n        a: 表示态\\n        l: 平移步数\\n        q: 是否应用了反演 (0=否, 1=是)\\n        g: 是否应用了自旋翻转 (0=否, 1=是)\\n    \"\"\"\\n    smax = (1 << L) - 1  # 全1状态\\n    \\n    # 初始化最小状态为原始状态\\n    min_state = a0\\n    best_l = 0\\n    best_q = 0\\n    best_g = 0\\n    \\n    # 考虑所有可能的对称操作组合\\n    for q in [0, 1]:  # 是否应用反演\\n        for g in [0, 1]:  # 是否应用自旋翻转\\n            # 应用反演和自旋翻转\\n            if q == 1 and g == 1:\\n                transformed = smax - reverseBits(a0, L)  # 反演+自旋翻转\\n            elif q == 1:\\n                transformed = reverseBits(a0, L)  # 仅反演\\n            elif g == 1:\\n                transformed = smax - a0  # 仅自旋翻转\\n            else:\\n                transformed = a0  # 无操作\\n            \\n            # 考虑所有可能的平移\\n            for l in range(L):\\n                # 应用平移\\n                final_state = translate(L, transformed, l)\\n                \\n                # 检查是否找到更小的状态\\n                if final_state < min_state:\\n                    min_state = final_state\\n                    best_l = l\\n                    best_q = q\\n                    best_g = g\\n                \\n                # 如果状态已经是0，不需要继续检查（0是最小的）\\n                if min_state == 0:\\n                    break\\n            if min_state == 0:\\n                break\\n        if min_state == 0:\\n            break\\n    \\n    return min_state, best_l, best_q, best_g'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["'''def represent(L, a0):\n", "    \"\"\"\n", "    找到状态a0的表示态和达到该表示态所需的对称操作\n", "    \n", "    参数:\n", "        L: 系统大小\n", "        a0: 初始状态\n", "        \n", "    返回:\n", "        a: 表示态\n", "        l: 平移步数\n", "        q: 是否应用了反演 (0=否, 1=是)\n", "        g: 是否应用了自旋翻转 (0=否, 1=是)\n", "    \"\"\"\n", "    smax = (1 << L) - 1  # 全1状态\n", "    \n", "    # 初始化最小状态为原始状态\n", "    min_state = a0\n", "    best_l = 0\n", "    best_q = 0\n", "    best_g = 0\n", "    \n", "    # 考虑所有可能的对称操作组合\n", "    for q in [0, 1]:  # 是否应用反演\n", "        for g in [0, 1]:  # 是否应用自旋翻转\n", "            # 应用反演和自旋翻转\n", "            if q == 1 and g == 1:\n", "                transformed = smax - reverseBits(a0, L)  # 反演+自旋翻转\n", "            elif q == 1:\n", "                transformed = reverseBits(a0, L)  # 仅反演\n", "            elif g == 1:\n", "                transformed = smax - a0  # 仅自旋翻转\n", "            else:\n", "                transformed = a0  # 无操作\n", "            \n", "            # 考虑所有可能的平移\n", "            for l in range(L):\n", "                # 应用平移\n", "                final_state = translate(L, transformed, l)\n", "                \n", "                # 检查是否找到更小的状态\n", "                if final_state < min_state:\n", "                    min_state = final_state\n", "                    best_l = l\n", "                    best_q = q\n", "                    best_g = g\n", "                \n", "                # 如果状态已经是0，不需要继续检查（0是最小的）\n", "                if min_state == 0:\n", "                    break\n", "            if min_state == 0:\n", "                break\n", "        if min_state == 0:\n", "            break\n", "    \n", "    return min_state, best_l, best_q, best_g'''"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 14, 0, 0, 1)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["represent(4,14)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def c_for_sigma(sigma, k,l,N):\n", "    kk = 2 * np.pi * k / N\n", "    if sigma == 1:\n", "        return np.cos(kk * l)\n", "    else:\n", "        return np.sin(kk * l)\n", "def build_projection_matrix(N, k, p, z, repr_list, typee, peri, mtrf, ntrf,mntrf, capr):\n", "    \"\"\"\n", "    构建投影矩阵，将原始希尔伯特空间投影到对称性适应的子空间。\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        k: 动量量子数\n", "        p: 反演对称性量子数\n", "        z: 自旋翻转对称性量子数\n", "        repr_list: 代表性构型列表\n", "        typee, peri, mtrf, ntrf, capr: 对称性信息（来自findbasis）\n", "        \n", "    返回:\n", "        proj_matrix: 投影矩阵，形状为 (nrep, 2^N)\n", "    \"\"\"\n", "    nrep = len(repr_list)\n", "    dim_hilbert = 2 ** N\n", "    proj_matrix = np.zeros((dim_hilbert,nrep), dtype=complex)\n", "    #print(repr_list)\n", "    for idx in range(nrep):\n", "        s = repr_list[idx]\n", "        ca = capr[idx]\n", "        R = peri[idx]\n", "        s_sigma = typee[idx]#sigma\n", "        tp = mtrf[idx]\n", "        tz = ntrf[idx]\n", "        tpz = mntrf[idx]\n", "        # 从typee中提取sigma（typee = 2*ca + (sigma+1)/2）\n", "        #sigma = 2 * (typee[idx] - 2 * ca) - 1\n", "        #print(\"态，分类\")\n", "        #print(s,ca)\n", "        for state in range(2**N):\n", "            representative,state_orginal, l, q, g = represent(N, state)\n", "            #print(representative)\n", "            #R,tp,tz,tpz = checkstate(representative,k,N)\n", "            kk = 2 * np.pi * k / N\n", "            if representative == s:\n", "                #ib = repr_list.index(representative)\n", "                #if ib > 1 and repr_list[ib] == repr_list[ib - 1]:\n", "                #    ib = ib - 1\n", "                #    nb = 2\n", "                #elif ib < nrep - 1 and repr_list[ib] == repr_list[ib + 1]:\n", "                #    nb = 2\n", "                #else:\n", "                #    nb = 1\n", "                #for jj in range(ib, ib + nb):\n", "\n", "                #假设变量类型为 my_int，使用 printf 格式化输出（更适合C++代码风格）\n", "                # 使用 f-string 格式化，添加字段说明\n", "                #print(f\"s: {s}, state: {state}, R: {R}, tp: {tp}, tz: {tz}, tpz: {tpz}, k: {k}, p: {p}, z: {z}, sigma:{s_sigma}\")\n", "                matelement = 1/R/2\n", "                \n", "                #print(R,m,n)\n", "                if k == 0 or k == N//2:\n", "                    matelement = matelement * 2#g_k项\n", "                # 根据分类ca处理不同的对称性\n", "                if ((None == tp) and (None == tz) and (None == tpz)):\n", "                    # 只有平移对称性\n", "                    #print(ca)\n", "                    proj_matrix[state,idx] += np.sqrt(matelement)#* c_for_sigma(s_sigma,k,l,N) #phase / norm\n", "                        \n", "                if (None != tp) and (None == tz) and (None == tpz):\n", "                    # 反演对称性\n", "                    \n", "                    '''if np.isclose(ggun(s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    elif s_sigma == -1 and not np.isclose(ggun(-s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    else:'''\n", "                    proj_matrix[state,idx] += np.sqrt(matelement/ (1+ s_sigma * p * np.cos(kk*tp)))# * c_for_sigma(s_sigma,k,l,N)  #phase2 / norm    \n", "                if (None != tz) and (None == tp) and (None == tpz):\n", "                    # 自旋翻转对称性\n", "                    #if np.isclose(ggun(z, tz, k, N), 0, atol=1e-8):\n", "                    #    continue\n", "                    #else:\n", "                    proj_matrix[state,idx] += np.sqrt(matelement / (1+ z * np.cos(kk*tz))) #* c_for_sigma(s_sigma,k,l,N)  #phase3 / norm\n", "                    \n", "                if (None != tpz) and (None == tz) and (None == tp):\n", "                    # 联合对称性（反演+自旋翻转）\n", "                    '''if np.isclose(ggun(s_sigma * p * z, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    elif s_sigma == -1 and not np.isclose(ggun(-s_sigma * p * z, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    else:'''\n", "                    proj_matrix[state,idx] += np.sqrt(matelement/(1+ s_sigma * p * z * np.cos(kk*tpz)))# * c_for_sigma(s_sigma,k,l,N) #phase4 / norm* p#phase4 / norm\n", "                        \n", "                if (None != tp) and (None != tz) and (None != tpz):\n", "                    #print(representative,tp,tz,tpz)\n", "                    # 多重对称性（反演和自旋翻转）\n", "                    '''if np.isclose(ggun(z, tz, k, N) * ggun(s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    elif s_sigma == -1 and not np.isclose(ggun(-s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    else:'''\n", "                    #print(f\" s_sigma: {s_sigma}, p: {p}, z: {z}, kk: {kk}, lp: {tp}, lz: {tz}, lzp: {tpz}\")\n", "                    proj_matrix[ state,idx] += np.sqrt(matelement / (1+ s_sigma * p * np.cos(kk*tp))/(1+ z * np.cos(kk*tz))) #* c_for_sigma(s_sigma,k,l,N)  \n", "                    #phase5 / norm4 / norm\n", "                    #print(proj_matrix[ state,idx],state,idx)\n", "        #归一化\n", "        proj_matrix[:,idx] = proj_matrix[:,idx] / np.sqrt(np.sum(proj_matrix[:,idx]**2,axis=0))\n", "    #print(proj_matrix)                \n", "    return proj_matrix"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["def helement(a,b,typee,peri,mtrf,ntrf,mntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = typee[a]\n", "    t = typee[b]\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mntrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mntrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mntrf[b],k,N))/ggun(t*p*z,mntrf[b],k,N)\n", "    return matelement"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["def Ham_total_TPZ(J,h,N, nrep, repr, typee, peri, mtrf, ntrf, mntrf,capr, p, z, k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        state_orginal_rep = []\n", "        sa = repr[ia]\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J\n", "            else:\n", "                Ez -= J\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            #横场项\n", "            #sb = flip_state(sa, i)\n", "            #if ai == 1:\n", "            sb = flip_state(sa, i)\n", "            #else:\n", "                #sb = flip_state(flip_state(sa, j), i)\n", "            representative,state_orginal, l, q, g = represent(N, sb)\n", "            if representative == repr[ia]:\n", "                state_orginal_rep.append(state_orginal)\n", "            if representative in repr:\n", "                ib = repr.index(representative)\n", "                if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                    ib = ib - 1\n", "                    nb = 2\n", "                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                    nb = 2\n", "                else:\n", "                    nb = 1\n", "                for ii in range(ia, ia + na):\n", "                    for jj in range(ib, ib + nb):\n", "                        try:\n", "                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf,mntrf, capr, p, z, l, q, g, N, k)\n", "                            if np.isfinite(elem):\n", "                                Hk[ii, jj] += elem\n", "                        except Exception as e:\n", "                            print(f\"helement error at ii={ii}, jj={jj}: {e}\")\n", "        #state_orginal_all.append(state_orginal_rep)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["def sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,mntrf,capr,p,z,k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia>1 and sa==repr[ia-1]):\n", "            continue\n", "        elif (ia<nrep-1 and sa==repr[ia+1]):\n", "            na=2\n", "        else:\n", "            na=1\n", "        for a in range(ia,ia+na):\n", "            Hk[a,a] += (1/2 )*N    \n", "        for i in range(N):\n", "            for j in range(i+1,N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                if ai != aj:\n", "                    if  ai == 1:   \n", "                        sb = flip_state(flip_state(sa,i),j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa,j),i)\n", "                    representative,state, l,q,g = represent(N,sb)\n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        if ib >1 and repr[ib]==repr[ib-1]:\n", "                            ib = ib-1\n", "                            nb=2\n", "                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\n", "                            nb=2\n", "                        else:\n", "                            nb=1\n", "                        for ii in range(ia,ia+na):\n", "                            for jj in range(ib,ib+nb):\n", "                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,mntrf,capr,p,z,l,q,g,N,k)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    #print(a)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    "]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["def fullspectrum(J,h,N):\n", "    E=[]\n", "    #k=0\n", "    k_min = []\n", "    p_min = []\n", "    z_min = []\n", "    E_min = []\n", "    spi_min = []\n", "\n", "    v_full = []\n", "    Hk_full = []\n", "    new_basis_matrix = []\n", "\n", "    quasi_energies = []\n", "    for k in range(N):\n", "        if k==0 or k==N//2:\n", "            p1=-1\n", "            p2=1\n", "        else:\n", "            p1=1\n", "            p2=1\n", "        for p in range(p1,p2+1,2):\n", "            for z in [-1,1]:\n", "                nrep,repr,typee,peri,mtrf,ntrf,mntrf,capr = findbasis(N,k,p,z)#k,p,z是标记是否有对称性的参数\n", "                print(f\" k: {k}, p: {p}, z: {z}, repr: {repr}, mtrf: {mtrf}, ntrf: {ntrf}, mntrf: {mntrf}, capr: {capr}, sigma: {typee} \")\n", "                if nrep != 0:\n", "                    Hk_1 = Ham_total_TPZ(0*J,h,N,nrep,repr,typee,peri,mtrf,ntrf,mntrf,capr,p,z,k)\n", "                    Hk_2 = Ham_total_TPZ(J,0*h,N,nrep,repr,typee,peri,mtrf,ntrf,mntrf,capr,p,z,k)\n", "                    H_f = expm(-1j*t_1*Hk_1) @ expm(-1j*t_2*Hk_2)\n", "                    #print(repr)\n", "                    # 拼接为完整矩阵\n", "                    if len(Hk_full) == 0:\n", "                        Hk_full = H_f\n", "                    else:\n", "                        Hk_full = block_direct_sum([Hk_full,H_f])#np.block(block_structure)\n", "\n", "                    eigenvalue, featurevector =np.linalg.eig(H_f)\n", "                    Hk_spin = sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,mntrf,capr,p,z,k)\n", "                    spn = transform(nrep,Hk_spin,featurevector)\n", "                    spin = []\n", "                    for spin_i in range(len(spn)):\n", "                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\n", "                    E1 = eigenvalue.tolist()\n", "                    #print(E1)\n", "                    #print(k,p,z,repr)\n", "                    <PERSON>.extend(eigenvalue.tolist())\n", "                    \n", "                    if len(v_full) == 0:\n", "                        v_full = featurevector\n", "                    else:\n", "                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)\n", "                    # 构造投影矩阵 V_k\n", "                    #V_k = build_projection_matrix(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    V_k = build_projection_matrix(N, k, p, z, repr, typee, peri, mtrf, ntrf,mntrf, capr)\n", "                    #print(V_k)\n", "                    #V_k = build_projection_matrix_for_k_test(N, repr, peri, mtrf, ntrf, capr, p, z, k, sigma=1)\n", "                    #矩阵按列直接拼接\n", "                    if len(new_basis_matrix) == 0:\n", "                        new_basis_matrix = V_k\n", "                    else:\n", "                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）\n", "                    #print(new_basis_matrix.shape)\n", "                    if len(E1) != 0:\n", "                        for i in range(len(E1)):\n", "                            idx = E1.index(np.min(E1))\n", "                            k_min.append(k)\n", "                            p_min.append(p)\n", "                            z_min.append(z)\n", "                            E_min.append(E1[idx])\n", "                            spi_min.append(spin[idx])\n", "                            #print(len(E1))\n", "                            #print(np.min(E1),E1.index(np.min(E1)))\n", "                            E1.pop(idx)\n", "                            spin.pop(idx)   \n", "    full_eigenvectors = new_basis_matrix @ v_full\n", "    quasi_energies.extend(-np.angle(E))  # Floquet准能\n", "    # 使用您的方法计算全空间 IPR\n", "    #ipr1 = np.abs(full_eigenvectors) **4\n", "    ipr1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), \n", "                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))\n", "    ipr = np.sum(ipr1, axis=0)\n", "    return quasi_energies,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_full,ipr"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["H1 = Ham_total(N,0*J_z,h_x)\n", "H2 = Ham_total(N,J_z,0*h_x)\n", "H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)\n", "\n", "eigvals_all, v_full = np.linalg.eig(H_F)\n", "#eigvals_all= np.linalg.eigvalsh(H_tot)\n", "quasienergy = [0 for index in range(2**N)]\n", "for i in range(0,2**N,1):\n", "    quasienergy[i] = (-cmath.phase(eigvals_all[i]))\n", "quasienergy1 = quasienergy.copy()\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["#eigvals_all"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["#min_n =8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,0)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" k: 0, p: -1, z: -1, repr: [11, 19, 23, 43], mtrf: [None, None, None, None], ntrf: [None, None, None, None], mntrf: [None, None, 0, 0], capr: [1, 1, 4, 4], sigma: [1, 1, 1, 1] \n", " k: 0, p: -1, z: 1, repr: [11, 19], mtrf: [None, None], ntrf: [None, None], mntrf: [None, None], capr: [1, 1], sigma: [1, 1] \n", " k: 0, p: 1, z: -1, repr: [0, 1, 3, 5, 7, 9, 11, 17, 19, 21, 27, 37], mtrf: [0, 7, 6, 5, 5, 4, None, 3, None, 3, 3, 5], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None], mntrf: [None, None, None, None, None, None, None, None, None, None, None, None], capr: [2, 2, 2, 2, 2, 2, 1, 2, 1, 2, 2, 2], sigma: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] \n", " k: 0, p: 1, z: 1, repr: [0, 1, 3, 5, 7, 9, 11, 15, 17, 19, 21, 23, 27, 37, 43, 45, 51, 85], mtrf: [0, 7, 6, 5, 5, 4, None, 4, 3, None, 3, None, 3, 5, None, 2, 2, 1], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, 4, 2, 1], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, 0, None, None, 0, 6, 0, 0], capr: [2, 2, 2, 2, 2, 2, 1, 5, 2, 1, 2, 4, 2, 2, 4, 5, 5, 5], sigma: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] \n", " k: 1, p: 1, z: -1, repr: [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45], mtrf: [7, 6, 5, 5, 4, None, None, 4, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, 4], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, 0, None, None, 0, 6], capr: [2, 2, 2, 2, 2, 1, 1, 5, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, 1, 1, 1, -1, -1, 1, -1, -1, 1, 1, -1, 1, 1, -1, 1] \n", " k: 1, p: 1, z: 1, repr: [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43], mtrf: [7, 6, 5, 5, 4, None, None, None, None, 3, None, 3, 5, None], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None, None, None], mntrf: [None, None, None, None, None, None, None, None, None, None, 0, None, None, 0], capr: [2, 2, 2, 2, 2, 1, 1, 1, 1, 2, 4, 2, 2, 4], sigma: [1, 1, 1, 1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1] \n", " k: 2, p: 1, z: -1, repr: [1, 3, 5, 7, 9, 11, 11, 17, 19, 19, 21, 23, 27, 37, 43, 51], mtrf: [7, 6, 5, 5, 4, None, None, 3, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 2], mntrf: [None, None, None, None, None, None, None, None, None, None, None, 0, None, None, 0, 0], capr: [2, 2, 2, 2, 2, 1, 1, 2, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, -1, 1, 1, 1, -1, 1, 1, -1, 1, 1, -1, 1, 1, -1, -1] \n", " k: 2, p: 1, z: 1, repr: [1, 3, 5, 7, 9, 11, 11, 15, 17, 19, 19, 21, 23, 27, 37, 43, 45], mtrf: [7, 6, 5, 5, 4, None, None, 4, 3, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, None, 4], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, None, 0, None, None, 0, 6], capr: [2, 2, 2, 2, 2, 1, 1, 5, 2, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, 1, 1, 1, -1] \n", " k: 3, p: 1, z: -1, repr: [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45], mtrf: [7, 6, 5, 5, 4, None, None, 4, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, 4], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, 0, None, None, 0, 6], capr: [2, 2, 2, 2, 2, 1, 1, 5, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, 1, 1, 1, -1, -1, 1, -1, -1, 1, 1, -1, 1, 1, -1, 1] \n", " k: 3, p: 1, z: 1, repr: [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43], mtrf: [7, 6, 5, 5, 4, None, None, None, None, 3, None, 3, 5, None], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None, None, None], mntrf: [None, None, None, None, None, None, None, None, None, None, 0, None, None, 0], capr: [2, 2, 2, 2, 2, 1, 1, 1, 1, 2, 4, 2, 2, 4], sigma: [1, 1, 1, 1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1] \n", " k: 4, p: -1, z: -1, repr: [1, 5, 7, 11, 17, 19, 21, 23, 27, 37, 43, 85], mtrf: [7, 5, 5, None, 3, None, 3, None, 3, 5, None, 1], ntrf: [None, None, None, None, None, None, None, None, None, None, None, 1], mntrf: [None, None, None, None, None, None, None, 0, None, None, 0, 0], capr: [2, 2, 2, 1, 2, 1, 2, 4, 2, 2, 4, 5], sigma: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] \n", " k: 4, p: -1, z: 1, repr: [1, 5, 7, 11, 17, 19, 21, 27, 37], mtrf: [7, 5, 5, None, 3, None, 3, 3, 5], ntrf: [None, None, None, None, None, None, None, None, None], mntrf: [None, None, None, None, None, None, None, None, None], capr: [2, 2, 2, 1, 2, 1, 2, 2, 2], sigma: [1, 1, 1, 1, 1, 1, 1, 1, 1] \n", " k: 4, p: 1, z: -1, repr: [3, 9, 11, 19], mtrf: [6, 4, None, None], ntrf: [None, None, None, None], mntrf: [None, None, None, None], capr: [2, 2, 1, 1], sigma: [1, 1, 1, 1] \n", " k: 4, p: 1, z: 1, repr: [3, 9, 11, 15, 19, 23, 43, 45, 51], mtrf: [6, 4, None, 4, None, None, None, 2, 2], ntrf: [None, None, None, 4, None, None, None, 4, 2], mntrf: [None, None, None, 0, None, 0, 0, 6, 0], capr: [2, 2, 1, 5, 1, 4, 4, 5, 5], sigma: [1, 1, 1, 1, 1, 1, 1, 1, 1] \n", " k: 5, p: 1, z: -1, repr: [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45], mtrf: [7, 6, 5, 5, 4, None, None, 4, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, 4], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, 0, None, None, 0, 6], capr: [2, 2, 2, 2, 2, 1, 1, 5, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, 1, 1, 1, -1, -1, 1, -1, -1, 1, 1, -1, 1, 1, -1, 1] \n", " k: 5, p: 1, z: 1, repr: [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43], mtrf: [7, 6, 5, 5, 4, None, None, None, None, 3, None, 3, 5, None], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None, None, None], mntrf: [None, None, None, None, None, None, None, None, None, None, 0, None, None, 0], capr: [2, 2, 2, 2, 2, 1, 1, 1, 1, 2, 4, 2, 2, 4], sigma: [1, 1, 1, 1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1] \n", " k: 6, p: 1, z: -1, repr: [1, 3, 5, 7, 9, 11, 11, 17, 19, 19, 21, 23, 27, 37, 43, 51], mtrf: [7, 6, 5, 5, 4, None, None, 3, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 2], mntrf: [None, None, None, None, None, None, None, None, None, None, None, 0, None, None, 0, 0], capr: [2, 2, 2, 2, 2, 1, 1, 2, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, -1, 1, 1, 1, -1, 1, 1, -1, 1, 1, -1, 1, 1, -1, -1] \n", " k: 6, p: 1, z: 1, repr: [1, 3, 5, 7, 9, 11, 11, 15, 17, 19, 19, 21, 23, 27, 37, 43, 45], mtrf: [7, 6, 5, 5, 4, None, None, 4, 3, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, None, 4], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, None, 0, None, None, 0, 6], capr: [2, 2, 2, 2, 2, 1, 1, 5, 2, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, 1, 1, 1, -1] \n", " k: 7, p: 1, z: -1, repr: [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45], mtrf: [7, 6, 5, 5, 4, None, None, 4, None, None, 3, None, 3, 5, None, 2], ntrf: [None, None, None, None, None, None, None, 4, None, None, None, None, None, None, None, 4], mntrf: [None, None, None, None, None, None, None, 0, None, None, None, 0, None, None, 0, 6], capr: [2, 2, 2, 2, 2, 1, 1, 5, 1, 1, 2, 4, 2, 2, 4, 5], sigma: [1, 1, 1, 1, -1, -1, 1, -1, -1, 1, 1, -1, 1, 1, -1, 1] \n", " k: 7, p: 1, z: 1, repr: [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43], mtrf: [7, 6, 5, 5, 4, None, None, None, None, 3, None, 3, 5, None], ntrf: [None, None, None, None, None, None, None, None, None, None, None, None, None, None], mntrf: [None, None, None, None, None, None, None, None, None, None, 0, None, None, 0], capr: [2, 2, 2, 2, 2, 1, 1, 1, 1, 2, 4, 2, 2, 4], sigma: [1, 1, 1, 1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1] \n"]}], "source": ["E_full_block,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform ,Hk_block_full,ipr= fullspectrum(J_z,h_x,N)\n", "    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))\n", "    #print(np.array(eigvals))\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["256 256\n"]}], "source": ["print (len(quasienergy),len(quasienergy))\n", "for x in E_full_block:\n", "    index = np.abs(quasienergy - x).argmin()\n", "    if np.isclose(quasienergy[index], x) != True:\n", "        print(np.isclose(quasienergy[index], x), quasienergy[index], x)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(quasienergy1)), np.sort(quasienergy1), label='full',s = 50)\n", "plt.scatter(range(len(E_full_block)), np.sort(E_full_block), label='block',s=10,marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'file = open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') \\nlines = [\"\"] \\nwith open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') as file: \\n    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\\n    for i in range(len(k_min)):\\n        if i%(min_n)==0:\\n            file.write(\"----------------------------------------\"+\"\\n\")\\n        file.write(str(k_min[i])+\"\\t\")\\n        file.write(str(p_min[i])+\"\\t\")\\n        file.write(str(z_min[i])+\"\\t\")\\n        file.write(str(E_min[i])+\"\\t\")\\n        file.write(str(spi_min[i])+\"\\t\")\\n        file.write(\"\\n\")\\nfile.close()'"]}, "execution_count": 2461, "metadata": {}, "output_type": "execute_result"}], "source": ["'''file = open('ED_TPZ_1D_N=16.txt', 'w') \n", "lines = [\"\"] \n", "with open('ED_TPZ_1D_N=16.txt', 'w') as file: \n", "    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\n", "    for i in range(len(k_min)):\n", "        if i%(min_n)==0:\n", "            file.write(\"----------------------------------------\"+\"\\n\")\n", "        file.write(str(k_min[i])+\"\\t\")\n", "        file.write(str(p_min[i])+\"\\t\")\n", "        file.write(str(z_min[i])+\"\\t\")\n", "        file.write(str(E_min[i])+\"\\t\")\n", "        file.write(str(spi_min[i])+\"\\t\")\n", "        file.write(\"\\n\")\n", "file.close()'''"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["\n", "full_evecs_norm = U_transform @ v_block_full \n", "direct_evecs_norm = v_full "]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), \n", "                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))\n", "IPR_block_full = ipr\n", "ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), \n", "                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))\n", "IPR_full = np.sum(ipr2, axis=0)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.06248699, 0.01953125, 0.01953125, 0.01562175, 0.03125   ,\n", "       0.03125   , 0.49979181, 0.02342937, 0.02342937, 0.031237  ,\n", "       0.03123699, 0.01024913, 0.01024913, 0.03124349, 0.03123699,\n", "       0.01367188, 0.01367188, 0.02538534, 0.49979181, 0.02342889,\n", "       0.02342659, 0.0234266 , 0.0234289 , 0.49979181, 0.02342889,\n", "       0.0234289 , 0.02342659, 0.0234266 , 0.01281431, 0.01281431,\n", "       0.01281273, 0.01281273, 0.01757081, 0.01757081, 0.014594  ,\n", "       0.02304835, 0.02343017, 0.02343505, 0.02343507, 0.0234302 ,\n", "       0.0234302 , 0.02343017, 0.0087858 , 0.00878579, 0.01268945,\n", "       0.01268942, 0.012694  , 0.01269398, 0.02343505, 0.02343507,\n", "       0.00878814, 0.00878815, 0.0234326 , 0.03124809, 0.02343264,\n", "       0.02343264, 0.0234326 , 0.03124809, 0.00521732, 0.00521732,\n", "       0.01626225, 0.00521656, 0.01626222, 0.00521657, 0.01385875,\n", "       0.01482034, 0.02343262, 0.03124349, 0.02343262, 0.02343262,\n", "       0.02343262, 0.02402965, 0.02402965, 0.00745971, 0.01757447,\n", "       0.00745971, 0.03124349, 0.01757447, 0.00916708, 0.02178963,\n", "       0.01842061, 0.00842575, 0.0234326 , 0.02343263, 0.02343261,\n", "       0.02343264, 0.0234326 , 0.02343263, 0.02343261, 0.02343264,\n", "       0.01772739, 0.0177274 , 0.0177274 , 0.01772739, 0.00668159,\n", "       0.00668158, 0.00668158, 0.00668159, 0.03125   , 0.02343505,\n", "       0.02343019, 0.02343017, 0.02343507, 0.02343017, 0.02343019,\n", "       0.01269326, 0.01269326, 0.00878684, 0.00878685, 0.01269398,\n", "       0.012694  , 0.02343505, 0.02343507, 0.00878815, 0.00878814,\n", "       0.02343263, 0.03123889, 0.02343261, 0.02343263, 0.03123889,\n", "       0.02343261, 0.00521445, 0.00521445, 0.00521709, 0.01626392,\n", "       0.00521709, 0.0162639 , 0.01464963, 0.0294547 , 0.03124349,\n", "       0.03123699, 0.02342937, 0.02342937, 0.031237  , 0.49979181,\n", "       0.01024913, 0.01024913, 0.03123699, 0.02538534, 0.01367187,\n", "       0.01367187, 0.04685834, 0.04687214, 0.01611203, 0.01610599,\n", "       0.01610599, 0.01611203, 0.04685834, 0.04687214, 0.0390625 ,\n", "       0.06248699, 0.01562175, 0.01953125, 0.01953125, 0.04687214,\n", "       0.04685834, 0.02343679, 0.02343334, 0.02343679, 0.02343334,\n", "       0.04685834, 0.04687214, 0.03125   , 0.02343505, 0.0234302 ,\n", "       0.02343017, 0.02343507, 0.02343017, 0.0234302 , 0.01269002,\n", "       0.01269   , 0.00878523, 0.00878521, 0.012694  , 0.02343505,\n", "       0.02343507, 0.01269398, 0.00878814, 0.00878815, 0.02343263,\n", "       0.03123889, 0.02343261, 0.02343263, 0.03123889, 0.02343261,\n", "       0.01626299, 0.01626299, 0.01626252, 0.00521686, 0.01626253,\n", "       0.00521685, 0.00275238, 0.01427387, 0.02343262, 0.03124349,\n", "       0.02343262, 0.02343262, 0.02343262, 0.00745971, 0.00745971,\n", "       0.02402965, 0.01757447, 0.02402965, 0.03124349, 0.01757447,\n", "       0.00767827, 0.01442222, 0.02507056, 0.02076676, 0.0234326 ,\n", "       0.02343263, 0.02343261, 0.02343264, 0.0234326 , 0.02343263,\n", "       0.02343261, 0.02343264, 0.00668115, 0.00668115, 0.00668115,\n", "       0.00668115, 0.01772784, 0.01772781, 0.01772781, 0.01772784,\n", "       0.03125   , 0.02343017, 0.02343505, 0.02343507, 0.02343019,\n", "       0.02343019, 0.02343017, 0.00878742, 0.00878742, 0.01269269,\n", "       0.01269268, 0.012694  , 0.02343505, 0.02343507, 0.01269398,\n", "       0.00878814, 0.00878815, 0.0234326 , 0.03124809, 0.02343264,\n", "       0.02343264, 0.0234326 , 0.03124809, 0.01626586, 0.01626586,\n", "       0.00521681, 0.01626361, 0.0052168 , 0.01626363, 0.00411425,\n", "       0.00349829])"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["(IPR_block_full)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6 0.49979181034555425\n", "18 0.49979181034555337\n", "23 0.499791810345554\n", "134 0.4997918103455543\n"]}], "source": ["for i in range(len(IPR_block_full)):\n", "    if IPR_block_full[i] > 0.4:\n", "        print(i,IPR_block_full[i])"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.49979181, 0.49979181, 0.49979181, 0.49979181, 0.02342889,\n", "       0.02342889, 0.02342659, 0.0234289 , 0.02342937, 0.02342937,\n", "       0.0234289 , 0.02342659, 0.0234266 , 0.0316357 , 0.02342937,\n", "       0.0234266 , 0.03123699, 0.0291093 , 0.02893295, 0.02342937,\n", "       0.03313721, 0.03185586, 0.02833282, 0.031237  , 0.03300696,\n", "       0.02839513, 0.0276794 , 0.02352386, 0.031237  , 0.03250313,\n", "       0.03363402, 0.03284965, 0.02867056, 0.02372036, 0.03546236,\n", "       0.03526644, 0.03451258, 0.03299149, 0.02549242, 0.02680016,\n", "       0.03108232, 0.02546008, 0.03114599, 0.02436228, 0.03123699,\n", "       0.03704232, 0.03213659, 0.03561138, 0.03388354, 0.03417325,\n", "       0.03668269, 0.0329155 , 0.02814165, 0.0298773 , 0.02293624,\n", "       0.02549072, 0.02736854, 0.03476743, 0.02811257, 0.03065712,\n", "       0.02786348, 0.03625283, 0.02769807, 0.01281431, 0.04682057,\n", "       0.03942253, 0.04225503, 0.03467189, 0.05408063, 0.03189203,\n", "       0.02807624, 0.03706312, 0.03952229, 0.03506809, 0.02958611,\n", "       0.03164381, 0.03334319, 0.02688976, 0.03048053, 0.02918434,\n", "       0.03354944, 0.03319721, 0.03565863, 0.0284845 , 0.01281431,\n", "       0.04919468, 0.03202805, 0.03347289, 0.0270089 , 0.0444197 ,\n", "       0.0276986 , 0.03939521, 0.03023077, 0.03995878, 0.04251269,\n", "       0.03124054, 0.03335692, 0.03773574, 0.02793414, 0.02524037,\n", "       0.03476552, 0.03618357, 0.03070234, 0.03632175, 0.03838937,\n", "       0.04141649, 0.0615436 , 0.05999644, 0.0393297 , 0.04542816,\n", "       0.03468579, 0.05198934, 0.0443082 , 0.02408419, 0.02963159,\n", "       0.04072026, 0.02563253, 0.01281273, 0.0366854 , 0.01024913,\n", "       0.01827799, 0.01817493, 0.01024913, 0.01822331, 0.0183042 ,\n", "       0.01281273, 0.01024913, 0.01024913, 0.0181255 , 0.01117344,\n", "       0.01272769, 0.01311928, 0.01145714, 0.01045107, 0.01154921,\n", "       0.01224239, 0.01310536, 0.01274412, 0.01303969, 0.01640821,\n", "       0.01371374, 0.01350829, 0.01518451, 0.01345006, 0.01563858,\n", "       0.01113696, 0.01175479, 0.0160858 , 0.01356784, 0.01120501,\n", "       0.0159554 , 0.01320472, 0.01596402, 0.01153506, 0.01234971,\n", "       0.01645987, 0.012385  , 0.01414484, 0.01589745, 0.01366684,\n", "       0.01370255, 0.01097484, 0.01470559, 0.01080259, 0.01139462,\n", "       0.01512316, 0.01236713, 0.03123699, 0.01948262, 0.01964574,\n", "       0.01923315, 0.01902864, 0.01311537, 0.01314505, 0.01311404,\n", "       0.01316845, 0.01243471, 0.01241889, 0.01317859, 0.01315786,\n", "       0.0138809 , 0.01537642, 0.01551967, 0.01757081, 0.01757081,\n", "       0.01250415, 0.01307503, 0.01611912, 0.03123699, 0.01070679,\n", "       0.01115143, 0.01670143, 0.0106747 , 0.03378117, 0.01011644,\n", "       0.01248437, 0.01168078, 0.01080826, 0.01017248, 0.01427133,\n", "       0.01149671, 0.01417903, 0.01133019, 0.00977305, 0.01794456,\n", "       0.01384329, 0.01342155, 0.0195229 , 0.01078795, 0.01184622,\n", "       0.01472937, 0.02092726, 0.01199924, 0.01058875, 0.01247099,\n", "       0.01715633, 0.01371742, 0.01515664, 0.01096137, 0.01522309,\n", "       0.01134843, 0.01077112, 0.01313764, 0.01217245, 0.01372186,\n", "       0.01207785, 0.01157278, 0.01484318, 0.01484937, 0.01420135,\n", "       0.01103808, 0.01242943, 0.01221134, 0.01353746, 0.01274533,\n", "       0.01872278, 0.02379969, 0.01471817, 0.02184983, 0.01726319,\n", "       0.01620861, 0.01321986, 0.01321409, 0.02194709, 0.01275908,\n", "       0.01456392, 0.01545178, 0.02738667, 0.01341298, 0.01316145,\n", "       0.01633504, 0.01088308, 0.01122261, 0.0121606 , 0.01611856,\n", "       0.01286736])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["IPR_full"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 代码位置：[floquet_ED_TPZ copy_forTFIM copy 2.ipynb](./floquet_ED_TPZ%20copy_forTFIM%20copy%202.ipynb#L100-L120)\n", "# 修正前：\n", "# U_transform = full_vecs @ full_vecs_eig\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1129100461.py:41: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def plot_matrix(matrix, title=\"矩阵热图\", cmap=\"viridis\", annot=True, figsize=(8, 6)):\n", "    \"\"\"\n", "    可视化矩阵，用颜色深浅表示元素值差异\n", "    \n", "    参数:\n", "        matrix: 待可视化的NumPy矩阵\n", "        title: 图表标题\n", "        cmap: 颜色映射（如\"viridis\", \"coolwarm\", \"RdBu\"）\n", "        annot: 是否在单元格中显示数值\n", "        figsize: 图表尺寸\n", "    \"\"\"\n", "    # 设置绘图风格\n", "    plt.style.use(\"default\")\n", "    \n", "    # 创建画布\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # 绘制热图\n", "    sns.heatmap(\n", "        matrix,\n", "        annot=annot,          # 显示数值\n", "        fmt=\".2f\",            # 数值格式（保留2位小数）\n", "        cmap=cmap,            # 颜色映射\n", "        cbar=True,            # 显示颜色条\n", "        square=True,          # 单元格为正方形\n", "        ax=ax,                # 子图对象\n", "        linewidths=0.5,       # 单元格边框宽度\n", "        linecolor=\"gray\"      # 单元格边框颜色\n", "    )\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=14, pad=10)\n", "    ax.set_xlabel(\"列索引\", fontsize=12, labelpad=10)\n", "    ax.set_ylabel(\"行索引\", fontsize=12, labelpad=10)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig\n", "\n", "fig = plot_matrix(\n", "        #np.abs(U_transform @ v_block_full ),\n", "        #np.abs((U_transform @ v_block_full ) - v_full),\n", "        #np.abs(v_full),\n", "        #np.abs(U_transform @ U_transform.T.conj()),\n", "        #np.abs(full_vecs_eig),\n", "        np.abs(U_transform),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(V @ np.linalg.inv(V)),\n", "        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),\n", "        #np.abs(H_F),\n", "        #np.abs(V ** 4),\n", "        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(H_F),\n", "        #np.abs(np.linalg.inv(v_block_full) @ np.linalg.inv(U_transform) @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  - np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ H_F @ U_transform @ v_block_full  ),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  ),\n", "        #np.abs(np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(U_transform)),\n", "        #np.abs(Hk_full),\n", "        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),\n", "        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data - E )),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data) - np.diag(E)),\n", "        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),\n", "        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),\n", "        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),\n", "        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),\n", "        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),\n", "        title=\"示例矩阵热图（颜色表示元素值大小）\",\n", "        cmap=\"coolwarm\"  # 冷暖色映射（正值暖色，负值冷色）\n", "    )\n", "    \n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 25490 (\\N{CJK UNIFIED IDEOGRAPH-6392}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 21518 (\\N{CJK UNIFIED IDEOGRAPH-540E}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 30340 (\\N{CJK UNIFIED IDEOGRAPH-7684}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 26412 (\\N{CJK UNIFIED IDEOGRAPH-672C}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 24449 (\\N{CJK UNIFIED IDEOGRAPH-5F81}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/3029708358.py:23: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25490 (\\N{CJK UNIFIED IDEOGRAPH-6392}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21518 (\\N{CJK UNIFIED IDEOGRAPH-540E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30340 (\\N{CJK UNIFIED IDEOGRAPH-7684}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 26412 (\\N{CJK UNIFIED IDEOGRAPH-672C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24449 (\\N{CJK UNIFIED IDEOGRAPH-5F81}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ipr_full_sorted = np.sort(IPR_full)\n", "ipr_block_sorted = np.sort(IPR_block_full)\n", "# 步骤6：可视化对比\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# 子图1：排序后的IPR对比（散点图）\n", "ax1.scatter(quasienergy1, IPR_full, label=\"full space\", alpha=0.8, s=50)\n", "ax1.scatter((E_full_block), IPR_block_full, label=\"block space\", alpha=0.8, s=30, marker=\"x\")\n", "ax1.set_xlabel(\"排序后的本征态索引\")\n", "ax1.set_ylabel(\"IPR值\")\n", "#ax1.set_title(f\"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）\")\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "ax2.scatter(range(len(ipr_full_sorted)), np.sort(quasienergy), label=\"full space\", alpha=0.8, s=50)\n", "ax2.scatter(range(len(ipr_block_sorted)), np.sort(E_full_block), label=\"block space\", alpha=0.8, s=30, marker=\"x\")\n", "ax2.set_xlabel(\"排序后的本征态索引\")\n", "ax2.set_ylabel(\"E值\")\n", "#ax1.set_title(f\"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）\")\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.+0.j, 1.+0.j, 1.+0.j, 0.+0.j, 1.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       1.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       1.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       1.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       1.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 1.+0.j,\n", "       1.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 1.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 1.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 1.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 0.+0.j, 1.+0.j,\n", "       0.+0.j, 0.+0.j, 0.+0.j, 1.+0.j, 0.+0.j, 1.+0.j, 1.+0.j, 0.+0.j])"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["U_transform @ U_transform.T.conj()[:,1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j,\n", "       1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j, 1.+0.j])"]}, "execution_count": 2471, "metadata": {}, "output_type": "execute_result"}], "source": ["np.diag(U_transform @ U_transform.T.conj())"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["256\n", "找到4个IPR大于0.4的向量，索引为: [  6  18  23 134]\n", "[[7.07033163e-01 7.07033163e-01 9.58243370e-10 0.00000000e+00]\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009360e-08]\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009360e-08]\n", " ...\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009360e-08]\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009360e-08]\n", " [7.07033163e-01 7.07033163e-01 9.58243370e-10 0.00000000e+00]]\n", "2.3416737410660495\n", "-0.7999189125237436\n", "0.7999189125237436\n", "-2.3416737410660495\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["256\n", "找到4个IPR大于0.4的向量，索引为: [0 1 2 3]\n", "[[7.07033163e-01 7.07033163e-01 9.58243029e-10 5.87481276e-17]\n", " [3.60728113e-03 3.60728113e-03 9.39065970e-08 9.39009361e-08]\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009360e-08]\n", " ...\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009358e-08]\n", " [3.60728113e-03 3.60728113e-03 9.39065969e-08 9.39009362e-08]\n", " [7.07033163e-01 7.07033163e-01 9.58243712e-10 6.86356629e-17]]\n", "-0.7999189125237415\n", "2.34167374106605\n", "0.7999189125237447\n", "-2.341673741066051\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_54606/1825277464.py:56: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_selected_vectors(ipr,v,E, ipr_threshold):\n", "    \"\"\"\n", "    筛选IPR大于阈值的列向量并绘制\n", "    \n", "    参数:\n", "    V: 输入矩阵，每行代表一个基矢，每列代表一个向量\n", "    ipr_threshold: IPR筛选阈值，默认0.5\n", "    \"\"\"\n", "    # 计算IPR\n", "    print(len(ipr))\n", "    # 筛选IPR大于阈值的列索引\n", "    selected_indices = np.where(ipr > ipr_threshold)[0]\n", "    if len(selected_indices) == 0:\n", "        print(f\"没有找到IPR大于{ipr_threshold}的向量\")\n", "        return\n", "    \n", "    print(f\"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}\")\n", "    \n", "    # 提取对应的列向量\n", "    selected_vectors = v[:,selected_indices]\n", "    print(selected_vectors)\n", "    \n", "    \n", "    # 设置绘图风格\n", "    plt.style.use('seaborn-v0_8-ticks')\n", "    \n", "    # 计算子图布局（最多5列）\n", "    n_cols = min(5, len(selected_indices))\n", "    n_rows = (len(selected_indices) + n_cols - 1) // n_cols\n", "    E_slect = []\n", "    # 创建画布\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))\n", "    axes = np.ravel(axes)  # 转换为一维数组便于索引\n", "    for i, idx in enumerate(selected_indices):\n", "        E_slect.append(E[idx])\n", "        print(E[idx])\n", "    # 绘制每个选中的向量\n", "    for i, idx in enumerate(selected_indices):\n", "        ax = axes[i]\n", "        # 绘制向量的绝对值点线图\n", "        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')\n", "        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))\n", "        ax.set_title(f'向量索引: {idx}\\nIPR = {ipr[idx]:.4f},E = {E[idx]:.4f}', fontsize=10)\n", "        ax.set_xlabel('基矢索引', fontsize=8)\n", "        ax.set_ylabel('|振幅|', fontsize=8)\n", "        ax.tick_params(axis='both', which='major', labelsize=6)\n", "    \n", "    # 隐藏未使用的子图\n", "    for i in range(len(selected_indices), len(axes)):\n", "        axes[i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return selected_vectors, selected_indices\n", "\n", "# 示例使用\n", "if __name__ == \"__main__\":\n", "    # 筛选并绘图\n", "    selected_vecs_k, selected_idx_k = plot_selected_vectors(ipr,np.abs(U_transform @ v_block_full),E_full_block, ipr_threshold=0.4)\n", "    selected_vecs, selected_idx = plot_selected_vectors(IPR_full,np.abs(v_full), quasienergy1,ipr_threshold=0.4)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "nikola": {"category": "Strongly Correlated Systems", "date": "2020-12-20 13:18:31 UTC+08:00", "description": "", "link": "", "slug": "exact-diagonalization", "tags": "Correlated Electronic Systems, Numerical Method, Exact Diagonalization", "title": "Exact Diagonalization", "type": "text"}, "toc-autonumbering": true}, "nbformat": 4, "nbformat_minor": 4}