#!/usr/bin/env python3
"""
简化的2D TFIM块对角化测试代码
修正了原代码中的关键问题
"""

import numpy as np
from collections import defaultdict

# 基本函数
def get_site_value(state: int, site: int) -> int:
    """获取状态state在site位置的自旋值（0或1）"""
    return (state >> site) & 1

def flip_state(state: int, site: int) -> int:
    """翻转状态state在site位置的自旋"""
    return state ^ (1 << site)

def translate_2d(state: int, row: int, col: int, shift_row: int, shift_col: int) -> int:
    """二维格点上的平移操作"""
    N = row * col
    new_state = 0
    
    for i in range(N):
        old_row, old_col = divmod(i, col)
        new_row = (old_row + shift_row) % row
        new_col = (old_col + shift_col) % col
        new_i = new_row * col + new_col
        
        if get_site_value(state, i):
            new_state |= (1 << new_i)
    
    return new_state

def represent(N: int, state: int, row: int, col: int):
    """找到状态的代表元及平移步数"""
    min_state = state
    best_l_row, best_l_col = 0, 0
    
    for l_row in range(row):
        for l_col in range(col):
            translated = translate_2d(state, row, col, l_row, l_col)
            if translated < min_state:
                min_state = translated
                best_l_row, best_l_col = l_row, l_col
    
    return min_state, best_l_row, best_l_col, 0

def orbit_period_2d(state: int, row: int, col: int):
    """计算二维轨道的周期"""
    # 行方向周期
    R_row = 1
    current = translate_2d(state, row, col, 1, 0)
    while current != state and R_row < row:
        current = translate_2d(current, row, col, 1, 0)
        R_row += 1
    
    # 列方向周期
    R_col = 1
    current = translate_2d(state, row, col, 0, 1)
    while current != state and R_col < col:
        current = translate_2d(current, row, col, 0, 1)
        R_col += 1
    
    return R_row, R_col

def is_k_compatible(N: int, R: int, k: int) -> bool:
    """检查动量k与轨道周期R的兼容性"""
    return (k * R) % N == 0

def build_translation_basis_by_k(col: int, row: int):
    """
    构建二维平移对称性下的基，按动量量子数(k_row, k_col)分块
    修正：每个代表元只属于一个动量块（选择最小的兼容动量）
    """
    basis = {}
    seen = set()
    N = col * row

    print(f"构建 {row}x{col} 格点的平移基...")

    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s, row, col)
        if rep in seen:
            continue
        seen.add(rep)

        # 计算轨道周期
        R_row, R_col = orbit_period_2d(rep, row, col)
        print(f"代表元 {rep:0{N}b}: R_row={R_row}, R_col={R_col}")

        # 找到第一个兼容的动量（按字典序）
        found = False
        for k_row in range(row):
            for k_col in range(col):
                # 检查动量兼容性
                if is_k_compatible(row, R_row, k_row) and is_k_compatible(col, R_col, k_col):
                    k = (k_row, k_col)
                    if k not in basis:
                        basis[k] = {
                            'repr': [],
                            'peri_row': [],
                            'peri_col': []
                        }
                    basis[k]['repr'].append(rep)
                    basis[k]['peri_row'].append(R_row)
                    basis[k]['peri_col'].append(R_col)
                    print(f"  添加到块 k=({k_row},{k_col})")
                    found = True
                    break
            if found:
                break

    print(f"构建完成，共 {len(basis)} 个动量块")

    # 调试信息
    total_reps = sum(len(data['repr']) for data in basis.values())
    print(f"总代表元数: {total_reps}")

    # 计算每个代表元对应的轨道大小
    total_states = 0
    for k, data in basis.items():
        for i, rep in enumerate(data['repr']):
            orbit_size = data['peri_row'][i] * data['peri_col'][i]
            total_states += orbit_size
            print(f"块 {k}, 代表元 {rep:0{N}b}: 轨道大小 = {orbit_size}")

    print(f"总状态数: {total_states}, 期望: {2**N}")

    return basis

def helement_translation_2d_simple(Ra_row: int, Ra_col: int, Rb_row: int, Rb_col: int, 
                                  l_row: int, l_col: int, k_row: int, k_col: int, 
                                  row: int, col: int) -> complex:
    """二维平移对称性下的矩阵元相位因子"""
    return (np.sqrt(Ra_row / Rb_row) * np.sqrt(Ra_col / Rb_col) * 
            np.exp(1j * 2 * np.pi * k_row * l_row / row) * 
            np.exp(1j * 2 * np.pi * k_col * l_col / col))

def build_block_Hamiltonian_2d_corrected(row: int, col: int, 
                                         reps: list, 
                                         peri_row: list, peri_col: list,
                                         k_row: int, k_col: int,
                                         J: float, h: float) -> np.ndarray:
    """构建二维平移对称性下的块对角哈密顿量（修正版本）"""
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    N = row * col
    
    for ia in range(nrep):
        sa = reps[ia]
        Ra_row = peri_row[ia]
        Ra_col = peri_col[ia]
        
        # 对角项：计算二维格点上的相邻自旋相互作用
        Ez = 0.0
        for i in range(N):
            row_i, col_i = divmod(i, col)
            
            # 行方向相邻
            j_row = (row_i + 1) % row
            j = j_row * col + col_i
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)
            
            # 列方向相邻
            j_col = (col_i + 1) % col
            j = row_i * col + j_col
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)
        
        Hk[ia, ia] += Ez
        
        # 非对角项：横向场引起的自旋翻转
        for i in range(N):
            sb = flip_state(sa, i)
            rep_b, l_row, l_col, _ = represent(N, sb, row, col)
            if rep_b in reps:
                ib = reps.index(rep_b)
                Rb_row = peri_row[ib]
                Rb_col = peri_col[ib]
                elem = h * helement_translation_2d_simple(
                    Ra_row, Ra_col, Rb_row, Rb_col,
                    l_row, l_col, k_row, k_col,
                    row, col
                )
                Hk[ia, ib] += elem
    
    return Hk

def build_full_hamiltonian_2d(row: int, col: int, J: float, h: float) -> np.ndarray:
    """构建完整的2D TFIM哈密顿量（用于验证）"""
    N = row * col
    dim = 2 ** N
    H = np.zeros((dim, dim), dtype=complex)

    for state in range(dim):
        # 对角项：相邻自旋相互作用
        Ez = 0.0
        for i in range(N):
            row_i, col_i = divmod(i, col)

            # 行方向相邻
            j_row = (row_i + 1) % row
            j = j_row * col + col_i
            ai = get_site_value(state, i)
            aj = get_site_value(state, j)
            Ez += (J if ai == aj else -J)

            # 列方向相邻
            j_col = (col_i + 1) % col
            j = row_i * col + j_col
            ai = get_site_value(state, i)
            aj = get_site_value(state, j)
            Ez += (J if ai == aj else -J)

        H[state, state] = Ez

        # 非对角项：横向场
        for i in range(N):
            flipped_state = flip_state(state, i)
            H[state, flipped_state] += h

    return H

def compute_2d_spectrum(col: int, row: int, J: float, h: float):
    """计算二维TFIM的完整能谱"""
    basis = build_translation_basis_by_k(col, row)

    # 验证：计算所有轨道覆盖的状态总数
    all_states = set()
    for k, data in basis.items():
        for i, rep in enumerate(data['repr']):
            R_row = data['peri_row'][i]
            R_col = data['peri_col'][i]
            # 生成该代表元的完整轨道
            for t_row in range(R_row):
                for t_col in range(R_col):
                    state = translate_2d(rep, row, col, t_row, t_col)
                    all_states.add(state)

    N = col * row
    print(f"验证维度: 轨道覆盖状态数 = {len(all_states)}, 期望 = {2**N}")

    if len(all_states) != 2**N:
        print("错误：轨道覆盖不完整或有重叠")
        missing = set(range(2**N)) - all_states
        if missing:
            print(f"缺失状态: {[f'{s:0{N}b}' for s in sorted(missing)]}")
        return [], []

    total_dim = sum(len(v['repr']) for v in basis.values())
    print(f"代表元总数: {total_dim}")
    
    energies = []
    labels = []
    
    for k, data in sorted(basis.items()):
        k_row, k_col = k
        reps = data['repr']
        peri_row = data['peri_row']
        peri_col = data['peri_col']
        
        if len(reps) == 0:
            continue
            
        print(f"处理动量块 k=({k_row},{k_col}), 包含 {len(reps)} 个代表元")
        
        # 构建哈密顿量
        Hk = build_block_Hamiltonian_2d_corrected(
            row, col, reps, peri_row, peri_col,
            k_row, k_col, J, h
        )
        
        # 求本征值
        w, _ = np.linalg.eig(Hk)
        print(f"  本征值数量: {len(w)}")
        
        energies.extend(w.real.tolist())
        labels.extend([k_row * col + k_col] * len(w))
    
    return energies, labels

def test_2d_corrected():
    """测试修正后的2D代码"""
    print("="*60)
    print("测试修正后的2D TFIM块对角化代码")
    print("="*60)

    # 测试参数
    row, col = 2, 2
    J, h = 1.0, 0.5

    print(f"测试 {row}x{col} 二维格点，J={J}, h={h}")

    try:
        # 1. 块对角化方法
        print("\n1. 块对角化方法:")
        energies_block, labels = compute_2d_spectrum(col, row, J, h)

        print(f"块对角化本征值数量: {len(energies_block)}")

        # 按动量块分组显示结果
        energy_by_k = defaultdict(list)
        for e, k in zip(energies_block, labels):
            energy_by_k[k].append(e)

        print("各动量块的本征值:")
        for k in sorted(energy_by_k.keys()):
            k_row, k_col = divmod(k, col)
            energies_k = sorted(energy_by_k[k])
            print(f"k=({k_row},{k_col}): {len(energies_k)} 个本征值")
            if len(energies_k) <= 5:
                print(f"  能量: {[f'{e:.4f}' for e in energies_k]}")
            else:
                print(f"  能量: {[f'{e:.4f}' for e in energies_k[:3]]} ... {[f'{e:.4f}' for e in energies_k[-2:]]}")

        # 2. 直接对角化方法（用于验证）
        print("\n2. 直接对角化方法（验证）:")
        H_full = build_full_hamiltonian_2d(row, col, J, h)
        energies_full, _ = np.linalg.eigh(H_full)
        energies_full = sorted(energies_full.real)

        print(f"直接对角化本征值数量: {len(energies_full)}")
        print(f"前几个本征值: {[f'{e:.4f}' for e in energies_full[:5]]}")
        print(f"后几个本征值: {[f'{e:.4f}' for e in energies_full[-5:]]}")

        # 3. 比较结果
        print("\n3. 结果比较:")
        energies_block_sorted = sorted(energies_block)

        print("注意：块对角化给出的是每个不可约表示的一个代表本征值")
        print("直接对角化给出所有本征值（包括简并的）")

        # 检查块对角化的本征值是否都在直接对角化的结果中
        matches = 0
        for e_block in energies_block_sorted:
            for e_full in energies_full:
                if abs(e_block - e_full) < 1e-10:
                    matches += 1
                    break

        print(f"匹配的本征值数量: {matches}/{len(energies_block_sorted)}")

        if matches == len(energies_block_sorted):
            print("✓ 所有块对角化本征值都在直接对角化结果中找到匹配")
        else:
            print("✗ 存在不匹配的本征值")

        print("\n✓ 测试成功!")
        return True

    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_2d_corrected()
    
    if success:
        print("\n" + "="*60)
        print("主要修正:")
        print("1. 基构建函数现在正确地按(k_row, k_col)分块")
        print("2. 哈密顿量构建函数使用块的固定动量，而不是每个态的动量")
        print("3. 矩阵元计算遵循1D代码的正确模式")
        print("4. 二维相邻相互作用正确实现（行方向和列方向）")
        print("5. 移除了冗余的调试输出和错误的数据结构")
        print("="*60)
