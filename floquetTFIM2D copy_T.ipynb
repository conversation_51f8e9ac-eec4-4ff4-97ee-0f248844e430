import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import expm
from math import * 
import pandas as pd
import cmath
import os #导入os库
import random
import time
import seaborn as sns
import cmath
import scipy
import functools
import matplotlib
import matplotlib.pyplot as plt
%matplotlib inline

def get_hamiltonian_sparse(col,row, Jz, hx):
    '''
    Creates the Hamiltonian of the Transverse Field Ising model
    on a linear chain lattice with periodic boundary conditions.

    The Hamiltonian is given by:
    H = -Jz \sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \sum_{i=1}^{L} S_i^x - hz \sum_{i=1}^{L} S_i^z
    Args:
        L (int): length of chain
        J (float): coupling constant for Ising term
        hx (float): coupling constant for transverse field

    Returns:
        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:
        hamiltonian_rows (list of ints): row index of non-zero elements
        hamiltonian_cols (list of ints): column index of non-zero elements
        hamiltonian_data (list of floats): value of non-zero elements
    '''
    L = col * row
    def get_site_value(state, site):
        ''' Function to get local value at a given site '''
        return (state >> site) & 1
        #返回值为state的第site位上的二进制值

    def hilbertspace_dimension(L):
        ''' return dimension of hilbertspace '''
        return 2**L

    def flip_state(state: int, index: int) -> int:
        """翻转一个整数某位置处的二进制值"""
        mask = 1 << index
        return state ^ mask
    # Define chain lattice
    #ising_bonds_col = [(site, (site+1)%col) for site in range(col-1)]#开放边条件
    #ising_bonds_row = [(site, (site+1)%row) for site in range(row-1)]#开放边条件
    #ising_bonds_col = [(site, (site+1)%col) for site in range(col)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0
    #ising_bonds_row = [(site, (site+1)%row) for site in range(row)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0
    ising_bonds = []

    #  周期边条件
    for i in range(row):          # 遍历每一行
        for j in range(col):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 水平方向链接（同一行内相邻列）
            # 右侧相邻格点，列索引取模实现周期边界
            right = i*col+ (j + 1) % col
            ising_bonds.append((current, right))
            # 垂直方向链接（同一列内相邻行）
            # 下方相邻格点，行索引取模实现周期边界
            down = ((i + 1) % row) *col + j
            ising_bonds.append((current, down))
    #非周期边条件
    '''
    for i in range(row):          # 遍历每一行
        for j in range(col-1):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 水平方向链接（同一行内相邻列）
            # 右侧相邻格点，列索引取模实现非周期边界
            right = i*col+ (j + 1) % col
            ising_bonds.append((current, right))

    for i in range(row-1):          # 遍历每一行
        for j in range(col):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 垂直方向链接（同一列内相邻行）
            # 下方相邻格点，行索引取模实现非周期边界
            down = ((i + 1) % row) *col + j
            ising_bonds.append((current, down))'''
    print(ising_bonds)
    # Empty lists for sparse matrix
    hamiltonian_rows = []
    hamiltonian_cols = []
    hamiltonian_data = []
    
    # Run through all spin configurations
    for state in range(hilbertspace_dimension(L)):

        # Apply Ising bonds
        ising_diagonal = 0
        for bond in ising_bonds:
            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):
                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5
            else:
                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5
        hamiltonian_rows.append(state)
        hamiltonian_cols.append(state)
        hamiltonian_data.append(ising_diagonal)

        # Apply transverse field
        for site in range(L):
            # Flip spin at site
            new_state = flip_state(state,site)#state ^ (1 << site)
            hamiltonian_rows.append(new_state)
            hamiltonian_cols.append(state)
            hamiltonian_data.append(hx)#(mid3[state])#hx
    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data

#测试函数
col_y = 4
row_x = 3
Hr11,Hc11,Hd11 = get_hamiltonian_sparse(col_y,row_x ,1, 1)
#矩阵形式输出
H11=np.zeros((2**(row_x*col_y),2**(row_x*col_y)))  
H11[Hr11,Hc11] = Hd11
#print(H11)
E_test,V_test=np.linalg.eig(H11)


E_test_sort = np.sort(E_test)
E_test_sort

#plt.plot(np.arange(len(E)),quasienergy,
#					marker='x',color='r',markersize=2,label='real space ED')
plt.scatter(np.arange(len(E_test)),E_test_sort,
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('state number',fontsize=16)
plt.ylabel('energy',fontsize=16)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
#plt.savefig('example5a.pdf', bbox_inches='tight')
plt.show()

import numpy as np
import matplotlib.pyplot as plt


def plot_selected_vectors_test(IPR,V, ipr_threshold_min, ipr_threshold_max):
    """
    筛选IPR大于阈值的列向量并绘制
    
    参数:
    V: 输入矩阵，每行代表一个基矢，每列代表一个向量
    ipr_threshold: IPR筛选阈值，默认0.5
    """
    # 计算IPR
    ipr = IPR
    print(len(ipr))
    # 筛选IPR大于阈值的列索引
    selected_indices = np.where((ipr > ipr_threshold_min) & (ipr < ipr_threshold_max))[0]
    if len(selected_indices) == 0:
        #print(f"没有找到IPR大于{ipr_threshold}的向量")
        return
    
    #print(f"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}")
    
    # 提取对应的列向量
    selected_vectors = V[:, selected_indices]
    
    
    # 设置绘图风格
    plt.style.use('seaborn-v0_8-ticks')
    
    # 计算子图布局（最多5列）
    n_cols = min(5, len(selected_indices))
    n_rows = (len(selected_indices) + n_cols - 1) // n_cols
    
    # 创建画布
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))
    axes = np.ravel(axes)  # 转换为一维数组便于索引
    for i, idx in enumerate(selected_indices):
        E_slect = E_test[idx]
        print(E_slect)
    # 绘制每个选中的向量
    for i, idx in enumerate(selected_indices):
        ax = axes[i]
        # 绘制向量的绝对值点线图
        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')
        print(selected_vectors[:, i])
        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))
        ax.set_title(f'向量索引: {idx}\nIPR = {ipr[idx]:.4f}.E = {E_test[idx]:.4f}', fontsize=10)
        ax.set_xlabel('基矢索引', fontsize=8)
        ax.set_ylabel('|振幅|', fontsize=8)
        ax.tick_params(axis='both', which='major', labelsize=6)
    
    # 隐藏未使用的子图
    for i in range(len(selected_indices), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return selected_vectors, selected_indices

# 示例使用
if __name__ == "__main__":
    # 筛选并绘图
    selected_vecs, selected_idx = plot_selected_vectors_test(E_test,V_test, -0.01, 0.01)


col = 4
row = 3
L = col * row
#T=t1+t2
lam_h = 0.1
lam_J = 0.1
hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5
J=np.pi/4 - lam_J#J*t2# 0.75
Hr1,Hc1,Hd1 = get_hamiltonian_sparse(col,row,0 * J,hx)
Hr2,Hc2,Hd2 = get_hamiltonian_sparse(col,row,J,0 * hx)
#创建3X3的0矩阵
H1=np.zeros((2**L,2**L))  
H2=np.zeros((2**L,2**L))
H1[Hr1,Hc1] = Hd1
H2[Hr2,Hc2] = Hd2
H_F = expm(-1j*H1) @ expm(-1j*H2)

E,V=np.linalg.eig(H_F)

print(H_F)

quasienergy = [0 for index in range(2**L)]
for i in range(0,2**L,1):
    quasienergy[i] = (cmath.phase(E[i]))
quasienergy1 = quasienergy.copy()

# 输出准能量为 txt 文件
import numpy as np
np.savetxt('quasienergy_output.txt', quasienergy)
print('准能量已保存到 quasienergy_output.txt')

IPR1 = np.multiply(np.multiply(abs(V),abs(V)),np.multiply(abs(V),abs(V)))
IPR = np.sum((IPR1),axis=0)
IPR

quasienergy.sort()
quasienergy

delta_n = [0 for index in range(2**L-1)]
for i in range(2**L-1):
    delta_n[i]=quasienergy[i+1]-quasienergy[i]

#平均值
mean = np.mean(delta_n)
#mean
delta_n = delta_n / mean
#画间距为0.1 的直方图
delta_n
#画直方分布图
plt.hist(delta_n,bins=50)
plt.show()

#plt.plot(np.arange(len(E)),quasienergy,
#					marker='x',color='r',markersize=2,label='real space ED')
plt.scatter(np.arange(len(E)),quasienergy,
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('state number',fontsize=16)
plt.ylabel('energy',fontsize=16)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
#plt.savefig('example5a.pdf', bbox_inches='tight')
plt.show()

print(IPR)

c=0
number=[]
for i in IPR:    
    if i>0.3:
        number.append([i,c])
        print(quasienergy1[c])
    c= 1+c
number

plt.scatter(quasienergy1,list(IPR),
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('quasienergy',fontsize=16)
plt.ylabel('IPR',fontsize=16)
# 设置纵坐标范围为0到0.5
plt.ylim(0, 0.55)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
plt.savefig('example5a.eps', bbox_inches='tight')
plt.show()

#按照 准能量的 分布画出柱状分布图
plt.hist(quasienergy, bins=50)
plt.xlabel('quiasenergy', fontsize=16)
plt.ylabel('Count', fontsize=16)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.tight_layout()
# 在每个柱子上标记数值
#for i in range(len(number)):
   # plt.text(number[i][1], i+0.5, str(number[i][0]), ha='center', va='bottom')
plt.show()

# 绘制直方图并获取统计信息
n, bins, patches = plt.hist(quasienergy, bins=500, range=(-np.pi, np.pi))

# 设置坐标轴
plt.xlabel('Quasienergy', fontsize=16)
plt.ylabel('Count', fontsize=16)
plt.xticks([-np.pi, -np.pi/2, 0, np.pi/2, np.pi],
           [r'$-\pi$', r'$-\pi/2$', '0', r'$\pi/2$', r'$\pi$'],
           fontsize=14)
plt.yticks(fontsize=14)

# 在每个柱子上方标记数量
bin_width = bins[1] - bins[0]  # 计算柱子宽度
for i in range(len(n)):
    count = n[i]
    if count > 0:  # 只标记有数据的柱子
        # 计算柱子中心位置
        x_pos = bins[i] + bin_width / 2
        # 放置文本标签
        plt.text(x_pos, count, f'{int(count)}',
                 ha='center', va='bottom',
                 fontsize=8, color='darkred')

plt.tight_layout()
plt.show()

V

np.power(np.abs(V), 4)

#V的行数
print(len(V))
#V的列数
print(len(V[0]))

import numpy as np
import matplotlib.pyplot as plt


def plot_selected_vectors(IPR, ipr_threshold):
    """
    筛选IPR大于阈值的列向量并绘制
    
    参数:
    V: 输入矩阵，每行代表一个基矢，每列代表一个向量
    ipr_threshold: IPR筛选阈值，默认0.5
    """
    # 计算IPR
    ipr = IPR
    print(len(ipr))
    # 筛选IPR大于阈值的列索引
    selected_indices = np.where(ipr > ipr_threshold)[0]
    if len(selected_indices) == 0:
        print(f"没有找到IPR大于{ipr_threshold}的向量")
        return
    
    print(f"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}")
    
    # 提取对应的列向量
    selected_vectors = V[:, selected_indices]
    
    
    # 设置绘图风格
    plt.style.use('seaborn-v0_8-ticks')
    
    # 计算子图布局（最多5列）
    n_cols = min(5, len(selected_indices))
    n_rows = (len(selected_indices) + n_cols - 1) // n_cols
    
    # 创建画布
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))
    axes = np.ravel(axes)  # 转换为一维数组便于索引
    for i, idx in enumerate(selected_indices):
        E_slect = quasienergy1[idx]
        print(E_slect)
    # 绘制每个选中的向量
    for i, idx in enumerate(selected_indices):
        ax = axes[i]
        # 绘制向量的绝对值点线图
        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')
        print(selected_vectors[:, i])
        print(np.where((np.abs(selected_vectors[:, i]) > 0.1))[0])
        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))
        ax.set_title(f'向量索引: {idx}\nIPR = {ipr[idx]:.4f}.E = {quasienergy1[idx]:.4f}', fontsize=10)
        ax.set_xlabel('基矢索引', fontsize=8)
        ax.set_ylabel('|振幅|', fontsize=8)
        ax.tick_params(axis='both', which='major', labelsize=6)
    
    # 隐藏未使用的子图
    for i in range(len(selected_indices), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return selected_vectors, selected_indices

# 示例使用
if __name__ == "__main__":
    # 筛选并绘图
    selected_vecs, selected_idx = plot_selected_vectors(IPR, ipr_threshold=0.3)


selected_vecs
#把这个结果导出为一个可以编辑的表格
import pandas as pd
# 创建一个DataFrame
df = pd.DataFrame(np.abs(selected_vecs))

# 将DataFrame保存为Excel文件
df.to_excel('selected_vectors.xlsx', index=False)
