{"cells": [{"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy\n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:2: SyntaxWarning: invalid escape sequence '\\s'\n", "<>:2: SyntaxWarning: invalid escape sequence '\\s'\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/4083803210.py:2: SyntaxWarning: invalid escape sequence '\\s'\n", "  '''\n"]}], "source": ["def get_hamiltonian_sparse(col,row, Jz, hx):\n", "    '''\n", "    Creates the Hamiltonian of the Transverse Field Ising model\n", "    on a linear chain lattice with periodic boundary conditions.\n", "\n", "    The Hamiltonian is given by:\n", "    H = -Jz \\sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \\sum_{i=1}^{L} S_i^x - hz \\sum_{i=1}^{L} S_i^z\n", "    Args:\n", "        L (int): length of chain\n", "        J (float): coupling constant for Ising term\n", "        hx (float): coupling constant for transverse field\n", "\n", "    Returns:\n", "        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:\n", "        hamiltonian_rows (list of ints): row index of non-zero elements\n", "        hamiltonian_cols (list of ints): column index of non-zero elements\n", "        hamiltonian_data (list of floats): value of non-zero elements\n", "    '''\n", "    L = col * row\n", "    def get_site_value(state, site):\n", "        ''' Function to get local value at a given site '''\n", "        return (state >> site) & 1\n", "        #返回值为state的第site位上的二进制值\n", "\n", "    def hilbertspace_dimension(L):\n", "        ''' return dimension of hilbertspace '''\n", "        return 2**L\n", "\n", "    def flip_state(state: int, index: int) -> int:\n", "        \"\"\"翻转一个整数某位置处的二进制值\"\"\"\n", "        mask = 1 << index\n", "        return state ^ mask\n", "    # Define chain lattice\n", "    #ising_bonds_col = [(site, (site+1)%col) for site in range(col-1)]#开放边条件\n", "    #ising_bonds_row = [(site, (site+1)%row) for site in range(row-1)]#开放边条件\n", "    #ising_bonds_col = [(site, (site+1)%col) for site in range(col)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0\n", "    #ising_bonds_row = [(site, (site+1)%row) for site in range(row)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0\n", "    ising_bonds = []\n", "\n", "    #  周期边条件\n", "    for i in range(row):          # 遍历每一行\n", "        for j in range(col):      # 遍历每一列\n", "            # 当前格点坐标 (i,j)\n", "            current = i* col + j\n", "            # 水平方向链接（同一行内相邻列）\n", "            # 右侧相邻格点，列索引取模实现周期边界\n", "            right = i*col+ (j + 1) % col\n", "            ising_bonds.append((current, right))\n", "            # 垂直方向链接（同一列内相邻行）\n", "            # 下方相邻格点，行索引取模实现周期边界\n", "            down = ((i + 1) % row) *col + j\n", "            ising_bonds.append((current, down))\n", "    #非周期边条件\n", "    '''\n", "    for i in range(row):          # 遍历每一行\n", "        for j in range(col-1):      # 遍历每一列\n", "            # 当前格点坐标 (i,j)\n", "            current = i* col + j\n", "            # 水平方向链接（同一行内相邻列）\n", "            # 右侧相邻格点，列索引取模实现非周期边界\n", "            right = i*col+ (j + 1) % col\n", "            ising_bonds.append((current, right))\n", "\n", "    for i in range(row-1):          # 遍历每一行\n", "        for j in range(col):      # 遍历每一列\n", "            # 当前格点坐标 (i,j)\n", "            current = i* col + j\n", "            # 垂直方向链接（同一列内相邻行）\n", "            # 下方相邻格点，行索引取模实现非周期边界\n", "            down = ((i + 1) % row) *col + j\n", "            ising_bonds.append((current, down))'''\n", "    print(ising_bonds)\n", "    # Empty lists for sparse matrix\n", "    hamiltonian_rows = []\n", "    hamiltonian_cols = []\n", "    hamiltonian_data = []\n", "    \n", "    # Run through all spin configurations\n", "    for state in range(hilbertspace_dimension(L)):\n", "\n", "        # Apply Ising bonds\n", "        ising_diagonal = 0\n", "        for bond in ising_bonds:\n", "            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):\n", "                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5\n", "            else:\n", "                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5\n", "        hamiltonian_rows.append(state)\n", "        hamiltonian_cols.append(state)\n", "        hamiltonian_data.append(ising_diagonal)\n", "\n", "        # Apply transverse field\n", "        for site in range(L):\n", "            # Flip spin at site\n", "            new_state = flip_state(state,site)#state ^ (1 << site)\n", "            hamiltonian_rows.append(new_state)\n", "            hamiltonian_cols.append(state)\n", "            hamiltonian_data.append(hx)#(mid3[state])#hx\n", "    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(0, 1), (0, 4), (1, 2), (1, 5), (2, 3), (2, 6), (3, 0), (3, 7), (4, 5), (4, 8), (5, 6), (5, 9), (6, 7), (6, 10), (7, 4), (7, 11), (8, 9), (8, 0), (9, 10), (9, 1), (10, 11), (10, 2), (11, 8), (11, 3)]\n"]}], "source": ["#测试函数\n", "col_y = 4\n", "row_x = 3\n", "Hr11,Hc11,Hd11 = get_hamiltonian_sparse(col_y,row_x ,1, 1)\n", "#矩阵形式输出\n", "H11=np.zeros((2**(row_x*col_y),2**(row_x*col_y)))  \n", "H11[Hr11,Hc11] = Hd11\n", "#print(H11)\n", "E_test,V_test=np.linalg.eig(H11)\n"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-18.91385628+0.j, -18.74606893+0.j, -18.74606893+0.j, ...,\n", "        18.2824259 +0.j,  25.50803675+0.j,  25.5080648 +0.j])"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["E_test_sort = np.sort(E_test)\n", "E_test_sort"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.12/site-packages/matplotlib/cbook.py:1762: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return math.isfinite(val)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/matplotlib/collections.py:197: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  offsets = np.asanyarray(offsets, float)\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1745857215.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#plt.plot(np.arange(len(E)),quasienergy,\n", "#\t\t\t\t\tmarker='x',color='r',markersize=2,label='real space ED')\n", "plt.scatter(np.arange(len(E_test)),E_test_sort,\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('state number',fontsize=16)\n", "plt.ylabel('energy',fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "#plt.savefig('example5a.pdf', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4096\n", "(-0.00210653012623821+0j)\n", "(-0.0017591529897413337+0j)\n", "(-0.0017591529897385166+0j)\n", "(-1.4667485898550773e-15+0j)\n", "(7.480100593561212e-16+0j)\n", "(-2.9987881485621217e-16+0j)\n", "(-1.1912059245455446e-16+0j)\n", "(2.1070633465220473e-16+0j)\n", "[ 0.00086592+0.j -0.00173199+0.j -0.00173199+0.j ... -0.00173199+0.j\n", " -0.00173199+0.j  0.00086592+0.j]\n", "[ 1.08825956e-15+0.j -3.82471491e-03+0.j  3.82471491e-03+0.j ...\n", "  3.82471491e-03+0.j -3.82471491e-03+0.j  1.06645707e-15+0.j]\n", "[-9.38878969e-17+0.j -2.26195210e-04+0.j  2.26195210e-04+0.j ...\n", "  2.26195210e-04+0.j -2.26195210e-04+0.j -1.01503555e-16+0.j]\n", "[ 2.05236586e-16+0.j -2.17239038e-15+0.j  1.36709091e-15+0.j ...\n", "  7.96720960e-16+0.j -1.75955065e-15+0.j  2.11358922e-16+0.j]\n", "[ 4.82431262e-17+0.j -2.61084712e-16+0.j  3.02624710e-17+0.j ...\n", " -4.78262300e-17+0.j -1.65633385e-16+0.j  5.46949121e-17+0.j]\n", "[ 4.97306927e-17+0.j -3.39639491e-16+0.j  2.12215949e-16+0.j ...\n", "  1.02809413e-16+0.j -4.62510981e-16+0.j  5.56800659e-17+0.j]\n", "[ 2.38553768e-17+0.j -3.72469880e-17+0.j  1.56244973e-17+0.j ...\n", " -6.36383387e-17+0.j -1.95499250e-16+0.j  2.90132768e-17+0.j]\n", "[-6.37579570e-19+0.j  1.17480416e-16+0.j -3.92642651e-17+0.j ...\n", " -4.34799228e-18+0.j -8.04795662e-17+0.j  4.06182902e-18+0.j]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3410858787.py:57: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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***********************************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", "text/plain": ["<Figure size 2000x600 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_selected_vectors_test(IPR,V, ipr_threshold_min, ipr_threshold_max):\n", "    \"\"\"\n", "    筛选IPR大于阈值的列向量并绘制\n", "    \n", "    参数:\n", "    V: 输入矩阵，每行代表一个基矢，每列代表一个向量\n", "    ipr_threshold: IPR筛选阈值，默认0.5\n", "    \"\"\"\n", "    # 计算IPR\n", "    ipr = IPR\n", "    print(len(ipr))\n", "    # 筛选IPR大于阈值的列索引\n", "    selected_indices = np.where((ipr > ipr_threshold_min) & (ipr < ipr_threshold_max))[0]\n", "    if len(selected_indices) == 0:\n", "        #print(f\"没有找到IPR大于{ipr_threshold}的向量\")\n", "        return\n", "    \n", "    #print(f\"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}\")\n", "    \n", "    # 提取对应的列向量\n", "    selected_vectors = V[:, selected_indices]\n", "    \n", "    \n", "    # 设置绘图风格\n", "    plt.style.use('seaborn-v0_8-ticks')\n", "    \n", "    # 计算子图布局（最多5列）\n", "    n_cols = min(5, len(selected_indices))\n", "    n_rows = (len(selected_indices) + n_cols - 1) // n_cols\n", "    \n", "    # 创建画布\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))\n", "    axes = np.ravel(axes)  # 转换为一维数组便于索引\n", "    for i, idx in enumerate(selected_indices):\n", "        E_slect = E_test[idx]\n", "        print(E_slect)\n", "    # 绘制每个选中的向量\n", "    for i, idx in enumerate(selected_indices):\n", "        ax = axes[i]\n", "        # 绘制向量的绝对值点线图\n", "        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')\n", "        print(selected_vectors[:, i])\n", "        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))\n", "        ax.set_title(f'向量索引: {idx}\\nIPR = {ipr[idx]:.4f}.E = {E_test[idx]:.4f}', fontsize=10)\n", "        ax.set_xlabel('基矢索引', fontsize=8)\n", "        ax.set_ylabel('|振幅|', fontsize=8)\n", "        ax.tick_params(axis='both', which='major', labelsize=6)\n", "    \n", "    # 隐藏未使用的子图\n", "    for i in range(len(selected_indices), len(axes)):\n", "        axes[i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return selected_vectors, selected_indices\n", "\n", "# 示例使用\n", "if __name__ == \"__main__\":\n", "    # 筛选并绘图\n", "    selected_vecs, selected_idx = plot_selected_vectors_test(E_test,V_test, -0.01, 0.01)\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(0, 1), (0, 4), (1, 2), (1, 5), (2, 3), (2, 6), (3, 0), (3, 7), (4, 5), (4, 8), (5, 6), (5, 9), (6, 7), (6, 10), (7, 4), (7, 11), (8, 9), (8, 0), (9, 10), (9, 1), (10, 11), (10, 2), (11, 8), (11, 3)]\n", "[(0, 1), (0, 4), (1, 2), (1, 5), (2, 3), (2, 6), (3, 0), (3, 7), (4, 5), (4, 8), (5, 6), (5, 9), (6, 7), (6, 10), (7, 4), (7, 11), (8, 9), (8, 0), (9, 10), (9, 1), (10, 11), (10, 2), (11, 8), (11, 3)]\n"]}], "source": ["col = 4\n", "row = 3\n", "L = col * row\n", "#T=t1+t2\n", "lam_h = 0.1\n", "lam_J = 0.1\n", "hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J=np.pi/4 - lam_J#J*t2# 0.75\n", "Hr1,Hc1,Hd1 = get_hamiltonian_sparse(col,row,0 * J,hx)\n", "Hr2,Hc2,Hd2 = get_hamiltonian_sparse(col,row,J,0 * hx)\n", "#创建3X3的0矩阵\n", "H1=np.zeros((2**L,2**L))  \n", "H2=np.zeros((2**L,2**L))\n", "H1[Hr1,Hc1] = Hd1\n", "H2[Hr2,Hc2] = Hd2\n", "H_F = expm(-1j*H1) @ expm(-1j*H2)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["E,V=np.linalg.eig(H_F)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-7.23131865e-13+6.62399121e-13j  9.76323333e-12+2.85203359e-13j\n", "   9.76316856e-12+2.85201467e-13j ... -9.44418748e-02-2.75883399e-03j\n", "  -9.44418748e-02-2.75883399e-03j -6.94381621e-01+6.36063488e-01j]\n", " [ 6.59724778e-12+7.20212321e-12j -2.85788046e-14+9.78324864e-13j\n", "   2.84302692e-12-9.73240120e-11j ...  2.76806703e-04-9.47579454e-03j\n", "  -2.74963174e-02+9.41268585e-01j -6.38192214e-02-6.96705523e-02j]\n", " [ 6.59708959e-12+7.20195052e-12j  2.84302141e-12-9.73238235e-11j\n", "  -2.85841810e-14+9.78508912e-13j ... -2.74963174e-02+9.41268585e-01j\n", "   2.76806703e-04-9.47579454e-03j -6.38192214e-02-6.96705523e-02j]\n", " ...\n", " [-6.38192214e-02-6.96705523e-02j  2.76806703e-04-9.47579454e-03j\n", "  -2.74963174e-02+9.41268585e-01j ... -2.86500769e-14+9.80764695e-13j\n", "   2.84305146e-12-9.73248521e-11j  6.59929416e-12+7.20435722e-12j]\n", " [-6.38192214e-02-6.96705523e-02j -2.74963174e-02+9.41268585e-01j\n", "   2.76806703e-04-9.47579454e-03j ...  2.84305645e-12-9.73250231e-11j\n", "  -2.86619868e-14+9.81172402e-13j  6.59973866e-12+7.20484247e-12j]\n", " [-6.94381621e-01+6.36063488e-01j -9.44418748e-02-2.75883399e-03j\n", "  -9.44418748e-02-2.75883399e-03j ...  9.76591477e-12+2.85281689e-13j\n", "   9.76648701e-12+2.85298406e-13j -7.22355311e-13+6.61687787e-13j]]\n"]}], "source": ["print(H_F)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["quasienergy = [0 for index in range(2**L)]\n", "for i in range(0,2**L,1):\n", "    quasienergy[i] = (cmath.phase(E[i]))\n", "quasienergy1 = quasienergy.copy()"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准能量已保存到 quasienergy_output.txt\n"]}], "source": ["# 输出准能量为 txt 文件\n", "import numpy as np\n", "np.savetxt('quasienergy_output.txt', quasienergy)\n", "print('准能量已保存到 quasienergy_output.txt')"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.01280286, 0.02825694, 0.03318557, ..., 0.0022135 , 0.00255562,\n", "       0.00198314])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["IPR1 = np.multiply(np.multiply(abs(V),abs(V)),np.multiply(abs(V),abs(V)))\n", "IPR = np.sum((IPR1),axis=0)\n", "IPR"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-3.1411196163908404,\n", " -3.1410027200391957,\n", " -3.141002720039194,\n", " -3.14091782167087,\n", " -3.1406289845131217,\n", " -3.1404328715633962,\n", " -3.139979734264573,\n", " -3.139979734264572,\n", " -3.138435190676234,\n", " -3.1312022330917952,\n", " -3.1288739104356567,\n", " -3.126381334649687,\n", " -3.126381334649686,\n", " -3.1263813346496856,\n", " -3.1263813346496847,\n", " -3.125670754730508,\n", " -3.122616457913716,\n", " -3.122616457913716,\n", " -3.1214666977529717,\n", " -3.1214666977529713,\n", " -3.1214666977529704,\n", " -3.1214666977529704,\n", " -3.1197484777156146,\n", " -3.119748477715611,\n", " -3.1196955392584345,\n", " -3.119695539258434,\n", " -3.11841262583424,\n", " -3.118412625834238,\n", " -3.1176728691323263,\n", " -3.1176728691323237,\n", " -3.1173860699957276,\n", " -3.1173860699957254,\n", " -3.1119159879231697,\n", " -3.1119159879231697,\n", " -3.1105997484641374,\n", " -3.110599748464136,\n", " -3.1086576723889374,\n", " -3.108657672388937,\n", " -3.1086576723889348,\n", " -3.108657672388933,\n", " -3.106344166468878,\n", " -3.106344166468877,\n", " -3.105092715893209,\n", " -3.104568406765647,\n", " -3.103607984160075,\n", " -3.103607984160073,\n", " -3.1036079841600728,\n", " -3.103607984160072,\n", " -3.1035980629806943,\n", " -3.1035980629806943,\n", " -3.103598062980694,\n", " -3.1035980629806925,\n", " -3.1032425721786416,\n", " -3.102104542661825,\n", " -3.1020339153117393,\n", " -3.10159198340046,\n", " -3.1015919834004566,\n", " -3.1014030441107447,\n", " -3.1000357637064315,\n", " -3.098830415678474,\n", " -3.0988304156784694,\n", " -3.0984090176548373,\n", " -3.096865659492531,\n", " -3.09686565949253,\n", " -3.094849800204802,\n", " -3.0948498002048006,\n", " -3.0948498002047997,\n", " -3.094849800204797,\n", " -3.092777840355101,\n", " -3.0913749392387264,\n", " -3.091374939238726,\n", " -3.0913749392387255,\n", " -3.0913749392387238,\n", " -3.0897287522825154,\n", " -3.089728752282514,\n", " -3.0878196802389017,\n", " -3.087819680238901,\n", " -3.087071227016355,\n", " -3.087071227016354,\n", " -3.0870712270163536,\n", " -3.087071227016351,\n", " -3.086627259091634,\n", " -3.0866272590916326,\n", " -3.086240660418143,\n", " -3.0832622527774878,\n", " -3.0832622527774856,\n", " -3.083131278280414,\n", " -3.0831312782804132,\n", " -3.081122445299705,\n", " -3.078824327176755,\n", " -3.078824327176754,\n", " -3.0785127109894446,\n", " -3.0785127109894437,\n", " -3.0739037953720447,\n", " -3.073903795372044,\n", " -3.0736702214477667,\n", " -3.0736702214477654,\n", " -3.0736702214477636,\n", " -3.0736702214477636,\n", " -3.0696962136930948,\n", " -3.0696962136930934,\n", " -3.06482623459627,\n", " -3.064826234596269,\n", " -3.0647202004290803,\n", " -3.06472020042908,\n", " -3.060903078665929,\n", " -3.060903078665928,\n", " -3.059366348164649,\n", " -3.05740287016768,\n", " -3.057402870167676,\n", " -3.056668976333972,\n", " -3.056668976333969,\n", " -3.0562808112570514,\n", " -3.0562808112570505,\n", " -3.0535714562025777,\n", " -3.0535714562025724,\n", " -3.0524075615593453,\n", " -3.052407561559342,\n", " -3.0488904001558668,\n", " -3.0488904001558623,\n", " -3.0478125559938642,\n", " -3.047812555993863,\n", " -3.047812555993861,\n", " -3.047812555993861,\n", " -3.047665628148767,\n", " -3.0455421675122873,\n", " -3.0455421675122865,\n", " -3.045542167512286,\n", " -3.0455421675122856,\n", " -3.0415070230751846,\n", " -3.0411908569736226,\n", " -3.041190856973618,\n", " -3.0405808780844072,\n", " -3.0405808780844064,\n", " -3.0322044941673445,\n", " -3.031902272356935,\n", " -3.031723734127995,\n", " -3.0311727161188236,\n", " -3.0311727161188227,\n", " -3.0311727161188213,\n", " -3.0311727161188213,\n", " -3.0301090991575723,\n", " -3.030109099157571,\n", " -3.0299879997272505,\n", " -3.029987999727245,\n", " -3.0292975466542558,\n", " -3.0291062732391176,\n", " -3.029106273239117,\n", " -3.028328879895166,\n", " -3.0283288798951653,\n", " -3.0280121177109076,\n", " -3.0280121177109067,\n", " -3.0264860513493836,\n", " -3.0264860513493823,\n", " -3.0263328879264124,\n", " -3.02569226220306,\n", " -3.02504436699252,\n", " -3.025044366992519,\n", " -3.024020602088684,\n", " -3.02230835438942,\n", " -3.022308354389416,\n", " -3.022308354389413,\n", " -3.0223083543894123,\n", " -3.0220490941261615,\n", " -3.022049094126161,\n", " -3.0220490941261597,\n", " -3.022049094126158,\n", " -3.021580601146298,\n", " -3.021580601146295,\n", " -3.0200198021215043,\n", " -3.0200198021215012,\n", " -3.0195862753971956,\n", " -3.019586275397187,\n", " -3.0195862753971867,\n", " -3.019586275397185,\n", " -3.0191970281267224,\n", " -3.0191818447609053,\n", " -3.019181844760904,\n", " -3.0141911114432554,\n", " -3.0140401051525245,\n", " -3.014040105152522,\n", " -3.0124438483774125,\n", " -3.0124438483774125,\n", " -3.0121178452685773,\n", " -3.0120795243668064,\n", " -3.0114489639301896,\n", " -3.0114489639301874,\n", " -3.008879403385786,\n", " -3.0088794033857846,\n", " -3.0085777100563273,\n", " -3.0047129807862754,\n", " -3.0036405127210326,\n", " -3.0036405127210295,\n", " -3.003640512721029,\n", " -3.003640512721028,\n", " -3.0018242110066717,\n", " -3.0015973316804088,\n", " -3.0015973316804088,\n", " -3.0012833799725076,\n", " -3.0012833799725054,\n", " -3.0000549887157173,\n", " -3.0000549887157164,\n", " -2.999898990166449,\n", " -2.999898990166446,\n", " -2.9991233434412443,\n", " -2.9991233434412434,\n", " -2.999123343441242,\n", " -2.9991233434412394,\n", " -2.9969499144814664,\n", " -2.996949914481465,\n", " -2.993102706349471,\n", " -2.992355967840661,\n", " -2.992355967840658,\n", " -2.9923559678406577,\n", " -2.992355967840656,\n", " -2.9918965091042353,\n", " -2.991896509104233,\n", " -2.9918965091042278,\n", " -2.9918965091042247,\n", " -2.9914018595424756,\n", " -2.991401859542474,\n", " -2.985622270020224,\n", " -2.98470360023155,\n", " -2.9847036002315495,\n", " -2.984424094686421,\n", " -2.9844240946864193,\n", " -2.9840178414216454,\n", " -2.984017841421639,\n", " -2.981963781181331,\n", " -2.981718400198205,\n", " -2.9817184001982,\n", " -2.9812095495144035,\n", " -2.9812095495144,\n", " -2.979717063366637,\n", " -2.979683305505558,\n", " -2.979683305505557,\n", " -2.979683305505556,\n", " -2.979683305505554,\n", " -2.977666740668444,\n", " -2.9764993098952144,\n", " -2.975836234509013,\n", " -2.9758362345090124,\n", " -2.9743792822952337,\n", " -2.9733423141972923,\n", " -2.9733423141972906,\n", " -2.9733423141972875,\n", " -2.973342314197287,\n", " -2.9724386979221107,\n", " -2.9705985195293714,\n", " -2.9703192318217377,\n", " -2.970319231821733,\n", " -2.969398167672245,\n", " -2.9687593810750044,\n", " -2.968759381075004,\n", " -2.9644947841310945,\n", " -2.9644947841310936,\n", " -2.964494784131092,\n", " -2.96449478413109,\n", " -2.9619939150208237,\n", " -2.961993915020823,\n", " -2.961227540026632,\n", " -2.96122754002663,\n", " -2.9579415951321004,\n", " -2.957941595132099,\n", " -2.956800961611144,\n", " -2.9568009616111395,\n", " -2.95452986873731,\n", " -2.9545298687373016,\n", " -2.9507229247830895,\n", " -2.948636447981865,\n", " -2.9460106978598035,\n", " -2.9460106978598026,\n", " -2.9458523375116594,\n", " -2.9458523375116585,\n", " -2.9455333864295037,\n", " -2.943277769050961,\n", " -2.9432777690509595,\n", " -2.9432777690509595,\n", " -2.943277769050956,\n", " -2.938649193908498,\n", " -2.9385476530545844,\n", " -2.9376093837844897,\n", " -2.9376093837844888,\n", " -2.9376093837844865,\n", " -2.937609383784485,\n", " -2.936725017784312,\n", " -2.9367250177843105,\n", " -2.9345368694978253,\n", " -2.9345368694978227,\n", " -2.934409308115502,\n", " -2.934409308115499,\n", " -2.933994665217016,\n", " -2.9339946652170066,\n", " -2.9339946652170052,\n", " -2.933994665217005,\n", " -2.933770028859898,\n", " -2.932600563177408,\n", " -2.932600563177404,\n", " -2.9316857992951397,\n", " -2.9316857992951366,\n", " -2.928311914515085,\n", " -2.927474840144283,\n", " -2.9274748401442783,\n", " -2.9269444077711966,\n", " -2.9238228801676587,\n", " -2.923822880167658,\n", " -2.923517254280929,\n", " -2.9235172542809265,\n", " -2.9230545028048995,\n", " -2.9215513542761635,\n", " -2.9206313710960385,\n", " -2.920631371096032,\n", " -2.9200124950664064,\n", " -2.919431725263474,\n", " -2.9194317252634723,\n", " -2.9179088599070298,\n", " -2.9179088599070298,\n", " -2.9149649381261624,\n", " -2.914964938126162,\n", " -2.9149649381261615,\n", " -2.91496493812616,\n", " -2.9134433597994893,\n", " -2.9125621165029063,\n", " -2.9125621165029005,\n", " -2.910282157433554,\n", " -2.9062984899178503,\n", " -2.906298489917846,\n", " -2.905825109468096,\n", " -2.90582510946809,\n", " -2.9057807717445416,\n", " -2.903583909667934,\n", " -2.9034517058679166,\n", " -2.903229365594861,\n", " -2.9032293655948576,\n", " -2.901699762259171,\n", " -2.901699762259167,\n", " -2.9012537826636944,\n", " -2.9012537826636917,\n", " -2.900162723399474,\n", " -2.898211085288376,\n", " -2.896813359263663,\n", " -2.8965077768641954,\n", " -2.896507776864194,\n", " -2.896425816043232,\n", " -2.89642581604323,\n", " -2.896425816043228,\n", " -2.896425816043226,\n", " -2.8959488782586136,\n", " -2.8954015534865682,\n", " -2.8954015534865674,\n", " -2.8954015534865665,\n", " -2.8954015534865656,\n", " -2.892478274793834,\n", " -2.8924782747938327,\n", " -2.8921582329447166,\n", " -2.8914757683385695,\n", " -2.8914757683385663,\n", " -2.888839975235784,\n", " -2.8888399752357836,\n", " -2.888839975235782,\n", " -2.8888399752357805,\n", " -2.8874010026799257,\n", " -2.8874010026799204,\n", " -2.8868764843142967,\n", " -2.886876484314293,\n", " -2.8836968926830133,\n", " -2.883696892683012,\n", " -2.8829988467011405,\n", " -2.8829988467011347,\n", " -2.8803174839831898,\n", " -2.880317483983189,\n", " -2.880030650633803,\n", " -2.8800306506338003,\n", " -2.8793770196560318,\n", " -2.8793770196560233,\n", " -2.878905732772582,\n", " -2.8789057327725818,\n", " -2.876313350069146,\n", " -2.8763133500691422,\n", " -2.8759628926780985,\n", " -2.8759628926780976,\n", " -2.8759628926780962,\n", " -2.875962892678095,\n", " -2.8748185333899103,\n", " -2.8748185333899094,\n", " -2.871257185839076,\n", " -2.869574994049842,\n", " -2.8695749940498407,\n", " -2.8695749940498394,\n", " -2.8695749940498376,\n", " -2.8672120498046563,\n", " -2.8672120498046545,\n", " -2.8672120498046527,\n", " -2.867212049804652,\n", " -2.8638162676899928,\n", " -2.863816267689991,\n", " -2.863775832346008,\n", " -2.863775832346005,\n", " -2.863660147895735,\n", " -2.863660147895731,\n", " -2.8504075241225175,\n", " -2.8504075241225144,\n", " -2.8489063318617895,\n", " -2.848906331861787,\n", " -2.848906331861787,\n", " -2.848906331861785,\n", " -2.844070475870761,\n", " -2.8420063939246623,\n", " -2.8420063939246583,\n", " -2.8405298351835166,\n", " -2.8403253828375186,\n", " -2.8400054639262526,\n", " -2.839737398728896,\n", " -2.839737398728894,\n", " -2.836493954168609,\n", " -2.8364939541686054,\n", " -2.8354108103112132,\n", " -2.834576648841473,\n", " -2.834576648841469,\n", " -2.831626644069818,\n", " -2.8316266440698157,\n", " -2.8315306363635564,\n", " -2.831530636363552,\n", " -2.829933489275609,\n", " -2.8295024110213918,\n", " -2.8295024110213878,\n", " -2.8295024110213856,\n", " -2.829502411021381,\n", " -2.8257624220661883,\n", " -2.825762422066188,\n", " -2.8257624220661857,\n", " -2.8257624220661826,\n", " -2.821677319136249,\n", " -2.821677319136249,\n", " -2.8216773191362448,\n", " -2.821677319136244,\n", " -2.8194665996961223,\n", " -2.818299424189385,\n", " -2.818299424189385,\n", " -2.8181740369843338,\n", " -2.818174036984324,\n", " -2.8172181189161014,\n", " -2.816380414732251,\n", " -2.8163804147322478,\n", " -2.8163804147322455,\n", " -2.8163804147322407,\n", " -2.8136669734726034,\n", " -2.8136669734725985,\n", " -2.812534350468015,\n", " -2.8125343504680127,\n", " -2.8098015566585297,\n", " -2.809801556658528,\n", " -2.809127279048774,\n", " -2.809127279048771,\n", " -2.8070616158757735,\n", " -2.807061615875773,\n", " -2.806989775604617,\n", " -2.8026378671151786,\n", " -2.8026378671151777,\n", " -2.8026378671151777,\n", " -2.80263786711517,\n", " -2.802378080342278,\n", " -2.8023780803422778,\n", " -2.8016232410212445,\n", " -2.8016232410212436,\n", " -2.8000056906681365,\n", " -2.800005690668124,\n", " -2.7962166568034683,\n", " -2.796216656803468,\n", " -2.7949384252270697,\n", " -2.7949384252270693,\n", " -2.7939672651283036,\n", " -2.793221211736166,\n", " -2.7932212117361632,\n", " -2.787868120723366,\n", " -2.7877549224952567,\n", " -2.787754922495252,\n", " -2.7877549224952496,\n", " -2.7877549224952474,\n", " -2.7869843780484183,\n", " -2.7869843780484165,\n", " -2.783025659586652,\n", " -2.78302565958665,\n", " -2.7830256595866496,\n", " -2.783025659586648,\n", " -2.78191737986304,\n", " -2.781511843876976,\n", " -2.7795682746133394,\n", " -2.779568274613334,\n", " -2.779504244578036,\n", " -2.7774527213543743,\n", " -2.774890855293329,\n", " -2.774890855293328,\n", " -2.7741850104415513,\n", " -2.774185010441546,\n", " -2.7741850104415455,\n", " -2.774185010441544,\n", " -2.770666081436455,\n", " -2.76958336017297,\n", " -2.7634545721527917,\n", " -2.7634545721527797,\n", " -2.7632719762474074,\n", " -2.75992116008169,\n", " -2.7599211600816793,\n", " -2.758314586883991,\n", " -2.758314586883991,\n", " -2.75831458688399,\n", " -2.7583145868839862,\n", " -2.756559431714612,\n", " -2.756559431714611,\n", " -2.7564942319650947,\n", " -2.7564942319650902,\n", " -2.756494231965089,\n", " -2.7564942319650885,\n", " -2.7558204048041164,\n", " -2.7558204048041066,\n", " -2.754937638484,\n", " -2.752960149870072,\n", " -2.7529601498700704,\n", " -2.751538022124579,\n", " -2.7480465447505087,\n", " -2.748046544750507,\n", " -2.748000810317609,\n", " -2.748000810317608,\n", " -2.748000810317607,\n", " -2.748000810317603,\n", " -2.7473270372717304,\n", " -2.7473270372717287,\n", " -2.7459287108348485,\n", " -2.7456585492939576,\n", " -2.7444064664076495,\n", " -2.7444064664076455,\n", " -2.744326480550449,\n", " -2.7403060878200285,\n", " -2.740266484767659,\n", " -2.740266484767651,\n", " -2.739063029324264,\n", " -2.7390630293242624,\n", " -2.7384976963565264,\n", " -2.7384976963565255,\n", " -2.738497696356525,\n", " -2.738497696356524,\n", " -2.7367939058695976,\n", " -2.736793905869594,\n", " -2.736564089662911,\n", " -2.7363334631537715,\n", " -2.7363334631537697,\n", " -2.735927922068705,\n", " -2.734113420191115,\n", " -2.733478236454443,\n", " -2.733478236454442,\n", " -2.733478236454442,\n", " -2.733478236454438,\n", " -2.732403529773675,\n", " -2.732403529773675,\n", " -2.7321401424710223,\n", " -2.7321401424710183,\n", " -2.7297260584479246,\n", " -2.7297260584479237,\n", " -2.725692766388593,\n", " -2.723642015964837,\n", " -2.723642015964836,\n", " -2.7230587126971937,\n", " -2.7228839978846677,\n", " -2.7210950734798813,\n", " -2.7201108067849558,\n", " -2.7196893748071176,\n", " -2.7190658158818115,\n", " -2.7183594651077296,\n", " -2.7128904885774423,\n", " -2.7128904885774405,\n", " -2.7121306475415876,\n", " -2.712130647541586,\n", " -2.712130647541586,\n", " -2.7121306475415814,\n", " -2.711677608318694,\n", " -2.71167760831869,\n", " -2.69955190713685,\n", " -2.6932614104470813,\n", " -2.692469923453564,\n", " -2.692469923453563,\n", " -2.6924184525762547,\n", " -2.6924184525762547,\n", " -2.6869559607512556,\n", " -2.6869340498323573,\n", " -2.6869340498323537,\n", " -2.6838191745869837,\n", " -2.6838191745869815,\n", " -2.683416720012745,\n", " -2.683416720012744,\n", " -2.681329890068622,\n", " -2.6811413169106477,\n", " -2.679998012301265,\n", " -2.679790012451031,\n", " -2.6797900124510305,\n", " -2.6797900124510288,\n", " -2.679790012451027,\n", " -2.678600742698486,\n", " -2.677583118245906,\n", " -2.6775831182459053,\n", " -2.6758796267950506,\n", " -2.675879626795049,\n", " -2.6757086800493313,\n", " -2.6737736930352654,\n", " -2.67108581703443,\n", " -2.670847535422895,\n", " -2.670847535422889,\n", " -2.6694078664611776,\n", " -2.669407866461177,\n", " -2.6694078664611767,\n", " -2.669407866461173,\n", " -2.665696748717592,\n", " -2.66569674871759,\n", " -2.6641487878454955,\n", " -2.660454739872777,\n", " -2.660454739872777,\n", " -2.659332534296435,\n", " -2.6593325342964342,\n", " -2.659332534296434,\n", " -2.659332534296433,\n", " -2.6578347412009964,\n", " -2.6578347412009906,\n", " -2.6569556319986396,\n", " -2.656955631998637,\n", " -2.6529187178221223,\n", " -2.652918717822121,\n", " -2.6516531684884335,\n", " -2.6516531684884295,\n", " -2.6468481252677534,\n", " -2.64684812526775,\n", " -2.6466404884327654,\n", " -2.6466404884327623,\n", " -2.6437922621120773,\n", " -2.6437922621120755,\n", " -2.643792262112075,\n", " -2.643792262112074,\n", " -2.6358480843270815,\n", " -2.6358480843270784,\n", " -2.635848084327077,\n", " -2.6358480843270735,\n", " -2.6356266205570957,\n", " -2.6356266205570935,\n", " -2.6351962410663674,\n", " -2.6314836529048873,\n", " -2.631483652904885,\n", " -2.6282698272122618,\n", " -2.628269827212259,\n", " -2.6249880719484815,\n", " -2.6249880719484806,\n", " -2.6249880719484797,\n", " -2.6249880719484766,\n", " -2.6197105864481327,\n", " -2.619710586448132,\n", " -2.6188264415523026,\n", " -2.6188264415523017,\n", " -2.618826441552301,\n", " -2.6188264415522964,\n", " -2.6178353655440643,\n", " -2.6178353655440567,\n", " -2.61714171799077,\n", " -2.6171417179907683,\n", " -2.6161683476681468,\n", " -2.615511680285557,\n", " -2.615511680285554,\n", " -2.6153851329541014,\n", " -2.6153851329540916,\n", " -2.6133477117661235,\n", " -2.6119760791666216,\n", " -2.6119760791666193,\n", " -2.611976079166614,\n", " -2.6119760791666087,\n", " -2.605482892336655,\n", " -2.6054828923366538,\n", " -2.6008540182524857,\n", " -2.600293644643852,\n", " -2.600293644643848,\n", " -2.598221279347169,\n", " -2.598221279347169,\n", " -2.5982212793471673,\n", " -2.598221279347167,\n", " -2.5963632129953904,\n", " -2.596363212995388,\n", " -2.5943367239042066,\n", " -2.5943367239042043,\n", " -2.5925041156007094,\n", " -2.5925041156007076,\n", " -2.585142242885225,\n", " -2.58344314663121,\n", " -2.5834431466312076,\n", " -2.5788689998245276,\n", " -2.578868999824522,\n", " -2.5771007754792974,\n", " -2.5771007754792934,\n", " -2.5714023872462612,\n", " -2.571402387246259,\n", " -2.5714023872462572,\n", " -2.571402387246253,\n", " -2.5695365825396896,\n", " -2.5693573855703233,\n", " -2.569357385570312,\n", " -2.5687118046616426,\n", " -2.568711804661637,\n", " -2.5661623909403013,\n", " -2.5651086282607696,\n", " -2.562103960796524,\n", " -2.5619543181500415,\n", " -2.5557523892310794,\n", " -2.5557523892310754,\n", " -2.5468799252391263,\n", " -2.5468799252391245,\n", " -2.546192331839795,\n", " -2.5461923318397925,\n", " -2.5461923318397908,\n", " -2.546192331839788,\n", " -2.5460824743988413,\n", " -2.5460824743988404,\n", " -2.54151266265998,\n", " -2.5415126626599776,\n", " -2.5411299375011205,\n", " -2.541129937501119,\n", " -2.540390582853803,\n", " -2.5403905828538025,\n", " -2.537199199269285,\n", " -2.537199199269281,\n", " -2.5324269530104617,\n", " -2.5271011824420277,\n", " -2.5271011824420273,\n", " -2.527101182442023,\n", " -2.5271011824420224,\n", " -2.525804537467803,\n", " -2.5258045374677986,\n", " -2.5242915773212786,\n", " -2.5237782307768653,\n", " -2.523776317784523,\n", " -2.523776317784523,\n", " -2.523053918636666,\n", " -2.522376672669651,\n", " -2.5223766726696475,\n", " -2.520785840066545,\n", " -2.5207858400665444,\n", " -2.5207858400665417,\n", " -2.5207858400665413,\n", " -2.5191323354337434,\n", " -2.5172445232248037,\n", " -2.516749657085203,\n", " -2.5167496570851973,\n", " -2.5141375591908575,\n", " -2.5141375591908477,\n", " -2.5096484886363077,\n", " -2.509648488636299,\n", " -2.496986487434588,\n", " -2.4969864874345857,\n", " -2.496986487434583,\n", " -2.49698648743458,\n", " -2.48762548362527,\n", " -2.477903931395911,\n", " -2.4779039313959057,\n", " -2.4779039313959053,\n", " -2.477903931395903,\n", " -2.4777197260520487,\n", " -2.477177143077887,\n", " -2.470097130322469,\n", " -2.4700971303224675,\n", " -2.4677479406268943,\n", " -2.467747940626893,\n", " -2.4652257952153143,\n", " -2.4608312388273768,\n", " -2.4608312388273688,\n", " -2.4572451272293514,\n", " -2.45724512722935,\n", " -2.457245127229349,\n", " -2.457245127229348,\n", " -2.4565450638401845,\n", " -2.4537226125503144,\n", " -2.4533301511054884,\n", " -2.4526555595895534,\n", " -2.4526555595895516,\n", " -2.4509734077991387,\n", " -2.4509734077991325,\n", " -2.449406791387177,\n", " -2.449406791387173,\n", " -2.4472544168016412,\n", " -2.4467312240463785,\n", " -2.4467312240463763,\n", " -2.4446033081607195,\n", " -2.4446033081607155,\n", " -2.443783308367212,\n", " -2.443783308367208,\n", " -2.443251142259924,\n", " -2.4419884708842807,\n", " -2.441887766780763,\n", " -2.4418877667807606,\n", " -2.441887766780759,\n", " -2.4418877667807584,\n", " -2.4417951039029973,\n", " -2.440555891582611,\n", " -2.44055589158261,\n", " -2.440412884837797,\n", " -2.4380612582983434,\n", " -2.4380612582983425,\n", " -2.4380612582983394,\n", " -2.4380612582983363,\n", " -2.4336688929614367,\n", " -2.4336688929614314,\n", " -2.4158025405362493,\n", " -2.4158025405362467,\n", " -2.414349316633815,\n", " -2.4143493166338126,\n", " -2.41434931663381,\n", " -2.414349316633806,\n", " -2.404851061102747,\n", " -2.404851061102745,\n", " -2.402891537685782,\n", " -2.4028915376857802,\n", " -2.401491971287765,\n", " -2.401491971287765,\n", " -2.3893924084136575,\n", " -2.3889588825528354,\n", " -2.388958882552834,\n", " -2.388534290302611,\n", " -2.38853429030261,\n", " -2.3856239586936736,\n", " -2.3856239586936696,\n", " -2.3852368918300924,\n", " -2.3837843649891157,\n", " -2.377739248545855,\n", " -2.377739248545853,\n", " -2.37773924854585,\n", " -2.3777392485458493,\n", " -2.3772323387089953,\n", " -2.3772323387089886,\n", " -2.367439789472911,\n", " -2.3669523967598467,\n", " -2.3669523967598445,\n", " -2.36695239675984,\n", " -2.366952396759839,\n", " -2.3566794957319606,\n", " -2.356679495731959,\n", " -2.3566794957319583,\n", " -2.356679495731953,\n", " -2.3491276369994654,\n", " -2.349127636999463,\n", " -2.3461679243711604,\n", " -2.3461679243711524,\n", " -2.335175441094879,\n", " -2.335175441094871,\n", " -2.33130514429437,\n", " -2.329534629811509,\n", " -2.329145642607602,\n", " -2.3291456426075983,\n", " -2.328117261895183,\n", " -2.3273161049017252,\n", " -2.327316104901723,\n", " -2.324666035489836,\n", " -2.3246660354898356,\n", " -2.3246660354898347,\n", " -2.3246660354898263,\n", " -2.3237696574185542,\n", " -2.323769657418553,\n", " -2.321686652688847,\n", " -2.3216866526888467,\n", " -2.321680602039287,\n", " -2.3216806020392866,\n", " -2.3216806020392826,\n", " -2.3216806020392813,\n", " -2.3199834178342975,\n", " -2.319983417834293,\n", " -2.3191524392306864,\n", " -2.3191524392306806,\n", " -2.3190707089692344,\n", " -2.3190707089692295,\n", " -2.3160144983635247,\n", " -2.3160144983635225,\n", " -2.3152595580361943,\n", " -2.315259558036193,\n", " -2.312214887633571,\n", " -2.3119974089217874,\n", " -2.3106884054005943,\n", " -2.309892931606238,\n", " -2.3092287636279596,\n", " -2.3092287636279565,\n", " -2.3088621107719587,\n", " -2.29129771445829,\n", " -2.2912977144582762,\n", " -2.2581661427555164,\n", " -2.2581661427555115,\n", " -2.2528091246762654,\n", " -2.2528091246762587,\n", " -2.251340112956381,\n", " -2.2513401129563797,\n", " -2.2513401129563793,\n", " -2.2513401129563766,\n", " -2.243291998990641,\n", " -2.2432919989906397,\n", " -2.2432919989906366,\n", " -2.2432919989906295,\n", " -2.241154910446152,\n", " -2.2411549104461446,\n", " -2.2395828582292294,\n", " -2.239582858229228,\n", " -2.2385459766343545,\n", " -2.2385459766343545,\n", " -2.229814415975765,\n", " -2.220628603473108,\n", " -2.220535423551982,\n", " -2.219770625254195,\n", " -2.2197706252541893,\n", " -2.219770625254183,\n", " -2.2197706252541813,\n", " -2.214006921594937,\n", " -2.214006921594934,\n", " -2.1896563192573706,\n", " -2.1896563192573697,\n", " -2.189459233820055,\n", " -2.189459233820051,\n", " -2.188471768248564,\n", " -2.1884717682485606,\n", " -2.188471768248558,\n", " -2.188471768248556,\n", " -2.1843606261191066,\n", " -2.184360626119096,\n", " -2.181924928172698,\n", " -2.165887641307529,\n", " -2.1624269681322335,\n", " -2.1618712296026232,\n", " -2.161871229602615,\n", " -2.151031107552182,\n", " -2.117404351418846,\n", " -2.1174043514188456,\n", " -2.1174043514188443,\n", " -2.1174043514188434,\n", " -2.116655118968017,\n", " -2.1166551189680143,\n", " -2.1152369869683096,\n", " -2.115236986968304,\n", " -2.0900505403090994,\n", " -2.0900505403090825,\n", " -2.089366783844802,\n", " -2.084077421611466,\n", " -2.0840774216114646,\n", " -2.0750086099047267,\n", " -2.0744655633559277,\n", " -2.0690510516496454,\n", " -2.0681915134345035,\n", " -2.0681915134345017,\n", " -2.067337854716953,\n", " -2.0243713190615122,\n", " -2.0243713190615007,\n", " -2.023315137731936,\n", " -2.0233151377319283,\n", " -2.020242004207923,\n", " -2.02024200420792,\n", " -2.0202420042079146,\n", " -2.0202420042079146,\n", " -1.9746178367586955,\n", " -1.9721706144653661,\n", " -1.9671325159653898,\n", " -1.967132515965385,\n", " -1.9538799258546509,\n", " -1.953872587477879,\n", " -1.9003415364012273,\n", " -1.8998188452310236,\n", " -1.8998188452310225,\n", " -1.8993718492744522,\n", " -1.899371849274451,\n", " -1.8993718492744482,\n", " -1.899371849274448,\n", " -1.8993451288781762,\n", " -1.899345128878174,\n", " -1.898893134028865,\n", " -1.8988931340288608,\n", " -1.898274536793704,\n", " -1.8876313668246154,\n", " -1.8876313668246139,\n", " -1.88715599989353,\n", " -1.8871559998935268,\n", " -1.8871559998935252,\n", " -1.8871559998935246,\n", " -1.8867049909097722,\n", " -1.8867049909097697,\n", " -1.7278211704011577,\n", " -1.7274753928552369,\n", " -1.7274753928552238,\n", " -1.7271141355575133,\n", " -1.6380950035562225,\n", " -1.6380950035562216,\n", " -1.638010054977998,\n", " -1.5245766370340939,\n", " -1.524576637034089,\n", " -1.5196619665478497,\n", " -1.5129007970251782,\n", " -1.500792614221685,\n", " -1.5007926142216799,\n", " -1.4889700407482735,\n", " -1.488970040748273,\n", " -1.47935741055206,\n", " -1.4660618232468514,\n", " -1.466061823246851,\n", " -1.4660618232468465,\n", " -1.4660618232468452,\n", " ...]"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["quasienergy.sort()\n", "quasienergy"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["delta_n = [0 for index in range(2**L-1)]\n", "for i in range(2**L-1):\n", "    delta_n[i]=quasienergy[i+1]-quasienergy[i]\n", "\n", "#平均值\n", "mean = np.mean(delta_n)\n", "#mean\n", "delta_n = delta_n / mean\n", "#画间距为0.1 的直方图\n", "delta_n\n", "#画直方分布图\n", "plt.hist(delta_n,bins=50)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/2519091373.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#plt.plot(np.arange(len(E)),quasienergy,\n", "#\t\t\t\t\tmarker='x',color='r',markersize=2,label='real space ED')\n", "plt.scatter(np.arange(len(E)),quasienergy,\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('state number',fontsize=16)\n", "plt.ylabel('energy',fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "#plt.savefig('example5a.pdf', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.01280286 0.02825694 0.03318557 ... 0.0022135  0.00255562 0.00198314]\n"]}], "source": ["print(IPR)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.187716370030987\n", "1.1877163700309914\n", "2.5305409651859763\n", "-0.6110517102234215\n"]}, {"data": {"text/plain": ["[[0.38139521100840534, 103],\n", " [0.3800587779691764, 104],\n", " [0.35925899598703226, 986],\n", " [0.35926110319171184, 1000]]"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["c=0\n", "number=[]\n", "for i in IPR:    \n", "    if i>0.3:\n", "        number.append([i,c])\n", "        print(quasienergy1[c])\n", "    c= 1+c\n", "number"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/3203828805.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(quasienergy1,list(IPR),\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('quasienergy',fontsize=16)\n", "plt.ylabel('IPR',fontsize=16)\n", "# 设置纵坐标范围为0到0.5\n", "plt.ylim(0, 0.55)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "plt.savefig('example5a.eps', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#按照 准能量的 分布画出柱状分布图\n", "plt.hist(quasienergy, bins=50)\n", "plt.xlabel('quiasenergy', fontsize=16)\n", "plt.ylabel('Count', fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.tight_layout()\n", "# 在每个柱子上标记数值\n", "#for i in range(len(number)):\n", "   # plt.text(number[i][1], i+0.5, str(number[i][0]), ha='center', va='bottom')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制直方图并获取统计信息\n", "n, bins, patches = plt.hist(quasienergy, bins=500, range=(-np.pi, np.pi))\n", "\n", "# 设置坐标轴\n", "plt.xlabel('Quasienergy', fontsize=16)\n", "plt.ylabel('Count', fontsize=16)\n", "plt.xticks([-np.pi, -np.pi/2, 0, np.pi/2, np.pi],\n", "           [r'$-\\pi$', r'$-\\pi/2$', '0', r'$\\pi/2$', r'$\\pi$'],\n", "           fontsize=14)\n", "plt.yticks(fontsize=14)\n", "\n", "# 在每个柱子上方标记数量\n", "bin_width = bins[1] - bins[0]  # 计算柱子宽度\n", "for i in range(len(n)):\n", "    count = n[i]\n", "    if count > 0:  # 只标记有数据的柱子\n", "        # 计算柱子中心位置\n", "        x_pos = bins[i] + bin_width / 2\n", "        # 放置文本标签\n", "        plt.text(x_pos, count, f'{int(count)}',\n", "                 ha='center', va='bottom',\n", "                 fontsize=8, color='darkred')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-2.75457234e-04+6.42651466e-05j,  2.68129365e-01+0.00000000e+00j,\n", "        -8.43905703e-04-1.23353355e-03j, ...,\n", "         3.24874148e-16-5.66275158e-16j,  5.91326802e-16+5.45812466e-16j,\n", "        -8.35157403e-16+1.37384852e-17j],\n", "       [ 1.96268662e-04+3.38517762e-05j, -1.51470926e-01-6.40408803e-02j,\n", "        -2.95051343e-03-1.45553397e-02j, ...,\n", "        -6.98332130e-05-3.12607819e-04j, -7.40022053e-04+4.83638568e-04j,\n", "        -7.26188670e-04+2.42203411e-04j],\n", "       [ 1.96268662e-04+3.38517762e-05j, -1.51470926e-01-6.40408803e-02j,\n", "        -2.95051343e-03-1.45553397e-02j, ...,\n", "         2.62513434e-04-3.60728233e-04j, -4.01805746e-04+1.19559507e-03j,\n", "         5.89976184e-04-1.11297067e-03j],\n", "       ...,\n", "       [ 1.96268662e-04+3.38517762e-05j, -1.51470926e-01-6.40408803e-02j,\n", "         2.95051343e-03+1.45553397e-02j, ...,\n", "         2.62513434e-04-3.60728233e-04j, -4.01805746e-04+1.19559507e-03j,\n", "         5.89976184e-04-1.11297067e-03j],\n", "       [ 1.96268662e-04+3.38517762e-05j, -1.51470926e-01-6.40408803e-02j,\n", "         2.95051343e-03+1.45553397e-02j, ...,\n", "        -6.98332130e-05-3.12607819e-04j, -7.40022053e-04+4.83638568e-04j,\n", "        -7.26188670e-04+2.42203411e-04j],\n", "       [-2.75457234e-04+6.42651466e-05j,  2.68129365e-01+2.79221091e-14j,\n", "         8.43905703e-04+1.23353355e-03j, ...,\n", "         3.92815090e-17+6.33429158e-16j, -1.63733497e-15-4.56926957e-16j,\n", "        -6.11317272e-16-4.40988441e-16j]])"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["V"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[6.40107157e-15, 5.16865471e-03, 4.98978132e-12, ...,\n", "        1.81655618e-61, 4.19358089e-61, 4.86752685e-61],\n", "       [1.57349710e-15, 7.31414487e-04, 4.86482329e-08, ...,\n", "        1.05268270e-14, 6.10803058e-13, 3.43410623e-13],\n", "       [1.57349711e-15, 7.31414487e-04, 4.86482329e-08, ...,\n", "        3.96161895e-14, 2.53094824e-12, 2.51785684e-12],\n", "       ...,\n", "       [1.57349711e-15, 7.31414487e-04, 4.86482329e-08, ...,\n", "        3.96161895e-14, 2.53094824e-12, 2.51785684e-12],\n", "       [1.57349710e-15, 7.31414487e-04, 4.86482329e-08, ...,\n", "        1.05268270e-14, 6.10803058e-13, 3.43410623e-13],\n", "       [6.40107157e-15, 5.16865471e-03, 4.98978132e-12, ...,\n", "        1.62228131e-61, 8.35006591e-60, 3.22828072e-61]])"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["np.power(np.abs(V), 4)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4096\n", "4096\n"]}], "source": ["#V的行数\n", "print(len(V))\n", "#V的列数\n", "print(len(V[0]))"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4096\n", "找到4个IPR大于0.3的向量，索引为: [ 103  104  986 1000]\n", "1.187716370030987\n", "1.1877163700309914\n", "2.5305409651859763\n", "-0.6110517102234215\n", "[-8.89462124e-16-1.67502447e-15j  6.28814100e-05+1.28230428e-04j\n", " -1.54621431e-05+1.05696159e-04j ...  1.54621431e-05-1.05696159e-04j\n", " -6.28814100e-05-1.28230428e-04j -8.20994773e-16-1.07271440e-15j]\n", "[ 819 1638 2457 3276]\n", "[-1.66350539e-15+8.37463016e-16j  6.11687422e-05+9.99605226e-05j\n", "  1.37972737e-05-1.33730268e-04j ... -1.37972737e-05+1.33730268e-04j\n", " -6.11687422e-05-9.99605226e-05j -1.67280309e-15+6.32710814e-16j]\n", "[ 819 1638 2457 3276]\n", "[0.65063949+0.00000000e+00j 0.07081324+2.99393564e-02j\n", " 0.07081324+2.99393564e-02j ... 0.07081324+2.99393564e-02j\n", " 0.07081324+2.99393564e-02j 0.65063949-1.06581410e-14j]\n", "[   0 4095]\n", "[ 0.65064045+0.00000000e+00j  0.07081329+2.99393784e-02j\n", "  0.07081329+2.99393784e-02j ... -0.07081329-2.99393784e-02j\n", " -0.07081329-2.99393784e-02j -0.65064045+1.37945211e-14j]\n", "[   0 4095]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_41293/1580034197.py:58: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_selected_vectors(IPR, ipr_threshold):\n", "    \"\"\"\n", "    筛选IPR大于阈值的列向量并绘制\n", "    \n", "    参数:\n", "    V: 输入矩阵，每行代表一个基矢，每列代表一个向量\n", "    ipr_threshold: IPR筛选阈值，默认0.5\n", "    \"\"\"\n", "    # 计算IPR\n", "    ipr = IPR\n", "    print(len(ipr))\n", "    # 筛选IPR大于阈值的列索引\n", "    selected_indices = np.where(ipr > ipr_threshold)[0]\n", "    if len(selected_indices) == 0:\n", "        print(f\"没有找到IPR大于{ipr_threshold}的向量\")\n", "        return\n", "    \n", "    print(f\"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}\")\n", "    \n", "    # 提取对应的列向量\n", "    selected_vectors = V[:, selected_indices]\n", "    \n", "    \n", "    # 设置绘图风格\n", "    plt.style.use('seaborn-v0_8-ticks')\n", "    \n", "    # 计算子图布局（最多5列）\n", "    n_cols = min(5, len(selected_indices))\n", "    n_rows = (len(selected_indices) + n_cols - 1) // n_cols\n", "    \n", "    # 创建画布\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))\n", "    axes = np.ravel(axes)  # 转换为一维数组便于索引\n", "    for i, idx in enumerate(selected_indices):\n", "        E_slect = quasienergy1[idx]\n", "        print(E_slect)\n", "    # 绘制每个选中的向量\n", "    for i, idx in enumerate(selected_indices):\n", "        ax = axes[i]\n", "        # 绘制向量的绝对值点线图\n", "        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')\n", "        print(selected_vectors[:, i])\n", "        print(np.where((np.abs(selected_vectors[:, i]) > 0.1))[0])\n", "        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))\n", "        ax.set_title(f'向量索引: {idx}\\nIPR = {ipr[idx]:.4f}.E = {quasienergy1[idx]:.4f}', fontsize=10)\n", "        ax.set_xlabel('基矢索引', fontsize=8)\n", "        ax.set_ylabel('|振幅|', fontsize=8)\n", "        ax.tick_params(axis='both', which='major', labelsize=6)\n", "    \n", "    # 隐藏未使用的子图\n", "    for i in range(len(selected_indices), len(axes)):\n", "        axes[i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return selected_vectors, selected_indices\n", "\n", "# 示例使用\n", "if __name__ == \"__main__\":\n", "    # 筛选并绘图\n", "    selected_vecs, selected_idx = plot_selected_vectors(IPR, ipr_threshold=0.3)\n"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["selected_vecs\n", "#把这个结果导出为一个可以编辑的表格\n", "import pandas as pd\n", "# 创建一个DataFrame\n", "df = pd.DataFrame(np.abs(selected_vecs))\n", "\n", "# 将DataFrame保存为Excel文件\n", "df.to_excel('selected_vectors.xlsx', index=False)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}