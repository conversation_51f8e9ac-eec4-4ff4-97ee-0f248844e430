import numpy as np
from scipy.sparse.linalg import eigsh

J=1
h=1
N=4

# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state

import numpy as np
#仅保留平移对称性
reprcount = []  # 全局记录列表（按需保留）

def Ham_total(N,J,h):
    H = np.zeros((2 ** N, 2 ** N))

    # a is a basis
    for a in range(2 ** N):
        # i is position of this basis
        for i in range(N):
            # j is the nearest neighbor, mod N for periodic boundary conditions
            j = (i + 1) % N
            ai = get_site_value(a,i)
            aj = get_site_value(a,j)

            # Sz
            b = a
            if ai == aj:
                H[a, b] += J
            else:
                H[a, b] += -J

            # SxSx + SySy
            if ai != aj:
                b = flip_state(a, i)
                b = flip_state(b, j)
                H[a, b] += h
    return H

def represent(L,a0):
    at = a0
    a = a0
    l=0
    q=0
    g=0
    smax = 2**L-1
    for t in range(1,L+1):
        at = translate(L,a0,t)
        if at < a:
            a = at
            l=t
            g=0
        #az=smax-at
        #if az<a:
        #    a=az
        #   l=t
        #    g=1    
    '''at = reverseBits(a0,L)
    for t in range(L):
        if at<a:
            a=at
            l=t
            q=1
            g=0
        az=smax-at
        if az<a:
            a=az
            l=t
            q=1
            g=1
        at = translate(L, at, 1)'''
    return a,l,q,g


# 辅助函数：仅考虑平移对称性的ffun（示例实现）
def ffun(t, s, l, k, N):
    """仅计算平移l步在动量k扇区的相位（布洛赫相位）"""
    # 核心：平移l步的相位因子 e^(i * 2πk l / N)
    return np.exp(1j * 2 * np.pi * k * l / N)

# ========= 新增：仅基于平移对称与总磁化(M,k)的基与块哈密顿量 =========
def get_magnetization(state: int, N: int) -> int:
    m = 0
    for i in range(N):
        m += get_site_value(state, i)
    return m

def orbit_period(N: int, s: int) -> int:
    t = translate(N, s, 1)
    R = 1
    while t != s:
        t = translate(N, t, 1)
        R += 1
    return R

def is_k_compatible(N: int, R: int, k: int) -> bool:
    return (k * R) % N == 0

if __name__ == "__main__":
    # ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========
    def orbit_period(N: int, s: int) -> int:
        t = translate(N, s, 1)
        R = 1
        while t != s:
            t = translate(N, t, 1)
            R += 1
        return R

    def get_magnetization(state: int, N: int) -> int:
        m = 0
        for i in range(N):
            m += get_site_value(state, i)
        return m

    def is_k_compatible(N: int, R: int, k: int) -> bool:
        return (k * R) % N == 0

    def build_translation_basis_by_k(N: int):
        basis = {}
        seen = set()
        for s in range(2 ** N):
            rep, _, _, _ = represent(N, s)
            if rep in seen:
                continue
            seen.add(rep)
            R = orbit_period(N, rep)
            for k in range(N):
                if is_k_compatible(N, R, k):
                    if k not in basis:
                        basis[k] = {'repr': [], 'peri': []}
                    basis[k]['repr'].append(rep)
                    basis[k]['peri'].append(R)
        return basis

    def helement_translation_simple(Ra: int, Rb: int, l: int, k: int, N: int):
        return np.sqrt(Ra / Rb) * np.exp(1j * 2 * np.pi * k * l / N)

    def build_block_Hamiltonian_translation(N: int, reps: list, peri: list, k: int):
        nrep = len(reps)
        Hk = np.zeros((nrep, nrep), dtype=complex)
        for ia in range(nrep):
            sa = reps[ia]
            Ra = peri[ia]
            # 对角项（SzSz 总和，平移不变，取代表元即可）
            Ez = 0.0
            for i in range(N):
                j = (i + 1) % N
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                Ez += (J if ai == aj else -J)
            Hk[ia, ia] += Ez
            # 非对角：当相邻不同则成对翻转（与文件定义一致）
            for i in range(N):
                j = (i + 1) % N
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                if ai != aj:
                    #sb = flip_state(sa, i)
                    sb = flip_state(flip_state(sa, i), j)
                    rep_b, l, _, _ = represent(N, sb)
                    if rep_b in reps:
                        ib = reps.index(rep_b)
                        Rb = peri[ib]
                        elem = h * helement_translation_simple(Ra, Rb, l, k, N)
                        Hk[ia, ib] += elem
        return Hk

    def fullspectrum_blocks_direct(N: int):
        basis = build_translation_basis_by_k(N)
        total_dim = sum(len(v['repr']) for v in basis.values())
        assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
        energies = []
        labels = []
        for k, data in sorted(basis.items()):
            reps = data['repr']
            peri = data['peri']
            if len(reps) == 0:
                continue
            Hk = build_block_Hamiltonian_translation(N, reps, peri, k)
            w, _ = np.linalg.eigh(Hk)
            print(len(w))
            energies.extend(w.real.tolist())
            labels.extend([k] * len(w))
        return energies, labels

    # 验证与全空间一致（小尺寸）
    #H_full = Ham_total(N, J, h)
    #eval_full, _ = np.linalg.eigh(H_full)
    #eval_full = np.sort(eval_full.real)
    E_blocks, labels = fullspectrum_blocks_direct(N)
    E_blocks = np.sort(np.array(E_blocks))
    basis = build_translation_basis_by_k(N)
    print(basis)
    total_dim = sum(len(v['repr']) for v in basis.values())
    print(f"N={N}，所有 k 块维度之和: {total_dim}，应为 {2**N}")
    #if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):
    #    print("警告：块对角化谱与全空间谱不一致！")
    #    print(f"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}")
    #    m = min(len(E_blocks), len(eval_full))
    #    if m > 0:
    #        print(f"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}")
    #else:
    #    print("验证成功：块对角化本征值与全空间本征值一致。")
    print(E_blocks)
        #print(eval_full)
