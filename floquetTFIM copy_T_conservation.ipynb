import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import expm
from scipy.sparse.linalg import eigsh
from math import * 
import pandas as pd
import cmath
import os #导入os库
import random
import time
import seaborn as sns
import cmath
import scipy
import functools
import matplotlib
import matplotlib.pyplot as plt
%matplotlib inline

def get_hamiltonian_sparse(L, Jz, hx):
    '''
    Creates the Hamiltonian of the Transverse Field Ising model
    on a linear chain lattice with periodic boundary conditions.

    The Hamiltonian is given by:
    H = -Jz \sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \sum_{i=1}^{L} S_i^x - hz \sum_{i=1}^{L} S_i^z
    Args:
        L (int): length of chain
        J (float): coupling constant for Ising term
        hx (float): coupling constant for transverse field

    Returns:
        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:
        hamiltonian_rows (list of ints): row index of non-zero elements
        hamiltonian_cols (list of ints): column index of non-zero elements
        hamiltonian_data (list of floats): value of non-zero elements
    '''

    def get_site_value(state, site):
        ''' Function to get local value at a given site '''
        return (state >> site) & 1
        #返回值为state的第site位上的二进制值

    def hilbertspace_dimension(L):
        ''' return dimension of hilbertspace '''
        return 2**L

    def flip_state(state: int, index: int) -> int:
        """翻转一个整数某位置处的二进制值"""
        mask = 1 << index
        return state ^ mask
    # Define chain lattice
    #ising_bonds = [(site, (site+1)%L) for site in range(L-1)]#开放边条件
    ising_bonds = [(site, (site+1)%L) for site in range(L)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0

    # Empty lists for sparse matrix
    hamiltonian_rows = []
    hamiltonian_cols = []
    hamiltonian_data = []
    
    # Run through all spin configurations
    for state in range(hilbertspace_dimension(L)):

        # Apply Ising bonds
        ising_diagonal = 0
        for bond in ising_bonds:
            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):
                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5
            else:
                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5
        hamiltonian_rows.append(state)
        hamiltonian_cols.append(state)
        hamiltonian_data.append(ising_diagonal)

        # Apply transverse field
        for site in range(L):
            # Flip spin at site
            new_state = flip_state(state,site)#state ^ (1 << site)
            hamiltonian_rows.append(new_state)
            hamiltonian_cols.append(state)
            hamiltonian_data.append(hx)#(mid3[state])#hx
    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data

#引入T平移对称性下的分块矩阵
# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state
#仅保留平移对称性
reprcount = []  # 全局记录列表（按需保留）

def Ham_total(N,J,h):
    H = np.zeros((2 ** N, 2 ** N))

    # a is a basis
    for a in range(2 ** N):
        # i is position of this basis
        for i in range(N):
            # j is the nearest neighbor, mod N for periodic boundary conditions
            j = (i + 1) % N
            ai = get_site_value(a,i)
            aj = get_site_value(a,j)

            # Sz
            b = a
            if ai == aj:
                H[a, b] += J
            else:
                H[a, b] += -J

            # SxSx + SySy
            #if ai != aj:
            b = flip_state(a, i)
            #b = flip_state(b, j)
            H[a, b] += h
    return H
def represent(L,a0):
    at = a0
    a = a0
    l=0
    q=0
    g=0
    smax = 2**L-1
    for t in range(1,L+1):
        at = translate(L,a0,t)
        if at < a:
            a = at
            l=t
            g=0
        #az=smax-at
        #if az<a:
        #    a=az
        #   l=t
        #    g=1    
    return a,l,q,g
def orbit_period(N: int, s: int) -> int:
        t = translate(N, s, 1)
        R = 1
        while t != s:
            t = translate(N, t, 1)
            R += 1
        return R
def get_magnetization(state: int, N: int) -> int:
    m = 0
    for i in range(N):
        m += get_site_value(state, i)
    return m

def is_k_compatible(N: int, R: int, k: int) -> bool:
    return (k * R) % N == 0

# ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========
def build_translation_basis_by_k(N: int):
    basis = {}
    seen = set()
    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s)
        if rep in seen:
            continue
        seen.add(rep)
        R = orbit_period(N, rep)
        for k in range(N):
            if is_k_compatible(N, R, k):
                if k not in basis:
                    basis[k] = {'repr': [], 'peri': []}
                basis[k]['repr'].append(rep)
                basis[k]['peri'].append(R)
    return basis
def helement_translation_simple(Ra: int, Rb: int, l: int, k: int, N: int):
        return np.sqrt(Ra / Rb) * np.exp(1j * 2 * np.pi * k * l / N)
def build_block_Hamiltonian_translation(N: int, reps: list, peri: list, k: int,J,h):
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    for ia in range(nrep):
        sa = reps[ia]
        Ra = peri[ia]
        # 对角项（SzSz 总和，平移不变，取代表元即可）
        Ez = 0.0
        for i in range(N):
            j = (i + 1) % N
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)
        Hk[ia, ia] += Ez
        # 非对角：当相邻不同则成对翻转（与文件定义一致）
        for i in range(N):
            #j = (i + 1) % N
            #ai = get_site_value(sa, i)
            #aj = get_site_value(sa, j)
            #if ai != aj:
            sb = flip_state(sa, i)
            #sb = flip_state(flip_state(sa, i), j)
            rep_b, l, _, _ = represent(N, sb)
            if rep_b in reps:
                ib = reps.index(rep_b)
                Rb = peri[ib]
                elem = h * helement_translation_simple(Ra, Rb, l, k, N)
                Hk[ia, ib] += elem
    return Hk
def fullspectrum_blocks_direct(N: int):
    basis = build_translation_basis_by_k(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    energies = []
    labels = []
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
        Hk = build_block_Hamiltonian_translation(N, reps, peri, k)
        w, _ = np.linalg.eigh((Hk))
        print(len(w))
        energies.extend(w.real.tolist())
        labels.extend([k] * len(w))
    return energies, labels
# ========= 基于平移与磁化(M,k)构建块矩阵 ==========
def floquet_spectrum(N: int, J: float, h: float, t1: float, t2: float):
    basis = build_translation_basis_by_k(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    energies = []
    labels = []
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k,0*J,h)
        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k,J,0*h)
        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)
        #print(Hk)
        #求解本征值和本征矢并同时存在
        w, _ = np.linalg.eig(Hk)
        print(w)
        energies.extend(w)#tolist()函数将数组转换为列表
        labels.extend([k] * len(w))
    return energies, labels    
    
        #通过块对角化的本征矢求出全空间的本征矢
        #V = np.zeros((2 ** N, len(reps)), dtype=complex)
        #for i, rep in enumerate(reps):
        #    V[rep, i] = 1
        #V = V @ Hk
        #V = V @ np.linalg.inv(Hk_1) @ np.linalg.inv(Hk_2)
        #V = V @ np.linalg.inv(expm(-1j * t1 * Hk_1)) @ np.linalg.inv(expm(-1j * t2 * Hk_2))


def floquet_spectrum_with_IPR(N: int, J: float, h: float, t1: float, t2: float):
    """计算 Floquet 谱和对应的 IPR（使用您的计算方法）"""
    basis = build_translation_basis_by_k(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    
    energies = []
    labels = []
    ipr_values = []
    
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
            
        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)
        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)
        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)
        
        # 求解本征值和本征矢
        w, v = np.linalg.eig(Hk)
        
        # 使用您的 IPR 计算方法：直接四次方
        IPR1 = np.multiply(np.multiply(np.abs(v), np.abs(v)), 
                           np.multiply(np.abs(v), np.abs(v)))
        IPR = np.sum(IPR1, axis=0)
        
        # 收集结果
        energies.extend(w)
        labels.extend([k] * len(w))
        ipr_values.extend(IPR)
    
    return energies, labels, ipr_values

def floquet_spectrum_with_correct_IPR(N: int, J: float, h: float, t1: float, t2: float):
    """计算分块基上的正确 IPR"""
    basis = build_translation_basis_by_k(N)
    energies = []
    labels = []
    ipr_values = []
    
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
            
        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)
        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)
        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)
        
        w, v = np.linalg.eig(Hk)
        
        # 在分块基上计算 IPR
        for i in range(len(w)):
            eigenvector = v[:, i]
            # 关键：考虑轨道权重
            psi_sq = np.abs(eigenvector)**2
            # 每个代表元对应一个轨道，权重为 1/R
            weights = np.array([1.0/period for period in peri])
            weighted_psi_sq = psi_sq * weights
            
            #ipr = np.sum(weighted_psi_sq**2) / (np.sum(weighted_psi_sq)**2)
            ipr = np.sum(weighted_psi_sq**2)
            ipr_values.append(ipr)
        
        energies.extend(w)
        labels.extend([k] * len(w))
    
    return energies, labels, ipr_values

Ham_total(4, 1, 1)

def floquet_spectrum_with_fullspace_IPR(N: int, J: float, h: float, t1: float, t2: float):
    """计算 Floquet 谱和全空间 IPR（通过 k 分块叠加）"""
    basis = build_translation_basis_by_k(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    
    energies = []
    labels = []
    
    # 初始化全空间本征矢矩阵
    dim_full = 2 ** N
    full_eigenvectors = np.zeros((dim_full, total_dim), dtype=complex)
    current_col = 0
    
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
            
        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)
        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)
        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)
        
        # 求解本征值和本征矢
        w, v = np.linalg.eig(Hk)
        n_eigenstates = len(w)
        
        # 构造投影矩阵 V_k
        V_k = build_projection_matrix_for_k(N, reps, peri, k)
        
        # 将分块本征矢投影到全空间
        for i in range(n_eigenstates):
            # ψ_full = V_k @ ψ_k
            full_eigenvectors[:, current_col] = V_k @ v[:, i]
            current_col += 1
        
        energies.extend(w)
        labels.extend([k] * n_eigenstates)
    
    # 使用您的方法计算全空间 IPR
    IPR1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), 
                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))
    IPR = np.sum(IPR1, axis=0)
    
    return energies, labels, IPR, full_eigenvectors

def build_projection_matrix_for_k(N: int, reps: list, peri: list, k: int):
    """为特定 k 构造投影矩阵 V_k"""
    dim_full = 2 ** N
    nrep = len(reps)
    V = np.zeros((dim_full, nrep), dtype=complex)
    
    for col, rep in enumerate(reps):
        # 生成该代表元的完整轨道
        orbit = generate_orbit_states(N, rep)
        R = len(orbit)
        norm = 1.0 / np.sqrt(R)
        
        # 填充轨道上的所有状态
        for r, s in enumerate(orbit):
            phase = np.exp(-1j * 2 * np.pi * k * r / N)
            V[s, col] = norm * phase
    
    return V

def generate_orbit_states(N: int, rep: int):
    """生成代表元 rep 的完整平移轨道"""
    """修正后的轨道生成"""
    states = [rep]
    t = translate(N, rep, 1)
    count = 0
    max_iter = N  # 防止无限循环
    
    while t != rep and count < max_iter:
        states.append(t)
        t = translate(N, t, 1)
        count += 1
    
    if count >= max_iter:
        print(f"警告：轨道生成可能有问题，rep={rep}")
    
    return states

#测试函数
LL = 4
Hr11,Hc11,Hd11 = get_hamiltonian_sparse(LL, 1, 1)
#矩阵形式输出
H11=np.zeros((2**LL,2**LL))  
H11[Hr11,Hc11] = Hd11
E111,V111=np.linalg.eig(H11)
print(H11)
print(E111)

L = 4
#T=t1+t2
t_1 = 1
t_2 = 1
lam_h = 0
lam_J = 0.01
hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5
J=np.pi/4 - lam_J#J*t2# 0.75


Hr1,Hc1,Hd1 = get_hamiltonian_sparse(L,0 * J,hx)
Hr2,Hc2,Hd2 = get_hamiltonian_sparse(L,J,0 * hx)
#创建3X3的0矩阵
H1=np.zeros((2**L,2**L))  
H2=np.zeros((2**L,2**L))
H1[Hr1,Hc1] = Hd1
H2[Hr2,Hc2] = Hd2
H_F = expm(-1j*H1) @ expm(-1j*H2)

E,V=np.linalg.eig(H_F)

print(E)

#if __name__ == "__main__":
# 验证与全空间一致（小尺寸）
#H_full = Ham_total(N, J, h)
#eval_full, _ = np.linalg.eigh(H_full)
#eval_full = np.sort(eval_full.real)
E_blocks, labels,IPR_K,full_vecs = floquet_spectrum_with_fullspace_IPR(L, J, hx, t_1, t_2)
quasienergy_k = [0 for index in range(2**L)]
for i in range(0,2**L,1):
    quasienergy_k[i] = (cmath.phase(E_blocks[i]))
quasienergy1_k = quasienergy_k.copy()
#print(quasienergy_k)
basis = build_translation_basis_by_k(L)
#print(basis)
total_dim = sum(len(v['repr']) for v in basis.values())
print(f"N={L}，所有 k 块维度之和: {total_dim}，应为 {2**L}")
#if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):
#    print("警告：块对角化谱与全空间谱不一致！")
#    print(f"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}")
#    m = min(len(E_blocks), len(eval_full))
#    if m > 0:
#        print(f"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}")
#else:
#    print("验证成功：块对角化本征值与全空间本征值一致。")
#print(E_blocks,len(E_blocks))
    #print(eval_full)

#V的每一个矩阵元取绝对值
V = np.abs(V)

print(H_F)

quasienergy = [0 for index in range(2**L)]
for i in range(0,2**L,1):
    quasienergy[i] = (cmath.phase(E[i]))
quasienergy1 = quasienergy.copy()

# 输出准能量为 txt 文件
import numpy as np
np.savetxt('quasienergy_output.txt', quasienergy)
print('准能量已保存到 quasienergy_output.txt')

#真能量按大小排序

np.sort(quasienergy_k)

np.sort(quasienergy)

def debug_floquet_spectrum_consistency(N: int, J: float, h: float, t1: float, t2: float):
    """调试分块与全空间 Floquet 谱的一致性"""
    
    print("=== 步骤1：全空间计算（标准结果） ===")
    # 全空间方法
    Hr1, Hc1, Hd1 = get_hamiltonian_sparse(N, 0*J, h)
    Hr2, Hc2, Hd2 = get_hamiltonian_sparse(N, J, 0*h)
    
    H1_full = np.zeros((2**N, 2**N))
    H2_full = np.zeros((2**N, 2**N))
    H1_full[Hr1, Hc1] = Hd1
    H2_full[Hr2, Hc2] = Hd2
    
    H_F_full = expm(-1j * t1 * H1_full) @ expm(-1j * t2 * H2_full)
    E_full, V_full = np.linalg.eig(H_F_full)
    
    print(f"全空间维度: {H_F_full.shape}")
    print(f"全空间准能数量: {len(E_full)}")
    print(f"全空间前5个准能: {E_full[:5]}")
    
    print("\n=== 步骤2：分块计算 ===")
    basis = build_translation_basis_by_k(N)
    print(f"分块信息: {basis.keys()}")
    
    energies_block = []
    labels_block = []
    all_block_states = 0
    
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
            
        print(f"\nk={k}: 代表元数量={len(reps)}, 周期={peri}")
        
        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)
        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)
        
        print(f"  Hk_1 维度: {Hk_1.shape}")
        print(f"  Hk_2 维度: {Hk_2.shape}")
        
        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)
        
        w, v = np.linalg.eig(Hk)
        print(f"  本征值数量: {len(w)}")
        print(f"  前3个本征值: {w[:3]}")
        
        energies_block.extend(w)
        labels_block.extend([k] * len(w))
        all_block_states += len(w)
    
    print(f"\n分块总状态数: {all_block_states}")
    print(f"全空间状态数: {len(E_full)}")
    
    # 验证状态数量
    if all_block_states != len(E_full):
        print(f"❌ 状态数量不一致！分块={all_block_states}, 全空间={len(E_full)}")
        return None, None, None
    
    print("\n=== 步骤3：对比本征值 ===")
    # 排序后对比
    E_full_sorted = np.sort(E_full)
    E_block_sorted = np.sort(energies_block)
    
    diff = np.abs(E_full_sorted - E_block_sorted)
    print(f"最大绝对差异: {np.max(diff)}")
    print(f"平均绝对差异: {np.mean(diff)}")
    
    if np.allclose(E_full_sorted, E_block_sorted, atol=1e-10):
        print("✅ 本征值完全一致！")
    else:
        print("❌ 本征值不一致，需要进一步检查")
        
        # 进一步诊断
        print("\n=== 详细诊断 ===")
        for i in range(min(10, len(E_full_sorted))):
            print(f"索引{i}: 全空间={E_full_sorted[i]:.6f}, 分块={E_block_sorted[i]:.6f}, 差异={diff[i]:.2e}")
    
    return E_full, energies_block, V_full

# 测试函数
# 运行诊断函数
result = debug_floquet_spectrum_consistency(N=4, J=1, h=1, t1=1, t2=1)

if result[0] is not None:
    print("\n=== 成功！两种方法本征值一致 ===")
else:
    print("\n=== 需要进一步调试 ===")

IPR1 = np.multiply(np.multiply(abs(V),abs(V)),np.multiply(abs(V),abs(V)))
IPR = np.sum((IPR1),axis=0)
IPR

IPR_K

def corrected_floquet_spectrum_with_IPR(N: int, J: float, h: float, t1: float, t2: float):
    """修正后的分块IPR计算，确保与全空间结果一致"""
    
    # 第一步：全空间计算（作为标准）
    print("计算全空间标准结果...")
    H1_full = Ham_total(N, 0*J, h)
    H2_full = Ham_total(N, J, 0*h)
    H_F_full = expm(-1j * t1 * H1_full) @ expm(-1j * t2 * H2_full)
    
    E_full, V_full = np.linalg.eig(H_F_full)
    IPR1_full = np.multiply(np.multiply(np.abs(V_full), np.abs(V_full)), 
                             np.multiply(np.abs(V_full), np.abs(V_full)))
    IPR_full = np.sum(IPR1_full, axis=0)
    
    # 第二步：分块计算
    print("计算分块结果...")
    basis = build_translation_basis_by_k(N)
    energies_block = []
    labels_block = []
    
    # 收集所有分块本征矢
    all_block_eigenvectors = []
    all_block_energies = []
    
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
            
        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)
        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)
        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)
        
        w, v = np.linalg.eig(Hk)
        
        # 存储分块结果
        all_block_eigenvectors.append((k, reps, peri, v))
        all_block_energies.extend(w)
        labels_block.extend([k] * len(w))
    
    # 第三步：构造完整的投影矩阵
    print("构造投影矩阵...")
    dim_full = 2 ** N
    total_states = sum(len(v) for _, _, _, v in all_block_eigenvectors)
    
    # 初始化全空间本征矢矩阵
    full_eigenvectors_block = np.zeros((dim_full, total_states), dtype=complex)
    
    current_col = 0
    for k, reps, peri, eigenvectors in all_block_eigenvectors:
        n_eigenstates = eigenvectors.shape[1]
        
        for i in range(n_eigenstates):
            # 对每个本征矢，构造其在全空间的表示
            full_state = np.zeros(dim_full, dtype=complex)
            
            for j, rep in enumerate(reps):
                # 生成该代表元的完整轨道
                orbit = generate_orbit_states(N, rep)
                R = len(orbit)
                norm = 1.0 / np.sqrt(R)
                
                # 填充轨道上的所有状态
                for r, s in enumerate(orbit):
                    phase = np.exp(-1j * 2 * np.pi * k * r / N)
                    full_state[s] += norm * phase * eigenvectors[j, i]
            
            full_eigenvectors_block[:, current_col] = full_state
            current_col += 1
    
    # 第四步：计算分块投影后的IPR
    print("计算分块IPR...")
    IPR1_block = np.multiply(np.multiply(np.abs(full_eigenvectors_block), np.abs(full_eigenvectors_block)), 
                             np.multiply(np.abs(full_eigenvectors_block), np.abs(full_eigenvectors_block)))
    IPR_block = np.sum(IPR1_block, axis=0)
    
    # 第五步：验证结果
    print("\n=== 验证结果 ===")
    print(f"全空间维度: {V_full.shape}")
    print(f"分块投影后维度: {full_eigenvectors_block.shape}")
    print(f"全空间状态数: {len(IPR_full)}")
    print(f"分块状态数: {len(IPR_block)}")
    
    if len(IPR_full) == len(IPR_block):
        # 排序后对比
        IPR_full_sorted = np.sort(IPR_full)
        IPR_block_sorted = np.sort(IPR_block)
        
        diff = np.abs(IPR_full_sorted - IPR_block_sorted)
        print(f"最大绝对差异: {np.max(diff)}")
        print(f"平均绝对差异: {np.mean(diff)}")
        
        if np.allclose(IPR_full_sorted, IPR_block_sorted, atol=1e-10):
            print("✅ IPR 计算结果一致！")
        else:
            print("❌ IPR 计算结果不一致")
            
            # 进一步诊断
            print("\n=== 诊断信息 ===")
            print("检查投影矩阵的正交性...")
            
            # 检查投影矩阵的正交性
            for k, reps, peri, eigenvectors in all_block_eigenvectors:
                print(f"k={k}: 代表元数量={len(reps)}, 本征矢数量={eigenvectors.shape[1]}")
    
    return E_full, IPR_full, all_block_energies, IPR_block, full_eigenvectors_block

# 测试函数
E_full, IPR_full, E_block, IPR_block, full_vecs = corrected_floquet_spectrum_with_IPR(
    N=4, J=1, h=1, t1=1, t2=1
)

# 调用函数

# 打印结果
print("准能数量:", len(quasienergy1_k))
print("IPR 数量:", len(IPR_K))
print("前5个 IPR 值:", IPR_K[:5])

# 绘制 IPR vs 准能
plt.scatter(quasienergy1_k, IPR_K, s=10)
plt.xlabel('准能')
plt.ylabel('IPR')
plt.title('IPR vs 准能')
plt.show()

quasienergy.sort()
quasienergy

delta_n = [0 for index in range(2**L-1)]
for i in range(2**L-1):
    delta_n[i]=quasienergy[i+1]-quasienergy[i]

#平均值
mean = np.mean(delta_n)
#mean
delta_n = delta_n / mean
#画间距为0.1 的直方图
delta_n
#画直方分布图
plt.hist(delta_n,bins=50)
plt.show()

#plt.plot(np.arange(len(E)),quasienergy,
#					marker='x',color='r',markersize=2,label='real space ED')
plt.scatter(np.arange(len(E)),quasienergy,
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('state number',fontsize=16)
plt.ylabel('energy',fontsize=16)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
#plt.savefig('example5a.pdf', bbox_inches='tight')
plt.show()

print(IPR)

c=0
number=[]
for i in IPR:    
    if i>0.2:
        number.append([i,c])
        print(quasienergy1[c])
    c= 1+c
number

plt.scatter(quasienergy1,list(IPR),
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('quasienergy',fontsize=16)
plt.ylabel('IPR',fontsize=16)
# 设置纵坐标范围为0到0.5
plt.ylim(0, 0.5)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
plt.savefig('example5a.eps', bbox_inches='tight')
plt.show()

#按照 准能量的 分布画出柱状分布图
plt.hist(quasienergy, bins=50)
plt.xlabel('quiasenergy', fontsize=16)
plt.ylabel('Count', fontsize=16)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.tight_layout()
# 在每个柱子上标记数值
#for i in range(len(number)):
   # plt.text(number[i][1], i+0.5, str(number[i][0]), ha='center', va='bottom')
plt.show()

# 绘制直方图并获取统计信息
n, bins, patches = plt.hist(quasienergy1, bins=5000, range=(-np.pi, np.pi))

# 设置坐标轴
plt.xlabel('Quasienergy', fontsize=16)
plt.ylabel('Count', fontsize=16)
plt.xticks([-np.pi, -np.pi/2, 0, np.pi/2, np.pi],
           [r'$-\pi$', r'$-\pi/2$', '0', r'$\pi/2$', r'$\pi$'],
           fontsize=14)
plt.yticks(fontsize=14)

# 在每个柱子上方标记数量
bin_width = bins[1] - bins[0]  # 计算柱子宽度
for i in range(len(n)):
    count = n[i]
    if count > 0:  # 只标记有数据的柱子
        # 计算柱子中心位置
        x_pos = bins[i] + bin_width / 2
        # 放置文本标签
        plt.text(x_pos, count, f'{int(count)}',
                 ha='center', va='bottom',
                 fontsize=8, color='darkred')

plt.tight_layout()
plt.show()

import numpy as np
import matplotlib.pyplot as plt


def plot_selected_vectors(IPR, ipr_threshold):
    """
    筛选IPR大于阈值的列向量并绘制
    
    参数:
    V: 输入矩阵，每行代表一个基矢，每列代表一个向量
    ipr_threshold: IPR筛选阈值，默认0.5
    """
    # 计算IPR
    ipr = IPR
    print(len(ipr))
    # 筛选IPR大于阈值的列索引
    selected_indices = np.where(ipr > ipr_threshold)[0]
    if len(selected_indices) == 0:
        print(f"没有找到IPR大于{ipr_threshold}的向量")
        return
    
    print(f"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}")
    
    # 提取对应的列向量
    selected_vectors = V[:,selected_indices]
    print(selected_vectors)
    
    
    # 设置绘图风格
    plt.style.use('seaborn-v0_8-ticks')
    
    # 计算子图布局（最多5列）
    n_cols = min(5, len(selected_indices))
    n_rows = (len(selected_indices) + n_cols - 1) // n_cols
    
    # 创建画布
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))
    axes = np.ravel(axes)  # 转换为一维数组便于索引
    for i, idx in enumerate(selected_indices):
        E_slect = quasienergy1[idx]
        print(E_slect)
    # 绘制每个选中的向量
    for i, idx in enumerate(selected_indices):
        ax = axes[i]
        # 绘制向量的绝对值点线图
        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')
        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))
        ax.set_title(f'向量索引: {idx}\nIPR = {ipr[idx]:.4f}', fontsize=10)
        ax.set_xlabel('基矢索引', fontsize=8)
        ax.set_ylabel('|振幅|', fontsize=8)
        ax.tick_params(axis='both', which='major', labelsize=6)
    
    # 隐藏未使用的子图
    for i in range(len(selected_indices), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return selected_vectors, selected_indices

# 示例使用
if __name__ == "__main__":
    # 筛选并绘图
    selected_vecs, selected_idx = plot_selected_vectors(IPR, ipr_threshold=0.1)



'''import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
import cmath
from scipy.linalg import expm
import time

# 假设 get_hamiltonian_sparse 已定义
# def get_hamiltonian_sparse(col, row, J, hx):
#     # 实现略
#     pass
col = 2
row = 3
L = 7#col * row
def phasecrate(lam_h, lam_J):

    # 注意：原函数中这两行覆盖了输入参数，建议修改
    # lam_h = 0.01
    # lam_J = 0.01
    hx = np.pi/2 - lam_h
    J_val = np.pi/2 - lam_J
    Hr1, Hc1, Hd1 = get_hamiltonian_sparse(L, 0 * J_val, hx)
    Hr2, Hc2, Hd2 = get_hamiltonian_sparse(L, J_val, 0 * hx)
    
    # 创建哈密顿量矩阵
    H1 = np.zeros((2**L, 2**L), dtype=complex)  
    H2 = np.zeros((2**L, 2**L), dtype=complex)
    H1[Hr1, Hc1] = Hd1
    H2[Hr2, Hc2] = Hd2
    
    # 计算演化算符
    H_F = expm(-1j * H1) @ expm(-1j * H2)
    
    # 求解本征值和本征向量
    E, V = np.linalg.eig(H_F)
    
    # 计算准能（本征值的相位）
    quasienergy = [cmath.phase(e) for e in E]
    
    # 计算IPR
    abs4 = np.power(np.abs(V), 4)
    IPR = np.sum(abs4, axis=0)
    
    return quasienergy, IPR

def test_phasecrate_scan(n_points=6):
    """
    遍历 lam_h 和 lam_J 从 0 到 π，绘制 IPR 随准能变化的关系图
    n_points: 每个参数的采样点数
    """
    # 设置参数扫描范围
    lam_h_vals = np.linspace(0, np.pi, n_points)
    lam_J_vals = np.linspace(0, np.pi, n_points)
    
    # 存储结果的数组
    all_quasienergies = []
    all_iprs = []
    params_list = []
    
    print(f"开始扫描 {n_points}x{n_points} 个参数点...")
    start_time = time.time()
    
    # 遍历参数空间
    for i, lam_h in enumerate(lam_h_vals):
        for j, lam_J in enumerate(lam_J_vals):
            # 显示进度
            if (i * n_points + j) % 10 == 0:
                progress = (i * n_points + j) / (n_points * n_points) * 100
                print(f"进度: {progress:.1f}%")
            
            # 计算准能和IPR
            quasienergy, ipr = phasecrate(lam_h, lam_J)
            
            # 存储结果
            all_quasienergies.extend(quasienergy)
            all_iprs.extend(ipr)
            params_list.extend([(lam_h, lam_J)] * len(quasienergy))
    
    end_time = time.time()
    print(f"扫描完成，耗时 {end_time - start_time:.2f} 秒")
    
    # 转换为numpy数组
    all_quasienergies = np.array(all_quasienergies)
    all_iprs = np.array(all_iprs)
    params_array = np.array(params_list)
    
    # 绘制IPR随准能变化的散点图
    plt.figure(figsize=(12, 6))
    
    # 主图：IPR vs 准能，颜色表示lam_h，大小表示IPR值
    scatter = plt.scatter(
        all_quasienergies, all_iprs,
        c=params_array[:, 0],  # 用lam_h编码颜色
        s=all_iprs * 50,       # 用IPR值编码大小
        cmap='viridis', 
        alpha=0.6,
        edgecolors='black', 
        linewidth=0.5
    )
    plt.colorbar(scatter, label='λ_h')
    plt.xlabel('准能 (Quasienergy)')
    plt.ylabel('逆参与率 (IPR)')
    plt.title('IPR随准能的变化关系')
    plt.grid(True, alpha=0.3)
    
    # 添加IPR分布的直方图
    plt.figure(figsize=(12, 5))
    plt.subplot(121)
    plt.hist(all_iprs, bins=30, color='skyblue', edgecolor='black')
    plt.xlabel('IPR值')
    plt.ylabel('出现频率')
    plt.title('IPR分布直方图')
    plt.grid(True, alpha=0.3)
    
    # 添加准能分布的直方图
    plt.subplot(122)
    plt.hist(all_quasienergies, bins=30, color='lightgreen', edgecolor='black')
    plt.xlabel('准能值')
    plt.ylabel('出现频率')
    plt.title('准能分布直方图')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 绘制参数空间中的IPR最大值分布
    max_ipr = np.zeros((n_points, n_points))
    for i, lam_h in enumerate(lam_h_vals):
        for j, lam_J in enumerate(lam_J_vals):
            idx = i * n_points + j
            start = idx * (2**L)  # 假设系统大小是8个格点
            end = start + (2**L)
            max_ipr[i, j] = np.max(all_iprs[start:end])
    
    plt.figure(figsize=(8, 6))
    im = plt.imshow(
        max_ipr, 
        extent=[0, np.pi, 0, np.pi],
        origin='lower',
        cmap='plasma',
        norm=LogNorm()
    )
    plt.colorbar(im, label='最大IPR值')
    plt.xlabel('λ_J')
    plt.ylabel('λ_h')
    plt.title('参数空间中最大IPR的分布')
    plt.show()
    
    return all_quasienergies, all_iprs, params_array

# 运行测试函数
if __name__ == "__main__":
    # 注意：对于8个格点系统(2^8=256维)，15x15的网格会有3375个点，计算量较大
    # 可先使用较小的n_points进行测试
    quasi, ipr, params = test_phasecrate_scan(n_points=64)'''
