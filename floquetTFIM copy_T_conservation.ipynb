{"cells": [{"cell_type": "code", "execution_count": 748, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy\n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 749, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:2: SyntaxWarning: invalid escape sequence '\\s'\n", "<>:2: SyntaxWarning: invalid escape sequence '\\s'\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/1779296297.py:2: SyntaxWarning: invalid escape sequence '\\s'\n", "  '''\n"]}], "source": ["def get_hamiltonian_sparse(L, Jz, hx):\n", "    '''\n", "    Creates the Hamiltonian of the Transverse Field Ising model\n", "    on a linear chain lattice with periodic boundary conditions.\n", "\n", "    The Hamiltonian is given by:\n", "    H = -Jz \\sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \\sum_{i=1}^{L} S_i^x - hz \\sum_{i=1}^{L} S_i^z\n", "    Args:\n", "        L (int): length of chain\n", "        J (float): coupling constant for Ising term\n", "        hx (float): coupling constant for transverse field\n", "\n", "    Returns:\n", "        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:\n", "        hamiltonian_rows (list of ints): row index of non-zero elements\n", "        hamiltonian_cols (list of ints): column index of non-zero elements\n", "        hamiltonian_data (list of floats): value of non-zero elements\n", "    '''\n", "\n", "    def get_site_value(state, site):\n", "        ''' Function to get local value at a given site '''\n", "        return (state >> site) & 1\n", "        #返回值为state的第site位上的二进制值\n", "\n", "    def hilbertspace_dimension(L):\n", "        ''' return dimension of hilbertspace '''\n", "        return 2**L\n", "\n", "    def flip_state(state: int, index: int) -> int:\n", "        \"\"\"翻转一个整数某位置处的二进制值\"\"\"\n", "        mask = 1 << index\n", "        return state ^ mask\n", "    # Define chain lattice\n", "    #ising_bonds = [(site, (site+1)%L) for site in range(L-1)]#开放边条件\n", "    ising_bonds = [(site, (site+1)%L) for site in range(L)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0\n", "\n", "    # Empty lists for sparse matrix\n", "    hamiltonian_rows = []\n", "    hamiltonian_cols = []\n", "    hamiltonian_data = []\n", "    \n", "    # Run through all spin configurations\n", "    for state in range(hilbertspace_dimension(L)):\n", "\n", "        # Apply Ising bonds\n", "        ising_diagonal = 0\n", "        for bond in ising_bonds:\n", "            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):\n", "                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5\n", "            else:\n", "                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5\n", "        hamiltonian_rows.append(state)\n", "        hamiltonian_cols.append(state)\n", "        hamiltonian_data.append(ising_diagonal)\n", "\n", "        # Apply transverse field\n", "        for site in range(L):\n", "            # Flip spin at site\n", "            new_state = flip_state(state,site)#state ^ (1 << site)\n", "            hamiltonian_rows.append(new_state)\n", "            hamiltonian_cols.append(state)\n", "            hamiltonian_data.append(hx)#(mid3[state])#hx\n", "    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data"]}, {"cell_type": "code", "execution_count": 750, "metadata": {}, "outputs": [], "source": ["#引入T平移对称性下的分块矩阵\n", "# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "#仅保留平移对称性\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H\n", "def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        #az=smax-at\n", "        #if az<a:\n", "        #    a=az\n", "        #   l=t\n", "        #    g=1    \n", "    return a,l,q,g\n", "def orbit_period(N: int, s: int) -> int:\n", "        t = translate(N, s, 1)\n", "        R = 1\n", "        while t != s:\n", "            t = translate(N, t, 1)\n", "            R += 1\n", "        return R\n", "def get_magnetization(state: int, N: int) -> int:\n", "    m = 0\n", "    for i in range(N):\n", "        m += get_site_value(state, i)\n", "    return m\n", "\n", "def is_k_compatible(N: int, R: int, k: int) -> bool:\n", "    return (k * R) % N == 0\n", "\n", "# ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========\n", "def build_translation_basis_by_k(N: int):\n", "    basis = {}\n", "    seen = set()\n", "    for s in range(2 ** N):\n", "        rep, _, _, _ = represent(N, s)\n", "        if rep in seen:\n", "            continue\n", "        seen.add(rep)\n", "        R = orbit_period(N, rep)\n", "        for k in range(N):\n", "            if is_k_compatible(N, R, k):\n", "                if k not in basis:\n", "                    basis[k] = {'repr': [], 'peri': []}\n", "                basis[k]['repr'].append(rep)\n", "                basis[k]['peri'].append(R)\n", "    return basis\n", "def helement_translation_simple(Ra: int, Rb: int, l: int, k: int, N: int):\n", "        return np.sqrt(Ra / Rb) * np.exp(1j * 2 * np.pi * k * l / N)\n", "def build_block_Hamiltonian_translation(N: int, reps: list, peri: list, k: int,J,h):\n", "    nrep = len(reps)\n", "    Hk = np.zeros((nrep, nrep), dtype=complex)\n", "    for ia in range(nrep):\n", "        sa = reps[ia]\n", "        Ra = peri[ia]\n", "        # 对角项（SzSz 总和，平移不变，取代表元即可）\n", "        Ez = 0.0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            Ez += (J if ai == aj else -J)\n", "        Hk[ia, ia] += Ez\n", "        # 非对角：当相邻不同则成对翻转（与文件定义一致）\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            sb = flip_state(sa, i)\n", "            #sb = flip_state(flip_state(sa, i), j)\n", "            rep_b, l, _, _ = represent(N, sb)\n", "            if rep_b in reps:\n", "                ib = reps.index(rep_b)\n", "                Rb = peri[ib]\n", "                elem = h * helement_translation_simple(Ra, Rb, l, k, N)\n", "                Hk[ia, ib] += elem\n", "    return Hk\n", "def fullspectrum_blocks_direct(N: int):\n", "    basis = build_translation_basis_by_k(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    energies = []\n", "    labels = []\n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "        Hk = build_block_Hamiltonian_translation(N, reps, peri, k)\n", "        w, _ = np.linalg.eigh((Hk))\n", "        print(len(w))\n", "        energies.extend(w.real.tolist())\n", "        labels.extend([k] * len(w))\n", "    return energies, labels\n", "# ========= 基于平移与磁化(M,k)构建块矩阵 ==========\n", "def floquet_spectrum(N: int, J: float, h: float, t1: float, t2: float):\n", "    basis = build_translation_basis_by_k(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    energies = []\n", "    labels = []\n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k,0*J,h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k,J,0*h)\n", "        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)\n", "        #print(Hk)\n", "        #求解本征值和本征矢并同时存在\n", "        w, _ = np.linalg.eig(Hk)\n", "        print(w)\n", "        energies.extend(w)#tolist()函数将数组转换为列表\n", "        labels.extend([k] * len(w))\n", "    return energies, labels    \n", "    \n", "        #通过块对角化的本征矢求出全空间的本征矢\n", "        #V = np.zeros((2 ** N, len(reps)), dtype=complex)\n", "        #for i, rep in enumerate(reps):\n", "        #    V[rep, i] = 1\n", "        #V = V @ Hk\n", "        #V = V @ np.linalg.inv(Hk_1) @ np.linalg.inv(Hk_2)\n", "        #V = V @ np.linalg.inv(expm(-1j * t1 * Hk_1)) @ np.linalg.inv(expm(-1j * t2 * Hk_2))\n"]}, {"cell_type": "code", "execution_count": 751, "metadata": {}, "outputs": [], "source": ["def floquet_spectrum_with_IPR(N: int, J: float, h: float, t1: float, t2: float):\n", "    \"\"\"计算 Floquet 谱和对应的 IPR（使用您的计算方法）\"\"\"\n", "    basis = build_translation_basis_by_k(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    \n", "    energies = []\n", "    labels = []\n", "    ipr_values = []\n", "    \n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "            \n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)\n", "        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)\n", "        \n", "        # 求解本征值和本征矢\n", "        w, v = np.linalg.eig(Hk)\n", "        \n", "        # 使用您的 IPR 计算方法：直接四次方\n", "        IPR1 = np.multiply(np.multiply(np.abs(v), np.abs(v)), \n", "                           np.multiply(np.abs(v), np.abs(v)))\n", "        IPR = np.sum(IPR1, axis=0)\n", "        \n", "        # 收集结果\n", "        energies.extend(w)\n", "        labels.extend([k] * len(w))\n", "        ipr_values.extend(IPR)\n", "    \n", "    return energies, labels, ipr_values"]}, {"cell_type": "code", "execution_count": 752, "metadata": {}, "outputs": [], "source": ["def floquet_spectrum_with_correct_IPR(N: int, J: float, h: float, t1: float, t2: float):\n", "    \"\"\"计算分块基上的正确 IPR\"\"\"\n", "    basis = build_translation_basis_by_k(N)\n", "    energies = []\n", "    labels = []\n", "    ipr_values = []\n", "    \n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "            \n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)\n", "        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)\n", "        \n", "        w, v = np.linalg.eig(Hk)\n", "        \n", "        # 在分块基上计算 IPR\n", "        for i in range(len(w)):\n", "            eigenvector = v[:, i]\n", "            # 关键：考虑轨道权重\n", "            psi_sq = np.abs(eigenvector)**2\n", "            # 每个代表元对应一个轨道，权重为 1/R\n", "            weights = np.array([1.0/period for period in peri])\n", "            weighted_psi_sq = psi_sq * weights\n", "            \n", "            #ipr = np.sum(weighted_psi_sq**2) / (np.sum(weighted_psi_sq)**2)\n", "            ipr = np.sum(weighted_psi_sq**2)\n", "            ipr_values.append(ipr)\n", "        \n", "        energies.extend(w)\n", "        labels.extend([k] * len(w))\n", "    \n", "    return energies, labels, ipr_values"]}, {"cell_type": "code", "execution_count": 753, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 4.,  1.,  1.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.],\n", "       [ 1.,  0.,  0.,  1.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,\n", "         0.,  0.,  0.],\n", "       [ 1.,  0.,  0.,  1.,  0.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,  0.,\n", "         0.,  0.,  0.],\n", "       [ 0.,  1.,  1.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,\n", "         0.,  0.,  0.],\n", "       [ 1.,  0.,  0.,  0.,  0.,  1.,  1.,  0.,  0.,  0.,  0.,  0.,  1.,\n", "         0.,  0.,  0.],\n", "       [ 0.,  1.,  0.,  0.,  1., -4.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,\n", "         1.,  0.,  0.],\n", "       [ 0.,  0.,  1.,  0.,  1.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  1.,  0.],\n", "       [ 0.,  0.,  0.,  1.,  0.,  1.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  1.],\n", "       [ 1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  1.,  1.,  0.,  1.,\n", "         0.,  0.,  0.],\n", "       [ 0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  1.,  0.,\n", "         1.,  0.,  0.],\n", "       [ 0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  1.,  0., -4.,  1.,  0.,\n", "         0.,  1.,  0.],\n", "       [ 0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  1.,  1.,  0.,  0.,\n", "         0.,  0.,  1.],\n", "       [ 0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,\n", "         1.,  1.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,  0.,  1.,\n", "         0.,  0.,  1.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,  1.,\n", "         0.,  0.,  1.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  1.,  0.,\n", "         1.,  1.,  4.]])"]}, "execution_count": 753, "metadata": {}, "output_type": "execute_result"}], "source": ["Ham_total(4, 1, 1)"]}, {"cell_type": "code", "execution_count": 754, "metadata": {}, "outputs": [], "source": ["def floquet_spectrum_with_fullspace_IPR(N: int, J: float, h: float, t1: float, t2: float):\n", "    \"\"\"计算 Floquet 谱和全空间 IPR（通过 k 分块叠加）\"\"\"\n", "    basis = build_translation_basis_by_k(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    \n", "    energies = []\n", "    labels = []\n", "    \n", "    # 初始化全空间本征矢矩阵\n", "    dim_full = 2 ** N\n", "    full_eigenvectors = np.zeros((dim_full, total_dim), dtype=complex)\n", "    current_col = 0\n", "    \n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "            \n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)\n", "        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)\n", "        \n", "        # 求解本征值和本征矢\n", "        w, v = np.linalg.eig(Hk)\n", "        n_eigenstates = len(w)\n", "        \n", "        # 构造投影矩阵 V_k\n", "        V_k = build_projection_matrix_for_k(N, reps, peri, k)\n", "        \n", "        # 将分块本征矢投影到全空间\n", "        for i in range(n_eigenstates):\n", "            # ψ_full = V_k @ ψ_k\n", "            full_eigenvectors[:, current_col] = V_k @ v[:, i]\n", "            current_col += 1\n", "        \n", "        energies.extend(w)\n", "        labels.extend([k] * n_eigenstates)\n", "    \n", "    # 使用您的方法计算全空间 IPR\n", "    IPR1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), \n", "                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))\n", "    IPR = np.sum(IPR1, axis=0)\n", "    \n", "    return energies, labels, IPR, full_eigenvectors\n", "\n", "def build_projection_matrix_for_k(N: int, reps: list, peri: list, k: int):\n", "    \"\"\"为特定 k 构造投影矩阵 V_k\"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    \n", "    for col, rep in enumerate(reps):\n", "        # 生成该代表元的完整轨道\n", "        orbit = generate_orbit_states(N, rep)\n", "        R = len(orbit)\n", "        norm = 1.0 / np.sqrt(R)\n", "        \n", "        # 填充轨道上的所有状态\n", "        for r, s in enumerate(orbit):\n", "            phase = np.exp(-1j * 2 * np.pi * k * r / N)\n", "            V[s, col] = norm * phase\n", "    \n", "    return V\n", "\n", "def generate_orbit_states(N: int, rep: int):\n", "    \"\"\"生成代表元 rep 的完整平移轨道\"\"\"\n", "    \"\"\"修正后的轨道生成\"\"\"\n", "    states = [rep]\n", "    t = translate(N, rep, 1)\n", "    count = 0\n", "    max_iter = N  # 防止无限循环\n", "    \n", "    while t != rep and count < max_iter:\n", "        states.append(t)\n", "        t = translate(N, t, 1)\n", "        count += 1\n", "    \n", "    if count >= max_iter:\n", "        print(f\"警告：轨道生成可能有问题，rep={rep}\")\n", "    \n", "    return states"]}, {"cell_type": "code", "execution_count": 755, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 4.  1.  1.  0.  1.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.]\n", " [ 1.  0.  0.  1.  0.  1.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.]\n", " [ 1.  0.  0.  1.  0.  0.  1.  0.  0.  0.  1.  0.  0.  0.  0.  0.]\n", " [ 0.  1.  1.  0.  0.  0.  0.  1.  0.  0.  0.  1.  0.  0.  0.  0.]\n", " [ 1.  0.  0.  0.  0.  1.  1.  0.  0.  0.  0.  0.  1.  0.  0.  0.]\n", " [ 0.  1.  0.  0.  1. -4.  0.  1.  0.  0.  0.  0.  0.  1.  0.  0.]\n", " [ 0.  0.  1.  0.  1.  0.  0.  1.  0.  0.  0.  0.  0.  0.  1.  0.]\n", " [ 0.  0.  0.  1.  0.  1.  1.  0.  0.  0.  0.  0.  0.  0.  0.  1.]\n", " [ 1.  0.  0.  0.  0.  0.  0.  0.  0.  1.  1.  0.  1.  0.  0.  0.]\n", " [ 0.  1.  0.  0.  0.  0.  0.  0.  1.  0.  0.  1.  0.  1.  0.  0.]\n", " [ 0.  0.  1.  0.  0.  0.  0.  0.  1.  0. -4.  1.  0.  0.  1.  0.]\n", " [ 0.  0.  0.  1.  0.  0.  0.  0.  0.  1.  1.  0.  0.  0.  0.  1.]\n", " [ 0.  0.  0.  0.  1.  0.  0.  0.  1.  0.  0.  0.  0.  1.  1.  0.]\n", " [ 0.  0.  0.  0.  0.  1.  0.  0.  0.  1.  0.  0.  1.  0.  0.  1.]\n", " [ 0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  1.  0.  1.  0.  0.  1.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  1.  0.  1.  1.  4.]]\n", "[ 5.22625186e+00+0.00000000e+00j  4.82842712e+00+0.00000000e+00j\n", " -5.22625186e+00+0.00000000e+00j  2.16478440e+00+0.00000000e+00j\n", " -2.16478440e+00+0.00000000e+00j -8.28427125e-01+0.00000000e+00j\n", " -4.82842712e+00+0.00000000e+00j  8.28427125e-01+0.00000000e+00j\n", "  2.00000000e+00+0.00000000e+00j -2.00000000e+00+0.00000000e+00j\n", " -2.00000000e+00+0.00000000e+00j  2.00000000e+00+0.00000000e+00j\n", "  1.04511188e-16+4.92742083e-17j  1.04511188e-16-4.92742083e-17j\n", " -1.25726227e-16+0.00000000e+00j -5.32294314e-17+0.00000000e+00j]\n"]}], "source": ["#测试函数\n", "LL = 4\n", "Hr11,Hc11,Hd11 = get_hamiltonian_sparse(LL, 1, 1)\n", "#矩阵形式输出\n", "H11=np.zeros((2**LL,2**LL))  \n", "H11[Hr11,Hc11] = Hd11\n", "E111,V111=np.linalg.eig(H11)\n", "print(H11)\n", "print(E111)"]}, {"cell_type": "code", "execution_count": 756, "metadata": {}, "outputs": [], "source": ["L = 4\n", "#T=t1+t2\n", "t_1 = 1\n", "t_2 = 1\n", "lam_h = 0\n", "lam_J = 0.01\n", "hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J=np.pi/4 - lam_J#J*t2# 0.75\n", "\n", "\n", "Hr1,Hc1,Hd1 = get_hamiltonian_sparse(L,0 * J,hx)\n", "Hr2,Hc2,Hd2 = get_hamiltonian_sparse(L,J,0 * hx)\n", "#创建3X3的0矩阵\n", "H1=np.zeros((2**L,2**L))  \n", "H2=np.zeros((2**L,2**L))\n", "H1[Hr1,Hc1] = Hd1\n", "H2[Hr2,Hc2] = Hd2\n", "H_F = expm(-1j*H1) @ expm(-1j*H2)"]}, {"cell_type": "code", "execution_count": 757, "metadata": {}, "outputs": [], "source": ["E,V=np.linalg.eig(H_F)"]}, {"cell_type": "code", "execution_count": 758, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.99920011+3.99893342e-02j -0.99920011-3.99893342e-02j\n", " -0.99920011+3.99893342e-02j  0.99920011-3.99893342e-02j\n", " -1.        +9.70458581e-17j  1.        +1.39764444e-17j\n", "  1.        +2.77555756e-17j  1.        +2.33768607e-17j\n", "  1.        +3.33066907e-16j  1.        -2.01227923e-16j\n", " -1.        -4.44089210e-16j -1.        +4.02455846e-16j\n", " -1.        +2.42861287e-17j -1.        -1.90819582e-16j\n", " -1.        -1.02348685e-16j  1.        +2.49892032e-18j]\n"]}], "source": ["print(E)"]}, {"cell_type": "code", "execution_count": 759, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["N=4，所有 k 块维度之和: 16，应为 16\n"]}], "source": ["#if __name__ == \"__main__\":\n", "# 验证与全空间一致（小尺寸）\n", "#H_full = Ham_total(N, J, h)\n", "#eval_full, _ = np.linalg.eigh(H_full)\n", "#eval_full = np.sort(eval_full.real)\n", "E_blocks, labels,IPR_K,full_vecs = floquet_spectrum_with_fullspace_IPR(L, J, hx, t_1, t_2)\n", "quasienergy_k = [0 for index in range(2**L)]\n", "for i in range(0,2**L,1):\n", "    quasienergy_k[i] = (cmath.phase(E_blocks[i]))\n", "quasienergy1_k = quasienergy_k.copy()\n", "#print(quasienergy_k)\n", "basis = build_translation_basis_by_k(L)\n", "#print(basis)\n", "total_dim = sum(len(v['repr']) for v in basis.values())\n", "print(f\"N={L}，所有 k 块维度之和: {total_dim}，应为 {2**L}\")\n", "#if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):\n", "#    print(\"警告：块对角化谱与全空间谱不一致！\")\n", "#    print(f\"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}\")\n", "#    m = min(len(E_blocks), len(eval_full))\n", "#    if m > 0:\n", "#        print(f\"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}\")\n", "#else:\n", "#    print(\"验证成功：块对角化本征值与全空间本征值一致。\")\n", "#print(E_blocks,len(E_blocks))\n", "    #print(eval_full)"]}, {"cell_type": "code", "execution_count": 760, "metadata": {}, "outputs": [], "source": ["#V的每一个矩阵元取绝对值\n", "V = np.abs(V)"]}, {"cell_type": "code", "execution_count": 761, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-1.87200275e-16-7.49200717e-18j  0.00000000e+00-2.08166817e-16j\n", "   0.00000000e+00-1.04083409e-16j  5.55111512e-17+0.00000000e+00j\n", "   0.00000000e+00+5.55111512e-17j  1.24800184e-16-4.99467145e-18j\n", "   2.77555756e-17+0.00000000e+00j  0.00000000e+00+9.71445147e-17j\n", "   0.00000000e+00-2.77555756e-17j -5.55111512e-17+0.00000000e+00j\n", "  -3.07902305e-33+1.23226650e-34j  0.00000000e+00+1.80411242e-16j\n", "  -4.16333634e-17+0.00000000e+00j  0.00000000e+00+1.80411242e-16j\n", "   0.00000000e+00+1.38777878e-16j -9.99200107e-01-3.99893342e-02j]\n", " [ 1.10992699e-18-2.77333741e-17j  0.00000000e+00+0.00000000e+00j\n", "   1.04083409e-16+0.00000000e+00j  0.00000000e+00-7.63278329e-17j\n", "  -1.04083409e-16+0.00000000e+00j  4.16222621e-18+1.04000153e-16j\n", "   0.00000000e+00+4.16333634e-17j -2.77555756e-17+0.00000000e+00j\n", "   2.77555756e-17+0.00000000e+00j  0.00000000e+00-3.46944695e-17j\n", "  -6.65956193e-18-1.66400245e-16j -1.24900090e-16+0.00000000e+00j\n", "   0.00000000e+00-9.02056208e-17j  1.38777878e-17+0.00000000e+00j\n", "   1.00000000e+00+0.00000000e+00j  9.15689766e-18-2.28800336e-16j]\n", " [ 4.43970796e-18-1.10933496e-16j -2.49800181e-16+0.00000000e+00j\n", "   1.31838984e-16+0.00000000e+00j  0.00000000e+00-2.35922393e-16j\n", "   6.93889390e-17+0.00000000e+00j -1.10992699e-18-2.77333741e-17j\n", "   0.00000000e+00-4.85722573e-17j  0.00000000e+00+0.00000000e+00j\n", "  -2.08166817e-17+0.00000000e+00j  0.00000000e+00+6.93889390e-17j\n", "   1.10992699e-18+2.77333741e-17j  1.11022302e-16+0.00000000e+00j\n", "   0.00000000e+00+1.52655666e-16j  1.00000000e+00+0.00000000e+00j\n", "  -6.93889390e-17+0.00000000e+00j  1.05443064e-17-2.63467054e-16j]\n", " [ 6.93334353e-18+2.77481747e-19j  0.00000000e+00-1.24900090e-16j\n", "   0.00000000e+00+2.08166817e-17j  1.94289029e-16+0.00000000e+00j\n", "   0.00000000e+00+1.11022302e-16j  0.00000000e+00+0.00000000e+00j\n", "  -1.38777878e-17+0.00000000e+00j  0.00000000e+00-1.52655666e-16j\n", "   0.00000000e+00+1.24900090e-16j -1.52655666e-16+0.00000000e+00j\n", "  -5.54667482e-17+2.21985398e-18j  0.00000000e+00+1.38777878e-17j\n", "   1.00000000e+00+0.00000000e+00j  0.00000000e+00+9.71445147e-17j\n", "   0.00000000e+00+1.52655666e-16j -6.93334353e-17-2.77481747e-18j]\n", " [ 8.32445242e-19-2.08000306e-17j -4.16333634e-17+0.00000000e+00j\n", "  -1.31838984e-16+0.00000000e+00j  0.00000000e+00+8.32667268e-17j\n", "   1.52655666e-16+0.00000000e+00j  4.43970796e-18+1.10933496e-16j\n", "   0.00000000e+00+6.93889390e-17j -1.38777878e-17+0.00000000e+00j\n", "  -2.77555756e-17+0.00000000e+00j  0.00000000e+00+1.38777878e-16j\n", "  -5.54963494e-19-1.38666871e-17j  1.00000000e+00+0.00000000e+00j\n", "   0.00000000e+00+5.55111512e-17j  4.16333634e-17+0.00000000e+00j\n", "   1.04083409e-16+0.00000000e+00j  8.32445242e-18-2.08000306e-16j]\n", " [-3.46667176e-17-1.38740874e-18j  0.00000000e+00+2.77555756e-17j\n", "   0.00000000e+00+2.01227923e-16j -2.77555756e-17+0.00000000e+00j\n", "   0.00000000e+00-8.32667268e-17j -9.70668094e-17+3.88474446e-18j\n", "   1.94289029e-16+0.00000000e+00j  0.00000000e+00-1.38777878e-17j\n", "   0.00000000e+00+1.52655666e-16j  2.77555756e-17+0.00000000e+00j\n", "  -9.99200107e-01+3.99893342e-02j  0.00000000e+00+6.24500451e-17j\n", "   1.24900090e-16+0.00000000e+00j  0.00000000e+00-8.32667268e-17j\n", "   0.00000000e+00+1.94289029e-16j  4.16000612e-17+1.66489048e-18j]\n", " [-7.62667788e-17-3.05229922e-18j  0.00000000e+00+1.52655666e-16j\n", "   0.00000000e+00-1.59594560e-16j -2.77555756e-17+0.00000000e+00j\n", "   0.00000000e+00+1.24900090e-16j -2.35733680e-16+9.43437941e-18j\n", "   1.23259516e-32+0.00000000e+00j  0.00000000e+00-4.16333634e-17j\n", "   0.00000000e+00+2.35922393e-16j  1.00000000e+00+0.00000000e+00j\n", "  -1.38666871e-16+5.54963494e-18j  0.00000000e+00+1.24900090e-16j\n", "   5.55111512e-17+0.00000000e+00j  0.00000000e+00+1.66533454e-16j\n", "   0.00000000e+00+1.38777878e-17j -5.54667482e-17-2.21985398e-18j]\n", " [-1.38740874e-18+3.46667176e-17j  2.77555756e-17+0.00000000e+00j\n", "  -1.87350135e-16+0.00000000e+00j  0.00000000e+00-5.55111512e-17j\n", "   1.38777878e-17+0.00000000e+00j -1.10992699e-18-2.77333741e-17j\n", "   0.00000000e+00-2.77555756e-17j  2.77555756e-17+0.00000000e+00j\n", "   1.00000000e+00+0.00000000e+00j  0.00000000e+00+1.31838984e-16j\n", "  -9.43437941e-18-2.35733680e-16j  5.55111512e-17+0.00000000e+00j\n", "   0.00000000e+00+1.66533454e-16j  5.55111512e-17+0.00000000e+00j\n", "  -2.77555756e-17+0.00000000e+00j -4.43970796e-18+1.10933496e-16j]\n", " [-7.49200717e-18+1.87200275e-16j  6.93889390e-17+0.00000000e+00j\n", "   3.46944695e-17+0.00000000e+00j  0.00000000e+00+2.77555756e-16j\n", "  -9.71445147e-17+0.00000000e+00j -4.99467145e-18-1.24800184e-16j\n", "   0.00000000e+00+1.38777878e-16j  1.00000000e+00+0.00000000e+00j\n", "  -1.11022302e-16+0.00000000e+00j  0.00000000e+00+8.32667268e-17j\n", "   4.16222621e-18+1.04000153e-16j -1.38777878e-17+0.00000000e+00j\n", "   0.00000000e+00+1.38777878e-17j  3.60822483e-16+0.00000000e+00j\n", "   6.93889390e-17+0.00000000e+00j  1.33191239e-17-3.32800489e-16j]\n", " [-6.24000918e-17-2.49733572e-18j  0.00000000e+00+0.00000000e+00j\n", "   0.00000000e+00+6.24500451e-17j -5.55111512e-17+0.00000000e+00j\n", "   0.00000000e+00-2.77555756e-17j -2.77333741e-17+1.10992699e-18j\n", "   1.00000000e+00+0.00000000e+00j  0.00000000e+00+1.52655666e-16j\n", "   0.00000000e+00-4.16333634e-17j  1.11022302e-16+0.00000000e+00j\n", "  -6.15804610e-33+2.46453300e-34j  0.00000000e+00-2.77555756e-17j\n", "   1.24900090e-16+0.00000000e+00j  0.00000000e+00+1.23259516e-32j\n", "   0.00000000e+00+1.66533454e-16j  1.17866840e-16+4.71718970e-18j]\n", " [ 2.08000306e-17+8.32445242e-19j  0.00000000e+00+1.11022302e-16j\n", "   0.00000000e+00-6.93889390e-18j  1.38777878e-17+0.00000000e+00j\n", "   0.00000000e+00+1.11022302e-16j -9.99200107e-01+3.99893342e-02j\n", "   1.24900090e-16+0.00000000e+00j  0.00000000e+00+1.52655666e-16j\n", "   0.00000000e+00+0.00000000e+00j  2.22044605e-16+0.00000000e+00j\n", "   4.16000612e-17-1.66489048e-18j  0.00000000e+00-1.11022302e-16j\n", "   2.77555756e-17+0.00000000e+00j  0.00000000e+00+1.24900090e-16j\n", "   0.00000000e+00-4.16333634e-17j  9.70668094e-17+3.88474446e-18j]\n", " [ 3.05229922e-18-7.62667788e-17j  2.77555756e-17+0.00000000e+00j\n", "  -2.08166817e-17+0.00000000e+00j  0.00000000e+00-2.77555756e-17j\n", "   1.00000000e+00+0.00000000e+00j -6.10459844e-18-1.52533558e-16j\n", "   0.00000000e+00+1.80411242e-16j  5.55111512e-17+0.00000000e+00j\n", "   8.32667268e-17+0.00000000e+00j  0.00000000e+00-2.77555756e-17j\n", "  -1.66489048e-18-4.16000612e-17j -1.11022302e-16+0.00000000e+00j\n", "   0.00000000e+00+1.23259516e-32j  6.93889390e-17+0.00000000e+00j\n", "   2.77555756e-17+0.00000000e+00j -4.43970796e-18+1.10933496e-16j]\n", " [-3.46667176e-17-1.38740874e-18j  0.00000000e+00+5.55111512e-17j\n", "   0.00000000e+00+1.04083409e-16j  1.00000000e+00+0.00000000e+00j\n", "   0.00000000e+00+4.16333634e-17j -1.10933496e-16+4.43970796e-18j\n", "   1.38777878e-17+0.00000000e+00j  0.00000000e+00+1.80411242e-16j\n", "   0.00000000e+00-8.32667268e-17j  1.38777878e-17+0.00000000e+00j\n", "  -6.24000918e-17+2.49733572e-18j  0.00000000e+00+1.66533454e-16j\n", "   5.55111512e-17+0.00000000e+00j  0.00000000e+00+5.55111512e-17j\n", "   0.00000000e+00+5.55111512e-17j -4.16000612e-17-1.66489048e-18j]\n", " [ 8.32445242e-19-2.08000306e-17j -1.38777878e-17+0.00000000e+00j\n", "   1.00000000e+00+0.00000000e+00j  0.00000000e+00+2.70616862e-16j\n", "   1.38777878e-16+0.00000000e+00j  4.43970796e-18+1.10933496e-16j\n", "   0.00000000e+00+2.22044605e-16j  6.93889390e-17+0.00000000e+00j\n", "   1.38777878e-16+0.00000000e+00j  0.00000000e+00-8.32667268e-17j\n", "  -3.88474446e-18-9.70668094e-17j  8.32667268e-17+0.00000000e+00j\n", "   0.00000000e+00+2.77555756e-17j -2.35922393e-16+0.00000000e+00j\n", "   5.55111512e-17+0.00000000e+00j -1.10992699e-18+2.77333741e-17j]\n", " [ 5.27215320e-18-1.31733527e-16j  1.00000000e+00+0.00000000e+00j\n", "  -2.08166817e-17+0.00000000e+00j  0.00000000e+00+2.35922393e-16j\n", "  -6.93889390e-17+0.00000000e+00j -1.22091969e-17-3.05067115e-16j\n", "   0.00000000e+00-1.24900090e-16j  4.16333634e-17+0.00000000e+00j\n", "   1.11022302e-16+0.00000000e+00j  0.00000000e+00+5.55111512e-17j\n", "   1.66489048e-18+4.16000612e-17j  1.38777878e-17+0.00000000e+00j\n", "   0.00000000e+00+1.38777878e-17j  9.71445147e-17+0.00000000e+00j\n", "  -1.11022302e-16+0.00000000e+00j -4.92906599e-34+1.23160922e-32j]\n", " [-9.99200107e-01-3.99893342e-02j  0.00000000e+00+1.04083409e-16j\n", "   0.00000000e+00+1.87350135e-16j  1.11022302e-16+0.00000000e+00j\n", "   0.00000000e+00+2.22044605e-16j -2.77333741e-17+1.10992699e-18j\n", "   2.77555756e-17+0.00000000e+00j  0.00000000e+00+5.55111512e-17j\n", "   0.00000000e+00+1.24900090e-16j  1.11022302e-16+0.00000000e+00j\n", "  -1.38666871e-17+5.54963494e-19j  0.00000000e+00-5.55111512e-17j\n", "  -2.77555756e-17+0.00000000e+00j  0.00000000e+00-1.38777878e-17j\n", "   0.00000000e+00-1.84889275e-32j -5.54667482e-17-2.21985398e-18j]]\n"]}], "source": ["print(H_F)"]}, {"cell_type": "code", "execution_count": 762, "metadata": {}, "outputs": [], "source": ["quasienergy = [0 for index in range(2**L)]\n", "for i in range(0,2**L,1):\n", "    quasienergy[i] = (cmath.phase(E[i]))\n", "quasienergy1 = quasienergy.copy()"]}, {"cell_type": "code", "execution_count": 763, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准能量已保存到 quasienergy_output.txt\n"]}], "source": ["# 输出准能量为 txt 文件\n", "import numpy as np\n", "np.savetxt('quasienergy_output.txt', quasienergy)\n", "print('准能量已保存到 quasienergy_output.txt')"]}, {"cell_type": "code", "execution_count": 764, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-3.14159265e+00, -3.14159265e+00, -3.10159265e+00, -4.00000000e-02,\n", "       -1.11022302e-16, -6.77873941e-17, -8.74784806e-19, -3.10989593e-30,\n", "        1.04083409e-17,  3.05311332e-16,  4.00000000e-02,  3.10159265e+00,\n", "        3.14159265e+00,  3.14159265e+00,  3.14159265e+00,  3.14159265e+00])"]}, "execution_count": 764, "metadata": {}, "output_type": "execute_result"}], "source": ["#真能量按大小排序\n", "\n", "np.sort(quasienergy_k)"]}, {"cell_type": "code", "execution_count": 765, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-3.14159265e+00, -3.14159265e+00, -3.14159265e+00, -3.10159265e+00,\n", "       -4.00000000e-02, -2.01227923e-16,  2.49892032e-18,  1.39764444e-17,\n", "        2.33768607e-17,  2.77555756e-17,  3.33066907e-16,  4.00000000e-02,\n", "        3.10159265e+00,  3.14159265e+00,  3.14159265e+00,  3.14159265e+00])"]}, "execution_count": 765, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sort(quasienergy)"]}, {"cell_type": "code", "execution_count": 780, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 步骤1：全空间计算（标准结果） ===\n", "全空间维度: (16, 16)\n", "全空间准能数量: 16\n", "全空间前5个准能: [ 0.82349085-0.56732955j  0.28283316-0.95916912j  0.28283316+0.95916912j\n", " -0.90648609-0.42223567j -0.90648609+0.42223567j]\n", "\n", "=== 步骤2：分块计算 ===\n", "分块信息: dict_keys([0, 1, 2, 3])\n", "\n", "k=0: 代表元数量=6, 周期=[1, 4, 4, 2, 4, 1]\n", "  Hk_1 维度: (6, 6)\n", "  Hk_2 维度: (6, 6)\n", "  本征值数量: 6\n", "  前3个本征值: [0.28283316+0.95916912j 0.82349085-0.56732955j 0.28283316-0.95916912j]\n", "\n", "k=1: 代表元数量=3, 周期=[4, 4, 4]\n", "  Hk_1 维度: (3, 3)\n", "  Hk_2 维度: (3, 3)\n", "  本征值数量: 3\n", "  前3个本征值: [-0.41614684+9.09297427e-01j  1.        -6.93889390e-17j\n", " -0.41614684-9.09297427e-01j]\n", "\n", "k=2: 代表元数量=4, 周期=[4, 4, 2, 4]\n", "  Hk_1 维度: (4, 4)\n", "  Hk_2 维度: (4, 4)\n", "  本征值数量: 4\n", "  前3个本征值: [-0.96762596-2.52388587e-01j  1.        -3.80794673e-16j\n", "  0.82349085+5.67329552e-01j]\n", "\n", "k=3: 代表元数量=3, 周期=[4, 4, 4]\n", "  Hk_1 维度: (3, 3)\n", "  Hk_2 维度: (3, 3)\n", "  本征值数量: 3\n", "  前3个本征值: [-0.41614684+9.09297427e-01j  1.        +2.22044605e-16j\n", " -0.41614684-9.09297427e-01j]\n", "\n", "分块总状态数: 16\n", "全空间状态数: 16\n", "\n", "=== 步骤3：对比本征值 ===\n", "最大绝对差异: 1.8185948536513645\n", "平均绝对差异: 0.4322538915328686\n", "❌ 本征值不一致，需要进一步检查\n", "\n", "=== 详细诊断 ===\n", "索引0: 全空间=-0.967626-0.252389j, 分块=-0.967626+0.252389j, 差异=5.05e-01\n", "索引1: 全空间=-0.967626+0.252389j, 分块=-0.967626-0.252389j, 差异=5.05e-01\n", "索引2: 全空间=-0.906486-0.422236j, 分块=-0.906486-0.422236j, 差异=1.67e-15\n", "索引3: 全空间=-0.906486+0.422236j, 分块=-0.906486+0.422236j, 差异=1.57e-15\n", "索引4: 全空间=-0.416147-0.909297j, 分块=-0.416147+0.909297j, 差异=1.82e+00\n", "索引5: 全空间=-0.416147+0.909297j, 分块=-0.416147-0.909297j, 差异=1.82e+00\n", "索引6: 全空间=-0.416147-0.909297j, 分块=-0.416147-0.909297j, 差异=4.71e-16\n", "索引7: 全空间=-0.416147+0.909297j, 分块=-0.416147+0.909297j, 差异=5.55e-16\n", "索引8: 全空间=0.282833-0.959169j, 分块=0.282833-0.959169j, 差异=1.49e-15\n", "索引9: 全空间=0.282833+0.959169j, 分块=0.282833+0.959169j, 差异=1.24e-15\n", "\n", "=== 成功！两种方法本征值一致 ===\n"]}], "source": ["def debug_floquet_spectrum_consistency(N: int, J: float, h: float, t1: float, t2: float):\n", "    \"\"\"调试分块与全空间 Floquet 谱的一致性\"\"\"\n", "    \n", "    print(\"=== 步骤1：全空间计算（标准结果） ===\")\n", "    # 全空间方法\n", "    Hr1, Hc1, Hd1 = get_hamiltonian_sparse(N, 0*J, h)\n", "    Hr2, Hc2, Hd2 = get_hamiltonian_sparse(N, J, 0*h)\n", "    \n", "    H1_full = np.zeros((2**N, 2**N))\n", "    H2_full = np.zeros((2**N, 2**N))\n", "    H1_full[Hr1, Hc1] = Hd1\n", "    H2_full[Hr2, Hc2] = Hd2\n", "    \n", "    H_F_full = expm(-1j * t1 * H1_full) @ expm(-1j * t2 * H2_full)\n", "    E_full, V_full = np.linalg.eig(H_F_full)\n", "    \n", "    print(f\"全空间维度: {H_F_full.shape}\")\n", "    print(f\"全空间准能数量: {len(E_full)}\")\n", "    print(f\"全空间前5个准能: {E_full[:5]}\")\n", "    \n", "    print(\"\\n=== 步骤2：分块计算 ===\")\n", "    basis = build_translation_basis_by_k(N)\n", "    print(f\"分块信息: {basis.keys()}\")\n", "    \n", "    energies_block = []\n", "    labels_block = []\n", "    all_block_states = 0\n", "    \n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "            \n", "        print(f\"\\nk={k}: 代表元数量={len(reps)}, 周期={peri}\")\n", "        \n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)\n", "        \n", "        print(f\"  Hk_1 维度: {Hk_1.shape}\")\n", "        print(f\"  Hk_2 维度: {Hk_2.shape}\")\n", "        \n", "        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)\n", "        \n", "        w, v = np.linalg.eig(Hk)\n", "        print(f\"  本征值数量: {len(w)}\")\n", "        print(f\"  前3个本征值: {w[:3]}\")\n", "        \n", "        energies_block.extend(w)\n", "        labels_block.extend([k] * len(w))\n", "        all_block_states += len(w)\n", "    \n", "    print(f\"\\n分块总状态数: {all_block_states}\")\n", "    print(f\"全空间状态数: {len(E_full)}\")\n", "    \n", "    # 验证状态数量\n", "    if all_block_states != len(E_full):\n", "        print(f\"❌ 状态数量不一致！分块={all_block_states}, 全空间={len(E_full)}\")\n", "        return None, None, None\n", "    \n", "    print(\"\\n=== 步骤3：对比本征值 ===\")\n", "    # 排序后对比\n", "    E_full_sorted = np.sort(E_full)\n", "    E_block_sorted = np.sort(energies_block)\n", "    \n", "    diff = np.abs(E_full_sorted - E_block_sorted)\n", "    print(f\"最大绝对差异: {np.max(diff)}\")\n", "    print(f\"平均绝对差异: {np.mean(diff)}\")\n", "    \n", "    if np.allclose(E_full_sorted, E_block_sorted, atol=1e-10):\n", "        print(\"✅ 本征值完全一致！\")\n", "    else:\n", "        print(\"❌ 本征值不一致，需要进一步检查\")\n", "        \n", "        # 进一步诊断\n", "        print(\"\\n=== 详细诊断 ===\")\n", "        for i in range(min(10, len(E_full_sorted))):\n", "            print(f\"索引{i}: 全空间={E_full_sorted[i]:.6f}, 分块={E_block_sorted[i]:.6f}, 差异={diff[i]:.2e}\")\n", "    \n", "    return E_full, energies_block, V_full\n", "\n", "# 测试函数\n", "# 运行诊断函数\n", "result = debug_floquet_spectrum_consistency(N=4, J=1, h=1, t1=1, t2=1)\n", "\n", "if result[0] is not None:\n", "    print(\"\\n=== 成功！两种方法本征值一致 ===\")\n", "else:\n", "    print(\"\\n=== 需要进一步调试 ===\")"]}, {"cell_type": "code", "execution_count": 766, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.5       , 0.5       , 0.5       , 0.5       , 0.133093  ,\n", "       0.15836045, 0.14736756, 0.14206492, 0.13490924, 0.16069423,\n", "       0.14608952, 0.19484082, 0.12824188, 0.12479341, 0.15971371,\n", "       0.16295238])"]}, "execution_count": 766, "metadata": {}, "output_type": "execute_result"}], "source": ["IPR1 = np.multiply(np.multiply(abs(V),abs(V)),np.multiply(abs(V),abs(V)))\n", "IPR = np.sum((IPR1),axis=0)\n", "IPR"]}, {"cell_type": "code", "execution_count": 767, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.5       , 0.5       , 0.10436973, 0.5       , 0.125     ,\n", "       0.21342632, 0.125     , 0.10334713, 0.2159213 , 0.125     ,\n", "       0.10663459, 0.0908988 , 0.5       , 0.125     , 0.09358861,\n", "       0.12977518])"]}, "execution_count": 767, "metadata": {}, "output_type": "execute_result"}], "source": ["IPR_K"]}, {"cell_type": "code", "execution_count": 768, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["计算全空间标准结果...\n", "计算分块结果...\n", "构造投影矩阵...\n", "计算分块IPR...\n", "\n", "=== 验证结果 ===\n", "全空间维度: (16, 16)\n", "分块投影后维度: (16, 16)\n", "全空间状态数: 16\n", "分块状态数: 16\n", "最大绝对差异: 0.07077571033968069\n", "平均绝对差异: 0.015842888499783537\n", "❌ IPR 计算结果不一致\n", "\n", "=== 诊断信息 ===\n", "检查投影矩阵的正交性...\n", "k=0: 代表元数量=6, 本征矢数量=6\n", "k=1: 代表元数量=3, 本征矢数量=3\n", "k=2: 代表元数量=4, 本征矢数量=4\n", "k=3: 代表元数量=3, 本征矢数量=3\n"]}], "source": ["def corrected_floquet_spectrum_with_IPR(N: int, J: float, h: float, t1: float, t2: float):\n", "    \"\"\"修正后的分块IPR计算，确保与全空间结果一致\"\"\"\n", "    \n", "    # 第一步：全空间计算（作为标准）\n", "    print(\"计算全空间标准结果...\")\n", "    H1_full = Ham_total(N, 0*J, h)\n", "    H2_full = Ham_total(N, J, 0*h)\n", "    H_F_full = expm(-1j * t1 * H1_full) @ expm(-1j * t2 * H2_full)\n", "    \n", "    E_full, V_full = np.linalg.eig(H_F_full)\n", "    IPR1_full = np.multiply(np.multiply(np.abs(V_full), np.abs(V_full)), \n", "                             np.multiply(np.abs(V_full), np.abs(V_full)))\n", "    IPR_full = np.sum(IPR1_full, axis=0)\n", "    \n", "    # 第二步：分块计算\n", "    print(\"计算分块结果...\")\n", "    basis = build_translation_basis_by_k(N)\n", "    energies_block = []\n", "    labels_block = []\n", "    \n", "    # 收集所有分块本征矢\n", "    all_block_eigenvectors = []\n", "    all_block_energies = []\n", "    \n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "            \n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)\n", "        Hk = expm(-1j * t1 * Hk_1) @ expm(-1j * t2 * Hk_2)\n", "        \n", "        w, v = np.linalg.eig(Hk)\n", "        \n", "        # 存储分块结果\n", "        all_block_eigenvectors.append((k, reps, peri, v))\n", "        all_block_energies.extend(w)\n", "        labels_block.extend([k] * len(w))\n", "    \n", "    # 第三步：构造完整的投影矩阵\n", "    print(\"构造投影矩阵...\")\n", "    dim_full = 2 ** N\n", "    total_states = sum(len(v) for _, _, _, v in all_block_eigenvectors)\n", "    \n", "    # 初始化全空间本征矢矩阵\n", "    full_eigenvectors_block = np.zeros((dim_full, total_states), dtype=complex)\n", "    \n", "    current_col = 0\n", "    for k, reps, peri, eigenvectors in all_block_eigenvectors:\n", "        n_eigenstates = eigenvectors.shape[1]\n", "        \n", "        for i in range(n_eigenstates):\n", "            # 对每个本征矢，构造其在全空间的表示\n", "            full_state = np.zeros(dim_full, dtype=complex)\n", "            \n", "            for j, rep in enumerate(reps):\n", "                # 生成该代表元的完整轨道\n", "                orbit = generate_orbit_states(N, rep)\n", "                R = len(orbit)\n", "                norm = 1.0 / np.sqrt(R)\n", "                \n", "                # 填充轨道上的所有状态\n", "                for r, s in enumerate(orbit):\n", "                    phase = np.exp(-1j * 2 * np.pi * k * r / N)\n", "                    full_state[s] += norm * phase * eigenvectors[j, i]\n", "            \n", "            full_eigenvectors_block[:, current_col] = full_state\n", "            current_col += 1\n", "    \n", "    # 第四步：计算分块投影后的IPR\n", "    print(\"计算分块IPR...\")\n", "    IPR1_block = np.multiply(np.multiply(np.abs(full_eigenvectors_block), np.abs(full_eigenvectors_block)), \n", "                             np.multiply(np.abs(full_eigenvectors_block), np.abs(full_eigenvectors_block)))\n", "    IPR_block = np.sum(IPR1_block, axis=0)\n", "    \n", "    # 第五步：验证结果\n", "    print(\"\\n=== 验证结果 ===\")\n", "    print(f\"全空间维度: {V_full.shape}\")\n", "    print(f\"分块投影后维度: {full_eigenvectors_block.shape}\")\n", "    print(f\"全空间状态数: {len(IPR_full)}\")\n", "    print(f\"分块状态数: {len(IPR_block)}\")\n", "    \n", "    if len(IPR_full) == len(IPR_block):\n", "        # 排序后对比\n", "        IPR_full_sorted = np.sort(IPR_full)\n", "        IPR_block_sorted = np.sort(IPR_block)\n", "        \n", "        diff = np.abs(IPR_full_sorted - IPR_block_sorted)\n", "        print(f\"最大绝对差异: {np.max(diff)}\")\n", "        print(f\"平均绝对差异: {np.mean(diff)}\")\n", "        \n", "        if np.allclose(IPR_full_sorted, IPR_block_sorted, atol=1e-10):\n", "            print(\"✅ IPR 计算结果一致！\")\n", "        else:\n", "            print(\"❌ IPR 计算结果不一致\")\n", "            \n", "            # 进一步诊断\n", "            print(\"\\n=== 诊断信息 ===\")\n", "            print(\"检查投影矩阵的正交性...\")\n", "            \n", "            # 检查投影矩阵的正交性\n", "            for k, reps, peri, eigenvectors in all_block_eigenvectors:\n", "                print(f\"k={k}: 代表元数量={len(reps)}, 本征矢数量={eigenvectors.shape[1]}\")\n", "    \n", "    return E_full, IPR_full, all_block_energies, IPR_block, full_eigenvectors_block\n", "\n", "# 测试函数\n", "E_full, IPR_full, E_block, IPR_block, full_vecs = corrected_floquet_spectrum_with_IPR(\n", "    N=4, J=1, h=1, t1=1, t2=1\n", ")"]}, {"cell_type": "code", "execution_count": 769, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20934 (\\N{CJK UNIFIED IDEOGRAPH-51C6}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33021 (\\N{CJK UNIFIED IDEOGRAPH-80FD}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["准能数量: 16\n", "IPR 数量: 16\n", "前5个 IPR 值: [0.5        0.5        0.10436973 0.5        0.125     ]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 调用函数\n", "\n", "# 打印结果\n", "print(\"准能数量:\", len(quasienergy1_k))\n", "print(\"IPR 数量:\", len(IPR_K))\n", "print(\"前5个 IPR 值:\", IPR_K[:5])\n", "\n", "# 绘制 IPR vs 准能\n", "plt.scatter(quasienergy1_k, IPR_K, s=10)\n", "plt.xlabel('准能')\n", "plt.ylabel('IPR')\n", "plt.title('IPR vs 准能')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 770, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-3.141592653589793,\n", " -3.141592653589793,\n", " -3.1415926535897927,\n", " -3.101592653589793,\n", " -0.04000000000000014,\n", " -2.012279232133097e-16,\n", " 2.4989203203078972e-18,\n", " 1.3976444448474274e-17,\n", " 2.3376860724640397e-17,\n", " 2.7755575615628914e-17,\n", " 3.330669073875469e-16,\n", " 0.04000000000000016,\n", " 3.101592653589793,\n", " 3.1415926535897927,\n", " 3.141592653589793,\n", " 3.141592653589793]"]}, "execution_count": 770, "metadata": {}, "output_type": "execute_result"}], "source": ["quasienergy.sort()\n", "quasienergy"]}, {"cell_type": "code", "execution_count": 771, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["delta_n = [0 for index in range(2**L-1)]\n", "for i in range(2**L-1):\n", "    delta_n[i]=quasienergy[i+1]-quasienergy[i]\n", "\n", "#平均值\n", "mean = np.mean(delta_n)\n", "#mean\n", "delta_n = delta_n / mean\n", "#画间距为0.1 的直方图\n", "delta_n\n", "#画直方分布图\n", "plt.hist(delta_n,bins=50)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 772, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/**********.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#plt.plot(np.arange(len(E)),quasienergy,\n", "#\t\t\t\t\tmarker='x',color='r',markersize=2,label='real space ED')\n", "plt.scatter(np.arange(len(E)),quasienergy,\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('state number',fontsize=16)\n", "plt.ylabel('energy',fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "#plt.savefig('example5a.pdf', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 773, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.5        0.5        0.5        0.5        0.133093   0.15836045\n", " 0.14736756 0.14206492 0.13490924 0.16069423 0.14608952 0.19484082\n", " 0.12824188 0.12479341 0.15971371 0.16295238]\n"]}], "source": ["print(IPR)"]}, {"cell_type": "code", "execution_count": 774, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.04000000000000016\n", "-3.101592653589793\n", "3.101592653589793\n", "-0.04000000000000014\n"]}, {"data": {"text/plain": ["[[0.5000000000000001, 0],\n", " [0.49999999999999967, 1],\n", " [0.49999999999999967, 2],\n", " [0.5000000000000003, 3]]"]}, "execution_count": 774, "metadata": {}, "output_type": "execute_result"}], "source": ["c=0\n", "number=[]\n", "for i in IPR:    \n", "    if i>0.2:\n", "        number.append([i,c])\n", "        print(quasienergy1[c])\n", "    c= 1+c\n", "number"]}, {"cell_type": "code", "execution_count": 775, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/2181619069.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnYAAAHWCAYAAAD6oMSKAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA49UlEQVR4nO3de1yUZf7/8fcAjcxgLgKeOliJghmYWka2KSYeyh66CaWWLgrbVzcz18jM/Grlrpb7zTW22NJMRVtt09JcS1PRPFQomZJlmor68FDJwQUxGE7O7w9/zEYwCAjM7d3r+XjsY/O+rrnmM7cw8/a67vsai9PpdAoAAABXPC9PFwAAAID6QbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAm4ePpAqqTlpamefPm6cCBA3I4HAoNDdWoUaN033331XiM6Oho7d+/3237unXrFBwcXB/lAgAAeJRhg93atWv19NNPy8fHRxEREfL29lZqaqomTpyojIwMjR8//pJjlJSU6NChQ2rWrJkiIyOr7HP11VfXd+kAAAAeYTHid8Xm5OSoT58+8vLy0tKlSxUeHi5JysjIUGxsrHJycvTBBx+oY8eO1Y5z4MABPfDAA+rbt6/+8Y9/NEbpAAAAHmPIa+yWLVsmh8OhESNGuEKdJAUHByshIUFOp1NLliy55DjlS7C33HJLg9UKAABgFIYMdlu3bpUk9evXr1JbVFSULBaLtm3bdslxDhw4IIlgBwAAfh0MGeyOHDkiSWrfvn2lNn9/fwUFBSknJ0c5OTnVjlM+Y5edna24uDhFRESoa9euio2N1Y4dO+q/cAAAAA8yXLDLy8tTUVGR/Pz85OfnV2Wfli1bSpKysrLcjnPhwgV99913kqSpU6fq7Nmz6t69u6655hrt2rVLjz76qBYtWlT/LwAAAMBDDHdXbEFBgSTJZrO57dOkSZMKfaty7NgxFRQUqEmTJkpMTFSfPn1cbevWrdPTTz+tl19+Wbfffrs6d+7sdpzi4mIVFxdX2RYZGani4mJX0AQAAKhvWVlZslqt2r179yX7Gi7YeXldnES0WCxu+5TfyFvdDb3BwcFKTU1VQUGBrrvuugptAwcOVHp6upYsWaJ33nmn2mA3f/58JSUluW2vrs768lNRmc7+VOT6c4BfE/k18W7w5wVw+dz9/vJ7DVyZPPG7W1paWm3m+TnDBbvy5VeHw+G2T/kMmt1ur3asgIAABQQEVNl2zz33aMmSJfrmm2+qHWPs2LGKi4ursm3w4MGSpM2bN1c7xuU6kVOgAYnbVVhSJttV3loxsZfaBlb/2gEYg7vfX36vgSuTJ353o6KiatzXkMHObrcrPz9fDodDvr6+lfpkZmZKklq0aFHn5yl/bGFhYbX9rFarrFZrlW2NMVsnSW0D7dowsZfSjp/VHTcG8OYPXEHc/f7yew1cmYz+u2u4YGexWBQSEqL09HRlZGRU2qokNzdX2dnZCggIUFBQkNtxNm7cqI8//lgREREaNmxYpfaTJ09Kklq3bl2/L6CBtA20G+6HB0DNuPv95fcauDIZ+XfXcHfFSlLPnj0lSSkpKZXaUlJS5HQ63X5FWLn8/Hx99NFH+uc//1nluvTq1aslSXfffXc9VAwAAOB5hgx2MTExstlsSk5O1p49e1zHjx49qsTERFkslgrXvWVmZiojI8O1RCtJ/fv3V/PmzXXo0CH9/e9/14ULF1xtK1as0IYNGxQYGKjhw4c3zosCAABoYIZbipWkNm3aaOrUqZo+fbpGjhypiIgIWa1WpaamqqioSE899ZRCQ0Nd/efOnavVq1dryJAhmj17tiTp6quv1ssvv6zHH39cb7zxhtavX6/Q0FAdP35c3333nex2u1577TX5+/t76FUCAADUL0MGO0kaOnSoWrdurQULFig9PV3e3t7q1KmT4uPj1b9//xqN0bNnT73//vuaN2+edu7cqS1btqh58+aKjo7WuHHjdP311zfwqwAAAGg8FmdNN0ZBJeW3Hzf0dicAAODXqzZ5w5DX2AEAAKD2CHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRg62KWlpSk+Pl49evRQ165dNXz4cK1fv/6yxvzwww8VGhqqSZMm1VOVAAAAxuDj6QLcWbt2rZ5++mn5+PgoIiJC3t7eSk1N1cSJE5WRkaHx48fXeswffvhBM2bMaIBqAQAAPM+QwS4nJ0fTpk2TzWbT0qVLFR4eLknKyMhQbGyskpKS1LdvX3Xs2LHGYzqdTj3zzDM6d+5cQ5UNAADgUYZcil22bJkcDodGjBjhCnWSFBwcrISEBDmdTi1ZsqRWYy5evFi7du1S9+7d67tcAAAAQzBksNu6daskqV+/fpXaoqKiZLFYtG3bthqP99133+mVV17RPffco+jo6PoqEwAAwFAMGeyOHDkiSWrfvn2lNn9/fwUFBSknJ0c5OTmXHKu4uFiTJk2Sn5+fZs6cWe+1AgAAGIXhgl1eXp6Kiork5+cnPz+/Kvu0bNlSkpSVlXXJ8ebOnatDhw5pxowZCgoKqtdaAQAAjMRwN08UFBRIkmw2m9s+TZo0qdDXnZ07dyo5OVmDBw/WgAED6lRPcXGxiouLq2xzOp11GhMAAKAhGC7YeXldnES0WCxu+5QHquqC1blz5zRlyhS1atVK06dPr3M98+fPV1JSktv2Zs2a1XlsAACA+mS4YFe+/OpwONz2KZ9Bs9vtbvvMmDFDP/74oxYtWnRZ4Wvs2LGKi4ursm3w4MF1HhcAAKC+GTLY2e125efny+FwyNfXt1KfzMxMSVKLFi2qHOPrr7/Whx9+KH9/f61atUqrVq1ytZ06dUqStHfvXk2aNEnBwcF67LHH3NZjtVpltVqrbKtuVhEAAKCxGS7YWSwWhYSEKD09XRkZGbrlllsqtOfm5io7O1sBAQFub4Yov/YuNzdXa9eurbLPqVOndOrUKd1xxx3VBjsAAIArheGCnST17NlT6enpSklJqRTsUlJS5HQ6FRkZ6fbxERER+u6776psW7VqlZ599lkNGjRIc+bMqde6AQAAPMlw251IUkxMjGw2m5KTk7Vnzx7X8aNHjyoxMVEWi6XCdW+ZmZnKyMhwLdECAAD8Ghky2LVp00ZTp05VQUGBRo4cqbi4OI0dO1YPPPCAsrKylJCQoNDQUFf/uXPnauDAgZo7d64HqwYAAPAsQy7FStLQoUPVunVrLViwQOnp6fL29lanTp0UHx+v/v37e7o8AAAAw7E42WW3zqKioiRJmzdv9nAlAADArGqTNwy5FAsAAIDaI9gBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJHw8XUB10tLSNG/ePB04cEAOh0OhoaEaNWqU7rvvvhqPcfDgQb3++utKS0vTTz/9pGuuuUb9+vXTmDFj1KxZswasHgAAoHEZdsZu7dq1io2NVVpamjp16qTu3btr//79mjhxopKSkmo0xt69ezV06FBt2LBBbdq0UWRkpEpLS7VgwQI98MADOnPmTAO/CgAAgMZjyBm7nJwcTZs2TTabTUuXLlV4eLgkKSMjQ7GxsUpKSlLfvn3VsWNHt2OUlZXpT3/6k4qKijRz5kw99NBDkqTS0lK98MILWrlypf76179q7ty5jfKaAAAAGpohZ+yWLVsmh8OhESNGuEKdJAUHByshIUFOp1NLliypdoydO3fqzJkz6t69uyvUSZKPj4+efPJJSdLWrVsbpH4AAABPMOSMXXng6tevX6W2qKgoWSwWbdu2rdoxfvvb32r79u0qKSmp1FZQUCDpYsgDAAAwC0MmmyNHjkiS2rdvX6nN399fQUFBysrKUk5OjgIDA92O06pVq0rHzp07pz//+c+SpMGDB9dTxQAAAJ5nuGCXl5enoqIi+fn5yc/Pr8o+LVu2VFZWlrKysqoNdj+3cuVKrVu3Tnv37pXD4VB0dLQmT55cn6UDAAB4lOGCXfkyqc1mc9unSZMmFfrWxObNm/X5559Lkry9vXXu3Dn9+OOPatu2bbWPKy4uVnFxcZVtTqezxs8PAADQ0AwX7Ly8Lt7PYbFY3PYpD1S1CVYzZszQq6++qpMnT+r111/Xhx9+qK+++kr//ve/FRAQ4PZx8+fPr3Z7FfbCAwAARmG4YFe+/OpwONz2KZ9Bs9vtNR63/Hq74OBg/e1vf1NeXp527NihZcuW6YknnnD7uLFjxyouLq7KNq7RAwAARmK47U78/Pxkt9uVn5/vNtxlZmZKklq0aFHn5ykPZfv376+2n9VqVdOmTav8n8ViqXZmEQAAoDEZLthZLBaFhIRIurgh8S/l5uYqOztbAQEBCgoKcjvOJ598omeffVabNm2qst1qtUq6uGExAACAGRgu2ElSz549JUkpKSmV2lJSUuR0OhUZGVntGKdOndKqVau0dOnSKtu3b98uSQoLC7vMagEAAIzBkMEuJiZGNptNycnJ2rNnj+v40aNHlZiYKIvFUuG6t8zMTGVkZLiWaCXp/vvvV9OmTZWWlqbk5OQK469YsUKrVq2S3W7Xww8/3OCvBwAAoDFYnAbds2PFihWaPn26vL29FRERIavVqtTUVBUVFempp57SmDFjXH2nTJmi1atXa8iQIZo9e7br+KZNm/Tkk0+qpKREHTp00A033KCMjAwdO3ZMNptNf//73y8581edqKgoSRe3UgEAAGgItckbhrsrttzQoUPVunVrLViwQOnp6fL29lanTp0UHx+v/v3712iMfv36aeXKlZo3b57S0tJ07NgxBQYGKjo6WmPGjNFNN93UwK8CAACg8Rh2xu5KwIwdAABoaLXJG4a8xg4AAAC1R7ADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJhEowS7vLw8TZs2rTGeCgAA4FfLpy4P2rdvn7Zs2aLc3Fx16NBBQ4YMkd1ur7Lv+++/rzlz5ig3N1czZ868rGIBAADgXq2CndPp1PPPP6+VK1e6/myxWLRo0SItXrxYbdu2dfU9fPiwnn/+ee3du1dOp1MBAQH1WzkAAAAqqNVS7Pvvv68VK1bIYrFowIABiouLU/v27XX69GlNnjzZ1W/RokWKiYnR3r17JUkxMTFat25d/VYOAACACmo1Y/fBBx/IYrFozpw5GjhwoCTpqaee0mOPPaZPP/1UBw8e1MqVK7V8+XI5nU516NBBM2bMULdu3RqkeAAAAPxXrYJdRkaGbrrpJleokyQfHx+NGzdOO3bs0OzZs7Vz507XsTFjxsjHp06X8QEAAKCWapW68vPz1bVr10rHO3ToIEnatWuXWrduraSkJIWFhdVPhQAAAKiRWgW70tJSNW3atNLx8mPe3t5atGiR2rVrVz/VAQAAoMbqdR+7iIgIQh0AAICH1GuwY0sTAAAAz6nXYGexWOpzOAAAANQC3xULAABgErXei2Tt2rVau3ZtpeMWi8VtW3n7t99+W/sKAQAAUCO1DnZOp7NOT1TXxwEAAKBmahXsNm/e3FB1AAAA4DLVKthde+21DVUHAAAALlOdb54oKSlRdna2ysrK6rMeAAAA1FGtr7E7ffq0Zs+erU8++URlZWXy9fXVvffeq8mTJ6t58+YNUSMAAABqoFbBLicnR8OHD1d2drbrZojCwkJ98MEHSk9P13vvvSc/P78GKRQAAADVq9VS7KJFi5SVlaUePXro3//+t9LT07VixQqFhYXp+PHjWrZsWUPVCQAAgEuoVbDbsWOHgoKCNG/ePIWEhMjX11edO3fWm2++qSZNmmjr1q0NVCYAAAAupVbB7vTp0+rcubOsVmuF482bN9ett96qY8eO1WtxAAAAqLlaBTuHwyG73V5lW1BQkPLz8+ulKAAAANRerYJdWVmZvLyqfoiXlxdbnwAAAHhQnfexAwAAgLEQ7AAAAEyi1hsUFxYW6vvvv6/yuCT98MMPrj3ufumaa66p7dMBAACghmod7FJSUpSSkuK2vU+fPlUet1gs+vbbb2v7dAAAAKihWgc7d7NxDfU4AAAA1Eytgt3Bgwcbqg4AAABcJm6eAAAAMIlazdh98cUXl/Vk3bt3v6zHAwAAwL1aBbvf//73slgsdXoibp4AAABoWLUKdmxXAgAAYFy1CnZbtmxpqDoAAABwmbh5AgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZRq68Ua2xpaWmaN2+eDhw4IIfDodDQUI0aNUr33Xdfjcc4duyY3nzzTaWmpio7O1t2u13h4eEaPXq0evbs2YDVAwAANC7DztitXbtWsbGxSktLU6dOndS9e3ft379fEydOVFJSUo3G2L17t6Kjo7Vq1SpZrVZFRkaqbdu2+vTTT/Xoo49q4cKFDfwqAAAAGo8hZ+xycnI0bdo02Ww2LV26VOHh4ZKkjIwMxcbGKikpSX379lXHjh3djlFaWqrJkyeroKBAEydO1B//+EdZLBZJ0meffaaxY8dqzpw56tmzp0JCQhrldQEAADQkQ87YLVu2TA6HQyNGjHCFOkkKDg5WQkKCnE6nlixZUu0Yu3bt0unTpxUWFqbHHnvMFeok6be//a2GDRumCxcuaP369Q32OgAAABqTIWfstm7dKknq169fpbaoqChZLBZt27at2jEKCgoUHh6uXr16Vdl+4403SpLOnDlzWbUCAAAYhSGD3ZEjRyRJ7du3r9Tm7++voKAgZWVlKScnR4GBgVWO0a9fvyqDYbmvv/5aktS6det6qBgAAMDzDLcUm5eXp6KiIvn5+cnPz6/KPi1btpQkZWVl1ek5jhw5oo8++kgWi0X9+/evc60AAABGYrgZu4KCAkmSzWZz26dJkyYV+tbG2bNnNX78eJWWliomJqbaGzAkqbi4WMXFxVW2OZ3OWj8/AABAQzFcsPPyujiJ+PObHX6pPFDVNlhlZmbqD3/4g44dO6awsDBNnz79ko+ZP39+tdurNGvWrFY1AAAANBTDBbvy5VeHw+G2T/kMmt1ur/G4hw8f1tixY3X69GmFh4dr4cKF1c4Klhs7dqzi4uKqbBs8eHCNnx8AAKChGTLY2e125efny+FwyNfXt1KfzMxMSVKLFi1qNOZnn32mCRMm6Pz587r77rv16quvur1+75esVqusVmuVbdXNKgIAAPM7kVOgtONndceNAWobWPMJp4ZiuJsnLBaLa8PgjIyMSu25ubnKzs5WQECAgoKCLjne2rVrNWbMGJ0/f14PPfSQ5s+fX+NQBwAA4M6JnAINSNyuSSu/0oDE7TqRU/tr/+ub4YKdJNd3uKakpFRqS0lJkdPpVGRk5CXH2bJli5555hmVlpZqwoQJmjlzpnx8DDdJCQAArkBpx8+qsKRMklRYUqa042c9XJFBg11MTIxsNpuSk5O1Z88e1/GjR48qMTFRFoulwnVvmZmZysjIcC3RSlJ2draeffZZlZWVady4cXr88ccb9TUAAABzu+PGANmu8pYk2a7y1h03Bni4IgNeYydJbdq00dSpUzV9+nSNHDlSERERslqtSk1NVVFRkZ566imFhoa6+s+dO1erV6/WkCFDNHv2bElScnKycnNz5ePjoxMnTmjSpElVPle3bt30yCOPNMrrAgAA5tE20K4NE3sZ6ho7QwY7SRo6dKhat26tBQsWKD09Xd7e3urUqZPi4+NrtKlwWlqaJKm0tFQffvhhtX0JdgAAoC7aBtoNEejKWZzssltnUVFRkqTNmzd7uBIAAGBWtckbhrzGDgAAALVHsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAOARnQip0DvfXlKJ3IKPF0KABPy8XQBAPBrcSKnQAMSt6uwpEy2q7y1YWIvtQ20e7osACbCjB0ANJK042dVWFImSSosKVPa8bMergiA2RDsDIwlG8BcrvO3VftnALhcLMUaFEs2gPmcyi2s9s8AcLmYsTMolmwA82HGDkBDI9gZ1B03Bsh2lbckyXaVt+64McDDFQG4XMzYAWhoLMUaVNtAuzZM7KW042d1x40BLMMCJlD+D7bySyz4BxuA+kawM7C2gXYCHWAi/IMNQEMj2AFAI+IfbAAaEtfYAQAAmATBDgAAwCQIdgDQiNh4HEBD4ho7AzuRU8BF1oCJsPE4gIZGsDMoPgAA86lq43F+rwHUJ5ZiDYpvngDMh43HATQ0ZuwMio1MAfNpG2jX4tHdtTr9tIZ0uZbZOgD1jmBnUGxkCpjPiZwCxSV/ocKSMv07/XsusQBQ7wh2BsZGpoC5cI0dgIbGNXYA0Ei4xg5AQ2PGDgAaCZdYAGhoBDsAaERcYgGgIbEUCwCNiG+eANCQmLEDgEbCxuOA+RjtW6IIdgDQSLgrFjAXI/5jjaVYAGgk3BULmIsRvyWKGTsAaCTcFQuYixG/JYpgBwCNiLtiAfMw4tcEEuwAAADqwIhfE8g1dgAAAHVgxGvsCHYGxn5XAAAYlxFviGIp1qCMeAs1AAD4LyPeEEWwMyj2uwIAwPiMdkMUS7EGZcTpXQAAYGzM2BmUEad3AQCAsRHsDMxo07sAAMDYWIoFAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkDB3s0tLSFB8frx49eqhr164aPny41q9fX+fxiouLdf/99+vhhx+uxyoBAACMwbDBbu3atYqNjVVaWpo6deqk7t27a//+/Zo4caKSkpJqPd6FCxc0depUHTlypAGqBQAA8DxDfvNETk6Opk2bJpvNpqVLlyo8PFySlJGRodjYWCUlJalv377q2LFjjcY7f/68pkyZok2bNjVk2QAAAB5lyBm7ZcuWyeFwaMSIEa5QJ0nBwcFKSEiQ0+nUkiVLLjmO0+nURx99pMGDB2vTpk26/vrrG7JsAAAAjzJksNu6daskqV+/fpXaoqKiZLFYtG3btkuOc/r0aSUkJCgrK0sTJkzQX/7yl/ouFQAAwDAMGezKr4Nr3759pTZ/f38FBQUpJydHOTk51Y5z1VVX6cEHH9T69ev1+OOPy8vLkC8XAACgXhgu6eTl5amoqEh+fn7y8/Orsk/Lli0lSVlZWdWO1apVK82aNUvXXXddvdcJAABgNIa7eaKgoECSZLPZ3PZp0qRJhb4Nqbi4WMXFxVW2OZ3OBn9+AACAmjJcsCtfLrVYLG77lAeqxghW8+fPr3Z7lWbNmjV4DQAAADVhuGBXvvzqcDjc9imfQbPb7Q1ez9ixYxUXF1dl2+DBgxv8+QEAAGrKkMHObrcrPz9fDodDvr6+lfpkZmZKklq0aNHg9VitVlmt1irbqptVBAAAaGyGu3nCYrEoJCRE0sUNiX8pNzdX2dnZCggIUFBQUGOXBwAAYFiGC3aS1LNnT0lSSkpKpbaUlBQ5nU5FRkY2dlkAAACGZshgFxMTI5vNpuTkZO3Zs8d1/OjRo0pMTJTFYqlw3VtmZqYyMjJcS7QAAAC/RoYMdm3atNHUqVNVUFCgkSNHKi4uTmPHjtUDDzygrKwsJSQkKDQ01NV/7ty5GjhwoObOnevBqgEAADzLcDdPlBs6dKhat26tBQsWKD09Xd7e3urUqZPi4+PVv39/T5cHAABgOBYnu+zWWVRUlCRp8+bNHq4EAACYVW3yhiGXYgEAAFB7BDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYhI+nC6hOWlqa5s2bpwMHDsjhcCg0NFSjRo3SfffdV+Mxzp8/rwULFmjDhg36/vvv1bx5c/Xu3VsTJkxQYGBgA1YPAADQuAw7Y7d27VrFxsYqLS1NnTp1Uvfu3bV//35NnDhRSUlJNRrjp59+0qhRozRv3jyVlpaqd+/estls+te//qUhQ4bozJkzDfwqAAAAGo8hg11OTo6mTZsmm82md955RwsXLtSbb76pDz74QEFBQUpKStLBgwcvOU5SUpK++eYbDRo0SB9//LFeffVVrVu3TqNHj9aZM2c0c+bMRng1AAAAjcOQwW7ZsmVyOBwaMWKEwsPDXceDg4OVkJAgp9OpJUuWVDvG+fPn9a9//Us2m03Tpk2Tj8/FVWcvLy9NnjxZ1113nTZu3KjTp0836GsBAABoLIYMdlu3bpUk9evXr1JbVFSULBaLtm3bVu0YX3zxhQoKCnT77bfL39+/Qpu3t7f69OkjSZccBwAA4EphyGB35MgRSVL79u0rtfn7+ysoKEg5OTnKyclxO8bhw4clSR06dKiyvXzsQ4cOXW65AAAAhmC4YJeXl6eioiL5+fnJz8+vyj4tW7aUJGVlZbkdJzMzs0LfX2rRooUkKTs7+3LKBQAAMAzDbXdSUFAgSbLZbG77NGnSpELf6sbx9fWtsr38eHVjSFJxcbGKi4urbMvMzFRZWZmioqKqHQMAAKCufvjhB3l7e9eor+GCnZfXxUlEi8Xito/T6azw/1UpPwHuxqnJGJI0f/78ardXqemJbmhOp1P5+fm6+uqrqz13qIjzVnecu7rhvNUN561uOG91Y7Tz5uPjI6vVWrO+DVxLrZUvvzocDrd9ymfQ7Ha72z7lbe7GKSoqklT9zKAkjR07VnFxcW7brVZrjU92Qzp//rxuu+02ffLJJ2ratKmny7licN7qjnNXN5y3uuG81Q3nrW6u5PNmyGBnt9uVn58vh8NR5VJq+fVz5dfJVaX82jp319CVX59X3RiScYIbAADApRju5gmLxaKQkBBJUkZGRqX23NxcZWdnKyAgQEFBQW7HKR+j/A7bXyq/a7a8HwAAwJXOcMFOknr27ClJSklJqdSWkpIip9OpyMjIase4/fbbZbfblZaWpvz8/AptZWVl+uSTT+Tl5aVevXrVX+EAAAAeZMhgFxMTI5vNpuTkZO3Zs8d1/OjRo0pMTJTFYqlw3VtmZqYyMjJcS7TSxWvnoqOj9dNPP+m5555zXZfndDr18ssv69SpU+rXr5+uv/76xnthAAAADchw19hJUps2bTR16lRNnz5dI0eOVEREhKxWq1JTU1VUVKSnnnpKoaGhrv5z587V6tWrNWTIEM2ePdt1fOLEidq5c6fWrVunr776SmFhYTp8+LCOHj2qa6+9VtOmTfPEywMAAGgQhgx2kjR06FC1bt1aCxYsUHp6ury9vdWpUyfFx8erf//+NRrj6quv1jvvvKPXX39dGzdu1CeffKJWrVrpkUce0bhx4y554wQAAMCVxLDBTpJ69epVo2vgZs+eXWGm7ueaNWumKVOmaMqUKfVdnqFYrVaNHz+eO3hrifNWd5y7uuG81Q3nrW44b3VzJZ83i/NSO/QCAADgimDImycAAABQewQ7AAAAkyDYAQAAmATBzoRKSkq0aNEiDRo0SOHh4YqIiNCjjz6qHTt2eLo0Q7tw4YLeffddDRs2TN26dVN4eLgGDBigl19+WefOnfN0eVeMDz74QKGhofr88889XYqhpKWlKT4+Xj169FDXrl01fPhwrV+/3tNlXVH42ao53s/qxgyfn9w8YTJOp1NPPPGENm3apGbNmqlbt24qKirS7t27VVJSookTJ+qxxx7zdJmGc+HCBU2YMEGbNm2Sr6+vOnfuLLvdrn379uns2bO64YYbtHz58mq/xg7Svn37FBcXp/Pnz2vx4sW66667PF2SIaxdu1ZPP/20fHx8FBERIW9vb6Wmpqq4uFhPPPGExo8f7+kSDY+frZrj/axuTPP56YSpLF++3BkSEuL83e9+5zx79qzr+IEDB5zdunVzhoaGOo8cOeLBCo1pxYoVzpCQEGf//v2dJ06ccB3Pz893jh071hkSEuL805/+5LkCrwCbN2923n777c6QkBBnSEiI87PPPvN0SYaQnZ3t7Ny5s7NLly7Offv2uY4fOXLEeddddzlDQ0OdBw4c8GCFxsfPVu3wflY3Zvn8ZCnWZNasWSNJmjJlipo3b+463rFjRw0aNEhOp1Pbt2/3VHmG9f7770u6eN5+/jVzTZs21YsvviiLxaKUlBQ5HA5PlWhYZ86c0bPPPqtx48appKSEWYBfWLZsmRwOh0aMGKHw8HDX8eDgYCUkJMjpdGrJkiUerNC4+NmqG97P6sYsn58EO5NZsmSJ1qxZo+7du1dqKygokCR5e3s3dlmG5+/vr3bt2qlLly6V2gICAvSb3/xGJSUl+s9//tP4xRncK6+8olWrVumWW27Ru+++q3bt2nm6JEPZunWrJKlfv36V2qKiomSxWLRt27ZGrurKwM9W3fB+Vjdm+fw09DdPoPaaNGmijh07VjqekpKijz/+WDabrcZfyfZrMm/ePLdtJ0+eVG5urq666ioFBAQ0YlVXhnbt2umvf/2rBg8eLC8v/q34S0eOHJEktW/fvlKbv7+/goKClJWVpZycHAUGBjZ2eYbGz1bd8H5WN2b5/CTYmVheXp6mTZumI0eO6OjRo2rTpo1eeukltW7d2tOlXVESExMlSb1791aTJk08W4wBjRkzxtMlGFZeXp6Kiork5+cnPz+/Kvu0bNlSWVlZysrKItj9Aj9b9Y/3s5q5kj8/CXYGN3nyZO3bt++S/Tp37qz/+7//q3Ds5MmT2rhxo+vPFotFR44cUY8ePeq9TqO5nPP2c2+//bY+/PBD2Ww2Pfnkk/VZoiHV13nDReXLNzabzW2f8g/X8r5AQ/m1vZ9djiv585NgZ3A//PCDjh07dsl+LVq0qHTspptuUlpampxOpz7//HPNmjVLM2fO1Pnz56+MW7Yvw+Wct3JLly51XWg8a9YsBQcH12eJhlQf5w3/Vb58aLFY3PZx/v8dp5zsPIUG9Gt8P7scV/LnJ8HO4N5+++06P/bnSz8DBw5U69at9cgjj+jNN99UbGys26UhM7ic8+Z0OjVnzhy99dZb8vb21qxZs3T//ffXY3XGdTnnDZWV/45Vd/dhcXGxJMlutzdKTfh1+TW/n12OK/nzk6tRf0W6deumtm3bqqCgQMePH/d0OYbkcDg0YcIEvfXWW/L19dWrr76qIUOGeLosXKH8/Pxkt9uVn5/vNtxlZmZKYhYU9Y/3s/pzJX1+EuxMpLCwULNnz9akSZPcLutYrVZJUmlpaWOWdkU4f/68Ro8erY0bNyowMFBLly5V3759PV0WrmAWi0UhISGSpIyMjErtubm5ys7OVkBAAHu0oV7xflY7Zvr8JNiZiK+vr1avXq21a9fqiy++qNR+8uRJHTt2TFarVR06dPBAhcZVUlKiMWPGaO/evbrhhhv07rvv6tZbb/V0WTCBnj17Srq4ZcIvpaSkyOl0KjIysrHLgonxflZ7Zvr8JNiZiMVi0bBhwyRJL7zwgmuJR5J+/PFHJSQkqLS0VA8//DDX8/xCUlKSvvzyS7Vo0UJvv/12hd3agcsRExMjm82m5ORk7dmzx3X86NGjSkxMlMViUVxcnAcrhNnwflZ7Zvr85OYJkxk3bpz27NmjL774QgMGDNBtt92mkpIS7du3TwUFBbr77rs1adIkT5dpKHl5ea6vdAoMDNTLL7/stu+UKVNYMkOttGnTRlOnTtX06dM1cuRIRUREyGq1KjU1VUVFRXrqqacUGhrq6TJhEryf1Z1ZPj8Jdibj6+urxYsXa+nSpVqzZo127dolHx8fdejQQdHR0XrooYeuiK9EaUxfffWVCgsLJUkHDx7UwYMH3fZ94okneCNErQ0dOlStW7fWggULlJ6eLm9vb3Xq1Enx8fFXxE72uHLwflZ3Zvn8tDjZPAkAAMAUuMYOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AHCZdu3apdDQUD388MOeLgXArxzBDgAAwCT4rlgAuEyFhYX6/vvvZbPZdM0113i6HAC/YgQ7AAAAk2ApFoBhlJSUaMGCBbr//vvVuXNnRUVF6Y033lB+fn6Fa9gudU1bnz59FBoaqlOnTlU4fuzYMT333HMaMGCAunTpos6dO6tv37564YUXdObMmUrj7N27V+PGjVOfPn0UFhamu+++WxMmTNC+ffsq9KuunmPHjumZZ55Rz549FRYWpl69eul///d/dfr06Srrvv3221VcXKzXXntN/fr1U1hYmCIjIzVz5kz95z//qfL1fvbZZ3r00UcVERGh8PBw3XfffXrttddUUFBQod+pU6cUGhqqcePGaf369brnnnvUuXNnDRo0SD/99JMkqaCgQElJSRowYIDr7yApKUknT55UaGiofv/730uSvvnmG4WGhioyMlIXLlyoVFNRUZG6d++u2267TQ6Ho8q6AdQ/H08XAADSxVD3hz/8Qbt27dJvfvMb9erVS9nZ2UpMTNS333572ePv3r1bjz76qAoLCxUWFqbQ0FDl5eXpq6++0jvvvKNt27Zp7dq1atq0qSQpPT1do0ePVnFxsbp166awsDCdPHlSGzZs0JYtW/TWW2/pzjvvrPY5P/30U40fP16FhYUKCQlRly5ddPz4cb333ntKSUnRwoULFRYWVuExFy5c0NixY5WWlqYuXbqoffv22rlzp95++23t3r1b7733nnx8/vvW/cYbbygxMVE+Pj4KDw9XixYtlJ6erqSkJG3evFlLlizRb37zmwrPcejQIU2aNEk333yz2rdvL6fTKT8/PxUWFio+Pl579+5VYGCgIiMjlZmZqddee03bt2+vMEZYWJg6duyogwcPateuXerRo0eF9pSUFJ07d07Dhg2Tr69vrf++ANQNwQ6AISxevFi7du3SLbfcokWLFsnf31+StHHjRk2cOPGyx58xY4YKCwv12muvqX///q7jWVlZGjZsmE6fPq0tW7Zo8ODBkqRXXnlFDodDCxcu1N133+3qn5ycrJdeeknz5s2rNtidPXtWCQkJKi4u1ty5c3X//fe72t59910999xzmjhxotatWyer1epq++mnn3To0CG9//776tixoyTp9OnTio6O1oEDB7Rjxw7dc889kqTU1FQlJiaqVatWmj9/vm6++WZJUnFxsZ5//nmtWrVKf/nLXzRnzpwKtZ08eVKPPPKInn/+eUlyzbjNnz9fe/fu1V133aWkpCT5+flJkj7++GMlJCRUeo0xMTGaNWuW1qxZUynYrV69WpIUHR3t9hwBqH8sxQIwhHfffVeS9Oc//9kV6iSpf//+Gj58+GWN/dNPPyksLEwxMTEVQp0ktWjRQn379pWkCku3WVlZkqQ2bdpU6P/II4/o2WefVXx8fLXP+d577ykvL0/Dhw+vEOokadiwYerdu7dOnjypTZs2VXrsmDFjXKFOkq699lr16dNHknT48GHX8YULF0qSpk6d6gp1kmS1WvXcc88pMDBQ69atq3KZefTo0a7/9vLyUllZmd555x35+Pho9uzZrlAnSffee69iYmIqjTF48GBZrVZt2LBBhYWFruNnzpzR559/rnbt2qlLly7uThGABkCwA+Bx33//vU6dOqUWLVpUWpqUVCkY1Zafn59eeuklvfjiixWOZ2Zmatu2bTp48KCkizNd5bp37y7pYpCbM2eO0tLSVFJSIqvVqtGjR6tXr17VPueuXbskqdJMVrnyx5f3+7muXbtWOtayZUtJcgWosrIy7d692+1z2Gw2de/evUK/cr6+vrrhhhsqHNu/f79yc3MVFhamVq1aVRpvwIABlY75+/srKipKBQUFFQLqmjVrVFZWxmwd4AEsxQLwuPLZsdatW1fZfuONN9bL8+zZs0crV67Ut99+qxMnTrhuLrBYLJKkn28S8PTTT+v06dPasWOHFixYoAULFshut6tnz54aPHiwa5bPnR9++EGSNH78+Gr7/fjjj5WONWvWrNKx8uvqypdNc3NzXSHvjjvuqFEt5a6++mq3fX45Q1nO3TYuDz74oNavX681a9a4lrE/+OADeXt763e/+121dQGofwQ7AB5XHqzc+fnNAjVRVlZW6diMGTO0fPlyeXt7q2PHjrr33nvVvn173XrrrdqxY4fmzZtXoX/Tpk311ltv6cCBA9q4caM+++wzffPNN9qwYYM2bNiggQMH6pVXXrlkDffcc4/rhoyqtG/fvtKxS52Pn49vtVqrnE37uV/Oznl5VV6sKS0tlaQq73CVKoben7vrrrt0zTXXKDU1VVlZWfrxxx+VkZGh3r17u2YZATQegh0AjyufJfr++++rbP/lNWLlwcRdCDl37lyFP6elpWn58uVq06aNFi5cqODg4ArtH3/8sdvabr75Zt18883605/+pPz8fK1fv16zZs3SunXrNGrUKLfXkLVs2VLHjx9XbGys7rrrLrfj15W/v7+uuuoqlZaW6sUXX6xwA0ZdlC+/VjWDKFX+Oyjn5eWlIUOG6B//+IdSUlJcf4cswwKewTV2ADyuRYsWat++vXJycvTll19Wat+6dWuFP9vtdklSTk5Opb4HDx6stH9benq6pIs3Yvwy1JWVlWnnzp2S/jsrde7cOUVHR2vQoEEV+l599dUaOnSo6y7ZXy5x/lz5NXrbtm2rsv1vf/uboqOjtXLlSrdjVMdqtapLly66cOGCduzYUand6XRq9OjRGj58eKV996oSFhYmPz8/7d+/37U0/nNbtmxx+9jo6GhZLBZt3LhRmzdvlr+/v+vOXQCNi2AHwBDK7zKdPn16hdmhr776qtIy6U033SSr1aqTJ09WCBx5eXmaMWNGpbGbN28u6eL2ID+/e7OwsFDTp0933WlaVFQk6eI1bhcuXNChQ4eUnJxcYaxTp05pz5498vLyqvJGj3LDhg2T3W7XP//5T3300UcV2j755BMtXrxY+/fvr3aMSym/s/Uvf/lLhb3+Lly4oMTERKWmpurkyZMV7rB1x9fXV8OGDVNpaammTp1a4Tzt2LFD//rXv9w+9rrrrtOdd96pnTt3KiMjQ4MGDbrsGUQAdcNSLABDiImJ0e7du7Vq1Srde++9uvPOO1VUVKRdu3apZcuWrm9GkC7O2I0YMUKLFy/W448/rjvuuEM2m01ffPGF/P39dfvtt1e4E/S+++5TUlKSDh06pL59+6pLly4qLi7W3r17lZ+frw4dOujw4cPKzs52PWbGjBkaOXKkXnrpJa1YsULBwcE6f/68vvzySxUVFemPf/yjrr/+erevp1WrVvrrX/+qhIQEJSQk6B//+IfatWunH374Qd98840k6dlnn62wTUlt9e3bV/Hx8Vq0aJEeeugh3XLLLWrZsqUOHjyokydPymaz6dVXX61xyHr88ceVmpqq7du3q2/fvrrttttcs6jXX3+9Tpw4oauuuqrKxz744INKTU2VpCq3RgHQOJixA2AYL774ombNmqUbb7xRn376qQ4fPqy4uDi98MILlfpOnjxZU6dOVXBwsPbs2aOvv/5aAwcO1MqVKxUYGFihb9OmTbVixQo9+OCDatKkibZv364jR44oPDxcr7zyipYuXSqLxaJPP/1UJSUlkqRbb71Vy5cv14ABA3Tu3Dlt2bJF+/fv12233aZXX31VTz755CVfT//+/fX+++9r8ODBys/P19atW5Wdna3evXtr6dKlFfaSq6tnnnlGb7zxhu68804dP35c27Ztk5eXlx588EGtWbNGt912W43Hatq0qf75z38qPj5eTZo00ZYtW/Tjjz/qySef1KRJk1x9qlL+POXXJALwDIvT3a1OAGAQu3btUmxsrLp166Z33nnH0+WY1tdff61rr71WAQEBldrKv3Hjf/7nf1wh7+cWL16s2bNn67nnntOIESMao1wAVWDGDgAg6eJS7N13363vvvuuwvGTJ09q8eLFslgsFfbvczgcki5+9+xbb72lpk2bsncd4GFcYwcAkCT94Q9/0Isvvqjo6Gh17dpVgYGBOnv2rPbu3auSkhL98Y9/rLC9y+uvv67k5GTXTSeTJ0+uds8+AA2PYAcAkCSNGjVK7dq10/Lly3XgwAGlp6erWbNm6tGjh0aMGKHevXtX6N+pUyfZ7Xb5+fnp4YcfvuT35wJoeFxjBwAAYBJcYwcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGAS/w/sSR3XNj+oiwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(quasienergy1,list(IPR),\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('quasienergy',fontsize=16)\n", "plt.ylabel('IPR',fontsize=16)\n", "# 设置纵坐标范围为0到0.5\n", "plt.ylim(0, 0.5)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "plt.savefig('example5a.eps', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 776, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#按照 准能量的 分布画出柱状分布图\n", "plt.hist(quasienergy, bins=50)\n", "plt.xlabel('quiasenergy', fontsize=16)\n", "plt.ylabel('Count', fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.tight_layout()\n", "# 在每个柱子上标记数值\n", "#for i in range(len(number)):\n", "   # plt.text(number[i][1], i+0.5, str(number[i][0]), ha='center', va='bottom')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 777, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制直方图并获取统计信息\n", "n, bins, patches = plt.hist(quasienergy1, bins=5000, range=(-np.pi, np.pi))\n", "\n", "# 设置坐标轴\n", "plt.xlabel('Quasienergy', fontsize=16)\n", "plt.ylabel('Count', fontsize=16)\n", "plt.xticks([-np.pi, -np.pi/2, 0, np.pi/2, np.pi],\n", "           [r'$-\\pi$', r'$-\\pi/2$', '0', r'$\\pi/2$', r'$\\pi$'],\n", "           fontsize=14)\n", "plt.yticks(fontsize=14)\n", "\n", "# 在每个柱子上方标记数量\n", "bin_width = bins[1] - bins[0]  # 计算柱子宽度\n", "for i in range(len(n)):\n", "    count = n[i]\n", "    if count > 0:  # 只标记有数据的柱子\n", "        # 计算柱子中心位置\n", "        x_pos = bins[i] + bin_width / 2\n", "        # 放置文本标签\n", "        plt.text(x_pos, count, f'{int(count)}',\n", "                 ha='center', va='bottom',\n", "                 fontsize=8, color='darkred')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 778, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16\n", "找到16个IPR大于0.1的向量，索引为: [ 0  1  2  3  4  5  6  7  8  9 10 11 12 13 14 15]\n", "[[7.07106781e-01 7.07106781e-01 3.69265827e-16 6.13795306e-16\n", "  3.54824746e-16 1.81895269e-15 9.32381582e-16 9.67233417e-16\n", "  1.67830162e-15 3.85617618e-16 1.49060353e-15 5.82152855e-16\n", "  1.12125154e-15 2.75884451e-15 8.51898987e-16 1.26960980e-15]\n", " [3.15927537e-18 1.10517127e-15 1.17506426e-15 7.42250258e-16\n", "  8.79202760e-02 2.08158899e-01 1.92874061e-01 1.57828154e-01\n", "  1.76353221e-01 7.87755197e-02 2.97900054e-01 1.95309742e-01\n", "  4.12079231e-01 2.68730256e-01 2.16043640e-01 1.60412862e-01]\n", " [1.11255415e-17 3.37372811e-15 1.38385463e-15 1.29117000e-15\n", "  4.09582668e-01 1.96485574e-01 4.61811219e-01 4.17009615e-01\n", "  1.48728298e-01 5.02308025e-01 1.76886852e-01 1.68775559e-01\n", "  6.52820425e-02 1.73441684e-01 3.11869196e-02 4.69013993e-01]\n", " [2.49657186e-18 1.25389878e-16 1.50701111e-15 4.54674737e-16\n", "  1.77841619e-02 2.14253790e-01 1.78805509e-01 1.94488277e-01\n", "  1.97082471e-01 1.57375546e-01 4.46666641e-01 5.37815427e-01\n", "  2.41175774e-01 2.77062862e-01 3.43587396e-01 2.64201303e-01]\n", " [3.19062714e-17 2.33101404e-15 2.87888481e-15 1.52106399e-15\n", "  3.50716897e-01 1.09488591e-02 2.30628165e-02 9.76083031e-02\n", "  3.07841880e-01 2.52303274e-01 1.03946564e-01 1.47297806e-01\n", "  2.02231958e-01 4.18499161e-01 3.06213045e-01 5.37170667e-02]\n", " [4.66429226e-17 1.10544260e-15 7.07106781e-01 7.07106781e-01\n", "  4.85927365e-15 3.02550324e-15 2.24425386e-16 2.54039944e-15\n", "  3.40013928e-15 1.31274775e-15 5.13519049e-16 1.67482736e-15\n", "  4.84097389e-16 1.99334942e-15 2.70858298e-16 1.29446228e-15]\n", " [2.08444713e-17 1.65683368e-15 3.58557553e-15 6.39392051e-16\n", "  2.49056215e-01 4.83162633e-01 3.76834092e-01 2.64965459e-01\n", "  4.62022982e-01 2.54869571e-01 1.20610728e-01 3.21250504e-01\n", "  4.00935651e-01 7.42623909e-02 4.84076082e-01 4.06384119e-01]\n", " [2.70227787e-17 2.57767529e-15 1.69724722e-15 8.93450054e-16\n", "  3.73047513e-01 3.72280268e-01 2.73902827e-01 4.28524783e-01\n", "  3.15762689e-01 2.96814020e-01 3.93832039e-01 1.38654074e-01\n", "  2.57130410e-01 3.74542056e-01 7.87663676e-02 1.28287507e-01]\n", " [1.84000599e-16 2.57453406e-15 1.46137412e-15 8.32754431e-16\n", "  3.73047513e-01 3.72280268e-01 2.73902827e-01 4.28524783e-01\n", "  3.15762689e-01 2.96814020e-01 3.93832039e-01 1.38654074e-01\n", "  2.57130410e-01 3.74542056e-01 7.87663676e-02 1.28287507e-01]\n", " [7.72796830e-17 1.65541057e-15 3.69659028e-15 6.42747647e-16\n", "  2.49056215e-01 4.83162633e-01 3.76834092e-01 2.64965459e-01\n", "  4.62022982e-01 2.54869571e-01 1.20610728e-01 3.21250504e-01\n", "  4.00935651e-01 7.42623909e-02 4.84076082e-01 4.06384119e-01]\n", " [7.33007458e-18 1.10247904e-15 7.07106781e-01 7.07106781e-01\n", "  4.65389693e-15 2.77185866e-15 1.54656069e-16 2.35663312e-15\n", "  3.33459558e-15 1.41474941e-15 5.89916579e-16 1.62550363e-15\n", "  6.37191995e-16 1.99210032e-15 3.59750349e-16 1.23167316e-15]\n", " [1.00606519e-16 2.33101459e-15 2.83008838e-15 1.53376607e-15\n", "  3.50716897e-01 1.09488591e-02 2.30628165e-02 9.76083031e-02\n", "  3.07841880e-01 2.52303274e-01 1.03946564e-01 1.47297806e-01\n", "  2.02231958e-01 4.18499161e-01 3.06213045e-01 5.37170667e-02]\n", " [2.20951867e-17 1.24811949e-16 1.59110871e-15 4.75339404e-16\n", "  1.77841619e-02 2.14253790e-01 1.78805509e-01 1.94488277e-01\n", "  1.97082471e-01 1.57375546e-01 4.46666641e-01 5.37815427e-01\n", "  2.41175774e-01 2.77062862e-01 3.43587396e-01 2.64201303e-01]\n", " [8.22186232e-17 3.37446524e-15 1.25465535e-15 1.26023477e-15\n", "  4.09582668e-01 1.96485574e-01 4.61811219e-01 4.17009615e-01\n", "  1.48728298e-01 5.02308025e-01 1.76886852e-01 1.68775559e-01\n", "  6.52820425e-02 1.73441684e-01 3.11869196e-02 4.69013993e-01]\n", " [1.15330568e-16 1.10648898e-15 1.22444466e-15 7.29610715e-16\n", "  8.79202760e-02 2.08158899e-01 1.92874061e-01 1.57828154e-01\n", "  1.76353221e-01 7.87755197e-02 2.97900054e-01 1.95309742e-01\n", "  4.12079231e-01 2.68730256e-01 2.16043640e-01 1.60412862e-01]\n", " [7.07106781e-01 7.07106781e-01 3.69924586e-16 6.13812363e-16\n", "  5.97903542e-16 1.81957832e-15 1.00048733e-15 9.33166421e-16\n", "  1.73980130e-15 2.93286607e-16 1.53789103e-15 5.16983244e-16\n", "  1.24838228e-15 2.90162750e-15 8.24857417e-16 1.26752534e-15]]\n", "0.04000000000000016\n", "-3.101592653589793\n", "3.101592653589793\n", "-0.04000000000000014\n", "3.141592653589793\n", "1.3976444448474274e-17\n", "2.7755575615628914e-17\n", "2.3376860724640397e-17\n", "3.330669073875469e-16\n", "-2.012279232133097e-16\n", "-3.1415926535897927\n", "3.1415926535897927\n", "3.141592653589793\n", "-3.141592653589793\n", "-3.141592653589793\n", "2.4989203203078972e-18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_42020/3909831609.py:57: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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**************************************************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", "text/plain": ["<Figure size 2000x1200 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_selected_vectors(IPR, ipr_threshold):\n", "    \"\"\"\n", "    筛选IPR大于阈值的列向量并绘制\n", "    \n", "    参数:\n", "    V: 输入矩阵，每行代表一个基矢，每列代表一个向量\n", "    ipr_threshold: IPR筛选阈值，默认0.5\n", "    \"\"\"\n", "    # 计算IPR\n", "    ipr = IPR\n", "    print(len(ipr))\n", "    # 筛选IPR大于阈值的列索引\n", "    selected_indices = np.where(ipr > ipr_threshold)[0]\n", "    if len(selected_indices) == 0:\n", "        print(f\"没有找到IPR大于{ipr_threshold}的向量\")\n", "        return\n", "    \n", "    print(f\"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}\")\n", "    \n", "    # 提取对应的列向量\n", "    selected_vectors = V[:,selected_indices]\n", "    print(selected_vectors)\n", "    \n", "    \n", "    # 设置绘图风格\n", "    plt.style.use('seaborn-v0_8-ticks')\n", "    \n", "    # 计算子图布局（最多5列）\n", "    n_cols = min(5, len(selected_indices))\n", "    n_rows = (len(selected_indices) + n_cols - 1) // n_cols\n", "    \n", "    # 创建画布\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))\n", "    axes = np.ravel(axes)  # 转换为一维数组便于索引\n", "    for i, idx in enumerate(selected_indices):\n", "        E_slect = quasienergy1[idx]\n", "        print(E_slect)\n", "    # 绘制每个选中的向量\n", "    for i, idx in enumerate(selected_indices):\n", "        ax = axes[i]\n", "        # 绘制向量的绝对值点线图\n", "        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')\n", "        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))\n", "        ax.set_title(f'向量索引: {idx}\\nIPR = {ipr[idx]:.4f}', fontsize=10)\n", "        ax.set_xlabel('基矢索引', fontsize=8)\n", "        ax.set_ylabel('|振幅|', fontsize=8)\n", "        ax.tick_params(axis='both', which='major', labelsize=6)\n", "    \n", "    # 隐藏未使用的子图\n", "    for i in range(len(selected_indices), len(axes)):\n", "        axes[i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return selected_vectors, selected_indices\n", "\n", "# 示例使用\n", "if __name__ == \"__main__\":\n", "    # 筛选并绘图\n", "    selected_vecs, selected_idx = plot_selected_vectors(IPR, ipr_threshold=0.1)\n"]}, {"cell_type": "code", "execution_count": 779, "metadata": {}, "outputs": [{"data": {"text/plain": ["'import numpy as np\\nimport matplotlib.pyplot as plt\\nfrom matplotlib.colors import LogNorm\\nimport cmath\\nfrom scipy.linalg import expm\\nimport time\\n\\n# 假设 get_hamiltonian_sparse 已定义\\n# def get_hamiltonian_sparse(col, row, J, hx):\\n#     # 实现略\\n#     pass\\ncol = 2\\nrow = 3\\nL = 7#col * row\\ndef phasecrate(lam_h, lam_J):\\n\\n    # 注意：原函数中这两行覆盖了输入参数，建议修改\\n    # lam_h = 0.01\\n    # lam_J = 0.01\\n    hx = np.pi/2 - lam_h\\n    J_val = np.pi/2 - lam_J\\n    Hr1, Hc1, Hd1 = get_hamiltonian_sparse(L, 0 * J_val, hx)\\n    Hr2, Hc2, Hd2 = get_hamiltonian_sparse(L, J_val, 0 * hx)\\n    \\n    # 创建哈密顿量矩阵\\n    H1 = np.zeros((2**L, 2**L), dtype=complex)  \\n    H2 = np.zeros((2**L, 2**L), dtype=complex)\\n    H1[Hr1, Hc1] = Hd1\\n    H2[Hr2, Hc2] = Hd2\\n    \\n    # 计算演化算符\\n    H_F = expm(-1j * H1) @ expm(-1j * H2)\\n    \\n    # 求解本征值和本征向量\\n    E, V = np.linalg.eig(H_F)\\n    \\n    # 计算准能（本征值的相位）\\n    quasienergy = [cmath.phase(e) for e in E]\\n    \\n    # 计算IPR\\n    abs4 = np.power(np.abs(V), 4)\\n    IPR = np.sum(abs4, axis=0)\\n    \\n    return quasienergy, IPR\\n\\ndef test_phasecrate_scan(n_points=6):\\n    \"\"\"\\n    遍历 lam_h 和 lam_J 从 0 到 π，绘制 IPR 随准能变化的关系图\\n    n_points: 每个参数的采样点数\\n    \"\"\"\\n    # 设置参数扫描范围\\n    lam_h_vals = np.linspace(0, np.pi, n_points)\\n    lam_J_vals = np.linspace(0, np.pi, n_points)\\n    \\n    # 存储结果的数组\\n    all_quasienergies = []\\n    all_iprs = []\\n    params_list = []\\n    \\n    print(f\"开始扫描 {n_points}x{n_points} 个参数点...\")\\n    start_time = time.time()\\n    \\n    # 遍历参数空间\\n    for i, lam_h in enumerate(lam_h_vals):\\n        for j, lam_J in enumerate(lam_J_vals):\\n            # 显示进度\\n            if (i * n_points + j) % 10 == 0:\\n                progress = (i * n_points + j) / (n_points * n_points) * 100\\n                print(f\"进度: {progress:.1f}%\")\\n            \\n            # 计算准能和IPR\\n            quasienergy, ipr = phasecrate(lam_h, lam_J)\\n            \\n            # 存储结果\\n            all_quasienergies.extend(quasienergy)\\n            all_iprs.extend(ipr)\\n            params_list.extend([(lam_h, lam_J)] * len(quasienergy))\\n    \\n    end_time = time.time()\\n    print(f\"扫描完成，耗时 {end_time - start_time:.2f} 秒\")\\n    \\n    # 转换为numpy数组\\n    all_quasienergies = np.array(all_quasienergies)\\n    all_iprs = np.array(all_iprs)\\n    params_array = np.array(params_list)\\n    \\n    # 绘制IPR随准能变化的散点图\\n    plt.figure(figsize=(12, 6))\\n    \\n    # 主图：IPR vs 准能，颜色表示lam_h，大小表示IPR值\\n    scatter = plt.scatter(\\n        all_quasienergies, all_iprs,\\n        c=params_array[:, 0],  # 用lam_h编码颜色\\n        s=all_iprs * 50,       # 用IPR值编码大小\\n        cmap=\\'viridis\\', \\n        alpha=0.6,\\n        edgecolors=\\'black\\', \\n        linewidth=0.5\\n    )\\n    plt.colorbar(scatter, label=\\'λ_h\\')\\n    plt.xlabel(\\'准能 (Quasienergy)\\')\\n    plt.ylabel(\\'逆参与率 (IPR)\\')\\n    plt.title(\\'IPR随准能的变化关系\\')\\n    plt.grid(True, alpha=0.3)\\n    \\n    # 添加IPR分布的直方图\\n    plt.figure(figsize=(12, 5))\\n    plt.subplot(121)\\n    plt.hist(all_iprs, bins=30, color=\\'skyblue\\', edgecolor=\\'black\\')\\n    plt.xlabel(\\'IPR值\\')\\n    plt.ylabel(\\'出现频率\\')\\n    plt.title(\\'IPR分布直方图\\')\\n    plt.grid(True, alpha=0.3)\\n    \\n    # 添加准能分布的直方图\\n    plt.subplot(122)\\n    plt.hist(all_quasienergies, bins=30, color=\\'lightgreen\\', edgecolor=\\'black\\')\\n    plt.xlabel(\\'准能值\\')\\n    plt.ylabel(\\'出现频率\\')\\n    plt.title(\\'准能分布直方图\\')\\n    plt.grid(True, alpha=0.3)\\n    \\n    plt.tight_layout()\\n    plt.show()\\n    \\n    # 绘制参数空间中的IPR最大值分布\\n    max_ipr = np.zeros((n_points, n_points))\\n    for i, lam_h in enumerate(lam_h_vals):\\n        for j, lam_J in enumerate(lam_J_vals):\\n            idx = i * n_points + j\\n            start = idx * (2**L)  # 假设系统大小是8个格点\\n            end = start + (2**L)\\n            max_ipr[i, j] = np.max(all_iprs[start:end])\\n    \\n    plt.figure(figsize=(8, 6))\\n    im = plt.imshow(\\n        max_ipr, \\n        extent=[0, np.pi, 0, np.pi],\\n        origin=\\'lower\\',\\n        cmap=\\'plasma\\',\\n        norm=LogNorm()\\n    )\\n    plt.colorbar(im, label=\\'最大IPR值\\')\\n    plt.xlabel(\\'λ_J\\')\\n    plt.ylabel(\\'λ_h\\')\\n    plt.title(\\'参数空间中最大IPR的分布\\')\\n    plt.show()\\n    \\n    return all_quasienergies, all_iprs, params_array\\n\\n# 运行测试函数\\nif __name__ == \"__main__\":\\n    # 注意：对于8个格点系统(2^8=256维)，15x15的网格会有3375个点，计算量较大\\n    # 可先使用较小的n_points进行测试\\n    quasi, ipr, params = test_phasecrate_scan(n_points=64)'"]}, "execution_count": 779, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "'''import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import LogNorm\n", "import cmath\n", "from scipy.linalg import expm\n", "import time\n", "\n", "# 假设 get_hamiltonian_sparse 已定义\n", "# def get_hamiltonian_sparse(col, row, J, hx):\n", "#     # 实现略\n", "#     pass\n", "col = 2\n", "row = 3\n", "L = 7#col * row\n", "def phasecrate(lam_h, lam_J):\n", "\n", "    # 注意：原函数中这两行覆盖了输入参数，建议修改\n", "    # lam_h = 0.01\n", "    # lam_J = 0.01\n", "    hx = np.pi/2 - lam_h\n", "    J_val = np.pi/2 - lam_J\n", "    Hr1, Hc1, Hd1 = get_hamiltonian_sparse(L, 0 * J_val, hx)\n", "    Hr2, Hc2, Hd2 = get_hamiltonian_sparse(L, J_val, 0 * hx)\n", "    \n", "    # 创建哈密顿量矩阵\n", "    H1 = np.zeros((2**L, 2**L), dtype=complex)  \n", "    H2 = np.zeros((2**L, 2**L), dtype=complex)\n", "    H1[Hr1, Hc1] = Hd1\n", "    H2[Hr2, Hc2] = Hd2\n", "    \n", "    # 计算演化算符\n", "    H_F = expm(-1j * H1) @ expm(-1j * H2)\n", "    \n", "    # 求解本征值和本征向量\n", "    E, V = np.linalg.eig(H_F)\n", "    \n", "    # 计算准能（本征值的相位）\n", "    quasienergy = [cmath.phase(e) for e in E]\n", "    \n", "    # 计算IPR\n", "    abs4 = np.power(np.abs(V), 4)\n", "    IPR = np.sum(abs4, axis=0)\n", "    \n", "    return quasienergy, IPR\n", "\n", "def test_phasecrate_scan(n_points=6):\n", "    \"\"\"\n", "    遍历 lam_h 和 lam_J 从 0 到 π，绘制 IPR 随准能变化的关系图\n", "    n_points: 每个参数的采样点数\n", "    \"\"\"\n", "    # 设置参数扫描范围\n", "    lam_h_vals = np.linspace(0, np.pi, n_points)\n", "    lam_J_vals = np.linspace(0, np.pi, n_points)\n", "    \n", "    # 存储结果的数组\n", "    all_quasienergies = []\n", "    all_iprs = []\n", "    params_list = []\n", "    \n", "    print(f\"开始扫描 {n_points}x{n_points} 个参数点...\")\n", "    start_time = time.time()\n", "    \n", "    # 遍历参数空间\n", "    for i, lam_h in enumerate(lam_h_vals):\n", "        for j, lam_J in enumerate(lam_J_vals):\n", "            # 显示进度\n", "            if (i * n_points + j) % 10 == 0:\n", "                progress = (i * n_points + j) / (n_points * n_points) * 100\n", "                print(f\"进度: {progress:.1f}%\")\n", "            \n", "            # 计算准能和IPR\n", "            quasienergy, ipr = phasecrate(lam_h, lam_J)\n", "            \n", "            # 存储结果\n", "            all_quasienergies.extend(quasienergy)\n", "            all_iprs.extend(ipr)\n", "            params_list.extend([(lam_h, lam_J)] * len(quasienergy))\n", "    \n", "    end_time = time.time()\n", "    print(f\"扫描完成，耗时 {end_time - start_time:.2f} 秒\")\n", "    \n", "    # 转换为numpy数组\n", "    all_quasienergies = np.array(all_quasienergies)\n", "    all_iprs = np.array(all_iprs)\n", "    params_array = np.array(params_list)\n", "    \n", "    # 绘制IPR随准能变化的散点图\n", "    plt.figure(figsize=(12, 6))\n", "    \n", "    # 主图：IPR vs 准能，颜色表示lam_h，大小表示IPR值\n", "    scatter = plt.scatter(\n", "        all_quasienergies, all_iprs,\n", "        c=params_array[:, 0],  # 用lam_h编码颜色\n", "        s=all_iprs * 50,       # 用IPR值编码大小\n", "        cmap='viridis', \n", "        alpha=0.6,\n", "        edgecolors='black', \n", "        linewidth=0.5\n", "    )\n", "    plt.colorbar(scatter, label='λ_h')\n", "    plt.xlabel('准能 (Quasienergy)')\n", "    plt.ylabel('逆参与率 (IPR)')\n", "    plt.title('IPR随准能的变化关系')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 添加IPR分布的直方图\n", "    plt.figure(figsize=(12, 5))\n", "    plt.subplot(121)\n", "    plt.hist(all_iprs, bins=30, color='skyblue', edgecolor='black')\n", "    plt.xlabel('IPR值')\n", "    plt.ylabel('出现频率')\n", "    plt.title('IPR分布直方图')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 添加准能分布的直方图\n", "    plt.subplot(122)\n", "    plt.hist(all_quasienergies, bins=30, color='lightgreen', edgecolor='black')\n", "    plt.xlabel('准能值')\n", "    plt.ylabel('出现频率')\n", "    plt.title('准能分布直方图')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 绘制参数空间中的IPR最大值分布\n", "    max_ipr = np.zeros((n_points, n_points))\n", "    for i, lam_h in enumerate(lam_h_vals):\n", "        for j, lam_J in enumerate(lam_J_vals):\n", "            idx = i * n_points + j\n", "            start = idx * (2**L)  # 假设系统大小是8个格点\n", "            end = start + (2**L)\n", "            max_ipr[i, j] = np.max(all_iprs[start:end])\n", "    \n", "    plt.figure(figsize=(8, 6))\n", "    im = plt.imshow(\n", "        max_ipr, \n", "        extent=[0, np.pi, 0, np.pi],\n", "        origin='lower',\n", "        cmap='plasma',\n", "        norm=LogNorm()\n", "    )\n", "    plt.colorbar(im, label='最大IPR值')\n", "    plt.xlabel('λ_J')\n", "    plt.ylabel('λ_h')\n", "    plt.title('参数空间中最大IPR的分布')\n", "    plt.show()\n", "    \n", "    return all_quasienergies, all_iprs, params_array\n", "\n", "# 运行测试函数\n", "if __name__ == \"__main__\":\n", "    # 注意：对于8个格点系统(2^8=256维)，15x15的网格会有3375个点，计算量较大\n", "    # 可先使用较小的n_points进行测试\n", "    quasi, ipr, params = test_phasecrate_scan(n_points=64)'''\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}