import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import expm
from scipy.sparse.linalg import eigsh
from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定
from math import * 
import pandas as pd
import cmath
import os #导入os库
import random
import time
import seaborn as sns
import cmath
import scipy
import functools
import matplotlib
import matplotlib.pyplot as plt

#引入T平移对称性下的分块矩阵
# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state

def state_to_2d(state, rows, cols):
    
    # 创建二维数组
    array_2d = [[0] * cols for _ in range(rows)]
    
    # 填充二维数组
    for i in range(rows):
        for j in range(cols):
            idx = i * cols + j
            array_2d[i][j] = get_site_value(state, idx)
    
    return array_2d
def array_2d_to_state(array_2d):
    """将二维数组转换回一维态"""
    rows = len(array_2d)
    cols = len(array_2d[0])
    state = 0
    
    for i in range(rows):
        for j in range(cols):
            idx = i * cols + j
            state = set_site_value(state, idx, array_2d[i][j])
    
    return state

def translate_2d( state, rows, cols, row_shift, col_shift):
    """
    在二维网格上进行平移操作
    
    参数:
        L: 总位数
        state: 原始状态
        rows: 行数
        cols: 列数
        row_shift: 行平移量
        col_shift: 列平移量
        
    返回:
        平移后的状态
    """
    # 转换为二维数组
    array_2d = state_to_2d(state, rows, cols)
    
    # 创建新的二维数组用于存储平移后的结果
    new_array_2d = [[0] * cols for _ in range(rows)]
    
    # 应用平移
    for i in range(rows):
        for j in range(cols):
            # 计算新位置（使用模运算实现循环平移）
            new_i = (i + row_shift) % rows
            new_j = (j + col_shift) % cols
            new_array_2d[new_i][new_j] = array_2d[i][j]
    
    # 转换回一维态
    return array_2d_to_state(new_array_2d)

def get_hamiltonian_sparse(col,row, Jz, hx):
    '''
    Creates the Hamiltonian of the Transverse Field Ising model
    on a linear chain lattice with periodic boundary conditions.

    The Hamiltonian is given by:
    H = -Jz \sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \sum_{i=1}^{L} S_i^x - hz \sum_{i=1}^{L} S_i^z
    Args:
        L (int): length of chain
        J (float): coupling constant for Ising term
        hx (float): coupling constant for transverse field

    Returns:
        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:
        hamiltonian_rows (list of ints): row index of non-zero elements
        hamiltonian_cols (list of ints): column index of non-zero elements
        hamiltonian_data (list of floats): value of non-zero elements
    '''
    L = col * row
    def hilbertspace_dimension(L):
        ''' return dimension of hilbertspace '''
        return 2**L
    '''
    def get_site_value(state, site):
 Function to get local value at a given site 
        return (state >> site) & 1
        #返回值为state的第site位上的二进制值

    

    def flip_state(state: int, index: int) -> int:
        """翻转一个整数某位置处的二进制值"""
        mask = 1 << index
        return state ^ mask
    '''
    # Define chain lattice
    #ising_bonds_col = [(site, (site+1)%col) for site in range(col-1)]#开放边条件
    #ising_bonds_row = [(site, (site+1)%row) for site in range(row-1)]#开放边条件
    #ising_bonds_col = [(site, (site+1)%col) for site in range(col)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0
    #ising_bonds_row = [(site, (site+1)%row) for site in range(row)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0
    ising_bonds = []

    #  周期边条件
    for i in range(row):          # 遍历每一行
        for j in range(col):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 水平方向链接（同一行内相邻列）
            # 右侧相邻格点，列索引取模实现周期边界
            right = i*col+ (j + 1) % col
            ising_bonds.append((current, right))
            # 垂直方向链接（同一列内相邻行）
            # 下方相邻格点，行索引取模实现周期边界
            down = ((i + 1) % row) *col + j
            ising_bonds.append((current, down))
    #非周期边条件
    '''
    for i in range(row):          # 遍历每一行
        for j in range(col-1):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 水平方向链接（同一行内相邻列）
            # 右侧相邻格点，列索引取模实现非周期边界
            right = i*col+ (j + 1) % col
            ising_bonds.append((current, right))

    for i in range(row-1):          # 遍历每一行
        for j in range(col):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 垂直方向链接（同一列内相邻行）
            # 下方相邻格点，行索引取模实现非周期边界
            down = ((i + 1) % row) *col + j
            ising_bonds.append((current, down))'''
    print(ising_bonds)
    # Empty lists for sparse matrix
    hamiltonian_rows = []
    hamiltonian_cols = []
    hamiltonian_data = []
    
    # Run through all spin configurations
    for state in range(hilbertspace_dimension(L)):

        # Apply Ising bonds
        ising_diagonal = 0
        for bond in ising_bonds:
            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):
                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5
            else:
                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5
        hamiltonian_rows.append(state)
        hamiltonian_cols.append(state)
        hamiltonian_data.append(ising_diagonal)

        # Apply transverse field
        for site in range(L):
            # Flip spin at site
            new_state = flip_state(state,site)#state ^ (1 << site)
            hamiltonian_rows.append(new_state)
            hamiltonian_cols.append(state)
            hamiltonian_data.append(hx)#(mid3[state])#hx
    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data

def block_direct_sum(blocks):
    """
    沿对角线构造块直和矩阵（块对角矩阵）
    
    参数:
        blocks: 子矩阵列表，每个元素为numpy数组（方阵）
    返回:
        块直和矩阵（对角线上是输入的子矩阵，其余为0）
    """
    # 检查输入是否为空
    if not blocks:
        return np.array([], dtype=float)
    
    # 检查所有子矩阵是否为方阵
    for i, b in enumerate(blocks):
        if b.ndim != 2 or b.shape[0] != b.shape[1]:
            raise ValueError(f"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）")
    
    # 初始化结果矩阵（从空矩阵开始）
    result = np.array([], dtype=blocks[0].dtype)
    
    for block in blocks:
        m = block.shape[0]  # 当前块的维度
        if result.size == 0:
            # 第一次拼接：直接用当前块作为初始矩阵
            result = block.copy()
        else:
            # 非第一次拼接：构造新的块对角矩阵
            n = result.shape[0]  # 现有矩阵的维度
            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）
            new_result = np.zeros((n + m, n + m), dtype=result.dtype)
            # 填充左上角为现有矩阵
            new_result[:n, :n] = result
            # 填充右下角为当前块
            new_result[n:, n:] = block
            result = new_result
    
    return result

#对于 2d 情况
def represent(L, a0, rows, cols):
    """
    在二维格点上找到表示态（最小代表态）
    
    参数:
        L: 总位数
        a0: 初始状态
        rows: 行数
        cols: 列数
        
    返回:
        a: 最小代表态
        l_row: 行平移量
        l_col: 列平移量
        g: 自旋翻转标志 (0=未翻转, 1=翻转)
    """
    # 初始化
    a = a0
    l_row = 0
    l_col = 0
    g = 0
    smax = (1 << L) - 1  # 所有位都为1的状态
    
    # 1. 仅考虑平移操作（不翻转）
    for row_shift in range(rows):
        for col_shift in range(cols):
            # 应用二维平移
            at = translate_2d(a0, rows, cols, row_shift, col_shift)
            
            # 如果找到更小的状态，更新代表态和平移量
            if at < a:
                a = at
                l_row = row_shift
                l_col = col_shift
                g = 0
    '''
    # 2. 考虑自旋翻转后的状态
    a_flipped = smax ^ a0  # 自旋翻转
    
    for row_shift in range(rows):
        for col_shift in range(cols):
            # 应用二维平移到翻转后的状态
            at = translate_2d(a_flipped, rows, cols, row_shift, col_shift)
            
            # 如果找到更小的状态，更新代表态和平移量
            if at < a:
                a = at
                l_row = row_shift
                l_col = col_shift
                g = 1
    '''
    return a, l_row, l_col, g

def orbit_period_2d(col: int, row: int, s: int) -> tuple:
    """
    计算二维网格上的平移周期
    
    参数:
        col: 列数
        row: 行数
        s: 初始状态
        
    返回:
        (row_period, col_period): 行方向和列方向的周期
    """
    # 计算行方向的周期
    t_row = translate_2d(s, row, col, 1, 0)  # 行方向平移1个单位
    row_period = 1
    while t_row != s and row_period <= row:
        t_row = translate_2d(t_row, row, col, 1, 0)
        row_period += 1
    
    # 计算列方向的周期
    t_col = translate_2d(s, row, col, 0, 1)  # 列方向平移1个单位
    col_period = 1
    while t_col != s and col_period <= col:
        t_col = translate_2d(t_col, row, col, 0, 1)
        col_period += 1
    
    return row_period, col_period

def orbit_period_full_2d(col: int, row: int, s: int) -> int:
    """
    计算二维网格上的完整周期（行和列同时平移）
    
    参数:
        col: 列数
        row: 行数
        s: 初始状态
        
    返回:
        full_period: 完整周期（行和列同时平移）
    """
    # 同时考虑行和列平移
    t = translate_2d(s, row, col, 1, 1)  # 行和列各平移1个单位
    full_period = 1
    while t != s and full_period < row * col:
        t = translate_2d(t, row, col, 1, 1)
        full_period += 1
    
    return full_period


import numpy as np

def linear_to_2d(i: int, row: int, col: int) -> tuple:
    """将线性索引i转换为二维坐标(row_idx, col_idx)"""
    row_idx = i // col  # 行索引（整除列数）
    col_idx = i % col   # 列索引（取余列数）
    return row_idx, col_idx

def two_d_to_linear(row_idx: int, col_idx: int, col: int) -> int:
    """将二维坐标(row_idx, col_idx)转换为线性索引i"""
    return row_idx * col + col_idx

def helement_translation_2d(Ra_row, Ra_col, Rb_row, Rb_col, 
                           l_row, l_col, k_row, k_col, row, col) -> complex:
    """
    二维平移对称性下的矩阵元相位因子（扩展一维版本）
    参数：
        Ra_row, Ra_col: 初始态的行/列平移周期
        Rb_row, Rb_col: 跃迁后态的行/列平移周期
        l_row, l_col: 跃迁在行列方向的平移步数
        k_row, k_col: 行列方向的动量量子数
        row, col: 二维格点的行数和列数
    返回：
        包含相位和归一化的矩阵元因子
    """
    # 行方向相位（2πk_row * l_row / row）
    phase_row = np.exp(1j * 2 * np.pi * k_row * l_row / row)
    # 列方向相位（2πk_col * l_col / col）
    phase_col = np.exp(1j * 2 * np.pi * k_col * l_col / col)
    # 总相位是行列相位的乘积（二维平移的叠加）
    phase = phase_row * phase_col
    
    # 归一化因子（考虑两个方向的平移周期）
    norm = np.sqrt((Ra_row / Rb_row) * (Ra_col / Rb_col))
    
    return norm * phase

def build_block_Hamiltonian_translation_2D(row: int, col: int, 
                                          reps: list, 
                                          peri_row: list, peri_col: list,  # 行/列方向的平移周期
                                          k_row: int, k_col: int,  # 二维动量（全局，而非每个态的）
                                          J: float, h: float) -> np.ndarray:
    """
    构建二维平移对称性下的块对角哈密顿量
    参数：
        row, col: 二维格点的行数和列数
        reps: 代表性构型列表（每个态是二维格点的占据模式）
        peri_row: 每个代表态的行方向平移周期
        peri_col: 每个代表态的列方向平移周期
        k_row, k_col: 选定的二维动量量子数（块对应的动量）
        J: 相邻自旋耦合强度（对角项）
        h: 跃迁强度（非对角项）
    返回：
        Hk: 块对角哈密顿量（维度为nrep×nrep）
    """
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    N = row * col  # 总格点数
    
    for ia in range(nrep):
        sa = reps[ia]  # 初始代表态
        Ra_row = peri_row[ia]  # 行方向平移周期
        Ra_col = peri_col[ia]  # 列方向平移周期
        k_row_a = k_row[ia]   # 初始态的行方向动量
        k_col_a = k_col[ia]   # 初始态的列方向动量
        
        # --------------------------
        # 1. 对角项（相互作用能）
        # 二维中相邻包括：水平（同列相邻行）和垂直（同行相邻列）
        # --------------------------
        Ez = 0.0
        for i in range(N):
            row_i, col_i = linear_to_2d(i, row, col)
            
            # a. 水平相邻（行方向：同一列，下一行）
            if row_i < row - 1:  # 非最后一行
                j = two_d_to_linear(row_i + 1, col_i, col)  # 下方格点
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                Ez += (J if ai == aj else -J)  # 自旋耦合
                
            # b. 垂直相邻（列方向：同一行，右一列）
            if col_i < col - 1:  # 非最后一列
                j = two_d_to_linear(row_i, col_i + 1, col)  # 右方格点
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                Ez += (J if ai == aj else -J)  # 自旋耦合
        
        Hk[ia, ia] += Ez  # 对角元赋值
        
        # --------------------------
        # 2. 非对角项（跃迁项）
        # 处理二维中水平和垂直方向的相邻格点跃迁
        # --------------------------
        for i in range(N):
            row_i, col_i = linear_to_2d(i, row, col)
            
            # 跃迁1：水平方向（与下方格点交换，需成对翻转）
            if row_i < row - 1:
                j = two_d_to_linear(row_i + 1, col_i, col)
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                if ai != aj:  # 只有自旋不同时才能跃迁
                    sb = (flip_state(sa, i))  # 翻转i和j的自旋
                    # 获取跃迁后态的代表元及行列平移步数
                    rep_b, l_row, l_col, _ = represent(N,sb, row, col)  # 二维represent函数
                    if rep_b in reps:
                        ib = reps.index(rep_b)
                        Rb_row = peri_row[ib]
                        Rb_col = peri_col[ib]
                        # 计算二维矩阵元（包含行列相位和归一化）
                        elem = h * helement_translation_2d(
                            Ra_row, Ra_col, Rb_row, Rb_col,
                            l_row, l_col, k_row_a, k_col_a, row, col
                        )
                        Hk[ia, ib] += elem
            
            # 跃迁2：垂直方向（与右方格点交换，需成对翻转）
            if col_i < col - 1:
                j = two_d_to_linear(row_i, col_i + 1, col)
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                if ai != aj:  # 只有自旋不同时才能跃迁
                    sb = (flip_state(sa, i))  # 翻转i的自旋
                    # 获取跃迁后态的代表元及行列平移步数
                    rep_b, l_row, l_col, _ = represent(N,sb, row, col)
                    if rep_b in reps:
                        ib = reps.index(rep_b)
                        Rb_row = peri_row[ib]
                        Rb_col = peri_col[ib]
                        # 计算二维矩阵元
                        elem = h * helement_translation_2d(
                            Ra_row, Ra_col, Rb_row, Rb_col,
                            l_row, l_col, k_row_a, k_col_a, row, col
                        )
                        Hk[ia, ib] += elem
    
    return Hk
    


#仅保留平移对称性
reprcount = []  # 全局记录列表（按需保留）


def is_k_compatible(N: int, R: int, k: int) -> bool:
    """检查动量k与轨道周期R的兼容性（与1D版本一致）"""
    return (k * R) % N == 0

# ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========
def build_translation_basis_by_k(col: int, row: int):
    """
    构建二维平移对称性下的基，按动量量子数(k_row, k_col)分块
    参考1D代码的正确做法：每个动量块包含所有兼容该动量的代表元
    """
    basis = {}
    seen = set()
    N = col * row

    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s, row, col)
        if rep in seen:
            continue
        seen.add(rep)

        # 计算轨道周期（行列方向分别计算）
        R_row, R_col = orbit_period_2d(rep, row, col)

        # 遍历所有可能的动量量子数组合
        for k_row in range(row):
            for k_col in range(col):
                # 检查动量兼容性（两个方向都要满足）
                if is_k_compatible(row, R_row, k_row) and is_k_compatible(col, R_col, k_col):
                    k = (k_row, k_col)  # 二维动量作为键
                    if k not in basis:
                        basis[k] = {
                            'repr': [],
                            'peri_row': [],
                            'peri_col': []
                        }
                    basis[k]['repr'].append(rep)
                    basis[k]['peri_row'].append(R_row)
                    basis[k]['peri_col'].append(R_col)

    return basis
def helement_translation_2d_simple(Ra_row: int, Ra_col: int, Rb_row: int, Rb_col: int,
                                  l_row: int, l_col: int, k_row: int, k_col: int,
                                  row: int, col: int) -> complex:
    """
    二维平移对称性下的矩阵元相位因子（参考1D版本）
    注意：k_row, k_col是块的固定动量，不是每个态的动量
    """
    return (np.sqrt(Ra_row / Rb_row) * np.sqrt(Ra_col / Rb_col) *
            np.exp(1j * 2 * np.pi * k_row * l_row / row) *
            np.exp(1j * 2 * np.pi * k_col * l_col / col))

def build_block_Hamiltonian_translation_2d_corrected(row: int, col: int,
                                                    reps: list,
                                                    peri_row: list, peri_col: list,
                                                    k_row: int, k_col: int,  # 块的固定动量
                                                    J: float, h: float) -> np.ndarray:
    """
    构建二维平移对称性下的块对角哈密顿量（修正版本，参考1D代码）
    关键修正：k_row, k_col是块的固定动量，不是每个态的动量
    """
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    N = row * col

    for ia in range(nrep):
        sa = reps[ia]
        Ra_row = peri_row[ia]
        Ra_col = peri_col[ia]

        # 对角项：计算二维格点上的相邻自旋相互作用
        Ez = 0.0
        for i in range(N):
            row_i, col_i = divmod(i, col)

            # 行方向相邻（周期边界条件）
            j_row = (row_i + 1) % row
            j = j_row * col + col_i
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)

            # 列方向相邻（周期边界条件）
            j_col = (col_i + 1) % col
            j = row_i * col + j_col
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)

        Hk[ia, ia] += Ez

        # 非对角项：横向场引起的自旋翻转
        for i in range(N):
            sb = flip_state(sa, i)
            rep_b, l_row, l_col, _ = represent(N, sb, row, col)
            if rep_b in reps:
                ib = reps.index(rep_b)
                Rb_row = peri_row[ib]
                Rb_col = peri_col[ib]
                elem = h * helement_translation_2d_simple(
                    Ra_row, Ra_col, Rb_row, Rb_col,
                    l_row, l_col, k_row, k_col,  # 使用块的固定动量
                    row, col
                )
                Hk[ia, ib] += elem

    return Hk
def fullspectrum_blocks_direct(col: int, row: int, J: float, h: float):
    """
    计算二维TFIM的完整能谱（修正版本，参考1D代码）
    """
    basis = build_translation_basis_by_k(col, row)
    total_dim = sum(len(v['repr']) for v in basis.values())
    N = col * row
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"

    energies = []
    labels = []

    for k, data in sorted(basis.items()):
        k_row, k_col = k  # 解包二维动量
        reps = data['repr']
        peri_row = data['peri_row']
        peri_col = data['peri_col']

        if len(reps) == 0:
            continue

        print(f"处理动量块 k=({k_row},{k_col}), 包含 {len(reps)} 个代表元")

        # 使用修正的哈密顿量构建函数
        Hk = build_block_Hamiltonian_translation_2d_corrected(
            row, col, reps, peri_row, peri_col,
            k_row, k_col,  # 块的固定动量
            J, h
        )

        w, _ = np.linalg.eig(Hk)
        print(f"块 k=({k_row},{k_col}) 的本征值数量: {len(w)}")

        energies.extend(w.real.tolist())
        labels.extend([k_row * col + k_col] * len(w))  # 将二维动量编码为标签

    return energies, labels

# 测试函数
def test_2d_corrected(row: int = 2, col: int = 2, J: float = 1.0, h: float = 0.5):
    """
    测试修正后的2D代码
    """
    print(f"测试 {row}x{col} 二维格点，J={J}, h={h}")
    print("="*50)

    # 计算能谱
    energies, labels = fullspectrum_blocks_direct(col, row, J, h)

    print(f"总本征值数量: {len(energies)}")
    print(f"期望本征值数量: {2**(row*col)}")

    # 按动量块分组显示结果
    from collections import defaultdict
    energy_by_k = defaultdict(list)
    for e, k in zip(energies, labels):
        energy_by_k[k].append(e)

    print("\n各动量块的本征值:")
    for k in sorted(energy_by_k.keys()):
        k_row, k_col = divmod(k, col)
        energies_k = sorted(energy_by_k[k])
        print(f"k=({k_row},{k_col}): {len(energies_k)} 个本征值")
        print(f"  能量: {[f'{e:.4f}' for e in energies_k[:5]]}" +
              (f" ... (共{len(energies_k)}个)" if len(energies_k) > 5 else ""))

    return energies, labels


def generate_orbit_states(row: int,col:int, rep: int):
    """生成代表元 rep 的完整平移轨道"""
    """修正后的轨道生成"""
    '''
    states = [rep]
    
    #t = translate(N, rep, 1)
    t = translate_2d(rep, row, col, 1, 0)
    count = 0
    max_iter = N  # 防止无限循环
    
    while t != rep and count < max_iter:
        states.append(t)
        #t = translate(N, t, 1)
        t = translate_2d(t, row, col, 1, 0)
        count += 1
    '''
    '''
    通过二维平移生成所有与代表态等价的态
    参数:
        rep: 代表态
        rows: 行数
        cols: 列数
        max_iter: 最大迭代次数，防止无限循环
        
    返回:
        states: 所有与代表态等价的态列表
    '''
    N = row*col
    states = []
    seen_states = set()  # 用于跟踪已经见过的状态，避免重复
    
    # 初始状态
    t = rep
    count = 0
    basis = {}
     # 尝试所有可能的行和列平移组合
    for row_shift in range(row):
        for col_shift in range(col):
            # 应用二维平移
            t_translated = translate_2d(t, row, col, col_shift, row_shift)
            #print(col,row)
            # 如果这个状态还没见过，添加到列表中 
            k = (row_shift, col_shift)  
            if k not in basis:
                #print(t,row,col,row_shift,col_shift,t_translated,translate_2d(t, row, col, col_shift, row_shift))
                
                states.append(t_translated)
                seen_states.add(t_translated)
                count += 1
                basis[k] = {'repr': [], 'row_shift_row': [], 'col_shift_col': []}
            basis[k]['repr'].append(t_translated)
            basis[k]['row_shift_row'].append(row_shift)
            basis[k]['col_shift_col'].append(col_shift)

                
            if count >= N:
                break
                
        if count >= N:
            break
    
    return basis

def build_projection_matrix_for_k(row: int, col: int, reps: list, peri_row: list, peri_col: list, k_row: list, k_col: list):
    """为特定 k 构造投影矩阵 V_k"""
    N = row*col
    dim_full = 2 ** N
    nrep = len(reps)
    V = np.zeros((dim_full, nrep), dtype=complex)
    
    
    for col_index, rep in enumerate(reps):
        # 生成该代表元的完整轨道
        orbit = generate_orbit_states(row,col, rep)
        k_col_a = k_col[col_index]
        k_row_a = k_row[col_index]
        #print(orbit)
        reps = []
        peri_row = []
        peri_col = []

        
        R = len(orbit)
        # 计算归一化因子
        norm = 1.0 / np.sqrt(R)
        for k, data in orbit.items():
            reps.append(data['repr'])
            peri_row.append(data['row_shift_row'])
            peri_col.append(data['col_shift_col'])
        # 填充轨道上的所有状态
        for k, data in orbit.items():
            reps_shift = (data['repr'])
            peri_row_shift = (data['row_shift_row'])
            peri_col_shift = (data['col_shift_col'])
            #print(reps_shift)
            phase = np.exp(-1j * 2 * np.pi * k_row_a * peri_row_shift[0] /len(peri_row) )*np.exp(-1j * 2 * np.pi * k_col_a * peri_col_shift[0] /len(peri_col) )
            V[reps_shift, col_index] = norm * phase            
        #归一化
        V[:, col] /= np.linalg.norm(V[:, col_index])
    return V


def floquet_spectrum_with_fullspace_IPR(row: int,col:int, J: float, h: float, t1: float, t2: float):
    """计算 Floquet 谱和全空间 IPR（通过 k 分块叠加）"""
    basis = build_translation_basis_by_k(col,row)
    #print(basis)
    N = row*col
    #print(basis)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    
    energies = []
    quasi_energies = []
    labels = []
    # 初始化全空间本征矢矩阵
    dim_full = 2 ** N
    #new_basis_matrix = np.zeros((dim_full, dim_full), dtype=complex)
    new_basis_matrix = []
    full_eigenvectors = np.zeros((dim_full, total_dim), dtype=complex)
    current_col = 0
    Hk_full = []
    v_full = []
    basis_for_sort = []
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri_row = data['peri_row']
        peri_col = data['peri_col']
        k_row = data['k_row']
        k_col = data['k_col']
        #print(reps)
        if len(reps) == 0:
            continue
            
        #Hk_1 = build_block_Hamiltonian_translation(row,  peri_row, k_row,col, peri_col, k_col, reps,0*J,h)
        Hk_1 = build_block_Hamiltonian_translation_2D(row, col, reps, peri_row, peri_col, k_row, k_col,0* J, h)
        #Hk_2 = build_block_Hamiltonian_translation(row,  peri_row, k_row,col, peri_col, k_col, reps,J,0*h)
        Hk_2 = build_block_Hamiltonian_translation_2D(row, col, reps, peri_row, peri_col, k_row, k_col,J, 0*h)
        Hk = scipy_expm(-1j * t1 * Hk_1) @ scipy_expm(-1j * t2 * Hk_2)
        # 拼接为完整矩阵
        if len(Hk_full) == 0:
            Hk_full = Hk
        else:
            Hk_full = block_direct_sum([Hk_full,Hk])#np.block(block_structure)

        #Hk_full = block_diag_concat([Hk_full, Hk])
        # 求解本征值和本征矢
        w, v = np.linalg.eig(Hk)
        n_eigenstates = len(w)
        
        if len(v_full) == 0:
            v_full = v
        else:
            v_full = block_direct_sum([v_full,v])#np.block(block_structure)

        # 构造投影矩阵 V_k
        V_k = build_projection_matrix_for_k(row, col, reps, peri_row, peri_col, k_row, k_col)
        #V_k = k_to_real_transform_matrix(N, reps, peri,k)
        #print(V_k)
        #for index, value in enumerate(reps):
        #    print(index,value)
        #    new_basis_matrix[:,value] = V_k[:,index]  
        #矩阵按列直接拼接
        if len(new_basis_matrix) == 0:
            new_basis_matrix = V_k
        else:
            new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）
        energies.extend(w)
        labels.extend([k] * n_eigenstates)

        basis_for_sort.extend(reps)
    # 整理能量（转换为准能）和标签
    
    # 在拼接 new_basis_matrix 后验证
    U = new_basis_matrix
    #new_basis_matrix = np.zeros((dim_full, dim_full), dtype=complex)
    #for i in range(len(basis_for_sort)):
        #print(i,basis_for_sort[i])
        #new_basis_matrix[:,basis_for_sort[i]] = U[:,i]
        #print(basis_for_sort[i])
    U_dagger = U.conj().T
    print("酉性验证（应接近单位矩阵）：\n", np.allclose(U_dagger @ U, np.eye(U.shape[1]), atol=1e-6))

    full_eigenvectors = new_basis_matrix @ v_full#np.linalg.inv(v_full) @ new_basis_matrix.T.conj() #@ new_basis_matrix.T
    quasi_energies.extend(-np.angle(energies))  # Floquet准能
    # 使用您的方法计算全空间 IPR
    #ipr1 = np.abs(full_eigenvectors) **4
    ipr1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), 
                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))
    ipr = np.sum(ipr1, axis=0)
    
    
    return quasi_energies,energies, labels, ipr, new_basis_matrix,v_full, Hk_full

row = 2
col = 3
L = row*col
#T=t1+t2
t_1 = 1
t_2 = 1
lam_h = 0.01
lam_J = 0.1
hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5
J=1#np.pi/4 - lam_J#J*t2# 0.75


Hr1,Hc1,Hd1 =get_hamiltonian_sparse(col,row,0 * J,hx)
Hr2,Hc2,Hd2 = get_hamiltonian_sparse(col,row, J,0 * hx)
#创建3X3的0矩阵
H1=np.zeros((2**L,2**L))  
H2=np.zeros((2**L,2**L))
H1[Hr1,Hc1] = Hd1
H2[Hr2,Hc2] = Hd2
H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)

E,V=np.linalg.eig(H_F)

#if __name__ == "__main__":
# 验证与全空间一致（小尺寸）
#H_full = Ham_total(N, J, h)
#eval_full, _ = np.linalg.eigh(H_full)
#eval_full = np.sort(eval_full.real)
E_blocks,E_blocks_data, labels,IPR_K,full_vecs,full_vecs_eig,Hk_full =floquet_spectrum_with_fullspace_IPR(row,col, J, hx, t_1, t_2)
quasienergy_k = E_blocks
#quasienergy_k = [0 for index in range(2**L)]
#for i in range(0,2**L,1):
    #quasienergy_k[i] = (cmath.phase(E_blocks[i]))
quasienergy1_k = quasienergy_k.copy()
#print(quasienergy_k)
basis = build_translation_basis_by_k(col,row)
#print(basis)
total_dim = sum(len(v['repr']) for v in basis.values())
print(f"N={L}，所有 k 块维度之和: {total_dim}，应为 {2**L}")
#if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):
#    print("警告：块对角化谱与全空间谱不一致！")
#    print(f"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}")
#    m = min(len(E_blocks), len(eval_full))
#    if m > 0:
#        print(f"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}")
#else:
#    print("验证成功：块对角化本征值与全空间本征值一致。")
#print(E_blocks,len(E_blocks))
    #print(eval_full)


# 1. 直接对角化全空间Floquet算子 F
#evals_direct, evecs_direct = np.linalg.eig(F)
# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列
matched_indices = []
for eval_block in E_blocks_data:  # energies是分块对角化得到的本征值（Hk的本征值）
    # 找到直接对角化中最接近的本征值索引
    idx = np.argmin(np.abs(E - eval_block))
    matched_indices.append(idx)
# 3. 按匹配顺序重新排列直接对角化的本征矢
print(matched_indices)
evecs_direct_matched = V[:, matched_indices]
# 4. 验证一致性（忽略相位，对比绝对值或内积）
# 方法1：归一化后对比绝对值
full_evecs_norm = full_vecs @ full_vecs_eig #/ np.linalg.norm(full_vecs_eig, axis=0)
direct_evecs_norm = evecs_direct_matched #/ np.linalg.norm(evecs_direct_matched, axis=0)
abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))
print(f"归一化后绝对值误差：{abs_error:.2e}")  # 正常应<1e-6

# 方法2：计算内积绝对值（应为1，说明是同一本征矢）
inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) 
                  for i in range(total_dim)]
print(f"最小内积绝对值：{min(inner_products):.2e}")  # 正常应>0.999

quasienergy = [0 for index in range(2**L)]
for i in range(0,2**L,1):
    quasienergy[i] = (-cmath.phase(E[i]))
quasienergy1 = quasienergy.copy()

IPR = []
for i in range(len(quasienergy)):
            # eigenvectors_k[:,i] 是本征矢在当前k扇区新基下的分量
            # 新基的正交性保证其他扇区分量为0，直接求和即可
            IPR1 = np.sum(np.abs(V[:, i]) **4)
            IPR.append(IPR1)
IPR = np.array(IPR)
print(IPR)

# 调用函数

ipr_full_sorted = np.sort(IPR)
ipr_block_sorted = np.sort(IPR_K)
# 步骤6：可视化对比
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))

# 子图1：排序后的IPR对比（散点图）
ax1.scatter(range(len(ipr_full_sorted)), ipr_full_sorted, label="full space", alpha=0.8, s=50)
ax1.scatter(range(len(ipr_block_sorted)), ipr_block_sorted, label="block space", alpha=0.8, s=30, marker="x")
ax1.set_xlabel("排序后的本征态索引")
ax1.set_ylabel("IPR值")
#ax1.set_title(f"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）")
ax1.legend()
ax1.grid(True, alpha=0.3)

ax2.scatter(range(len(ipr_full_sorted)), np.sort(quasienergy), label="full space", alpha=0.8, s=50)
ax2.scatter(range(len(ipr_block_sorted)), np.sort(quasienergy_k), label="block space", alpha=0.8, s=30, marker="x")
ax2.set_xlabel("排序后的本征态索引")
ax2.set_ylabel("E值")
#ax1.set_title(f"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）")
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()



# 假设分块计算的本征值和IPR为 evals_block, ipr_block
# 整体计算的为 evals_full, ipr_full
matched_ipr = []
for e_block, ipr_b in zip(E_blocks, IPR_K):
    # 找到整体计算中与e_block接近的本征值
    idx = np.argmin(np.abs(quasienergy1 - e_block))
    matched_ipr.append((ipr_b, IPR[idx]))
# 查看匹配后的IPR是否一致
print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))

# 绘制匹配后的IPR对比
plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')
plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker="x")
plt.legend()
plt.show()

# 添加测试修正后代码的部分
print("\n" + "="*80)
print("测试修正后的2D TFIM块对角化代码")
print("="*80)

# 测试小系统
print("测试 2x2 系统:")
try:
    energies_test, labels_test = test_2d_corrected(2, 2, 1.0, 0.5)
    print("✓ 2x2 系统测试成功")
except Exception as e:
    print(f"✗ 2x2 系统测试失败: {e}")

print("\n主要修正:")
print("1. 基构建函数现在正确地按(k_row, k_col)分块")
print("2. 哈密顿量构建函数使用块的固定动量，而不是每个态的动量")
print("3. 矩阵元计算遵循1D代码的正确模式")
print("4. 二维相邻相互作用正确实现（行方向和列方向）")