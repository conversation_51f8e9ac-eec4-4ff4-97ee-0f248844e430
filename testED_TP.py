import numpy as np
from scipy.sparse.linalg import eigsh

J=1
h=1
N=4

# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state
def reverseBits(s,N):
    bin_chars = ""
    temp = s
    for i in range(N):
        bin_char = bin(temp % 2)[-1]
        temp = temp // 2
        bin_chars = bin_char + bin_chars
    bits =  bin_chars.upper()
    return int(bits[::-1], 2)

def ggun(t,l,k,N):
    kk = 2*np.pi*k/N
    g=0
    if t == -1:
        g = 1-np.cos(kk*l)
    elif t==1:
        g = 1+np.cos(kk*l) 
    return g

def checkstate(s,k,N):
    R=-1
    tz=-1
    tp = -1
    tpz = -1
    smax = 2**N-1
    # 仅基于平移对称性判断周期 R（去除对总磁化的限制）
    t=s
    for i in range(1,N+1):
        t = translate(N,t,1)
        az = smax -t
        if t<s or az<s:
            break
        if t==s:
            R=i
            break
    return R,tp,tz,tpz

import numpy as np
#仅保留平移对称性
reprcount = []  # 全局记录列表（按需保留）

def findbasis(N, k, p=None, z=None):  # 1. 非平移参数p/z设为默认None（无需传入）
    repr_list = []
    typee = []
    peri = []
    mtrf = []  # 反演相关参数，后续无赋值（可保留占位）
    ntrf = []  # 自旋翻转相关参数，后续无赋值（可保留占位）
    capr = []  # 联合对称类标签，后续简化（可保留占位）
    
    for s in range(2 **N):
        # 2. 注释/删除sigma循环（自旋宇称与平移无关，无需遍历）
        # for sigma in (-1, 1):
        sigma = 1  # 固定sigma=1，避免无意义循环（仅保留形式，不影响逻辑）
        m, n = None, None
        ca = 1  # 3. 简化对称类：仅保留1类（无反演/自旋翻转对称）
        R, tp, tz, tpz = checkstate(s, k, N)  # 获取平移周期R（核心）
        
        # 4. 仅保留平移对称性的基础过滤：R>0表示满足平移对称
        if R <= -1:
            continue
        
        # 5. 注释/删除特殊k值的sigma约束（sigma已固定，该逻辑无效）
        # if (k == 0 or k == N // 2) and (sigma == -1):
        #     R = -1  # 标记为无效状态
        
        if R > 0:
            # 6. 注释/删除所有反演/自旋翻转相关的分支逻辑（ca已固定为1）
            # 原ca=2~5的分支全删除，仅保留ca=1的基础赋值
            m, n = None, None  # 反演/自旋翻转参数无意义，设为None
            
            # 7. 注释/删除反演/自旋翻转的兼容性验证（ggun与平移无关）
            # if np.isclose(ggun(...), 0, atol=1e-8):
            #     R = -1
            
            # 最终筛选：仅需ca已定义（固定为1）且R>0（平移对称有效）
            if ca is not None and R > 0:
                repr_list.append(s)
                # 8. 简化typee：仅体现ca=1和sigma=1（无需复杂计算）
                typee.append(2 * ca + (sigma + 1) / 2)
                capr.append(ca)  # 固定为1，标记仅平移对称
                peri.append(R)   # 记录平移周期（核心输出，体现平移对称性）
                mtrf.append(m)
                ntrf.append(n)
    
    nrep = len(repr_list)
    reprcount.append(repr_list)
    # 9. 简化打印：仅输出k和有效状态数（p/z已无关）
    print(f"k={k}: 有效状态数={nrep}（仅平移对称）")
    return nrep, repr_list, typee, peri, mtrf, ntrf, capr

def Ham_total(N,J,h):
    H = np.zeros((2 ** N, 2 ** N))

    # a is a basis
    for a in range(2 ** N):
        # i is position of this basis
        for i in range(N):
            # j is the nearest neighbor, mod N for periodic boundary conditions
            j = (i + 1) % N
            ai = get_site_value(a,i)
            aj = get_site_value(a,j)

            # Sz
            b = a
            if ai == aj:
                H[a, b] += J
            else:
                H[a, b] += -J

            # SxSx + SySy
            if ai != aj:
                b = flip_state(a, i)
                b = flip_state(b, j)
                H[a, b] += h
    return H

def represent(L,a0):
    at = a0
    a = a0
    l=0
    q=0
    g=0
    smax = 2**L-1
    for t in range(1,L+1):
        at = translate(L,a0,t)
        if at < a:
            a = at
            l=t
            g=0
        #az=smax-at
        #if az<a:
        #    a=az
        #   l=t
        #    g=1    
    '''at = reverseBits(a0,L)
    for t in range(L):
        if at<a:
            a=at
            l=t
            q=1
            g=0
        az=smax-at
        if az<a:
            a=az
            l=t
            q=1
            g=1
        at = translate(L, at, 1)'''
    return a,l,q,g
'''
def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):
    ca = capr[a]
    cb = capr[b]
    s = 2*(typee[a]%2)-1
    t = 2*(typee[b]%2)-1
    matelement = peri[a]/peri[b]
    if ca==2 or ca==5:
        matelement = matelement/ggun(s*p,mtrf[a],k,N)
    if ca==3 or ca==5:
        matelement = matelement/ggun(z,ntrf[a],k,N)
    if ca==4:
        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)
    if cb==2 or cb==5:
        matelement = matelement*ggun(t*p,mtrf[b],k,N)
    if cb==3 or cb==5:
        matelement = matelement*ggun(z,ntrf[b],k,N)
    if cb==4:
        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)
    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)
    if cb==1 or cb==3:
        matelement = matelement*ffun(t,s,l,k,N)
    elif cb==2 or cb==5:
        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)
    elif cb==4:
        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)
    return matelement

import numpy as np

def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, k):
    # 1. 删除与反演/自旋翻转相关的参数p、z（不再传入）
    Hk = np.zeros((nrep,) * 2).astype(complex)
    for ia in range(nrep):
        sa = repr[ia]
        # 跳过重复基矢（平移对称下可能出现的简并）
        if (ia > 1 and sa == repr[ia - 1]):
            continue
        # 确定当前基矢的简并度（na=1或2）
        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1
        
        # 2. 计算自旋-自旋相互作用对角项（仅与平移对称兼容）
        Ez = 0
        for i in range(N):
            j = (i + 1) % N  # 周期边界条件（平移对称性核心）
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            if ai == aj:
                Ez += J 
            else:
                Ez -= J 
        # 填充对角元（覆盖简并基矢）
        for a in range(ia, ia + na):
            Hk[a, a] += Ez
        
        # 3. 计算横场项（仅保留平移对称相关逻辑）
        for i in range(N):
            j = (i + 1) % N
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            if ai != aj:
                # 生成自旋翻转后的状态sb（仅与平移相关的翻转）
                if ai == 1:
                    sb = flip_state(flip_state(sa, i), j)
                else:
                    sb = flip_state(flip_state(sa, j), i)
                
                # 4. 仅用平移对称找代表态（忽略反演/自旋翻转参数）
                representative, l, _, _ = represent(N, sb)  # 忽略q、g（反演/翻转标记）
                
                if representative in repr:
                    ib = repr.index(representative)
                    # 确定目标基矢的简并度
                    if ib > 1 and repr[ib] == repr[ib - 1]:
                        ib = ib - 1
                        nb = 2
                    elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:
                        nb = 2
                    else:
                        nb = 1
                    
                    # 5. 计算矩阵元时仅保留平移相关参数（删除反演/翻转参数）
                    for ii in range(ia, ia + na):
                        for jj in range(ib, ib + nb):
                            try:
                                # 简化helement调用：仅传入平移相关参数（l是平移步数）
                                elem = h * helement_translation(ii, jj, typee, peri, mtrf, ntrf, capr,l,k,N)
                                if np.isfinite(elem):
                                    Hk[ii, jj] += elem
                            except Exception as e:
                                print(f"矩阵元计算错误 at ii={ii}, jj={jj}: {e}")
    return Hk

'''
# 6. 简化的矩阵元函数（仅考虑平移对称性）
def helement_translation(a, b, typee, peri, mtrf, ntrf, capr, l,k, N):
    # 1. 删除与反演/自旋翻转相关的参数：p, z, q, g
    # 2. 对称类简化：仅关注平移对称性（忽略ca/cb的反演/翻转分类）
    ca = capr[a]
    cb = capr[b]
    
    # 3. 自旋宇称简化：仅保留平移相关的自旋标记（或直接固定为1）
    s = 2 * (typee[a] % 2) - 1  # 可简化为 s = 1（若自旋宇称与平移无关）
    t = 2 * (typee[b] % 2) - 1  # 可简化为 t = 1
    
    # 4. 初始化矩阵元：仅保留平移周期比（核心平移对称参数）
    matelement = peri[a] / peri[b]
    
    # 5. 注释/删除所有反演/自旋翻转相关的振幅修正（基于ggun的部分）
    # 原ca=2/3/4/5的修正逻辑全部删除
    # if ca==2 or ca==5: ...
    # if ca==3 or ca==5: ...
    # if ca==4: ...
    # if cb==2 or cb==5: ...
    # if cb==3 or cb==5: ...
    # if cb==4: ...
    
    # 6. 注释/删除反演+自旋翻转的相位修正
    # 仅保留平移相关的振幅开方（若需要）
    matelement = np.sqrt(matelement)  # 若平移对称无需开方，可直接删除此步
    
    # 7. 仅保留平移跃迁的相位修正（基于ffun），删除反演/翻转相关的叠加项
    # 统一使用基础平移相位，忽略所有反演相关的term2
    matelement = matelement * ffun(t, s, l, k, N)  # l为平移步数（需从参数传入）
    
    return matelement


# 辅助函数：仅考虑平移对称性的ffun（示例实现）
def ffun(t, s, l, k, N):
    """仅计算平移l步在动量k扇区的相位（布洛赫相位）"""
    # 核心：平移l步的相位因子 e^(i * 2πk l / N)
    return np.exp(1j * 2 * np.pi * k * l / N)

# ========= 新增：仅基于平移对称与总磁化(M,k)的基与块哈密顿量 =========
def get_magnetization(state: int, N: int) -> int:
    m = 0
    for i in range(N):
        m += get_site_value(state, i)
    return m

def orbit_period(N: int, s: int) -> int:
    t = translate(N, s, 1)
    R = 1
    while t != s:
        t = translate(N, t, 1)
        R += 1
    return R

def is_k_compatible(N: int, R: int, k: int) -> bool:
    return (k * R) % N == 0

def build_translation_basis_by_Mk(N: int):
    basis = {}
    seen = set()
    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s)
        if rep in seen:
            continue
        seen.add(rep)
        R = orbit_period(N, rep)
        M = get_magnetization(rep, N)
        for k in range(N):
            if is_k_compatible(N, R, k):
                key = (M, k)
                if key not in basis:
                    basis[key] = {'repr': [], 'peri': []}
                basis[key]['repr'].append(rep)
                basis[key]['peri'].append(R)
    return basis

def generate_orbit_states(N: int, rep: int):
    states = [rep]
    t = translate(N, rep, 1)
    while t != rep:
        states.append(t)
        t = translate(N, t, 1)
    return states

def build_block_projection_matrix(N: int, reps: list, peri: list, k: int):
    dim_full = 2 ** N
    nrep = len(reps)
    V = np.zeros((dim_full, nrep), dtype=complex)
    for col, rep in enumerate(reps):
        orbit = generate_orbit_states(N, rep)
        R = len(orbit)
        #print(R,orbit)
        norm = 1.0 / np.sqrt(R)
        for r, s in enumerate(orbit):
            phase = np.exp(-1j * 2 * np.pi * k * r / N)
            V[s, col] = norm * phase
    return V

def fullspectrum_by_blocks(N: int):
    basis = build_translation_basis_by_Mk(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    H_full = Ham_total(N, J, h)
    energies = []
    labels = []
    for (M, k), data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
        V = build_block_projection_matrix(N, reps, peri, k)
        Hk = V.conj().T @ (H_full @ V)
        print(Hk)
        w, _ = np.linalg.eigh(Hk)
        #print(w)
        energies.extend(w.real.tolist())
        labels.extend([(M, k)] * len(w))
    return energies, labels
'''
def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,k):
    # 1. 删除与反演/自旋翻转相关的参数：p, z
    Hk = np.zeros((nrep,) * 2).astype(complex)
    for ia in range(nrep):
        sa = repr[ia]
        # 跳过重复基矢（平移对称下的简并态）
        if (ia > 1 and sa == repr[ia - 1]):
            continue
        # 确定当前基矢的简并度（na=1或2）
        elif (ia < nrep - 1 and sa == repr[ia + 1]):
            na = 2
        else:
            na = 1
        
        # 填充对角元（常数项，与平移对称兼容）
        for a in range(ia, ia + na):
            Hk[a, a] += (1/2) * N    
        
        # 遍历所有长程格点对（i < j）
        for i in range(N):
            for j in range(i + 1, N):
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                
                if ai != aj:  # 仅反向自旋有非零跃迁
                    # 生成自旋交换后的状态sb
                    if ai == 1:   
                        sb = flip_state(flip_state(sa, i), j)
                    else:
                        sb = flip_state(flip_state(sa, j), i)
                    
                    # 2. 仅保留平移相关的代表态参数（忽略反演/自旋翻转标记q、g）
                    representative, l, _, _ = represent(N, sb)  # 丢弃q、g
                    
                    if representative in repr:
                        ib = repr.index(representative)
                        # 确定目标基矢的简并度（nb=1或2）
                        if ib > 1 and repr[ib] == repr[ib - 1]:
                            ib = ib - 1
                            nb = 2
                        elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:
                            nb = 2
                        else:
                            nb = 1
                        
                        # 计算非对角矩阵元（仅保留平移对称修正）
                        for ii in range(ia, ia + na):
                            for jj in range(ib, ib + nb):
                                # 3. 调用简化的helement（仅传入平移相关参数）
                                Hk[ii, jj] += J * helement_translation(ii, jj, typee, peri, mtrf, ntrf, capr,l,k,N)
    return Hk

def transform(nrep,mat,vec):
    Hk = []
    a = np.transpose(vec) @ (mat @ vec)
    for i in range(nrep):
        Hk.append(a[i,i])
    return Hk    
'''
if __name__ == "__main__":
    # 全空间哈密顿量（验证用）
    H_full = Ham_total(N, J, h)
    eval_full, _ = np.linalg.eigh(H_full)
    eval_full = np.sort(eval_full.real)
    # 平移+磁化分块
    E_blocks, labels = fullspectrum_by_blocks(N)
    E_blocks = np.sort(np.array(E_blocks))
    # 维度核对
    basis = build_translation_basis_by_Mk(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    print(f"N={N}，所有 (M,k) 块维度之和: {total_dim}，应为 {2**N}")
    # 谱一致性
    if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):
        print("警告：块对角化谱与全空间谱不一致！")
        print(f"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}")
        m = min(len(E_blocks), len(eval_full))
        if m > 0:
            print(f"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}")
    else:
        print("验证成功：块对角化本征值与全空间本征值一致。")
        #print(E_blocks)
        #print(eval_full)

