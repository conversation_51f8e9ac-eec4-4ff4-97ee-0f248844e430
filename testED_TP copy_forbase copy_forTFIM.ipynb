import numpy as np
from scipy.sparse.linalg import eigsh
from scipy.sparse.linalg import expm

J=1
h=1
N=3

# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state

#仅保留平移对称性
reprcount = []  # 全局记录列表（按需保留）

def Ham_total(N,J,h):
    H = np.zeros((2 ** N, 2 ** N))

    # a is a basis
    for a in range(2 ** N):
        # i is position of this basis
        for i in range(N):
            # j is the nearest neighbor, mod N for periodic boundary conditions
            j = (i + 1) % N
            ai = get_site_value(a,i)
            aj = get_site_value(a,j)

            # Sz
            b = a
            if ai == aj:
                H[a, b] += J
            else:
                H[a, b] += -J

            # SxSx + SySy
            #if ai != aj:
            b = flip_state(a, i)
            #b = flip_state(b, j)
            H[a, b] += h
    return H

def represent(L,a0):
    at = a0
    a = a0
    l=0
    q=0
    g=0
    smax = 2**L-1
    for t in range(1,L+1):
        at = translate(L,a0,t)
        if at < a:
            a = at
            l=t
            g=0
        #az=smax-at
        #if az<a:
        #    a=az
        #   l=t
        #    g=1    
    return a,l,q,g

def orbit_period(N: int, s: int) -> int:
        t = translate(N, s, 1)
        R = 1
        while t != s:
            t = translate(N, t, 1)
            R += 1
        return R
def get_magnetization(state: int, N: int) -> int:
    m = 0
    for i in range(N):
        m += get_site_value(state, i)
    return m

def is_k_compatible(N: int, R: int, k: int) -> bool:
    return (k * R) % N == 0

# ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========
def build_translation_basis_by_k(N: int):
    basis = {}
    seen = set()
    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s)
        if rep in seen:
            continue
        seen.add(rep)
        R = orbit_period(N, rep)
        for k in range(N):
            if is_k_compatible(N, R, k):
                if k not in basis:
                    basis[k] = {'repr': [], 'peri': []}
                basis[k]['repr'].append(rep)
                basis[k]['peri'].append(R)
    return basis

def helement_translation_simple(Ra: int, Rb: int, l: int, k: int, N: int):
        return np.sqrt(Ra / Rb) * np.exp(1j * 2 * np.pi * k * l / N)

def block_direct_sum(blocks):
    """
    沿对角线构造块直和矩阵（块对角矩阵）
    
    参数:
        blocks: 子矩阵列表，每个元素为numpy数组（方阵）
    返回:
        块直和矩阵（对角线上是输入的子矩阵，其余为0）
    """
    # 检查输入是否为空
    if not blocks:
        return np.array([], dtype=float)
    
    # 检查所有子矩阵是否为方阵
    for i, b in enumerate(blocks):
        if b.ndim != 2 or b.shape[0] != b.shape[1]:
            raise ValueError(f"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）")
    
    # 初始化结果矩阵（从空矩阵开始）
    result = np.array([], dtype=blocks[0].dtype)
    
    for block in blocks:
        m = block.shape[0]  # 当前块的维度
        if result.size == 0:
            # 第一次拼接：直接用当前块作为初始矩阵
            result = block.copy()
        else:
            # 非第一次拼接：构造新的块对角矩阵
            n = result.shape[0]  # 现有矩阵的维度
            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）
            new_result = np.zeros((n + m, n + m), dtype=result.dtype)
            # 填充左上角为现有矩阵
            new_result[:n, :n] = result
            # 填充右下角为当前块
            new_result[n:, n:] = block
            result = new_result
    
    return result

def build_block_Hamiltonian_translation(N: int, reps: list, peri: list, k: int):
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    for ia in range(nrep):
        sa = reps[ia]
        Ra = peri[ia]
        # 对角项（SzSz 总和，平移不变，取代表元即可）
        Ez = 0.0
        for i in range(N):
            j = (i + 1) % N
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)
        Hk[ia, ia] += Ez
        # 非对角：当相邻不同则成对翻转（与文件定义一致）
        for i in range(N):
            j = (i + 1) % N
            #ai = get_site_value(sa, i)
            #aj = get_site_value(sa, j)
            #if ai != aj:
            sb = flip_state(sa, i)
            #sb = flip_state(flip_state(sa, i), j)
            rep_b, l, _, _ = represent(N, sb)
            if rep_b in reps:
                ib = reps.index(rep_b)
                Rb = peri[ib]
                elem = h * helement_translation_simple(Ra, Rb, l, k, N)
                Hk[ia, ib] += elem
    return Hk

def build_projection_matrix_for_k(N: int, reps: list, peri: list, k: int):
    """为特定 k 构造投影矩阵 V_k"""
    dim_full = 2 ** N
    nrep = len(reps)
    V = np.zeros((dim_full, nrep), dtype=complex)
    
    for col, rep in enumerate(reps):
        # 生成该代表元的完整轨道
        orbit = generate_orbit_states(N, rep)
        R = len(orbit)
        norm = 1.0 / np.sqrt(N*N / R)
        
        # 填充轨道上的所有状态
        for r, s in enumerate(orbit):
            phase = np.exp(-1j * 2 * np.pi * k * r / N)
            V[s, col] = norm * phase
    return V
def generate_orbit_states(N: int, rep: int):
    """生成代表元 rep 的完整平移轨道"""
    """修正后的轨道生成"""
    states = [rep]
    t = translate(N, rep, 1)
    count = 0
    max_iter = N  # 防止无限循环
    
    while t != rep and count < max_iter:
        states.append(t)
        t = translate(N, t, 1)
        count += 1
    
    if count >= max_iter:
        print(f"警告：轨道生成可能有问题，rep={rep}")
    
    return states

def fullspectrum_blocks_direct(N: int):
    basis = build_translation_basis_by_k(N)
    total_dim = sum(len(v['repr']) for v in basis.values())
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"
    energies = []
    labels = []
    v_full = []
    new_basis_matrix = []
    for k, data in sorted(basis.items()):
        reps = data['repr']
        peri = data['peri']
        if len(reps) == 0:
            continue
        Hk = build_block_Hamiltonian_translation(N, reps, peri, k)
        w, v = np.linalg.eig((Hk))
        if len(v_full) == 0:
            v_full = v
        else:
            v_full = block_direct_sum([v_full,v])#np.block(block_structure)
        # 构造投影矩阵 V_k
        V_k = build_projection_matrix_for_k(N, reps, peri, k)
        #矩阵按列直接拼接
        if len(new_basis_matrix) == 0:
            new_basis_matrix = V_k
        else:
            new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）
        
        print(len(w))
        energies.extend(w.real.tolist())
        labels.extend([k] * len(w))
    return energies, labels , v_full, new_basis_matrix


if __name__ == "__main__":
    # 验证与全空间一致（小尺寸）
    H_full = Ham_total(N, J, h)
    eval_full_data, v_full = np.linalg.eig((H_full))
    eval_full = np.sort(eval_full_data.real)
    E_blocks_data, labels,v_block_full,U_transform = fullspectrum_blocks_direct(N)
    E_blocks = np.sort(np.array(E_blocks_data))
    basis = build_translation_basis_by_k(N)
    print(basis)
    total_dim = sum(len(v['repr']) for v in basis.values())
    print(f"N={N}，所有 k 块维度之和: {total_dim}，应为 {2**N}")
    if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):
        print("警告：块对角化谱与全空间谱不一致！")
        print(f"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}")
        m = min(len(E_blocks), len(eval_full))
        if m > 0:
            print(f"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}")
    else:
        print("验证成功：块对角化本征值与全空间本征值一致。")
    print(E_blocks_data,len(E_blocks))
    print(eval_full_data)


import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def plot_matrix(matrix, title="矩阵热图", cmap="viridis", annot=True, figsize=(8, 6)):
    """
    可视化矩阵，用颜色深浅表示元素值差异
    
    参数:
        matrix: 待可视化的NumPy矩阵
        title: 图表标题
        cmap: 颜色映射（如"viridis", "coolwarm", "RdBu"）
        annot: 是否在单元格中显示数值
        figsize: 图表尺寸
    """
    # 设置绘图风格
    plt.style.use("default")
    
    # 创建画布
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制热图
    sns.heatmap(
        matrix,
        annot=annot,          # 显示数值
        fmt=".2f",            # 数值格式（保留2位小数）
        cmap=cmap,            # 颜色映射
        cbar=True,            # 显示颜色条
        square=True,          # 单元格为正方形
        ax=ax,                # 子图对象
        linewidths=0.5,       # 单元格边框宽度
        linecolor="gray"      # 单元格边框颜色
    )
    
    # 设置标题和标签
    ax.set_title(title, fontsize=14, pad=10)
    ax.set_xlabel("列索引", fontsize=12, labelpad=10)
    ax.set_ylabel("行索引", fontsize=12, labelpad=10)
    
    # 调整布局
    plt.tight_layout()
    
    return fig

fig = plot_matrix(
        np.abs(U_transform @ v_block_full ),
        #np.abs((U_transform @ v_block_full ) - v_full),
        #np.abs(v_full),
        #np.abs(full_vecs_eig),
        #np.abs(full_vecs.T.conj() @ V),
        #np.abs(V @ np.linalg.inv(V)),
        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),
        #np.abs(H_F),
        #np.abs(V ** 4),
        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),
        #np.abs(H_F),
        #np.abs(np.linalg.inv(v_block_full) @ np.linalg.inv(U_transform) @ (v_full @ np.diag(eval_full)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  - np.diag(E_blocks_data)),
        #np.abs(Hk_full),
        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),
        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),
        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),
        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),
        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),
        #np.abs(np.diag(E_blocks_data - E )),
        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),
        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),
        #np.abs(np.diag(E_blocks_data) - np.diag(E)),
        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),
        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),
        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),
        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),
        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),
        title="示例矩阵热图（颜色表示元素值大小）",
        cmap="coolwarm"  # 冷暖色映射（正值暖色，负值冷色）
    )
    
# 显示图像
plt.show()

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def plot_matrix(matrix, title="矩阵热图", cmap="viridis", annot=True, figsize=(8, 6)):
    """
    可视化矩阵，用颜色深浅表示元素值差异
    
    参数:
        matrix: 待可视化的NumPy矩阵
        title: 图表标题
        cmap: 颜色映射（如"viridis", "coolwarm", "RdBu"）
        annot: 是否在单元格中显示数值
        figsize: 图表尺寸
    """
    # 设置绘图风格
    plt.style.use("default")
    
    # 创建画布
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制热图
    sns.heatmap(
        matrix,
        annot=annot,          # 显示数值
        fmt=".2f",            # 数值格式（保留2位小数）
        cmap=cmap,            # 颜色映射
        cbar=True,            # 显示颜色条
        square=True,          # 单元格为正方形
        ax=ax,                # 子图对象
        linewidths=0.5,       # 单元格边框宽度
        linecolor="gray"      # 单元格边框颜色
    )
    
    # 设置标题和标签
    ax.set_title(title, fontsize=14, pad=10)
    ax.set_xlabel("列索引", fontsize=12, labelpad=10)
    ax.set_ylabel("行索引", fontsize=12, labelpad=10)
    
    # 调整布局
    plt.tight_layout()
    
    return fig

fig = plot_matrix(
        #np.abs(U_transform @ v_block_full ),
        #np.abs((U_transform @ v_block_full ) - v_full),
        np.abs(v_full),
        #np.abs(full_vecs_eig),
        #np.abs(full_vecs.T.conj() @ V),
        #np.abs(V @ np.linalg.inv(V)),
        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),
        #np.abs(H_F),
        #np.abs(V ** 4),
        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),
        #np.abs(H_F),
        #np.abs(np.linalg.inv(v_block_full) @ np.linalg.inv(U_transform) @ (v_full @ np.diag(eval_full)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  - np.diag(E_blocks_data)),
        #np.abs(Hk_full),
        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),
        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),
        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),
        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),
        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),
        #np.abs(np.diag(E_blocks_data - E )),
        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),
        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),
        #np.abs(np.diag(E_blocks_data) - np.diag(E)),
        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),
        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),
        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),
        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),
        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),
        title="示例矩阵热图（颜色表示元素值大小）",
        cmap="coolwarm"  # 冷暖色映射（正值暖色，负值冷色）
    )
    
# 显示图像
plt.show()

U_transform @ v_block_full[:,0]

v_full[:,0]

# 1. 直接对角化全空间Floquet算子 F
#evals_direct, evecs_direct = np.linalg.eig(F)
# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列
matched_indices = []
for eval_block in E_blocks_data:  # energies是分块对角化得到的本征值（Hk的本征值）
    # 找到直接对角化中最接近的本征值索引
    idx = np.argmin(np.abs(eval_full_data - eval_block))
    matched_indices.append(idx)
# 3. 按匹配顺序重新排列直接对角化的本征矢
print(matched_indices)
evecs_direct_matched = v_full[:, matched_indices]
# 4. 验证一致性（忽略相位，对比绝对值或内积）
# 方法1：归一化后对比绝对值
full_evecs_norm = U_transform @ v_block_full 
direct_evecs_norm = evecs_direct_matched 
abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))
print(f"归一化后绝对值误差：{abs_error:.2e}")  # 正常应<1e-6

# 方法2：计算内积绝对值（应为1，说明是同一本征矢）
inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) 
                  for i in range(total_dim)]
print(f"最小内积绝对值：{min(inner_products):.2e}")  # 正常应>0.999

ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), 
                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))
IPR_block_full = np.sum(ipr1, axis=0)
ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), 
                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))
IPR_full = np.sum(ipr2, axis=0)

# 假设分块计算的本征值和IPR为 evals_block, ipr_block
# 整体计算的为 evals_full, ipr_full
matched_ipr = []
for e_block, ipr_b in zip(E_blocks_data, IPR_block_full):
    # 找到整体计算中与e_block接近的本征值
    idx = np.argmin(np.abs(eval_full_data - e_block))
    matched_ipr.append((ipr_b, IPR_full[idx]))
# 查看匹配后的IPR是否一致
print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))

# 绘制匹配后的IPR对比
plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')
plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker="x")
plt.legend()
plt.show()