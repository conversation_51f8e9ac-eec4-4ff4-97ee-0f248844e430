{"cells": [{"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy\n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:2: SyntaxWarning: invalid escape sequence '\\s'\n", "<>:2: SyntaxWarning: invalid escape sequence '\\s'\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1779296297.py:2: SyntaxWarning: invalid escape sequence '\\s'\n", "  '''\n"]}], "source": ["def get_hamiltonian_sparse(L, Jz, hx):\n", "    '''\n", "    Creates the Hamiltonian of the Transverse Field Ising model\n", "    on a linear chain lattice with periodic boundary conditions.\n", "\n", "    The Hamiltonian is given by:\n", "    H = -Jz \\sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \\sum_{i=1}^{L} S_i^x - hz \\sum_{i=1}^{L} S_i^z\n", "    Args:\n", "        L (int): length of chain\n", "        J (float): coupling constant for Ising term\n", "        hx (float): coupling constant for transverse field\n", "\n", "    Returns:\n", "        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:\n", "        hamiltonian_rows (list of ints): row index of non-zero elements\n", "        hamiltonian_cols (list of ints): column index of non-zero elements\n", "        hamiltonian_data (list of floats): value of non-zero elements\n", "    '''\n", "\n", "    def get_site_value(state, site):\n", "        ''' Function to get local value at a given site '''\n", "        return (state >> site) & 1\n", "        #返回值为state的第site位上的二进制值\n", "\n", "    def hilbertspace_dimension(L):\n", "        ''' return dimension of hilbertspace '''\n", "        return 2**L\n", "\n", "    def flip_state(state: int, index: int) -> int:\n", "        \"\"\"翻转一个整数某位置处的二进制值\"\"\"\n", "        mask = 1 << index\n", "        return state ^ mask\n", "    # Define chain lattice\n", "    #ising_bonds = [(site, (site+1)%L) for site in range(L-1)]#开放边条件\n", "    ising_bonds = [(site, (site+1)%L) for site in range(L)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0\n", "\n", "    # Empty lists for sparse matrix\n", "    hamiltonian_rows = []\n", "    hamiltonian_cols = []\n", "    hamiltonian_data = []\n", "    \n", "    # Run through all spin configurations\n", "    for state in range(hilbertspace_dimension(L)):\n", "\n", "        # Apply Ising bonds\n", "        ising_diagonal = 0\n", "        for bond in ising_bonds:\n", "            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):\n", "                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5\n", "            else:\n", "                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5\n", "        hamiltonian_rows.append(state)\n", "        hamiltonian_cols.append(state)\n", "        hamiltonian_data.append(ising_diagonal)\n", "\n", "        # Apply transverse field\n", "        for site in range(L):\n", "            # Flip spin at site\n", "            new_state = flip_state(state,site)#state ^ (1 << site)\n", "            hamiltonian_rows.append(new_state)\n", "            hamiltonian_cols.append(state)\n", "            hamiltonian_data.append(hx)#(mid3[state])#hx\n", "    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["def block_direct_sum(blocks):\n", "    \"\"\"\n", "    沿对角线构造块直和矩阵（块对角矩阵）\n", "    \n", "    参数:\n", "        blocks: 子矩阵列表，每个元素为numpy数组（方阵）\n", "    返回:\n", "        块直和矩阵（对角线上是输入的子矩阵，其余为0）\n", "    \"\"\"\n", "    # 检查输入是否为空\n", "    if not blocks:\n", "        return np.array([], dtype=float)\n", "    \n", "    # 检查所有子矩阵是否为方阵\n", "    for i, b in enumerate(blocks):\n", "        if b.ndim != 2 or b.shape[0] != b.shape[1]:\n", "            raise ValueError(f\"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）\")\n", "    \n", "    # 初始化结果矩阵（从空矩阵开始）\n", "    result = np.array([], dtype=blocks[0].dtype)\n", "    \n", "    for block in blocks:\n", "        m = block.shape[0]  # 当前块的维度\n", "        if result.size == 0:\n", "            # 第一次拼接：直接用当前块作为初始矩阵\n", "            result = block.copy()\n", "        else:\n", "            # 非第一次拼接：构造新的块对角矩阵\n", "            n = result.shape[0]  # 现有矩阵的维度\n", "            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）\n", "            new_result = np.zeros((n + m, n + m), dtype=result.dtype)\n", "            # 填充左上角为现有矩阵\n", "            new_result[:n, :n] = result\n", "            # 填充右下角为当前块\n", "            new_result[n:, n:] = block\n", "            result = new_result\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["#引入T平移对称性下的分块矩阵\n", "# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "#仅保留平移对称性\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H\n", "def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        #az=smax-at\n", "        #if az<a:\n", "        #    a=az\n", "        #   l=t\n", "        #    g=1    \n", "    return a,l,q,g\n", "def orbit_period(N: int, s: int) -> int:\n", "        t = translate(N, s, 1)\n", "        R = 1\n", "        while t != s:\n", "            t = translate(N, t, 1)\n", "            R += 1\n", "        return R\n", "def get_magnetization(state: int, N: int) -> int:\n", "    m = 0\n", "    for i in range(N):\n", "        m += get_site_value(state, i)\n", "    return m\n", "\n", "def is_k_compatible(N: int, R: int, k: int) -> bool:\n", "    return (k * R) % N == 0\n", "\n", "# ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========\n", "def build_translation_basis_by_k(N: int):\n", "    basis = {}\n", "    seen = set()\n", "    for s in range(2 ** N):\n", "        rep, _, _, _ = represent(N, s)\n", "        if rep in seen:\n", "            continue\n", "        seen.add(rep)\n", "        R = orbit_period(N, rep)\n", "        for k in range(N):\n", "            if is_k_compatible(N, R, k):\n", "                if k not in basis:\n", "                    basis[k] = {'repr': [], 'peri': []}\n", "                basis[k]['repr'].append(rep)\n", "                basis[k]['peri'].append(R)\n", "    return basis\n", "def helement_translation_simple(Ra: int, Rb: int, l: int, k: int, N: int):\n", "        return np.sqrt(Ra / Rb) * np.exp(1j * 2 * np.pi * k * l / N)\n", "def build_block_Hamiltonian_translation(N: int, reps: list, peri: list, k: int,J,h):\n", "    nrep = len(reps)\n", "    Hk = np.zeros((nrep, nrep), dtype=complex)\n", "    for ia in range(nrep):\n", "        sa = reps[ia]\n", "        Ra = peri[ia]\n", "        # 对角项（SzSz 总和，平移不变，取代表元即可）\n", "        Ez = 0.0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            Ez += (J if ai == aj else -J)\n", "        Hk[ia, ia] += Ez\n", "        # 非对角：当相邻不同则成对翻转（与文件定义一致）\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            sb = flip_state(sa, i)\n", "            #sb = flip_state(flip_state(sa, i), j)\n", "            rep_b, l, _, _ = represent(N, sb)\n", "            if rep_b in reps:\n", "                ib = reps.index(rep_b)\n", "                Rb = peri[ib]\n", "                elem = h * helement_translation_simple(Ra, Rb, l, k, N)\n", "                Hk[ia, ib] += elem\n", "    return Hk\n", "def fullspectrum_blocks_direct(N: int):\n", "    basis = build_translation_basis_by_k(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    energies = []\n", "    labels = []\n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "        Hk = build_block_Hamiltonian_translation(N, reps, peri, k)\n", "        w, _ = np.linalg.eig((Hk))\n", "        print(len(w))\n", "        energies.extend(w.real.tolist())\n", "        labels.extend([k] * len(w))\n", "    return energies, labels\n", "  \n", "    \n", "        #通过块对角化的本征矢求出全空间的本征矢\n", "        #V = np.zeros((2 ** N, len(reps)), dtype=complex)\n", "        #for i, rep in enumerate(reps):\n", "        #    V[rep, i] = 1\n", "        #V = V @ Hk\n", "        #V = V @ np.linalg.inv(Hk_1) @ np.linalg.inv(Hk_2)\n", "        #V = V @ np.linalg.inv(expm(-1j * t1 * Hk_1)) @ np.linalg.inv(expm(-1j * t2 * Hk_2))\n"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [], "source": ["def build_projection_matrix_for_k(N: int, reps: list, peri: list, k: int):\n", "    \"\"\"为特定 k 构造投影矩阵 V_k\"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    \n", "    \n", "    for col, rep in enumerate(reps):\n", "        # 生成该代表元的完整轨道\n", "        orbit = generate_orbit_states(N, rep)\n", "        #print(orbit)\n", "        \n", "        R = len(orbit)\n", "        norm = 1.0 / np.sqrt(N**2/R)\n", "        \n", "        # 填充轨道上的所有状态\n", "        for r, s in enumerate(orbit):\n", "            phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "            V[s, col] = norm * phase\n", "        #归一化\n", "        V[:, col] /= np.linalg.norm(V[:, col])\n", "    return V\n", "def generate_orbit_states(N: int, rep: int):\n", "    \"\"\"生成代表元 rep 的完整平移轨道\"\"\"\n", "    \"\"\"修正后的轨道生成\"\"\"\n", "    states = [rep]\n", "    t = translate(N, rep, 1)\n", "    count = 0\n", "    max_iter = N  # 防止无限循环\n", "    \n", "    while t != rep and count < max_iter:\n", "        states.append(t)\n", "        t = translate(N, t, 1)\n", "        count += 1\n", "    \n", "    if count >= max_iter:\n", "        print(f\"警告：轨道生成可能有问题，rep={rep}\")\n", "    \n", "    return states"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [], "source": ["def floquet_spectrum_with_fullspace_IPR(N: int, J: float, h: float, t1: float, t2: float):\n", "    \"\"\"计算 Floquet 谱和全空间 IPR（通过 k 分块叠加）\"\"\"\n", "    basis = build_translation_basis_by_k(N)\n", "    print(basis)\n", "    #print(basis)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    \n", "    energies = []\n", "    quasi_energies = []\n", "    labels = []\n", "    # 初始化全空间本征矢矩阵\n", "    dim_full = 2 ** N\n", "    #new_basis_matrix = np.zeros((dim_full, dim_full), dtype=complex)\n", "    new_basis_matrix = []\n", "    full_eigenvectors = np.zeros((dim_full, total_dim), dtype=complex)\n", "    current_col = 0\n", "    Hk_full = []\n", "    v_full = []\n", "    basis_for_sort = []\n", "    for k, data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        #print(reps)\n", "        if len(reps) == 0:\n", "            continue\n", "            \n", "        Hk_1 = build_block_Hamiltonian_translation(N, reps, peri, k, 0*J, h)\n", "        Hk_2 = build_block_Hamiltonian_translation(N, reps, peri, k, J, 0*h)\n", "        Hk = scipy_expm(-1j * t1 * Hk_1) @ scipy_expm(-1j * t2 * Hk_2)\n", "        # 拼接为完整矩阵\n", "        if len(Hk_full) == 0:\n", "            Hk_full = Hk\n", "        else:\n", "            Hk_full = block_direct_sum([Hk_full,Hk])#np.block(block_structure)\n", "\n", "        #Hk_full = block_diag_concat([Hk_full, Hk])\n", "        # 求解本征值和本征矢\n", "        w, v = np.linalg.eig(Hk)\n", "        n_eigenstates = len(w)\n", "        \n", "        if len(v_full) == 0:\n", "            v_full = v\n", "        else:\n", "            v_full = block_direct_sum([v_full,v])#np.block(block_structure)\n", "\n", "        # 构造投影矩阵 V_k\n", "        V_k = build_projection_matrix_for_k(N, reps, peri, k)\n", "        #V_k = k_to_real_transform_matrix(N, reps, peri,k)\n", "        #print(V_k)\n", "        #for index, value in enumerate(reps):\n", "        #    print(index,value)\n", "        #    new_basis_matrix[:,value] = V_k[:,index]  \n", "        #矩阵按列直接拼接\n", "        if len(new_basis_matrix) == 0:\n", "            new_basis_matrix = V_k\n", "        else:\n", "            new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）\n", "        energies.extend(w)\n", "        labels.extend([k] * n_eigenstates)\n", "\n", "        basis_for_sort.extend(reps)\n", "    # 整理能量（转换为准能）和标签\n", "    basis_m = []\n", "    for i in range(len(energies)):\n", "        basis_m.append(translate(N, basis_for_sort[i], labels[i]))\n", "    basis_for_sort = basis_m\n", "    \n", "    # 在拼接 new_basis_matrix 后验证\n", "    U = new_basis_matrix\n", "    #new_basis_matrix = np.zeros((dim_full, dim_full), dtype=complex)\n", "    #for i in range(len(basis_for_sort)):\n", "        #print(i,basis_for_sort[i])\n", "        #new_basis_matrix[:,basis_for_sort[i]] = U[:,i]\n", "        #print(basis_for_sort[i])\n", "    U_dagger = U.conj().T\n", "    print(\"酉性验证（应接近单位矩阵）：\\n\", np.allclose(U_dagger @ U, np.eye(U.shape[1]), atol=1e-6))\n", "\n", "    full_eigenvectors = new_basis_matrix @ v_full#np.linalg.inv(v_full) @ new_basis_matrix.T.conj() #@ new_basis_matrix.T\n", "    quasi_energies.extend(-np.angle(energies))  # Floquet准能\n", "    # 使用您的方法计算全空间 IPR\n", "    #ipr1 = np.abs(full_eigenvectors) **4\n", "    ipr1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), \n", "                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))\n", "    ipr = np.sum(ipr1, axis=0)\n", "    \n", "    \n", "    return quasi_energies,energies, labels, ipr, new_basis_matrix,v_full, Hk_full"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["7//2"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [], "source": ["L = 4\n", "#T=t1+t2\n", "t_1 = 1\n", "t_2 = 1\n", "lam_h = 0.01\n", "lam_J = 0.1\n", "hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J=np.pi/4 - lam_J#J*t2# 0.75\n", "\n", "\n", "Hr1,Hc1,Hd1 = get_hamiltonian_sparse(L,0 * J,hx)\n", "Hr2,Hc2,Hd2 = get_hamiltonian_sparse(L,J,0 * hx)\n", "#创建3X3的0矩阵\n", "H1=np.zeros((2**L,2**L))  \n", "H2=np.zeros((2**L,2**L))\n", "H1[Hr1,Hc1] = Hd1\n", "H2[Hr2,Hc2] = Hd2\n", "H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [], "source": ["E,V=np.linalg.eig(H_F)"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0: {'repr': [0, 1, 3, 5, 7, 15], 'peri': [1, 4, 4, 2, 4, 1]}, 1: {'repr': [1, 3, 7], 'peri': [4, 4, 4]}, 2: {'repr': [1, 3, 5, 7], 'peri': [4, 4, 2, 4]}, 3: {'repr': [1, 3, 7], 'peri': [4, 4, 4]}}\n", "酉性验证（应接近单位矩阵）：\n", " False\n", "N=4，所有 k 块维度之和: 16，应为 16\n"]}], "source": ["#if __name__ == \"__main__\":\n", "# 验证与全空间一致（小尺寸）\n", "#H_full = Ham_total(N, J, h)\n", "#eval_full, _ = np.linalg.eigh(H_full)\n", "#eval_full = np.sort(eval_full.real)\n", "E_blocks,E_blocks_data, labels,IPR_K,full_vecs,full_vecs_eig,Hk_full = floquet_spectrum_with_fullspace_IPR (L, J, hx, t_1, t_2)\n", "quasienergy_k = E_blocks\n", "#quasienergy_k = [0 for index in range(2**L)]\n", "#for i in range(0,2**L,1):\n", "    #quasienergy_k[i] = (cmath.phase(E_blocks[i]))\n", "quasienergy1_k = quasienergy_k.copy()\n", "#print(quasienergy_k)\n", "basis = build_translation_basis_by_k(L)\n", "#print(basis)\n", "total_dim = sum(len(v['repr']) for v in basis.values())\n", "print(f\"N={L}，所有 k 块维度之和: {total_dim}，应为 {2**L}\")\n", "#if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):\n", "#    print(\"警告：块对角化谱与全空间谱不一致！\")\n", "#    print(f\"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}\")\n", "#    m = min(len(E_blocks), len(eval_full))\n", "#    if m > 0:\n", "#        print(f\"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}\")\n", "#else:\n", "#    print(\"验证成功：块对角化本征值与全空间本征值一致。\")\n", "#print(E_blocks,len(E_blocks))\n", "    #print(eval_full)"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 5, 4, 3, 2, 14, 10, 11, 8, 14, 6, 15, 13, 10, 11]\n", "归一化后绝对值误差：1.41e+00\n", "最小内积绝对值：1.04e-04\n"]}], "source": ["# 1. 直接对角化全空间Floquet算子 F\n", "#evals_direct, evecs_direct = np.linalg.eig(F)\n", "# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列\n", "matched_indices = []\n", "for eval_block in E_blocks_data:  # energies是分块对角化得到的本征值（Hk的本征值）\n", "    # 找到直接对角化中最接近的本征值索引\n", "    idx = np.argmin(np.abs(E - eval_block))\n", "    matched_indices.append(idx)\n", "# 3. 按匹配顺序重新排列直接对角化的本征矢\n", "print(matched_indices)\n", "evecs_direct_matched = V[:, matched_indices]\n", "# 4. 验证一致性（忽略相位，对比绝对值或内积）\n", "# 方法1：归一化后对比绝对值\n", "full_evecs_norm = full_vecs @ full_vecs_eig #/ np.linalg.norm(full_vecs_eig, axis=0)\n", "direct_evecs_norm = evecs_direct_matched #/ np.linalg.norm(evecs_direct_matched, axis=0)\n", "abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))\n", "print(f\"归一化后绝对值误差：{abs_error:.2e}\")  # 正常应<1e-6\n", "\n", "# 方法2：计算内积绝对值（应为1，说明是同一本征矢）\n", "inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) \n", "                  for i in range(total_dim)]\n", "print(f\"最小内积绝对值：{min(inner_products):.2e}\")  # 正常应>0.999"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [], "source": ["#full_vecs_eig = full_vecs_eig[:, matched_indices]"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/3674273654.py:41: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def plot_matrix(matrix, title=\"矩阵热图\", cmap=\"viridis\", annot=True, figsize=(8, 6)):\n", "    \"\"\"\n", "    可视化矩阵，用颜色深浅表示元素值差异\n", "    \n", "    参数:\n", "        matrix: 待可视化的NumPy矩阵\n", "        title: 图表标题\n", "        cmap: 颜色映射（如\"viridis\", \"coolwarm\", \"RdBu\"）\n", "        annot: 是否在单元格中显示数值\n", "        figsize: 图表尺寸\n", "    \"\"\"\n", "    # 设置绘图风格\n", "    plt.style.use(\"default\")\n", "    \n", "    # 创建画布\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # 绘制热图\n", "    sns.heatmap(\n", "        matrix,\n", "        annot=annot,          # 显示数值\n", "        fmt=\".2f\",            # 数值格式（保留2位小数）\n", "        cmap=cmap,            # 颜色映射\n", "        cbar=True,            # 显示颜色条\n", "        square=True,          # 单元格为正方形\n", "        ax=ax,                # 子图对象\n", "        linewidths=0.5,       # 单元格边框宽度\n", "        linecolor=\"gray\"      # 单元格边框颜色\n", "    )\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=14, pad=10)\n", "    ax.set_xlabel(\"列索引\", fontsize=12, labelpad=10)\n", "    ax.set_ylabel(\"行索引\", fontsize=12, labelpad=10)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig\n", "\n", "fig = plot_matrix(\n", "        #np.abs(full_vecs @ full_vecs_eig - V),\n", "        #np.abs((full_vecs @ full_vecs_eig )) - np.abs(V),\n", "        #np.abs(V),\n", "        np.abs(full_vecs),\n", "        #np.abs(full_vecs_eig),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(direct_evecs_norm),\n", "        #np.abs(full_evecs_norm),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs.T.conj() @ V @ np.diag(E) @ np.linalg.inv(V) @ full_vecs),\n", "        #np.abs(V @ np.linalg.inv(V)),\n", "        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),\n", "        #np.abs( np.linalg.inv(full_vecs) @ H_F @ full_vecs - Hk_full ),\n", "        #np.abs(H_F),\n", "        #np.abs(V ** 4),\n", "        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(H_F),\n", "        #np.abs(np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) @ (V @ np.diag(E)) @ np.linalg.inv(V) @ full_vecs @ full_vecs_eig ),\n", "        #np.abs(Hk_full),\n", "        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),\n", "        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data - E )),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data) - np.diag(E)),\n", "        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),\n", "        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),\n", "        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),\n", "        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),\n", "        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),\n", "        title=\"示例矩阵热图（颜色表示元素值大小）\",\n", "        cmap=\"coolwarm\"  # 冷暖色映射（正值暖色，负值冷色）\n", "    )\n", "    \n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.92107678-3.89380996e-01j,  0.92107678+3.89381002e-01j,\n", "       -0.92107678+3.89380996e-01j,  0.99960006+2.82794415e-02j,\n", "        0.99960006-2.82794415e-02j, -1.        -4.05404891e-05j,\n", "        0.92107678-3.89381002e-01j, -0.99980001+1.99986667e-02j,\n", "       -1.        +4.05404891e-05j, -0.99980001-1.99986667e-02j,\n", "       -0.99980001-1.99986667e-02j, -0.99980001+1.99986667e-02j,\n", "        1.        -2.77555886e-17j,  1.        +8.32667268e-17j,\n", "        1.        +9.71445147e-17j,  1.        -2.22044605e-16j])"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["E"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(-0.9210767829862456-0.38938099574030016j),\n", " (0.9210767804560648+0.3893810017254156j),\n", " (-0.9999999991782349-4.054048913945615e-05j),\n", " (0.9996000566175803-0.02827944147492937j),\n", " (0.9996000566175786+0.028279441474929393j),\n", " (-0.9210767829862458+0.38938099574030044j),\n", " (1+0j),\n", " (-0.9998000066665786-0.019998666693333004j),\n", " (-0.9998000066665783+0.019998666693333098j),\n", " (-0.9999999991782345+4.054048913938709e-05j),\n", " (1+9.74128454279685e-17j),\n", " (0.9210767804560658-0.38938100172541557j),\n", " (0.9999999999999998-1.110223024600787e-16j),\n", " (1.0000000000000004+3.469446951953614e-18j),\n", " (-0.9998000066665781-0.019998666693333004j),\n", " (-0.999800006666579+0.01999866669333293j)]"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["E_blocks_data"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.00101649-5.01452191e-03j, -0.24997397+2.06779038e-15j,\n", "       -0.24997397-5.32907052e-15j,  0.35355339+4.14945855e-15j,\n", "       -0.24997397+1.69309011e-15j,  0.00101068-4.98585247e-03j,\n", "        0.35355339+0.00000000e+00j, -0.24997397-2.63677968e-16j,\n", "       -0.24997397-2.08166817e-16j,  0.35355339-3.33066907e-16j,\n", "        0.00101068-4.98585247e-03j, -0.24997397+1.79023463e-15j,\n", "        0.35355339+4.06619183e-15j, -0.24997397-5.32907052e-15j,\n", "       -0.24997397+1.79023463e-15j, -0.00101649-5.01452191e-03j])"]}, "execution_count": 149, "metadata": {}, "output_type": "execute_result"}], "source": ["V[:,4]"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.00101068+4.98585247e-03j,  0.24997397+3.12250226e-16j,\n", "        0.24997397+3.12250226e-16j,  0.35355339+0.00000000e+00j,\n", "        0.24997397+3.12250226e-16j, -0.00101649+5.01452191e-03j,\n", "        0.35355339+0.00000000e+00j,  0.24997397+4.23272528e-16j,\n", "        0.24997397+3.12250226e-16j,  0.35355339+0.00000000e+00j,\n", "       -0.00101649+5.01452191e-03j,  0.24997397+4.23272528e-16j,\n", "        0.35355339+0.00000000e+00j,  0.24997397+4.23272528e-16j,\n", "        0.24997397+4.23272528e-16j,  0.00101068+4.98585247e-03j])"]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["full_vecs @ full_vecs_eig[:,4]"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.00101068+4.98585247e-03j,  0.49994794+6.24500451e-16j,\n", "        0.70710678+0.00000000e+00j, -0.00143754+7.09160489e-03j,\n", "        0.49994794+8.46545056e-16j,  0.00101068+4.98585247e-03j,\n", "        0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "        0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "        0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "        0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "        0.        +0.00000000e+00j,  0.        +0.00000000e+00j])"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["full_vecs_eig[:,4]"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0. +0.j, 0.5+0.j, 0. +0.j, 0. +0.j, 0. +0.j, 0. +0.j, 0.5+0.j,\n", "       0. +0.j, 0. +0.j, 0.5+0.j, 0. +0.j, 0. +0.j, 0. +0.j, 0.5+0.j,\n", "       0. +0.j, 0. +0.j])"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["full_vecs[1,:]"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["quasienergy = [0 for index in range(2**L)]\n", "for i in range(0,2**L,1):\n", "    quasienergy[i] = (-cmath.phase(E[i]))\n", "quasienergy1 = quasienergy.copy()"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准能量已保存到 quasienergy_output.txt\n"]}], "source": ["# 输出准能量为 txt 文件\n", "import numpy as np\n", "np.savetxt('quasienergy_output.txt', quasienergy)\n", "print('准能量已保存到 quasienergy_output.txt')"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.49989589 0.4998959  0.49989589 0.09373699 0.09373699 0.12497398\n", " 0.4998959  0.12842548 0.12497398 0.12001805 0.10870073 0.12048403\n", " 0.1563007  0.1038746  0.20182491 0.17512726]\n"]}], "source": ["IPR = []\n", "for i in range(len(quasienergy)):\n", "            # eigenvectors_k[:,i] 是本征矢在当前k扇区新基下的分量\n", "            # 新基的正交性保证其他扇区分量为0，直接求和即可\n", "            IPR1 = np.sum(np.abs(V[:, i]) **4)\n", "            IPR.append(IPR1)\n", "IPR = np.array(IPR)\n", "print(IPR)"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20934 (\\N{CJK UNIFIED IDEOGRAPH-51C6}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33021 (\\N{CJK UNIFIED IDEOGRAPH-80FD}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["准能数量: 16\n", "IPR 数量: 16\n", "前5个 IPR 值: [0.49989589 0.4998959  0.12497398 0.09373699 0.09373699]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 调用函数\n", "\n", "# 打印结果\n", "print(\"准能数量:\", len(quasienergy1_k))\n", "print(\"IPR 数量:\", len(IPR_K))\n", "print(\"前5个 IPR 值:\", IPR_K[:5])\n", "\n", "# 绘制 IPR vs 准能\n", "plt.scatter(quasienergy1_k, IPR_K, s=10)\n", "plt.xlabel('准能')\n", "plt.ylabel('IPR')\n", "plt.title('IPR vs 准能')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-3.1415521131006425,\n", " -3.1215926535897935,\n", " -3.121592653589793,\n", " -2.741633200576899,\n", " -0.3999594595108499,\n", " -0.028283212136648778,\n", " -9.714451465470122e-17,\n", " -8.32667268468867e-17,\n", " 2.77555885948104e-17,\n", " 2.2204460492503136e-16,\n", " 0.02828321213664891,\n", " 0.3999594595108494,\n", " 2.741633200576899,\n", " 3.121592653589793,\n", " 3.1215926535897935,\n", " 3.1415521131006425]"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["quasienergy.sort()\n", "quasienergy"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["delta_n = [0 for index in range(2**L-1)]\n", "for i in range(2**L-1):\n", "    delta_n[i]=quasienergy[i+1]-quasienergy[i]\n", "\n", "#平均值\n", "mean = np.mean(delta_n)\n", "#mean\n", "delta_n = delta_n / mean\n", "#画间距为0.1 的直方图\n", "delta_n\n", "#画直方分布图\n", "plt.hist(delta_n,bins=50)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/2519091373.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#plt.plot(np.arange(len(E)),quasienergy,\n", "#\t\t\t\t\tmarker='x',color='r',markersize=2,label='real space ED')\n", "plt.scatter(np.arange(len(E)),quasienergy,\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('state number',fontsize=16)\n", "plt.ylabel('energy',fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "#plt.savefig('example5a.pdf', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.49989589 0.4998959  0.49989589 0.09373699 0.09373699 0.12497398\n", " 0.4998959  0.12842548 0.12497398 0.12001805 0.10870073 0.12048403\n", " 0.1563007  0.1038746  0.20182491 0.17512726]\n"]}], "source": ["print(IPR)"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.741633200576899\n", "-0.3999594595108499\n", "-2.741633200576899\n", "0.3999594595108494\n", "-9.714451465470122e-17\n"]}, {"data": {"text/plain": ["[[0.49989588627838244, 0],\n", " [0.4998959023911922, 1],\n", " [0.49989588627838255, 2],\n", " [0.49989590239119197, 6],\n", " [0.20182491257956486, 14]]"]}, "execution_count": 161, "metadata": {}, "output_type": "execute_result"}], "source": ["c=0\n", "number=[]\n", "for i in IPR:    \n", "    if i>0.2:\n", "        number.append([i,c])\n", "        print(quasienergy1[c])\n", "    c= 1+c\n", "number"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/2126089061.py:12: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(fontsize=16)\n", "The PostScript backend does not support transparency; partially transparent artists will be rendered opaque.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(quasienergy1,list(IPR),\n", "            s=3\n", "\t\t\t\t\t#marker='x',color='r',markersize=2,\n", "     #label='IPR-quasienergy'\n", "     )\n", "plt.xlabel('quasienergy',fontsize=16)\n", "plt.ylabel('IPR',fontsize=16)\n", "# 设置纵坐标范围为0到0.5\n", "plt.ylim(0, 0.7)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.legend(fontsize=16)\n", "#plt.grid()\n", "plt.tight_layout()\n", "plt.savefig('example5a.eps', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnYAAAHWCAYAAAD6oMSKAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAy3UlEQVR4nO3deXhU5cH+8XtCFkjIAoQtIYABrMqFYRUQUVSQtVKL1A0FXIqigoIKCJZVQq1VePXFKvoD3FBRi6iACAaogFQBY0FZhESCbBIwCZA9z+8PrsxrmklImCRn5uH7ua65dM55zpw7xyy3Z3UZY4wAAADg9wKcDgAAAICqQbEDAACwBMUOAADAEhQ7AAAAS1DsAAAALEGxAwAAsATFDgAAwBIUOwAAAEsEOh3ACUVFRTp06JDCw8PlcrmcjgMAAFAmY4yysrIUExOjgIDy98ldkMXu0KFDiouLczoGAABAhaWlpalZs2bljrkgi114eLiksxsoIiLC4TQAAABly8zMVFxcnLu/lOeCLHbFh18jIiIodgAAwC9U5PQxLp4AAACwBMUOAADAEhQ7AAAAS1DsAAAALEGxAwAAsATFDgAAwBIUOwAAAEtQ7AAAACzhF8VuxIgRcrlc5b5ycnKcjgkAAOAov3ryRJs2bdSoUSOP8871UFwAAADb+VWxe/LJJzVixAinYwAAAPgkdnMBAABYgmIHAABgCb86FPv+++9r2b<PERSON>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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#按照 准能量的 分布画出柱状分布图\n", "plt.hist(quasienergy, bins=50)\n", "plt.xlabel('quiasenergy', fontsize=16)\n", "plt.ylabel('Count', fontsize=16)\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "plt.tight_layout()\n", "# 在每个柱子上标记数值\n", "#for i in range(len(number)):\n", "   # plt.text(number[i][1], i+0.5, str(number[i][0]), ha='center', va='bottom')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnYAAAHWCAYAAAD6oMSKAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABH80lEQVR4nO3de1hVVeL/8c9BBA05RwzvomRC3so0xguJwI8U0y6OmTcqqcxsGmcqzaLRAcPCnJlqiplsokFHB5XSLtPFqBQJL43fzExNMxMTNfOYHsgLiuzfH33P+UpcD9fD9v16nv0Ua6+91toctufz7L3X3hbDMAwBAACgyfNq7AEAAACgbhDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCe/GHsClpKSkRIcPH5a/v78sFktjDwcAADQBhmGosLBQnTp1kpdX5efkCHYN6PDhwwoKCmrsYQAAgCbo4MGD6tKlS6V1CHYNyN/fX9LPH4zVam3k0QAAgKagoKBAQUFBrhxRGYJdA3JefrVarQQ7AADglurcxsXkCQAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASfC4E5MoPntW70ycqOO7dsn7ssvk16GDhi9aJFtwcGMPDUAlzp48qfRevXTm+HFZmjVTcz8/jXnzTXUZOrSxhwagEp76vcsZOxO5Zto03bNnj6Zs26buN92krGnTGntIAKqh3/TpeujsWT185oy6RkfrzVtvbewhAagGT/zeJdiZhHeLFuo+apTr4YWdBg+W49tvG3lUAKrSonVrhScmut7/eOXNN+tcQUEjjwpAVTz1e9fjg92hQ4f0/PPPa8SIEeratat8fHzUoUMH3Xbbbfr000/daqukpESpqam65ppr1LJlS7Vt21bjx4/X3r17K9xmy5YtGjVqlAICAuTn56eBAwcqIyOjtrtV77a+8IKuvPnmxh4GADdtnDdPgVdf3djDAOAmT/ne9fhg9+KLL+rhhx/Wt99+q+HDh2vmzJkaOnSo3nrrLYWHhyszM7PabU2fPl0zZszQhQsXNGPGDI0aNUpvv/22fvWrX2nXrl1l6mdnZ2vo0KH65JNPNG7cOD3wwAOy2+2Ki4vT008/XZe7Wac2P/20Tuzdq6FPPdXYQwHghtdGjNDpH37QuKysxh4KADd41Peu4eFWrVpl5OTklCnPyckxmjdvbrRp08Y4e/Zsle2sXbvWkGRERESUqv/RRx8ZFovFGDZsWKn658+fN6688krD19fX2Lp1q6u8oKDA6NOnj+Ht7W18/fXXbu2Lw+EwJBkOh8Ot7dzx3z/9yfjXddcZZ06cqLc+ANS9VaNHG89ddplxMi+vsYcCwA0N8b3rTn7w+DN2Y8eOVURERJnyiIgIRUdH68cff9SXX35ZZTuvvPKKJGn+/Pny9fV1lcfExCg2NlY5OTn6+uuvXeVr167Vvn37NHnyZPXv399V7u/vr7lz56q4uFjp6em12bU69z/PPqvdy5fr9g8/VIvWrRt7OACq6Y1bb9V3a9cqfscO2bp1a+zhAKgmT/zebdKPO2nevLkkydu76t3Izs6Wn5+frr/++jLrYmNjtWbNGq1fv16hoaGu+pI0YsSIMvWdZevXr6/p0OtcYX6+smfOlK17d62MjpYkNfP11R1u3ocIoGEd2bJF+95+W17e3krv3VuS5NWsmX7/00+NPDIAlfHU790mG+y+++47ffTRR+rQoYOuruJG41OnTunIkSPq27evmjVrVmZ9SEiIJJWaROH8f+e6iwUEBCgwMLDSSRcNzb9LF80yjMYeBgA3dfzVrzh2gSbIU793m2SwO3/+vO68804VFRVp4cKF5Ya1izkcDkmSzWYrd73Vai1Vr7rb5OfnV9pvUVGRioqKXD8X8AgDAABQjzz+HrtfKikp0T333KOcnBzdd999uvPOOxt7SBVKSUmRzWZzLUFBQY09JAAAUIeCH3+3sYdQSpMKdoZh6L777tOyZct0xx13aNGiRdXaznnW7eIzchdznkm7+Oxcdbap6GyeU0JCghwOh2s5ePBgtcYLAABQE00m2JWUlOjee+/VP//5T02aNEmLFy92Pam9Kn5+furYsaP279+vCxculFlf3v105d1353TixAnZ7fZy77+7mK+vr6xWa6kFAACgvjSJYFdSUqKpU6cqPT1dEyZM0NKlS6u8r+6XIiMjderUKW3YsKHMug8++MBV5+L6kpRVzoNCnWUX1wcAAGhsHh/snGfq0tPTdfvtt2vZsmWVhjq73a7du3fLbreXKp/2vy/mnTNnjs6dO+cq//jjj/XBBx9o2LBhrkedSD8/36579+7KyMjQtm3bXOWFhYVKTk6Wt7e34uPj62YnAQAA6oDHz4p98skntXjxYrVq1UqhoaGaP39+mTpjxozRtddeK0lKTU3VvHnzlJiYqKSkJFed6OhoTZ06VWlpaerfv79Gjx6to0ePauXKlbJarXrppZdKtent7a20tDTFxsYqIiJCkyZNktVq1erVq7V//37Nnz+/VBAEAABobB4f7PLy8iRJP/30k56q4B1swcHBrmBXmZdfflnXXHONXn75Zb3wwgtq1aqVbr75Zj311FPlhrTo6Gjl5uYqMTFRmZmZOnfunPr06aPk5GTFxcXVZrcAAADqnMUwPPDpeiblnEnrcDiYSAEAgAkEP/6u8haMrtc+3MkPHn+PHQAAAKqHYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEk0iWC3bNky3X///QoLC5Ovr68sFosWL17sVhtRUVGyWCyVLkuXLi21TXBwcIV1p0+fXod7CAAAUHvejT2A6pgzZ44OHDigwMBAdezYUQcOHHC7jfj4eEVFRZUpP3/+vFJSUuTl5aWYmJgy6202mx566KEy5WFhYW6PAQAAoD41iWCXlpamkJAQdevWTQsWLFBCQoLbbcTHx5dbvmrVKhmGoVGjRqlTp05l1rdu3VpJSUlu9wcAANDQmkSwu+GGG+qt7bS0NEnSvffeW299AAAANIQmEezqS35+vrKystShQweNHj263DpFRUVasmSJDh06pICAAIWHh6tfv34NPFIAAICqXdLBLj09XSUlJYqPj5e3d/m/iu+//77MZdyRI0dq6dKlCgwMrLT9oqIiFRUVuX4uKCio9ZgBAAAq0iRmxdYHwzCUnp4uqeLLsPfcc4+ys7N17NgxFRQUaPPmzbrxxhu1Zs0a3XLLLTIMo9I+UlJSZLPZXEtQUFCd7wcAAIDTJRvs1q5dq/379ysyMlI9evQot84f//hHRUZGKjAwUP7+/ho0aJDeeecdDR06VJs2bdJ7771XaR8JCQlyOByu5eDBg/WxKwAAAJIu4WDnnDQxdepUt7bz8vLS3XffLUnasGFDpXV9fX1ltVpLLQAAAPXlkgx2J06c0BtvvKHWrVvrtttuc3t75711p0+fruuhAQAA1NglGeyWLVumoqIixcXFqWXLlm5v/+mnn0r6+c0UAAAAnsJ0wc5ut2v37t2y2+0V1nn11VclVf7sul27dunkyZNlynNzc/Xss8/K19dXY8eOrfV4AQAA6kqTeNxJWlqacnNzJUlffvmlqyw7O1uSNGbMGI0ZM0aSlJqaqnnz5ikxMbHcN0Z89tln+uKLLzRgwAD179+/wj4zMzO1cOFCxcTEKDg4WL6+vtqxY4eysrLk5eWlRYsWqWvXrnW6nwAAALXRJIJdbm6ulixZUqpsw4YNrskLwcHBrmBXFefZuqomTURHR+urr77S1q1btX79ep09e1bt27fXhAkT9PDDD2vgwIHu7wgAAEA9shhVPYwNdaagoEA2m00Oh4MZsgAAmEDw4+8qb0H5b6+qK+7kB9PdYwcAAHCpItgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyiSQS7ZcuW6f7771dYWJh8fX1lsVi0ePFit9rIzs6WxWKpcNm8eXO5223ZskWjRo1SQECA/Pz8NHDgQGVkZNTBXgEAANQt78YeQHXMmTNHBw4cUGBgoDp27KgDBw7UuK3IyEhFRUWVKe/SpUuZsuzsbMXGxsrHx0cTJ06UzWbT6tWrFRcXp7y8PD3xxBM1HgcAAEBdaxLBLi0tTSEhIerWrZsWLFighISEGrcVFRWlpKSkKusVFxdr6tSpslgsysnJUf/+/SVJiYmJGjJkiBITE3X77bcrJCSkxmMBAACoS03iUuwNN9ygbt26NWifa9eu1b59+zR58mRXqJMkf39/zZ07V8XFxUpPT2/QMQEAAFSmSZyxq0t79+7VCy+8oNOnT6tbt24aPny4AgMDy9TLzs6WJI0YMaLMOmfZ+vXr63WsAAAA7rjkgl1GRkapyQ8tW7bUvHnz9Oijj5aqt3fvXkkq91JrQECAAgMDXXUAAAA8QZO4FFsX2rZtqz/96U/66quvdOrUKR06dEjLli1TmzZtNHv2bL388sul6jscDkmSzWYrtz2r1eqqU5GioiIVFBSUWgAAAOrLJRPs+vTpo1mzZqlnz5667LLL1KlTJ8XFxWnNmjXy8fFRYmKiSkpK6rTPlJQU2Ww21xIUFFSn7QMAAFzskgl2Fenbt68GDRqko0eP6ptvvnGVO8/UVXRWrqCgoMKzeU4JCQlyOByu5eDBg3U3cAAAgF+45IOdJNfkidOnT7vKnPfWlXcf3YkTJ2S326t81Imvr6+sVmupBQAAoL5c8sGuuLhYW7dulcViUdeuXV3lkZGRkqSsrKwy2zjLnHUAAAA8gemCnd1u1+7du2W320uVb9q0SYZhlCorLi7Wo48+qgMHDig2NlZt2rRxrYuJiVH37t2VkZGhbdu2ucoLCwuVnJwsb29vxcfH1+euAAAAuKVJPO4kLS1Nubm5kqQvv/zSVeZ81tyYMWM0ZswYSVJqaqrmzZunxMTEUm+YmDRpkiwWi8LDw9W5c2edPHlSOTk52rNnj7p27apFixaV6tPb21tpaWmKjY1VRESEJk2aJKvVqtWrV2v//v2aP3++QkND633fAQAAqqtJBLvc3FwtWbKkVNmGDRu0YcMGSVJwcLAr2FXkgQce0Jo1a5SdnS273S5vb2/16NFDf/jDHzRz5kwFBASU2SY6Olq5ublKTExUZmamzp07pz59+ig5OVlxcXF1tn8AAAB1wWL88vok6o1zJq3D4WAiBQAAJhD8+LvKWzC6XvtwJz+Y7h47AACASxXBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAk2gSwW7ZsmW6//77FRYWJl9fX1ksFi1evNitNnJzczVz5kxdd911uvzyy9WiRQv17NlTjz32mE6ePFnuNsHBwbJYLOUu06dPr/2OAQAA1CHvxh5AdcyZM0cHDhxQYGCgOnbsqAMHDrjdxrhx42S32zV06FDdddddslgsys7O1sKFC7Vq1Spt3LhR7dq1K7OdzWbTQw89VKY8LCysJrsCAABQb5pEsEtLS1NISIi6deumBQsWKCEhwe02Hn74Yd11113q2LGjq8wwDD344IN66aWXNG/ePP3tb38rs13r1q2VlJRUm+EDAAA0iCZxKfaGG25Qt27datXGY489VirUSZLFYtHcuXMlSevXr69V+wAAAI2tSZyxq0/NmzeXJHl7l/+rKCoq0pIlS3To0CEFBAQoPDxc/fr1a8ghAgAAVMslH+z++c9/SpJGjBhR7vrvv/9e8fHxpcpGjhyppUuXKjAwsL6HBwAAUG1N4lJsfdm2bZvmzZundu3aafbs2WXW33PPPcrOztaxY8dUUFCgzZs368Ybb9SaNWt0yy23yDCMStsvKipSQUFBqQUAAKC+XLLBbv/+/brpppt04cIFrVixotyzb3/84x8VGRmpwMBA+fv7a9CgQXrnnXc0dOhQbdq0Se+9916lfaSkpMhms7mWoKCg+todAACASzPYHThwQNHR0Tp27Jhef/11RUdHV3tbLy8v3X333ZKkDRs2VFo3ISFBDofDtRw8eLBW4wYAAKjMJXePXV5enqKjo3X48GG99tpruummm9xuw3l27/Tp05XW8/X1la+vb43GCQAA4K5LKtjl5eUpKipKhw8f1sqVK3XrrbfWqJ1PP/1U0s9vpgAAAPAUprsUa7fbtXv3btnt9lLlzlB36NAhrVixQr/+9a8rbWfXrl3lvmosNzdXzz77rHx9fTV27Ni6HDoAAECtNIkzdmlpacrNzZUkffnll66y7OxsSdKYMWM0ZswYSVJqaqrmzZunxMTEUm+MiIqK0oEDBzR48GBt375d27dvL9PPxfUzMzO1cOFCxcTEKDg4WL6+vtqxY4eysrLk5eWlRYsWqWvXrvWyvwAAADXRJIJdbm6ulixZUqpsw4YNrskLwcHBrmBXEef7ZTdv3qzNmzeXW+fiYBcdHa2vvvpKW7du1fr163X27Fm1b99eEyZM0MMPP6yBAwfWfIcAAADqgcWo6mFslfjXv/6l9u3bKzY2tsq6WVlZ+v7773XXXXfVtLsmr6CgQDabTQ6HQ1artbGHAwAAain48XeVt2B0vfbhTn6o1T128fHxevrpp6tVNyUlxfWYEAAAANS9Wk+eqO4Jv1qcGAQAAEA1NNis2O+//15+fn4N1R0AAMAlx63JE999953y8vJKlTkcDuXk5FS4zZkzZ7R+/Xp9/fXXGjRoUI0GCQAAgKq5FezS09P15JNPlirbsWNHla/kcl6Gfeihh9wbHQAAAKrNrWAXHBysYcOGuX5ev369rFarrr322nLrWywWtWzZUt27d9eECRM0dOjQWg0WAAAAFXMr2E2ZMkVTpkxx/ezl5aWrr75a69atq/OBAQAAwD21ekDxunXrZLPZ6mosAAAAqIVaBbvIyMi6GgcAAABqqc5eKeZwOPTtt9/qp59+qvSZdRffowcAAIC6U+tgl5OTo8cff1yffvpplXUtFouKi4tr2yUAAADKUet77EaOHKnz58/L19dXwcHBateunby8Guy5xwAAAPhftQp2iYmJOn/+vOLi4vT888/r8ssvr6txAQAAwE21CnZbt25V69attXjxYjVr1qyuxgQAAIAaqNU1Ux8fH/Xo0YNQBwAA4AFqFewGDRqkvLy8SmfBAgAAoGHUKtglJibK4XDoz3/+c12NBwAAADVUq3vsOnXqpGeffVYzZ87Uxo0bde+99+rKK6+Un59fhdt07dq1Nl0CAACgArUKdsHBwbJYLDIMQ2+//bbefvvtSuvzHDsAAID6U6tg17VrV1kslroaCwAAAGqhVsEuLy+vjoYBAACA2uIVEQAAACZBsAMAADCJWl2K/e6779zehlmxAAAA9aNOZsVWF7NiAQAA6k+9zYo9deqU7Ha7JKl58+bq1KlTbboCAABAFep1VmxBQYFeeeUVJScna/LkyXrqqadq0x0AAAAqUatgVxWr1aqZM2eqT58+Gj16tHr27Kk777yzPrsEAAC4ZDXIrNiRI0eqW7du+utf/9oQ3V1yPv7d7/SP4GD92WLRsR07Lpm+gbrU0H/L5fXnCWMAmqLG/FuO+OhljzqOGuxxJ61bt9bu3bsbqrtLSui4cZqUmytrt26XVN9AXWrov+Xy+vOEMQBNUWP+Le+76nqPOo7q9VKs0w8//KCvvvpKfn5+DdHdJSdo2LBLsm+gLjX033J5/XnCGICmqDH/lg8H9ZV/ly6N1v8v1esZO7vdrvfff1833nijzp07pxtuuKE+uwMAALik1SrYNWvWrNKlffv2uummm/T555+rffv2WrBgQY36WbZsme6//36FhYXJ19dXFotFixcvdrudkpISpaam6pprrlHLli3Vtm1bjR8/Xnv37q1wmy1btmjUqFEKCAiQn5+fBg4cqIyMjBrtBwAAQH2q1aVYwzAqXe/n56fu3bvrxhtv1KxZsxQYGFijfubMmaMDBw4oMDBQHTt21IEDB2rUzvTp0/XKK6+od+/emjFjho4ePaqVK1cqKytLGzduVO/evUvVz87OVmxsrHx8fDRx4kTZbDatXr1acXFxysvL0xNPPFGjcQAAANSHWgW7kpKSuhpHpdLS0hQSEqJu3bppwYIFSkhIcLuNdevW6ZVXXlFERIQ+/PBD+fr6SpLuuusuDR8+XA888IDWr1/vql9cXKypU6fKYrEoJydH/fv3lyQlJiZqyJAhSkxM1O23366QkJC62UkAAIBaarBZsbVxww03qFstZ5u88sorkqT58+e7Qp0kxcTEKDY2Vjk5Ofr6669d5WvXrtW+ffs0efJkV6iTJH9/f82dO1fFxcVKT0+v1ZjqykcPPqhFXbqoMD9fr91wg9J69Lgk+gbqUkP/LZfXnyeMAWiKGvNveVjWS551HBlNTEpKiiHJSE9Pd2u7jh07Gn5+fkZxcXGZdc8995whyfjHP/7hKktISDAkGcuXLy9T/8cffzQkGeHh4W6NweFwGJIMh8Ph1nYAAMAzdXvsnXrvw538UCePOykqKtLy5cuVlZWlr7/+WoWFhfL391doaKhiY2M1ceLEUmfJGtqpU6d05MgR9e3bV82aNSuz3nk59eJJFM7/L+9Sa0BAgAIDAyuddCH9/HspKipy/VxQUFCj8QMAAFRHrYPd1q1bdfvttysvL6/MZIqtW7dq5cqVSk5OVmZmpgYMGFDb7mrE4XBIkmw2W7nrrVZrqXrV3SY/P7/SflNSUjRv3jy3xwsAAFATtQp2+fn5Gj58uE6cOKHAwEDdd9996tOnj9q3b6+jR49q586dSktL07fffqvY2Fht27ZNnTt3rquxe7yEhAQ98sgjrp8LCgoUFBTUiCMCAABmVqtgl5KSohMnTmjs2LFaunSpWrZsWabO3Llzdeedd2rVqlVKSUlRampqbbqsEedZt4vPyF3MeYn04rNz1dmmorN5Tr6+vo16CRoAAFxaajUr9v3335efn58WL15cbqiTpBYtWig9PV1+fn567733atNdjfn5+aljx47av3+/Lly4UGZ9effTlXffndOJEydkt9t51AkAAPAotQp2hw8fVq9evdSqVatK67Vq1Uq9evXSkSNHatNdrURGRurUqVPasGFDmXUffPCBq87F9SUpKyurTH1n2cX1AQAAGlutgp2/v7+OHj1arbpHjx6Vn59fbbqrFrvdrt27d8tut5cqnzZtmqSf32Jx7tw5V/nHH3+sDz74QMOGDVNoaKirPCYmRt27d1dGRoa2bdvmKi8sLFRycrK8vb0VHx9fr/sCAADgjloFu+uuu075+flasWJFpfWWL1+ugwcPKiwsrEb9pKWlKT4+XvHx8XrttdfKlL355puuuqmpqerVq1eZe/mio6M1depUffLJJ+rfv79mz56tKVOmaPTo0bJarXrppZdK1ff29lZaWppKSkoUERGhadOmadasWerXr5927typpKSkUkEQAACgsdVq8sSMGTOUlZWlKVOmaMuWLfrtb3+rK664wrV+//79evHFF/X3v/9dFotFv/vd72rUT25urpYsWVKqbMOGDa7LqsHBwRozZkyV7bz88su65ppr9PLLL+uFF15Qq1atdPPNN+upp54qN6RFR0crNzdXiYmJyszM1Llz59SnTx8lJycrLi6uRvsCAABQXyzGLx8+56aEhAQ988wzslgskn6eCdq2bVsdO3bM9XBewzCUkJCgp556qvYjbsKcM2kdDofr2XkAAKDpCn78XeUtGF2vfbiTH2r9rtiUlBS9/fbbGjJkiCwWi86ePauDBw/q7Nmzslgsuv766/Wf//znkg91AAAA9a1OXil200036aabbtKpU6f0zTff6KefflKrVq3Uo0ePBpkwAQAAgBoEu507d2rfvn1q166dBg8eXGqdn5+f+vXrV6ps8+bN+uGHH9SjRw/17t27dqMFAABAhdwKdqdPn9aIESNkt9u1bt26am1TUlKicePGqVOnTtqzZw9vYgAAAKgnbt1jt3z5ch05ckT33nuvwsPDq7VNeHi47rvvPh08eLDKx6IAAACg5twKdm+++WaNHlvy0EMPyTAMrVq1yq3tAAAAUH1uBbvPP/9cHTt2VM+ePd3qJCQkRJ07d9bnn3/u1nYAAACoPreCnd1uV+fOnWvUUadOncq85gsAAAB1x61g16JFC505c6ZGHZ05c0Y+Pj412hYAAABVcyvYdezYUfv27XO9UaK6ioqKtG/fPnXq1Mmt7QAAAFB9bgW7iIgInT17Vq+//rpbnbz22ms6c+aMIiIi3NoOAAAA1edWsIuPj5dhGHrsscd08ODBam3z3Xffafbs2bJYLJoyZUqNBgkAAICquRXswsPDdfvtt+vw4cMaNGiQXnvtNZWUlJRbt6SkRJmZmRo8eLCOHj2q2267Tddff32dDBoAAABluf1KscWLF+vQoUPauHGjJk6cqLZt2+r666/XFVdcIT8/P506dUr79+/Xxo0b9cMPP8gwDA0ZMkSLFy+uh+EDAADAye1g17JlS2VnZyspKUkvvviifvjhB73xxhuyWCyuOoZhSJJatWqlGTNmKCkpSc2bN6+7UQMAAKAMt4OdJHl7e2v+/PmaPXu23n33XW3cuFGHDh1SYWGh/P391blzZ4WHh2vUqFGy2Wx1PWYAAACUo0bBzslqtWrSpEmaNGlSXY0HAAAANeTW5AkAAAB4LoIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJhEkwl2W7Zs0ahRoxQQECA/Pz8NHDhQGRkZ1d4+KipKFoul0mXp0qWltgkODq6w7vTp0+t6FwEAAGrFu7EHUB3Z2dmKjY2Vj4+PJk6cKJvNptWrVysuLk55eXl64oknqmwjPj5eUVFRZcrPnz+vlJQUeXl5KSYmpsx6m82mhx56qEx5WFhYTXYFAACg3lgMwzAaexCVKS4uVs+ePZWfn69Nmzapf//+kqTCwkINGTJEe/bs0a5duxQSElKj9letWqVx48bp5ptv1ttvv11qXXBwsCQpLy+vNrvgUlBQIJvNJofDIavVWidtAgCAxhP8+LvKWzC6XvtwJz94/KXYtWvXat++fZo8ebIr1EmSv7+/5s6dq+LiYqWnp9e4/bS0NEnSvffeW+uxAgAANCaPvxSbnZ0tSRoxYkSZdc6y9evX16jt/Px8ZWVlqUOHDho9uvy0XVRUpCVLlujQoUMKCAhQeHi4+vXrV6P+AAAA6pPHB7u9e/dKUrmXWgMCAhQYGOiq46709HSVlJQoPj5e3t7l/yq+//57xcfHlyobOXKkli5dqsDAwBr1CwAAUB88/lKsw+GQ9PMkhvJYrVZXHXcYhuG6hFvRZdh77rlH2dnZOnbsmAoKCrR582bdeOONWrNmjW655RZVdXtiUVGRCgoKSi0AAAD1xeODXX1Zu3at9u/fr8jISPXo0aPcOn/84x8VGRmpwMBA+fv7a9CgQXrnnXc0dOhQbdq0Se+9916lfaSkpMhms7mWoKCg+tgVAAAASU0g2DnP1FV0Vs45U8RdzkkTU6dOdWs7Ly8v3X333ZKkDRs2VFo3ISFBDofDtRw8eNDtcQIAAFSXxwc757115d1Hd+LECdntdrcfdXLixAm98cYbat26tW677Ta3x+S8t+706dOV1vP19ZXVai21AAAA1BePD3aRkZGSpKysrDLrnGXOOtW1bNkyFRUVKS4uTi1btnR7TJ9++qmk/3vOHQAAgCfw+GAXExOj7t27KyMjQ9u2bXOVFxYWKjk5Wd7e3qVmrdrtdu3evVt2u73CNl999VVJlT+7bteuXTp58mSZ8tzcXD377LPy9fXV2LFj3d4fAACA+uLxwc7b21tpaWkqKSlRRESEpk2bplmzZqlfv37auXOnkpKSFBoa6qqfmpqqXr16KTU1tdz2PvvsM33xxRcaMGBAqQce/1JmZqY6deqkm2++WTNmzNCsWbM0cuRIDRs2TOfPn1dqaqq6du1a5/sLAABQUx7/HDtJio6OVm5urhITE5WZmalz586pT58+Sk5OVlxcnFttOc/WVTVpIjo6Wl999ZW2bt2q9evX6+zZs2rfvr0mTJighx9+WAMHDqzx/gAAANQHj39XrJnwrlgAAMyFd8UCAACgXhDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwCYIdAACASRDsAAAATIJgBwAAYBIEOwAAAJMg2AEAAJgEwQ4AAMAkCHYAAAAmQbADAAAwiSYT7LZs2aJRo0YpICBAfn5+GjhwoDIyMqq9fXZ2tiwWS4XL5s2b66VfAACAhuLd2AOojuzsbMXGxsrHx0cTJ06UzWbT6tWrFRcXp7y8PD3xxBPVbisyMlJRUVFlyrt06VKv/QIAANQ3i2EYRmMPojLFxcXq2bOn8vPztWnTJvXv31+SVFhYqCFDhmjPnj3atWuXQkJCKm0nOztb0dHRSkxMVFJSUoP1e7GCggLZbDY5HA5ZrdZqbwcAADxT8OPvKm/B6Hrtw5384PGXYteuXat9+/Zp8uTJrnAlSf7+/po7d66Ki4uVnp5umn4BAABqyuMvxWZnZ0uSRowYUWads2z9+vXVbm/v3r164YUXdPr0aXXr1k3Dhw9XYGBgvfcLAABQ3zw+2O3du1eSyr3kGRAQoMDAQFed6sjIyCg1+aFly5aaN2+eHn300Trvt6ioSEVFRa6fCwoKqj1OAAAAd3n8pViHwyFJstls5a63Wq2uOpVp27at/vSnP+mrr77SqVOndOjQIS1btkxt2rTR7Nmz9fLLL9d5vykpKbLZbK4lKCioynECAADUlMcHu7rSp08fzZo1Sz179tRll12mTp06KS4uTmvWrJGPj48SExNVUlJSp30mJCTI4XC4loMHD9Zp+wAAABfz+GDnPGNW0dkx50yRmurbt68GDRqko0eP6ptvvqnTfn19fWW1WkstAAAA9cXjg53zHrfy7mc7ceKE7Ha7W48cKY9z8sTp06cbtF8AAIC65PHBLjIyUpKUlZVVZp2zzFmnJoqLi7V161ZZLBZ17dq1wfoFAACoax4f7GJiYtS9e3dlZGRo27ZtrvLCwkIlJyfL29tb8fHxrnK73a7du3fLbreXamfTpk365bOYi4uL9eijj+rAgQOKjY1VmzZtatwvAABAY/P4x514e3srLS1NsbGxioiI0KRJk2S1WrV69Wrt379f8+fPV2hoqKt+amqq5s2bV+YNE5MmTZLFYlF4eLg6d+6skydPKicnR3v27FHXrl21aNGiWvULAADQ2Dw+2ElSdHS0cnNzlZiYqMzMTJ07d059+vRRcnKy4uLiqtXGAw88oDVr1ig7O1t2u13e3t7q0aOH/vCHP2jmzJkKCAiol34BAAAaise/K9ZMeFcsAADmwrtiAQAAUC8IdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABMgmAHAABgEgQ7AAAAkyDYAQAAmESTCXZbtmzRqFGjFBAQID8/Pw0cOFAZGRnV3j43N1czZ87Uddddp8svv1wtWrRQz5499dhjj+nkyZPlbhMcHCyLxVLuMn369DraMwAAgLrh3dgDqI7s7GzFxsbKx8dHEydOlM1m0+rVqxUXF6e8vDw98cQTVbYxbtw42e12DR06VHfddZcsFouys7O1cOFCrVq1Shs3blS7du3KbGez2fTQQw+VKQ8LC6uLXQMAAKgzFsMwjMYeRGWKi4vVs2dP5efna9OmTerfv78kqbCwUEOGDNGePXu0a9cuhYSEVNrOM888o7vuuksdO3Z0lRmGoQcffFAvvfSSfvOb3+hvf/tbqW2Cg4MlSXl5eXWyLwUFBbLZbHI4HLJarXXSJgAAaDzBj7+rvAWj67UPd/KDx1+KXbt2rfbt26fJkye7Qp0k+fv7a+7cuSouLlZ6enqV7Tz22GOlQp0kWSwWzZ07V5K0fv36uh04AABAA/P4S7HZ2dmSpBEjRpRZ5yyrTShr3ry5JMnbu/xfRVFRkZYsWaJDhw4pICBA4eHh6tevX437AwAAqC8eH+z27t0rSeVeag0ICFBgYKCrTk3885//lFR+cJSk77//XvHx8aXKRo4cqaVLlyowMLDStouKilRUVOT6uaCgoMbjBAAAqIrHX4p1OBySfp7EUB6r1eqq465t27Zp3rx5ateunWbPnl1m/T333KPs7GwdO3ZMBQUF2rx5s2688UatWbNGt9xyi6q6PTElJUU2m821BAUF1WicAAAA1eHxwa6+7N+/XzfddJMuXLigFStWlHv27Y9//KMiIyMVGBgof39/DRo0SO+8846GDh2qTZs26b333qu0j4SEBDkcDtdy8ODB+todAAAAzw92zjN1FZ2Vc84UcceBAwcUHR2tY8eO6fXXX1d0dHS1t/Xy8tLdd98tSdqwYUOldX19fWW1WkstAAAA9cXjg53z3rry7qM7ceKE7HZ7lY86uVheXp6ioqJ0+PBhZWZm6qabbnJ7TM6ze6dPn3Z7WwAAgPri8cEuMjJSkpSVlVVmnbPMWacqzlB36NAhrVy5UrfeemuNxvTpp59K+r/n3AEAAHgCjw92MTEx6t69uzIyMrRt2zZXeWFhoZKTk+Xt7V1q1qrdbtfu3btlt9tLtXNxqFuxYoV+/etfV9rvrl27yn3VWG5urp599ln5+vpq7Nixtdk1AACAOuXxjzvx9vZWWlqaYmNjFRERoUmTJslqtWr16tXav3+/5s+fr9DQUFf91NRUzZs3T4mJiUpKSnKVR0VF6cCBAxo8eLC2b9+u7du3l+nr4vqZmZlauHChYmJiFBwcLF9fX+3YsUNZWVny8vLSokWL1LVr1/rcdQAAALd4fLCTpOjoaOXm5ioxMVGZmZk6d+6c+vTpo+TkZMXFxVWrjQMHDkiSNm/erM2bN5db5+JgFx0dra+++kpbt27V+vXrdfbsWbVv314TJkzQww8/rIEDB9Z6vwAAAOqSx78r1kx4VywAAObCu2IBAABQLwh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTINgBAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAAACTaDLBbsuWLRo1apQCAgLk5+engQMHKiMjw602SkpKlJqaqmuuuUYtW7ZU27ZtNX78eO3du7de+wUAAGgI3o09gOrIzs5WbGysfHx8NHHiRNlsNq1evVpxcXHKy8vTE088Ua12pk+frldeeUW9e/fWjBkzdPToUa1cuVJZWVnauHGjevfuXS/9AgAANASLYRhGYw+iMsXFxerZs6fy8/O1adMm9e/fX5JUWFioIUOGaM+ePdq1a5dCQkIqbWfdunX6f//v/ykiIkIffvihfH19JUkff/yxhg8froiICK1fv77O+71YQUGBbDabHA6HrFaru78KAADgYYIff1d5C0bXax/u5AePvxS7du1a7du3T5MnT3aFK0ny9/fX3LlzVVxcrPT09CrbeeWVVyRJ8+fPd4U6SYqJiVFsbKxycnL09ddf13m/AAAADcXjg112drYkacSIEWXWOcsuPtNWWTt+fn66/vrry6yLjY0t005d9QsAANBQPD7YOSc2lHfJMyAgQIGBgZVOfpCkU6dO6ciRI7riiivUrFmzMuudbV/cTl30CwAA0JA8fvKEw+GQJNlstnLXW61W5efn17qNi+vVVb9FRUUqKioq02ZBQUGl2wEAgKahpOh0vX+vO9uvzrQIjw92TVlKSormzZtXpjwoKKgRRgMAAOqD7fmG6aewsLDCE05OHh/snDtw8dm0izlnitS2jYvr1VW/CQkJeuSRR1w/l5SU6Mcff9Tll18ui8VS6bY1UVBQoKCgIB08eJBZtybE52tufL7mxudrbvX9+RqGocLCQnXq1KnKuh4f7C6+/+26664rte7EiROy2+0KDw+vtA0/Pz917NhR+/fv14ULF8rcZ1fe/XR10a+vr2+pGbiS1Lp160q3qQtWq5V/OEyMz9fc+HzNjc/X3Orz863qZJKTx0+eiIyMlCRlZWWVWecsc9apqp1Tp05pw4YNZdZ98MEHZdqpq34BAAAaiscHu5iYGHXv3l0ZGRnatm2bq7ywsFDJycny9vZWfHy8q9xut2v37t2y2+2l2pk2bZokac6cOTp37pyr/OOPP9YHH3ygYcOGKTQ0tMb9AgAANDaPD3be3t5KS0tTSUmJIiIiNG3aNM2aNUv9+vXTzp07lZSUVCqQpaamqlevXkpNTS3VTnR0tKZOnapPPvlE/fv31+zZszVlyhSNHj1aVqtVL730Uq369QS+vr5KTEwsc/kX5sDna258vubG52tuHvX5Gk3Ep59+aowcOdKw2WxGy5YtjbCwMGPZsmVl6iUmJhqSjMTExDLrLly4YLzwwgtGnz59DF9fX+Pyyy83xo0bZ+zZs6fW/QIAADQ2j39XLAAAAKrH4y/FAgAAoHoIdgAAACZBsAOARrRlyxaNGjVKAQEB8vPz08CBA5WRkdHYwwLQRBHsABPavHmzLBaLnnrqKT3//PMaMWKEunbtKh8fH3Xo0EG33XabPv3008Ye5iUvOztbQ4cO1SeffKJx48bpgQcekN1uV1xcnJ5++unGHh7qgfPYfPXVV3Xo0CGOT9Q5Jk8AJpSQkKCFCxfqrrvu0uLFi3XllVcqMjJS7dq10969e/Xmm2/KMAwtX75c48ePb+zhXpKKi4vVs2dP5efna9OmTerfv7+kn5+VOWTIEO3Zs0e7du0q9UYcNH3OY/Pw4cN67rnn9Mwzz3B8om415pRc1K+VK1cakqpcQkJCGnuoqGO9evUywsPDjVWrVhk5OTll1ufk5BjNmzc32rRpY5w9e7YRRogPPvjAkGTcfffdZdatWLHCkGQkJCQ0wshQn5zHpmEYHJ9NmCd/v3r8u2JRc+3atdOUKVO0ZMkSXX/99brhhhtc67KysrRp0ybdfffdio2NbcRRoiKZmZmaMGFClfVCQkL09ddfu37+5ptv9NVXX+mZZ57R2LFjy90mIiJC0dHRysrK0pdffqmwsLA6GzeqJzs7W5I0YsSIMuucZevXr2/IIcENNTk+Lz42JXF8NmGe/P1KsDOxqKgo7du3T0uWLFF8fLymTp3qWvf5559Lkp566il17NixsYaIStT0H44333xTknTrrbdW2n7z5s0l/fyWFTS8vXv3SlK5l1oDAgIUGBjoqgPPU5Pjs7rHpsTx6ek8+fuVvxiT++KLLyRJ11xzTanybdu2KTAwkFDnwWr6D8dbb72l0NBQXXXVVRW2/d133+mjjz5Shw4ddPXVV9fPDqBSDodDkmSz2cpdb7ValZ+f35BDghtqcnxW59iUOD6bCk/9fmVWrIcLDAyUxWKp9uK8vOO0fft2eXl5qW/fvq6yH3/8Ud9995369evXwHsDd7n7D4fdbtfGjRs1ZsyYCts8f/687rzzThUVFWnhwoVq1qxZnY8buBS4c3xW59iUOD6bEk/9fuWMnYebNGmSCgsLq12/Q4cOpX7evn27QkJCdNlll7nKtm3bJqnsP0aoe4GBgTp+/Hi1669bt05RUVGunyv7hyMmJqbM9v/5z39UUlJS4aWekpIS3XPPPcrJydF9992nO++8s/o7gzrlPFPnPHP3SwUFBRWezYNncOf4rOrYlDg+mxpP/X4l2Hm4F198scbbfvfddzpx4oSGDx9eqtx5mYAzdvWvoYP5W2+9pXbt2mnw4MFl1hmGofvuu0/Lli3THXfcoUWLFlV7XKh7znvr9u7dq+uuu67UuhMnTshutys8PLwxhoZqcuf4rOzYlDg+mxpP/n4l2JlYZZcJyitH3WvIYH7mzBl9+OGHmjRpkry8St9lUVJSoqlTpyo9PV2TJk3S4sWLy9RBw4qMjFRKSoqysrI0ceLEUuuysrJcdeCZ3Dk+Kzs2JY7PpsiTv1/5yzGx7du3SyobAHbs2CGLxaLevXs3xrBQTe7+w/Hhhx/q9OnTZS71XPylMWHCBC1dupT7djxATEyMunfvroyMDNdnKv38gOLk5GR5e3srPj6+0caHyrlzfFZ0bEocn02VJ3+/EuxMzPkPzy//8I4fPy6LxaItW7bo5MmTjTAyVIe7/3C89dZbuuyyy0o9dqGkpET33nuv0tPTdfvtt2vZsmV8aXgIb29vpaWlqaSkRBEREZo2bZpmzZqlfv36aefOnUpKSlJoaGhjDxMVcOf4LO/YlDg+mzJP/n7lUqyJffHFFwoICFBQUFCp8ri4OL344ou69dZb9eGHH2rAgAGNNEJUpjr/cPTt21etW7dWSUmJ3nnnHY0YMUItW7Z01X3yySe1ePFitWrVSqGhoZo/f36ZfsaMGaNrr722XvcF5YuOjlZubq4SExOVmZmpc+fOqU+fPkpOTlZcXFxjDw+VqO7xabVayz02JY7Ppsyjv18b/F0XAKolNDTUCAgIKFP++OOPG35+fkabNm2Mzz77zDAMw8jNzTUkGenp6aXqTpkypcpX3vxyGwBVq+7xWdGxaRgcn6gfFsMwjIYOkwDq1uzZs/WXv/xFR48eVWBgYGMPB8D/4thEQyPYASZw1VVXqV27dvrkk08aeygALsKxiYZGsAMAADAJZsUCAACYBMEOAADAJAh2AAAAJkGwAwAAMAmCHQAAgEkQ7AAAAEyCYAcAAGASBDsAqAd5eXmyWCwKDg5u7KEAuIQQ7AB4hP/85z+aMGGCunbtqhYtWqhNmzYaMGCA5syZo++//76xhwcATQJvngDQqBwOhyZMmKAPPvhAktS2bVt169ZNBQUF2rt3rwzDUKtWrfSPf/xDkyZNauTRVt+hQ4cUExOjzp076+OPP27s4QC4RBDsADSac+fOaejQodqyZYuCg4P197//XSNHjpTFYpEkHTx4ULNnz9aKFStksVi0YsUKjR8/vpFHDQCei2AHoNEkJCRowYIF6tixo/773/+qS5cu5da75557lJ6eLpvNpt27d6tDhw4NPFIAaBq4xw5Aozh58qRSU1MlSX/+858rDHWS9Ne//lWBgYFyOBx68cUXXeXx8fGyWCxavHhxudslJSXJYrEoKSmpVPmZM2e0fPlyTZw4UVdddZVatWqlVq1a6dprr9X8+fN16tSpcts7fvy4Zs2apZ49e6pFixby8/NTcHCwRo4cqb///e+l6lY1eaK4uFiLFi3S0KFD1bp1a7Vo0UI9e/bUnDlzVFBQUKb+4sWLZbFYFB8fr6KiIiUlJalHjx5q0aKFgoKC9Mgjj1Q4bknKz8/X7373O4WGhqply5Zq3bq1oqOj9frrr5dbPyoqShaLRdnZ2dq2bZvGjRun9u3by8vLq9Tv+4cfftD999+vTp06ufYhJSVFxcXFpdqQpAsXLqhLly6yWCz67LPPKhzrb3/7W1ksFj366KMV1gFQAQMAGsG///1vQ5IRGBhonDt3rsr6v//97w1JRo8ePVxlU6ZMMSQZ6enp5W6TmJhoSDISExNLlX/yySeGJMPb29vo0qWLERYWZoSEhBje3t6GJGPAgAHG6dOnS21z8uRJ48orrzQkGT4+Pkbv3r2NAQMGGO3atTMsFoths9lK1d+/f78hyejWrVuZcTkcDmPYsGGGJMPLy8vo1q2b0bdvX8PHx8eQZPTq1cs4evRoqW3S09MNScbkyZONYcOGGRaLxejTp49x1VVXGV5eXoYkY/jw4eX+HrKzsw2bzWZIMlq2bGlcffXVRlBQkCHJkGTMnDmzzDaRkZGGJGPevHmGr6+v0apVK+O6664zunfv7vp9Hzx40OjatashyWjevLnRv39/IzQ01JBk3Hrrra421q1b52o3ISHBkGTMmDGj3LEWFRUZl19+uSHJ2LFjR7l1AFSMYAegUTz44IOGJOOWW26pVv1Vq1a5gsixY8cMw6h5sMvLyzMyMzONwsLCUuVHjhwxxo0bZ0gykpKSSq3785//bEgyRowYYRw/frzUugMHDhjPPfdcqbLKgt3EiRMNSUZMTIyxb98+V/mPP/5ojB071pBkjBs3rtQ2zmDXvHlzo3fv3saePXtc6zZt2mRYrVZDkvH++++X2u7QoUNGmzZtDIvFYjz99NPG2bNnXes2bNhgdO7c2ZBk/Oc//ym1nTOUNWvWzJg2bZpx6tQp1zpn6B09erQhyQgLCzMOHjzoWp+Tk2O0bt3aaN68eZlgt3fv3koDvfNzDgsLK7MOQNW4FAugURw6dEiSdOWVV1ar/sX1Dh8+XKu+u3Xrpttvv12tWrUqVd6hQwf961//ko+Pj/7973+XWrd3715J0oMPPqg2bdqUWte1a1c99NBD1ep7+/btWrFihbp166Y33nhD3bt3d60LCAjQ0qVLFRQUpFWrVunAgQNlti8uLtaSJUsUGhrqKhs8eLCmTp0qSXr//fdL1f/LX/6iH3/8UQ899JASEhLk6+vrWhceHq5FixZJkp577rlyx9u3b1+99NJLuuyyy1xlLVu21J49e/Tuu++qefPmyszMLHUpPSIiQs8995zOnz9fpr0ePXooIiJCdrtd7777bpn1S5YskfTzZXYA7iPYAWgUhYWFkiQ/P79q1b+4nnPb2igpKdFbb72lBx98UDfeeKMiIiI0dOhQDR8+XBaLRXv37tXp06dd9YOCgiRJb7zxhoqLi2vc7xtvvCFJGj9+vPz9/cusv+yyy3TDDTfIMAx98sknZdZfe+21CgsLK1P+q1/9SpL07bfflipfvXq1JLmC3y+NHDlSPj4+2rhxY7n7dccdd8jLq+xXxYcffijp53vxrrjiijLrJ06cqJYtW5bb5z333CPp/0Kc07Fjx/T+++/Lx8enST3aBvAk3o09AACXJmeoqeyG/4tdXO/is0c1cfLkSY0aNUqbNm2qtN6JEydcfd19993605/+pMWLF+v999/XyJEjFRERoejo6FJn3ary5ZdfSvo54G3cuLHcOs4zdc6zmher6Axnu3btJEk//fSTq+ynn35SXl6eJGnatGmVjuvs2bM6fvy42rdvX6q8V69e5dZ3nsG85ppryl3fokULhYSEaPv27WXW3X777frd736nd999V3a7XYGBgZKkjIwMnT9/XuPGjStzVhRA9RDsADSKzp07S5L27dtXrfoX1yvvDJE7HnnkEW3atElXXXWVnn76aQ0ePFiBgYHy8fGRJHXp0kWHDh0qdSmxU6dO2rRpk+bOnat3331XS5YscZ1xGjx4sJ599lkNGTKkyr4dDock6ZtvvtE333xTad0zZ86UKavoDKfzrJpx0ROsnH1J0oYNG6ocmzv9OYN2eWcdnSpa5+fnp/Hjx+vVV1/V8uXLNWPGDElchgXqApdiATQKZwiq6BLgL+Xk5Ej6+QxS69atJcn1IGOjgsdxlnc2sLi4WJmZmZKkt956S2PHjlWnTp1coa64uLjCV5j16tVLr7/+uk6ePKl169YpKSlJPXv21ObNmzVixAjX2bHKOO/re+WVV2T8PIGtwuWXj2lx18X3EJ47d67K/tx5r60z8F18hvCXKrtk/svLsV9++aU+//xzdejQQSNHjqz2OACURrAD0ChGjRqlyy67THa7Xa+99lqldQsLC12TGSZPnuwqd4aLY8eOlbtdeWfEjh07plOnTqlNmza66qqryqzfsWOHLly4UOl4fH19FRUVpcTERO3YsUPXX3+9fvrpJy1fvrzS7SSpd+/ern7qm81mU6dOnSRJO3furNO2nZM3yrvUKklFRUWuy7XlCQ8PV8+ePfXZZ59px44drmfj3XHHHWrWrFmdjhW4lBDsADSKgIAA/eY3v5EkzZw5U/n5+RXW/f3vfy+73a727dvrt7/9ravceW/bli1bymyTn5/vev/sxZw39BcUFJR76XHhwoVu7UezZs1cExeqM1v317/+tSRp2bJlOn78uFt91cTYsWMlSc8//3ydtjt8+HBJ0rp168qdvbty5cpyf78Xu/vuuyVJr776qiu4cxkWqB2CHYBGk5ycrH79+unIkSMaNmyY1qxZU+qyan5+viZPnqz09HT5+Pho2bJlrsuwknTjjTdKkt5880299957rvIjR44oLi6u3Eu8rVu3Vp8+fVRcXKyHH35Y586dk/TzWxGeeeYZrVy50nVZ9mJ/+MMf9Oqrr+rkyZOlynfs2OG6tDtgwIAq9zksLEzjx4/X8ePHNXz4cH3++eel1l+4cEHZ2dmKi4tTUVFRle1V5bHHHlObNm20ZMkSPfLII2XG/+OPP+qf//yn5s+f71a7oaGhGj16tM6fP6/x48eXCrUbNmzQww8/rObNm1faxl133SVvb2+lpqbq6NGjCgsLU58+fdwaB4BfaNjH5gFAacePH3c9DFeS0bZtWyMsLMy46qqrDIvFYkgyOnfuXOohtxe79957XdteccUVxrXXXmt4e3sbPXv2dL2t4pcPKH777bddbbdp08YICwszAgMDDUnG3LlzjW7duhmSjP3797u2ufXWW11viujRo4cxcOBAo0ePHq6+o6OjjfPnz7vqV/aA4sLCQmP48OGubbt27WoMGjTIuPrqq42WLVu6ys+cOePaxvmA4ilTppT7e1i3bp0hyYiMjCyzLjc317V/zZs3N66++mpj0KBBRvfu3V2/hwkTJpTapry3RvzSL988MWDAAOOqq65yPXja+XaNnJycCtu4+eabXfubmppaYT0A1cMZOwCNqk2bNsrOztbrr7+uMWPGqHnz5vriiy+0Z88eGYahFi1aKCcnR1FRUeVuv2jRIj355JO68sordejQIR07dkz333+/Nm3aVOrs3sVuvvlmvf/++woPD9eZM2e0Z88e9ejRQ8uWLdOTTz5Z7jZz5szR448/rl/96lf66aeftG3bNp05c0aRkZH617/+paysLHl7V+9BA61atdKaNWv073//W7GxsTp9+rS2bt0qu92ua665Ro899pj++9//qkWLFtVqryrXX3+9du3apT/84Q/q3bu39u/fr+3bt8vLy8v1ntu//vWvbrfbpUsX/fe//9W0adN0+eWXa+fOnSopKdGTTz6p119/3fUcwMpmzjovx/LsOqBuWAyjgulkANCI7Ha7oqKitHPnTl177bVav369rFZrYw8L1VRSUqI2bdrI4XDoxx9/VEBAQLn1Fi1apAceeEDjxo2rchINgKpxxg6ARwoMDNRHH32kkJAQbdu2Tb/+9a9d98PB861evVoOh0O9e/euMNRJP0+ckP7vzB2A2iHYAfBYHTp00Mcff6ykpCRFRETos88+a+wh4SJHjx7VwoULy8zuXbNmjaZPny5Jrv+WZ9WqVfqf//kfde/enWfXAXWES7EAgBrJy8vTFVdcIYvFoi5duqhDhw7Kz8/XkSNHJEmjR4/WW2+9Vea5dFFRUSosLNTnn38uwzCUkZHB/XVAHSHYAQBq5PTp01q4cKHWrFmj/fv3u96t27dvX91555269957y51QYrFY1KxZMwUHB2vmzJl64IEHGmH0gDkR7AAAAEyCe+wAAABMgmAHAABgEgQ7AAAAkyDYAQAAmATBDgAAwCQIdgAAACZBsAMAADAJgh0AAIBJEOwAAABM4v8DG9heKZtPjEsAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制直方图并获取统计信息\n", "n, bins, patches = plt.hist(quasienergy1, bins=5000, range=(-np.pi, np.pi))\n", "\n", "# 设置坐标轴\n", "plt.xlabel('Quasienergy', fontsize=16)\n", "plt.ylabel('Count', fontsize=16)\n", "plt.xticks([-np.pi, -np.pi/2, 0, np.pi/2, np.pi],\n", "           [r'$-\\pi$', r'$-\\pi/2$', '0', r'$\\pi/2$', r'$\\pi$'],\n", "           fontsize=14)\n", "plt.yticks(fontsize=14)\n", "\n", "# 在每个柱子上方标记数量\n", "bin_width = bins[1] - bins[0]  # 计算柱子宽度\n", "for i in range(len(n)):\n", "    count = n[i]\n", "    if count > 0:  # 只标记有数据的柱子\n", "        # 计算柱子中心位置\n", "        x_pos = bins[i] + bin_width / 2\n", "        # 放置文本标签\n", "        plt.text(x_pos, count, f'{int(count)}',\n", "                 ha='center', va='bottom',\n", "                 fontsize=8, color='darkred')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16\n", "找到4个IPR大于0.4的向量，索引为: [ 0  1  5 11]\n", "[[ 7.07069968e-01+0.00000000e+00j  7.07069974e-01+0.00000000e+00j\n", "  -3.39065652e-05+1.43354658e-05j  0.00000000e+00+0.00000000e+00j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 2.90579151e-06-1.43347195e-05j -1.37849565e-17-6.60908866e-17j\n", "   2.90579151e-06+1.43347195e-05j -4.25176188e-16+8.53101432e-17j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-3.39065652e-05-1.43354658e-05j -2.06359909e-17-1.42599265e-17j\n", "   7.07069968e-01+0.00000000e+00j  7.07069974e-01+0.00000000e+00j]\n", " [ 2.90579151e-06-1.43347195e-05j -1.37849565e-17-6.60908866e-17j\n", "   2.90579151e-06+1.43347195e-05j  4.25176188e-16-8.53101432e-17j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 2.90579151e-06-1.43347195e-05j -1.37849565e-17-6.60908866e-17j\n", "   2.90579151e-06+1.43347195e-05j  4.25176188e-16-8.53101432e-17j]\n", " [-3.39065652e-05-1.43354658e-05j -2.06359909e-17-1.42599265e-17j\n", "   7.07069968e-01+0.00000000e+00j  7.07069974e-01+1.73182196e-16j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 2.90579151e-06-1.43347195e-05j -1.37849565e-17-6.60908866e-17j\n", "   2.90579151e-06+1.43347195e-05j -4.25176188e-16+8.53101432e-17j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 7.07069968e-01+3.05311332e-16j -7.07069974e-01+9.02056208e-17j\n", "  -3.39065652e-05+1.43354658e-05j  0.00000000e+00+0.00000000e+00j]]\n", "2.741633200576899\n", "-0.3999594595108499\n", "3.1415521131006425\n", "-3.121592653589793\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["16\n", "找到4个IPR大于0.4的向量，索引为: [0 1 2 6]\n", "[[ 7.07069968e-01+4.44089210e-16j  7.07069974e-01+0.00000000e+00j\n", "  -3.39065652e-05+1.43354658e-05j -3.90174556e-17-1.92827052e-17j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 2.90579151e-06-1.43347195e-05j -9.10492318e-17+1.71204484e-16j\n", "   2.90579151e-06+1.43347195e-05j -1.45470879e-16-1.93941966e-16j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-3.39065652e-05-1.43354658e-05j  4.55580398e-17-1.88494582e-16j\n", "   7.07069968e-01+1.94289029e-16j  7.07069974e-01+0.00000000e+00j]\n", " [ 2.90579151e-06-1.43347195e-05j -2.96969027e-17-2.33757138e-16j\n", "   2.90579151e-06+1.43347195e-05j -8.91293647e-17-2.88059338e-16j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-7.16715035e-04+3.53566627e-03j -7.16671842e-04+3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 2.90579151e-06-1.43347195e-05j  1.02347029e-16-2.08124180e-16j\n", "   2.90579150e-06+1.43347195e-05j  1.00129106e-16-1.41483981e-16j]\n", " [-3.39065652e-05-1.43354658e-05j -1.00511967e-16+1.62170647e-16j\n", "   7.07069968e-01+0.00000000e+00j -7.07069974e-01-2.77555756e-16j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 2.90579151e-06-1.43347195e-05j  1.57295433e-18+1.90454756e-16j\n", "   2.90579150e-06+1.43347195e-05j -8.34535230e-17-1.48258293e-16j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j  7.16671842e-04+3.53545319e-03j]\n", " [-7.16715035e-04+3.53566627e-03j  7.16671842e-04-3.53545319e-03j\n", "   7.16715035e-04+3.53566627e-03j -7.16671842e-04-3.53545319e-03j]\n", " [ 7.07069968e-01+0.00000000e+00j -7.07069974e-01+5.13478149e-16j\n", "  -3.39065652e-05+1.43354658e-05j -1.23213537e-17+4.50832815e-17j]]\n", "2.741633200576899\n", "-0.3999594595108499\n", "-2.741633200576899\n", "0.3999594595108494\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/181441442.py:56: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25391 (\\N{CJK UNIFIED IDEOGRAPH-632F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24133 (\\N{CJK UNIFIED IDEOGRAPH-5E45}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21521 (\\N{CJK UNIFIED IDEOGRAPH-5411}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22522 (\\N{CJK UNIFIED IDEOGRAPH-57FA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30690 (\\N{CJK UNIFIED IDEOGRAPH-77E2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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*****************************************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", "text/plain": ["<Figure size 1600x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_selected_vectors(ipr,v, ipr_threshold):\n", "    \"\"\"\n", "    筛选IPR大于阈值的列向量并绘制\n", "    \n", "    参数:\n", "    V: 输入矩阵，每行代表一个基矢，每列代表一个向量\n", "    ipr_threshold: IPR筛选阈值，默认0.5\n", "    \"\"\"\n", "    # 计算IPR\n", "    print(len(ipr))\n", "    # 筛选IPR大于阈值的列索引\n", "    selected_indices = np.where(ipr > ipr_threshold)[0]\n", "    if len(selected_indices) == 0:\n", "        print(f\"没有找到IPR大于{ipr_threshold}的向量\")\n", "        return\n", "    \n", "    print(f\"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}\")\n", "    \n", "    # 提取对应的列向量\n", "    selected_vectors = v[:,selected_indices]\n", "    print(selected_vectors)\n", "    \n", "    \n", "    # 设置绘图风格\n", "    plt.style.use('seaborn-v0_8-ticks')\n", "    \n", "    # 计算子图布局（最多5列）\n", "    n_cols = min(5, len(selected_indices))\n", "    n_rows = (len(selected_indices) + n_cols - 1) // n_cols\n", "    \n", "    # 创建画布\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))\n", "    axes = np.ravel(axes)  # 转换为一维数组便于索引\n", "    for i, idx in enumerate(selected_indices):\n", "        E_slect = quasienergy1[idx]\n", "        print(E_slect)\n", "    # 绘制每个选中的向量\n", "    for i, idx in enumerate(selected_indices):\n", "        ax = axes[i]\n", "        # 绘制向量的绝对值点线图\n", "        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')\n", "        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))\n", "        ax.set_title(f'向量索引: {idx}\\nIPR = {ipr[idx]:.4f}', fontsize=10)\n", "        ax.set_xlabel('基矢索引', fontsize=8)\n", "        ax.set_ylabel('|振幅|', fontsize=8)\n", "        ax.tick_params(axis='both', which='major', labelsize=6)\n", "    \n", "    # 隐藏未使用的子图\n", "    for i in range(len(selected_indices), len(axes)):\n", "        axes[i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return selected_vectors, selected_indices\n", "\n", "# 示例使用\n", "if __name__ == \"__main__\":\n", "    # 筛选并绘图\n", "    selected_vecs_k, selected_idx_k = plot_selected_vectors(IPR_K,full_vecs @ full_vecs_eig, ipr_threshold=0.4)\n", "    selected_vecs, selected_idx = plot_selected_vectors(IPR,V, ipr_threshold=0.4)\n"]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 25490 (\\N{CJK UNIFIED IDEOGRAPH-6392}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 21518 (\\N{CJK UNIFIED IDEOGRAPH-540E}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 30340 (\\N{CJK UNIFIED IDEOGRAPH-7684}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 26412 (\\N{CJK UNIFIED IDEOGRAPH-672C}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 24449 (\\N{CJK UNIFIED IDEOGRAPH-5F81}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_30555/1056926466.py:23: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25490 (\\N{CJK UNIFIED IDEOGRAPH-6392}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21518 (\\N{CJK UNIFIED IDEOGRAPH-540E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30340 (\\N{CJK UNIFIED IDEOGRAPH-7684}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 26412 (\\N{CJK UNIFIED IDEOGRAPH-672C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24449 (\\N{CJK UNIFIED IDEOGRAPH-5F81}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ipr_full_sorted = np.sort(IPR)\n", "ipr_block_sorted = np.sort(IPR_K)\n", "# 步骤6：可视化对比\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# 子图1：排序后的IPR对比（散点图）\n", "ax1.scatter(range(len(ipr_full_sorted)), ipr_full_sorted, label=\"full space\", alpha=0.8, s=50)\n", "ax1.scatter(range(len(ipr_block_sorted)), ipr_block_sorted, label=\"block space\", alpha=0.8, s=30, marker=\"x\")\n", "ax1.set_xlabel(\"排序后的本征态索引\")\n", "ax1.set_ylabel(\"IPR值\")\n", "#ax1.set_title(f\"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）\")\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "ax2.scatter(range(len(ipr_full_sorted)), np.sort(quasienergy), label=\"full space\", alpha=0.8, s=50)\n", "ax2.scatter(range(len(ipr_block_sorted)), np.sort(quasienergy_k), label=\"block space\", alpha=0.8, s=30, marker=\"x\")\n", "ax2.set_xlabel(\"排序后的本征态索引\")\n", "ax2.set_ylabel(\"E值\")\n", "#ax1.set_title(f\"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）\")\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 假设分块计算的本征值和IPR为 evals_block, ipr_block\n", "# 整体计算的为 evals_full, ipr_full\n", "matched_ipr = []\n", "for e_block, ipr_b in zip(E_blocks, IPR_K):\n", "    # 找到整体计算中与e_block接近的本征值\n", "    idx = np.argmin(np.abs(quasienergy1 - e_block))\n", "    matched_ipr.append((ipr_b, IPR[idx]))\n", "# 查看匹配后的IPR是否一致\n", "print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')\n", "plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}