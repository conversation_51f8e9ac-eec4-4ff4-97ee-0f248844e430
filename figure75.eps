%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 388 293
%%HiResBoundingBox: 0.000000 0.000000 387.306615 292.537937
%%Invocation: gs -dBATCH -dNOPAUSE -r6000 -sDEVICE=ps2write -dEPSCrop -sOutputFile=? ?
%%Creator: GPL Ghostscript 10030 (ps2write)
%%LanguageLevel: 2
%%CreationDate: D:20250826174247+08'00'
%%EndComments
%%BeginProlog
save
countdictstack
mark
newpath
/showpage {} def
/setpagedevice {pop} def
%%EndProlog
%%Page 1 1
%%BeginProlog
10 dict dup begin
/DSC_OPDFREAD true def
/SetPageSize true def
/EPS2Write false def
end
count 0 ne{
dup type/dicttype eq{
dup/EPS2Write known{
dup/EPS2Write get not
}
{
true
}ifelse
}
{
true
}ifelse
}
{
true
}ifelse
10 dict begin
/this currentdict def
/y 720 def
/ebuf 200 string def
/prnt{
36//this/y get moveto//ebuf cvs show
//this/y 2 copy get 12 sub put
}bind def
/newline{
36//this/y get moveto
//this/y 2 copy get 12 sub put
}bind def
{
errordict/handleerror
{systemdict begin
$error begin
newerror
{(%%[ Error handled by opdfread.ps : )print errorname//ebuf cvs print(; OffendingCommand: )
print/command load//ebuf cvs print( ]%%)= flush
/newerror false store vmstatus pop pop 0 ne
{grestoreall
}if
errorname(VMerror)ne
{showpage
}if
initgraphics
0 720 moveto
errorname(VMerror)eq
{//this/ehsave known
{clear//this/ehsave get restore 2 vmreclaim
}if
vmstatus exch pop exch pop
}
/Courier 12 selectfont
{
(ERROR: )//prnt exec errorname//prnt exec
(OFFENDING COMMAND: )//prnt exec
/command load//prnt exec
$error/ostack known{
(%%[STACK:)=
(STACK:)//prnt exec
$error/ostack get aload length{
//newline exec
dup mark eq{
(-mark-)dup = show
}{
dup type/nametype eq{
dup xcheck not{
(/)show
(/)print
}if
}if
dup =//ebuf cvs show
}ifelse
}repeat
}if
}ifelse
(%%]%)=
//systemdict/showpage get exec
quit
}if
end
end
}bind readonly put
}if
end
50 dict begin
count 0 ne{
dup type/dicttype eq{
{def}forall
false
}
{
true
}ifelse
}
{
true
}ifelse
{
(   *** Warning: global definitions dictionary not found, file may be corrupted.\n)print flush
}if
/DefaultSwitch
{
dup where{
pop pop
}{
false def
}ifelse
}bind def
/=string 256 string def
/=only{
//=string cvs print
}bind def
/HexDigits(0123456789ABCDEF)readonly def
/PrintHex
{8{
dup -28 bitshift 15 and//HexDigits exch 1 getinterval//=only exec
4 bitshift
}repeat
pop
}bind def
/PDFR_DEBUG DefaultSwitch
/PDFR_DUMP DefaultSwitch
/PDFR_STREAM DefaultSwitch
/TTFDEBUG DefaultSwitch
/RotatePages DefaultSwitch
/FitPages DefaultSwitch
/CenterPages DefaultSwitch
/SetPageSize DefaultSwitch
/error
{
counttomark 1 sub -1 0{
index dup type/arraytype eq{==}{=only}ifelse
}for
()=
cleartomark
....Undefined
}bind def
//SetPageSize{
//RotatePages//FitPages or//CenterPages or{
mark(/RotatePages, /FitPages and CenterPages are not allowed with /SetPageSize)//error exec
}if
}
{
//FitPages//CenterPages and{
mark(CenterPages is not allowed with /FitPages)//error exec
}if
}
ifelse
/knownget
{
2 copy known{
get true
}{
pop pop false
}ifelse
}bind def
/IsUpper
{dup(A)0 get ge exch(Z)0 get le and
}bind def
/cpa2g{
dup length array
0 1 2 index length 1 sub{
dup 3 index exch get cp2g
3 copy put pop pop
}for
exch pop
}bind def
/cpd2g{
dup length dict exch{
cp2g 2 index 3 1 roll put
}forall
}bind def
/cps2g{
dup length string copy
}bind def
/cp2gprocs
<</arraytype//cpa2g/dicttype//cpd2g/packedarraytype//cpa2g/stringtype//cps2g >>
def
/cp2g{
dup gcheck not{
dup//cp2gprocs 1 index type
2 copy known{
get currentglobal 3 1 roll true setglobal exec exch setglobal
1 index wcheck not{readonly}if
1 index xcheck{cvx}if
exch pop
}{
pop pop
}ifelse
}if
}bind def
/BlockBuffer 65535 string def
/PDFReader currentdict def
/ObjectRegistryMaxLength 50000 def
/ObjectRegistry 10 dict def
ObjectRegistry
begin
0 ObjectRegistryMaxLength dict def
end
/CurrentObject null def
/DoneDocumentStructure false def
/GraphicState 20 dict begin
/InitialTextMatrix matrix def
/InitialMatrix matrix currentmatrix def
currentdict end def
/TempMatrix matrix def
/GraphicStateStack 20 array def
/GraphicStateStackPointer 0 def
/InitialTextMatrixStack 20 array def
/InitialTextMatrixStackPointer 0 def
/PDFColorSpaces 50 dict def
/InstalledFonts 50 dict def
/MacRomanEncodingInverse null def
currentglobal false setglobal
userdict/PDFR_InitialGS gstate put
userdict/PDFR_Patterns 50 dict put
userdict/FuncDataReader 10 dict put
setglobal
/InitialExtGState 20 dict begin
/BG2 currentblackgeneration cp2g def
/UCR2 currentundercolorremoval cp2g def
/TR2 currentglobal false setglobal[currentcolortransfer]exch setglobal cp2g def
/HT currenthalftone cp2g def
currentdict end readonly def
/InitialGraphicState 20 dict begin
/FontSize 0 def
/CharacterSpacing 0 def
/TextLeading 0 def
/TextRenderingMode 0 def
/WordSpacing 0 def
currentdict end readonly def
/SimpleColorSpaceNames 15 dict begin
/DeviceGray true def
/DeviceRGB true def
/DeviceCMYK true def
currentdict end readonly def
/1_24_bitshift_1_sub 1 24 bitshift 1 sub def
/ReadFontProcs 10 dict def
/GetObject
{
dup ObjectRegistryMaxLength idiv
//PDFReader/ObjectRegistry get exch knownget{
exch knownget
}{
pop false
}ifelse
}bind def
/PutObject
{
1 index ObjectRegistryMaxLength idiv
//PDFReader/ObjectRegistry get 1 index knownget{
exch pop
3 1 roll put
}{
//PDFReader/ObjectRegistry get dup
begin
1 index ObjectRegistryMaxLength dict def
end
exch get
3 1 roll put
}ifelse
}bind def
/Register
{
1 index GetObject{
dup xcheck{
4 3 roll pop
//PDFR_DEBUG{
(Have a daemon for )print 2 index ==
}if
exec
}{
dup null ne{
mark(The object )4 index(is already defined : )4 index//error exec
}{
pop
}ifelse
3 2 roll
exec
}ifelse
}{
3 2 roll
exec
}ifelse
PutObject
}bind def
/IsRegistered
{
GetObject{
null ne
}{
false
}ifelse
}bind def
/GetRegistered
{
dup GetObject not{
exch mark exch(Object )exch( isn't defined before needed (1).)//error exec
}if
dup xcheck{
exch mark exch(Object )exch( isn't defined before needed (2).)//error exec
}{
dup null eq{
exch mark exch(Object )exch( isn't defined before needed (3).)//error exec
}if
exch pop
}ifelse
}bind def
/StandardFontNames<<
/Times-Roman true
/Helvetica true
/Courier true
/Symbol true
/Times-Bold true
/Helvetica-Bold true
/Courier-Bold true
/ZapfDingbats true
/Times-Italic true
/Helvetica-Oblique true
/Courier-Oblique true
/Times-BoldItalic true
/Helvetica-BoldOblique true
/Courier-BoldOblique true
>>def
/CleanAllResources
{//PDFR_DEBUG{
(CleanAllResources beg)=
}if
//PDFReader/ObjectRegistry get{
dup length 0 exch 1 exch 1 sub{
2 copy get dup xcheck{
pop pop
}{
dup null eq{
pop pop
}{
dup type/dicttype eq{/.Global known}{pop false}ifelse{
pop
}{
//PDFR_DEBUG{
(Dropping )print dup =
}if
1 index exch/DroppedObject put
}ifelse
}ifelse
}ifelse
}for
pop
}forall
FontDirectory length dict begin
FontDirectory{
pop
dup//StandardFontNames exch known not{
dup null def
}if
pop
}forall
currentdict
end{
pop
//PDFR_DEBUG{
(Undefining font )print dup =
}if
undefinefont
}forall
//PDFR_DEBUG{
(CleanAllResources end)=
}if
}bind def
/PrintReference
{
//PDFR_DEBUG{
({ )print
dup{
=only( )print
}forall
( })=
}if
}bind def
/R
{
0 ne{
exch mark exch(A referred object generation )exch( isn't 0.)//error exec
}if
[
exch//GetRegistered/exec load
]cvx
//PrintReference exec
}bind def
/IsObjRef
{
dup type/arraytype eq{
dup length 3 eq{
dup xcheck exch
dup 0 get type/integertype eq 3 2 roll and exch
dup 1 get//GetRegistered eq 3 2 roll and exch
2 get/exec load eq and
}{
pop false
}ifelse
}{
pop false
}ifelse
}bind def
/DoNothing
{
}def
/RunTypeDaemon
{
dup type/dicttype eq{
dup/Type//knownget exec{
//PDFReader/TypeDaemons get exch
//knownget exec{
exec
}if
}if
}if
}bind def
/obj
{
//PDFR_DEBUG{
(Defining )print 1 index =only( )print dup =only( obj)=
}if
0 ne{
exch mark exch(An object generation )exch( isn't 0.)//error exec
}if
}bind def
/endobj
{
//PDFR_DEBUG{
(endobj )=
}if
count 1 eq{
pop
}{
dup type/dicttype eq{
dup/.endobj_daemon//knownget exec{
//PDFR_DEBUG{(.endobj_daemon for )print 2 index =}if
exec
}if
}if
dup type/dicttype eq{dup/ImmediateExec known}{false}ifelse{
pop pop
}{
//PDFR_DEBUG{
(Storing )print 1 index =
}if
//RunTypeDaemon exec
//DoNothing 3 1 roll//Register exec
}ifelse
}ifelse
}bind def
/StoreBlock
{
//PDFR_DEBUG{
(StoreBlock )print//PDFReader/BlockCount get =only(, Length = )print dup length =
}if
dup length string copy
//PDFReader/BlockCount get exch
//PDFReader/CurrentObject get 3 1 roll
put
//PDFReader/BlockCount get 1 add
//PDFReader exch/BlockCount exch put
}bind def
/CheckLength
{dup type/integertype ne{
mark(Object length isn't an integer.)//error exec
}if
}bind def
/ResolveD
{
3 copy pop get
dup//IsObjRef exec{
//PDFR_DEBUG{
(Resolving )print//PrintReference exec
}if
exec
exch exec
}{
exch pop
}ifelse
dup 4 1 roll
put
}bind def
/ResolveA
{2 index 2 index get
dup//IsObjRef exec{
exec
exch exec
3 copy put
}{
exch pop
}ifelse
exch pop exch pop
}bind def
/StoreStream
{
dup//PDFReader exch/CurrentObject exch put
//PDFReader/BlockCount 0 put
dup/Length//CheckLength//ResolveD exec
//PDFR_DEBUG{
(StoreStream Length = )print dup =
}if
currentfile exch()/SubFileDecode filter
{dup//BlockBuffer readstring{
//StoreBlock exec
}{
//StoreBlock exec
exit
}ifelse
}loop
pop
//PDFReader/CurrentObject null put
//PDFR_DEBUG{
(StoreStream end.)=
}if
}bind def
/MakeStreamDumper
{
//PDFR_DEBUG{
(MakeStreamDumper beg.)=
}if
currentglobal exch dup gcheck setglobal
[exch
1 dict dup/c 0 put exch
1024 string
{readstring pop
(StreamDumper )print 1 index/c get =string cvs print( )print
dup length =string cvs print( <)print dup print(>\n)print
dup length
3 2 roll
dup/c get
3 2 roll
add/c exch put
}/exec load
]
cvx 0()/SubFileDecode filter
exch setglobal
//PDFR_DEBUG{
(MakeStreamDumper end.)=
}if
}bind def
/ShortFilterNames 15 dict begin
/AHx/ASCIIHexDecode def
/A85/ASCII85Decode def
/LZW/LZWDecode def
/Fl/FlateDecode def
/RL/RunLengthDecode def
/CCF/CCITTFaxDecode def
/DCT/DCTDecode def
currentdict end readonly def
/AppendFilters
{
//PDFR_DEBUG{
(AppendFilters beg.)=
}if
dup 3 1 roll
/Filter//knownget exec{
dup type/nametype eq{
dup//ShortFilterNames exch//knownget exec{
exch pop
}if
2 index/DecodeParms//knownget exec{
exch
}if
filter
}{
dup 0 exch 1 exch length 1 sub{
2 copy get
dup//ShortFilterNames exch//knownget exec{
exch pop
}if
3 1 roll
4 index/DecodeParms//knownget exec{
exch get
}{
pop null
}ifelse
dup null eq{
pop 3 1 roll filter exch
}{
3 1 roll
4 1 roll filter exch
}ifelse
}for
pop
}ifelse
//PDFR_DEBUG//PDFR_DUMP and{
//MakeStreamDumper exec
}if
}if
exch pop
//PDFR_DEBUG{
(AppendFilters end.)=
}if
}bind def
/ExecuteStream
{
dup//PDFReader exch/CurrentObject exch put
dup/Length//CheckLength//ResolveD exec
//PDFR_DEBUG{
(ExecuteStream id = )print 2 index =only( Length = )print dup =
}if
//PDFReader/InitialGraphicState get
//PDFReader/GraphicState get copy pop
//PDFReader/Operators get begin
currentfile exch()/SubFileDecode filter
1 index//AppendFilters exec
cvx mark exch
exec
counttomark 0 ne{
mark(Data left on ostack after an immediate stream execution.)//error exec
}if
cleartomark
end
//PDFR_DEBUG{
(ExecuteStream end.)=
}if
//PDFReader/CurrentObject null put
dup/IsPage known{
dup/Context get/NumCopies//knownget exec{
1 sub{
copypage
}repeat
}if
EPS2Write not{showpage}if
pagesave restore
}if
}bind def
/stream
{
//PDFR_DEBUG{
1 index =only( stream)=
}if
1 index GetObject{
dup xcheck{
exec
1 index null PutObject
}{
pop
}ifelse
}if
dup/ImmediateExec known{
dup/GlobalExec//knownget exec{
currentglobal 4 1 roll
setglobal
//ExecuteStream exec
3 2 roll setglobal
}{
//ExecuteStream exec
}ifelse
}{
//StoreStream exec
}ifelse
dup/.CleanResources//knownget exec{
/All eq{
//CleanAllResources exec
}if
}if
}bind def
/HookFont
{
//PDFR_DEBUG{
(Loaded the font )print dup/FontName get =
}if
{
dup/FontFileType get dup/Type1 eq exch/MMType1 eq or{
dup/FontName get
//PDFReader/RemoveFontNamePrefix get exec
findfont
exit
}if
dup/FontFileType get/TrueType eq{
//PDFReader/MakeType42 get exec
//PDFR_DEBUG{
(Font dict <<)=
dup{
1 index/sfnts eq{
exch pop
(/sfnts [)print
{
(-string\()print length//=only exec(\)- )=
}forall
(])=
}{
exch//=only exec( )print ==
}ifelse
}forall
(>>)=
}if
dup/FontName get exch definefont
exit
}if
mark(FontHook has no proc for )2 index/FontFileType get//error exec
}loop
/Font exch put
}bind def
/endstream
{
}bind def
/xref
{
//PDFR_DEBUG{
(xref)=
//PDFR_DUMP{
//PDFReader/ObjectRegistry get ==
}if
}if
end
count 0 ne{
mark(Excessive data on estack at the end of the interpretation.)//error exec
}if
currentfile 1(%%EOF)/SubFileDecode filter
flushfile
cleardictstack
}bind def
/ResolveDict
{dup{
pop 1 index exch
//DoNothing//ResolveD exec
pop
}forall
pop
}bind def
/SetupPageView
{
//PDFR_DEBUG{
(SetupPageView beg)=
}if
//DSC_OPDFREAD not{
//GraphicState/InitialMatrix get setmatrix
}if
/MediaBox get aload pop
3 index neg 3 index neg translate
3 -1 roll sub 3 1 roll exch sub exch
userdict/.HWMargins//knownget exec{
aload pop
}{
currentpagedevice/.HWMargins//knownget exec{
aload pop
}{
0 0 0 0
}ifelse
}ifelse
currentpagedevice/PageSize get aload pop
3 -1 roll sub 3 1 roll exch sub exch
exch 3 index sub exch 3 index sub
//SetPageSize{
//PDFR_DEBUG{
(Setting page size to )print 1 index//=only exec( )print dup =
}if
pop pop 3 index 3 index 2 copy
currentglobal false setglobal 3 1 roll
currentpagedevice dup/PageSize known{
/PageSize get aload pop
}{
0 0
}ifelse
round cvi 2 index round cvi eq
exch round cvi 3 index round cvi eq and
{
//PDFR_DEBUG{(PageSize matches request)== flush}if
pop pop
}{
/MediaRequested where{
//PDFR_DEBUG{(MediaRequested is true, check against new request)== flush}if
/MediaRequested get aload pop
round cvi 2 index round cvi eq
exch round cvi 3 index round cvi eq and
{
//PDFR_DEBUG{(MediaRequested same as current request, ignore)== flush}if
pop pop false
}{
//PDFR_DEBUG{(MediaRequested different to current request)== flush}if
true
}ifelse
}{
//PDFR_DEBUG{(No MediaRequested yet)== flush}if
true
}ifelse
{
//PDFR_DEBUG{(Setting pagesize)== flush}if
2 array astore
dup/MediaRequested exch def
<< exch/PageSize exch >>setpagedevice
}if
}ifelse
userdict/PDFR_InitialGS gstate put
setglobal
}if
//RotatePages{
2 copy gt 6 index 6 index gt ne{
1 index 5 index le 1 index 5 index le and not
}{
false
}ifelse
}{
false
}ifelse
{//CenterPages{
//PDFR_DEBUG{
(Rotating page, and then centering it)==
}if
90 rotate
0 5 index neg translate
5 index 1 index exch sub 2 div
2 index 6 index sub 2 div neg
translate
}{
//FitPages{
1 index 5 index div 1 index 7 index div
2 copy gt{
exch
}if
pop dup scale
}if
90 rotate
0 5 index neg translate
}ifelse
}{
//CenterPages{
//PDFR_DEBUG{
(Ccentering page)==
}if
1 index 6 index sub 2 div
1 index 6 index sub 2 div
translate
}{
//FitPages{
1 index 6 index div 1 index 6 index div
2 copy gt{
exch
}if
pop dup scale
}if
}ifelse
}ifelse
pop pop
translate
pop pop
//PDFR_DEBUG{
(SetupPageView end)=
}if
}bind def
/PageContentsDaemon
{
//PDFR_DEBUG{
(Executing PageContentsDaemon for )print 2 index =
}if
1 index exch/Context exch put
dup/ImmediateExec true put
/pagesave save def
dup/IsPage true put
SetPageSize{dup/Context get//SetupPageView exec}if
}bind def
/FontFileDaemon
{
//PDFR_DEBUG{
(Executing FontFileDaemon for )print 2 index =
}if
dup/FontFileType get
2 index exch
dup//ReadFontProcs exch//knownget exec{
exch pop exec
}{
mark(FontFile reader for )2 index( isn't implemented yet.)//error exec
}ifelse
//PDFR_DEBUG{
(FontFileDaemon end)=
}if
pop
}bind def
/FontDescriptorDaemon
{
//PDFR_DEBUG{
(Executing FontDescriptorDaemon for )print 2 index =
}if
2 copy/FontResource exch put
/Subtype get 1 index exch/FontFileType exch put
}bind def
/UnPDFEscape{
dup dup length string cvs
dup(#)search{
{
pop
(16#--)2 index 0 2 getinterval
1 index 3 2 getinterval copy pop
cvi
0 exch put
0
1 index 2 1 index length 2 sub getinterval
3 copy putinterval
length
3 copy exch put
getinterval
(#)search not{
pop exit
}if
}loop
(\0)search pop exch pop exch pop
cvn
exch pop
}{
pop pop
}ifelse
}bind def
/TypeDaemons<<
/Page
{//PDFR_DEBUG{
(Recognized a page.)=
}if
dup/Contents//knownget exec{
0 get//DoNothing exch
[
3 index//PageContentsDaemon/exec load
]cvx
//Register exec
}{
(fixme: page with no Contents won't be printed.)=
}ifelse
}bind
/FontDescriptor
{//PDFR_DEBUG{
(Recognized a font descriptor.)=
}if
dup/FontName//knownget exec{
1 index/FontName 3 -1 roll//UnPDFEscape exec put
}if
dup dup/FontFile known{/FontFile}{/FontFile2}ifelse
//knownget exec{
0 get//DoNothing exch
[
3 index//FontFileDaemon/exec load
]cvx
//Register exec
}{
(Font descriptor )print 1 index =only( has no FontFile.)=
}ifelse
}bind
/Font
{//PDFR_DEBUG{
(Recognized a font resource.)=
}if
dup/BaseFont//knownget exec{
//UnPDFEscape exec 2 copy/BaseFont exch put
//PDFReader/RemoveFontNamePrefix get exec
currentglobal exch
dup/Font resourcestatus{
pop pop
//PDFReader/GetInstalledFont get exec pop
}{
pop
}ifelse
setglobal
}if
dup/FontDescriptor//knownget exec{
0 get
dup//IsRegistered exec{
//PDFR_DEBUG{
(already registered )print dup =
}if
pop
}{
//DoNothing exch
[
3 index//FontDescriptorDaemon/exec load
]cvx
//Register exec
}ifelse
}if
}bind
>>def
/MakeStreamReader
{dup
[
exch
//PDFR_DEBUG{
(Stream proc )
/print load
//PDFR_STREAM{
(<)
/print load
}if
}if
1 dict dup/i -1 put
/dup load
/i
/get load
1
/add load
/dup load
3
1
/roll load
/i
/exch load
/put load
//knownget
/exec load
/not load
{()}
/if load
//PDFR_DEBUG{
//PDFR_STREAM{
/dup load
/print load
(>)
/print load
}if
( end of stream proc.\n)
/print load
}if
]cvx
//PDFR_DEBUG{
(Stream reader )print dup ==
}if
0()/SubFileDecode filter
exch//AppendFilters exec
}bind def
/RunDelayedStream
{
//GraphicState/InitialTextMatrix get
//InitialTextMatrixStack//PDFReader/InitialTextMatrixStackPointer get
2 copy get null eq{
2 copy currentglobal true setglobal matrix exch setglobal put
}if
get copy pop
//PDFReader/InitialTextMatrixStackPointer 2 copy get 1 add put
//MakeStreamReader exec
mark exch
cvx exec
counttomark 0 ne{
mark(Data left on ostack after a delayed stream execution.)//error exec
}if
cleartomark
//PDFReader/InitialTextMatrixStackPointer 2 copy get 1 sub put
//InitialTextMatrixStack//PDFReader/InitialTextMatrixStackPointer get get
//GraphicState/InitialTextMatrix get
copy pop
}bind def
//ReadFontProcs begin
/Type1
{//PDFR_DEBUG{
(ReadFontProcs.Type1)=
}if
dup/.endobj_daemon[4 index//HookFont/exec load]cvx put
dup/ImmediateExec true put
/GlobalExec true put
}bind def
/MMType1//Type1 def
/TrueType
{//PDFR_DEBUG{
(ReadFontProcs.TrueType)=
}if
dup/.endobj_daemon[4 index//HookFont/exec load]cvx put
pop
}bind def
end
/.opdloadttfontdict 50 dict def
.opdloadttfontdict begin
/maxstring 65400 def
end
/.InsertionSort
{
/CompareProc exch def
/Array exch def
1 1 Array length 1 sub
{
/Ix exch def
/Value1 Array Ix get def
/Jx Ix 1 sub def
{
Jx 0 lt{
exit
}if
/Value2 Array Jx get def
Value1 Value2 CompareProc{
exit
}if
Array Jx 1 add Value2 put
/Jx Jx 1 sub def
}loop
Array Jx 1 add Value1 put
}for
Array
}bind def
/putu16{
3 copy -8 bitshift put
exch 1 add exch 16#ff and put
}bind def
/putu32{
3 copy -16 bitshift putu16
exch 2 add exch 16#ffff and putu16
}bind def
/.readtable{
dup dup 1 and add string
dup 0 4 -1 roll getinterval
3 -1 roll exch
dup()ne{readstring}if pop pop
}bind def
/.readbigtable{
dup maxstring lt{
.readtable
}{
currentuserparams/VMReclaim get -2 vmreclaim
[4 2 roll{
dup maxstring le{exit}if
1 index maxstring string readstring pop 3 1 roll maxstring sub
}loop .readtable]
exch vmreclaim
}ifelse
}bind def
/ReadTTF
{
.opdloadttfontdict begin
/TTFontFile exch def
/TableDir TTFontFile 12 string readstring pop def
/tables TTFontFile TableDir 4 getu16 16 mul string readstring pop def
/tabarray tables length 16 idiv array def
TableDir 0 4 getinterval(ttcf)eq{
QUIET not{(Can't handle TrueType font Collections.)=}if
/.loadttfonttables cvx/invalidfont signalerror
}{
0 16 tables length 1 sub{
dup
tables exch 16 getinterval
exch 16 div cvi exch
tabarray 3 1 roll put
}for
}ifelse
tabarray{exch 8 getu32 exch 8 getu32 gt}.InsertionSort pop
/Read TableDir length tables length add def
/tabs[
tabarray{
dup 8 getu32
Read sub
dup 0 gt{
dup string TTFontFile exch readstring pop pop
Read add/Read exch def
}{
pop
}ifelse
12 getu32
dup Read add
/Read exch def
TTFontFile exch .readbigtable
}forall
]def
end
}bind def
/GetLocaType
{
0 1 tabarray length 1 sub{
dup tabarray exch get
0 4 getinterval(head)eq{
tabs exch get
50 gets16
/LocaType exch def
exit
}{
pop
}ifelse
}for
}bind def
/GetNumGlyphs
{
0 1 tabarray length 1 sub{
dup tabarray exch get
0 4 getinterval(maxp)eq{
tabs exch get
4 getu16
/NumGlyphs exch def
exit
}{
pop
}ifelse
}for
}bind def
/StringToLoca
{
/LocaIndex exch def
/StringOffset 0 def
{
dup length StringOffset gt{
dup
LocaType 1 eq{
StringOffset getu32
LocaArray LocaIndex 3 -1 roll put
/LocaIndex LocaIndex 1 add def
/StringOffset StringOffset 4 add
def
}{
StringOffset getu16 2 mul
LocaArray length LocaIndex gt{
LocaArray LocaIndex 3 -1 roll put
}{
pop
}ifelse
/LocaIndex LocaIndex 1 add def
/StringOffset StringOffset 2 add
def
}ifelse
}{
pop
LocaIndex
exit
}ifelse
}loop
}bind def
/GetSortedLoca
{
NumGlyphs 1 add array/LocaArray exch def
0 1 tabarray length 1 sub{
dup tabarray exch get
0 4 getinterval(loca)eq{
tabs exch get
exit
}{
pop
}ifelse
}for
dup type/stringtype eq{
0 StringToLoca pop
}{
0 exch
{
exch StringToLoca
}forall
pop
}ifelse
LocaArray{gt}.InsertionSort pop
}bind def
/GetWorkingString
{
WorkString 0
GlyfArray GlyfStringIndex get
putinterval
/WorkBytes GlyfArray GlyfStringIndex get length def
/GlyfStringIndex GlyfStringIndex 1 add def
}bind def
/GetWorkingBytes
{
/BytesToRead exch def
WorkString 0 BytesToRead getinterval
dup length string copy
WorkString BytesToRead WorkBytes BytesToRead sub getinterval
dup length string copy
WorkString 0 3 -1 roll putinterval
/WorkBytes WorkBytes BytesToRead sub def
}bind def
/GetGlyfBytes
{
/ToRead exch def
WorkBytes 0 eq{
GetWorkingString
}if
WorkBytes ToRead ge{
ToRead string dup 0
ToRead GetWorkingBytes putinterval
}{
ToRead string
dup
0
WorkString 0 WorkBytes getinterval
putinterval
dup
WorkBytes
ToRead WorkBytes sub
GetWorkingString
GetWorkingBytes
putinterval
}ifelse
}bind def
/SplitGlyf
{
/GlyfArray exch def
/DestArray GlyfArray length 2 mul array def
/DestArrayIndex 0 def
/LastLoca 0 def
/NextLocaIndex 0 def
/LastLocaIndex 0 def
/GlyfStringIndex 0 def
/WorkString maxstring string def
/WorkBytes 0 def
{
LocaArray NextLocaIndex get
LastLoca sub maxstring gt
{
LocaArray LastLocaIndex get LastLoca sub
GetGlyfBytes
DestArray DestArrayIndex 3 -1 roll put
/DestArrayIndex DestArrayIndex 1 add def
LocaArray LastLocaIndex get/LastLoca exch def
}{
/LastLocaIndex NextLocaIndex def
/NextLocaIndex NextLocaIndex 1 add def
NextLocaIndex NumGlyphs gt
{
WorkBytes
GlyfStringIndex GlyfArray length lt{
GlyfArray GlyfStringIndex get length
add string dup
0
WorkString 0 WorkBytes getinterval
putinterval
dup
WorkBytes
GetWorkingString
WorkString 0 WorkBytes getinterval
putinterval
}{
pop
WorkString 0 WorkBytes getinterval
}ifelse
dup length string copy
DestArray DestArrayIndex 3 -1 roll put
exit
}if
}ifelse
}loop
DestArray
}bind def
/ProcessTTData
{
.opdloadttfontdict begin
0 1 tabarray length 1 sub{
/ix exch def
tabarray ix get
12 getu32 dup maxstring le{
dup 4 mod 0 ne{
4 div cvi 1 add 4 mul string/newstring exch def
/oldstring tabs ix get def
newstring 0 oldstring putinterval
0 1 newstring length oldstring length sub 1 sub{
newstring exch oldstring length add 0 put
}for
tabs ix newstring put
}{
pop
}ifelse
}{
dup 4 mod 0 ne{
dup maxstring idiv maxstring mul sub
4 idiv 1 add 4 mul string/newstring exch def
tabs ix get
dup length 1 sub dup/iy exch def get/oldstring exch def
newstring 0 oldstring putinterval
0 1 newstring length oldstring length sub 1 sub{
newstring exch oldstring length add 0 put
}for
tabs ix get iy newstring put
}{
pop
}ifelse
}ifelse
}for
0 1 tabarray length 1 sub{
dup tabarray exch get
dup 12 getu32 maxstring gt{
0 4 getinterval dup(glyf)eq{
pop
GetLocaType
GetNumGlyphs
GetSortedLoca
dup tabs exch get
SplitGlyf
tabs 3 1 roll put
}{
(Warning, table )print print( > 64Kb\n)print
pop
}ifelse
}{
pop
pop
}ifelse
}for
end
}bind def
/Makesfnts
{
.opdloadttfontdict begin
0
tabs{
dup type/stringtype eq{
pop
1 add
}{
{
type/stringtype eq{
1 add
}if
}forall
}ifelse
}forall
1 add
/TTOffset
TableDir length
tabarray length 16 mul add
def
0
tabarray{
exch dup 1 add
3 1 roll
dup
tabs exch get
dup type/stringtype eq{
length
2 index exch
TTOffset
dup 3 1 roll add
/TTOffset exch def
8 exch putu32
exch tabarray 3 1 roll
put
}{
0 exch
{
dup type/stringtype eq{
length add
}{
pop
}ifelse
}forall
2 index exch
TTOffset
dup 3 1 roll add
/TTOffset exch def
8 exch putu32
exch tabarray 3 1 roll
put
}ifelse
}forall
pop
array
dup 0
TableDir length
tables length add
string
dup 0 TableDir putinterval
dup 12 tables putinterval
put
dup
/ix 1 def
tabs{
dup type/stringtype eq{
ix exch
put dup
/ix ix 1 add def
}{
{
dup type/stringtype eq{
ix exch put dup
/ix ix 1 add def
}{
pop
}ifelse
}forall
}ifelse
}forall
pop
end
}bind def
/MakeType42
{
//PDFR_DEBUG{
(MakeType42 beg)=
}if
10 dict begin
/FontName 1 index/FontName get def
/FontType 42 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox 1 index/FontBBox get def
dup/FontResource get
dup/Encoding known{
//PDFReader/ObtainEncoding get exec
/Encoding get
}{
pop null
}ifelse
/PDFEncoding exch def
/CharStrings 2 index//PDFReader/MakeTTCharStrings get exec def
/sfnts 2 index//MakeStreamReader exec
ReadTTF
ProcessTTData
Makesfnts
def
/Encoding StandardEncoding def
/PaintType 0 def
currentdict end
//PDFR_DEBUG{
(MakeType42 end)=
}if
}bind def
/GetInstalledFont
{
dup//InstalledFonts exch knownget{
exch pop
}{
dup findfont dup 3 1 roll
//InstalledFonts 3 1 roll put
}ifelse
}bind def
/RemoveFontNamePrefix
{//=string cvs true
0 1 5{
2 index exch get//IsUpper exec not{
pop false exit
}if
}for
{(+)search{
pop pop
}if
}if
cvn
}bind def
/CheckFont
{dup/Type get/Font ne{
mark(Resource )3 index( must have /Type/Font .)//error exec
}if
}bind def
/CheckEncoding
{dup type/nametype ne{
dup/Type get/Encoding ne{
mark(Resource )3 index( must have /Type/Encoding .)//error exec
}if
}if
}bind def
/ObtainEncoding
{dup/Encoding known{
dup dup/Encoding//CheckEncoding//ResolveD exec
dup type dup/arraytype eq exch/packedarraytype eq or{
pop pop
}{
dup type/nametype eq{
/Encoding findresource
}{
dup/BaseEncoding//knownget exec not{
/StandardEncoding
}if
/Encoding findresource
exch
/Differences//knownget exec{
exch dup length array copy exch
0 exch
{
dup type/integertype eq{
exch pop
}{
3 copy put pop
1 add
}ifelse
}forall
pop
}if
}ifelse
/Encoding exch put
}ifelse
}{
dup/Encoding/StandardEncoding/Encoding findresource put
}ifelse
}bind def
/ObtainMetrics
{dup/Widths//knownget exec{
1 index/Encoding get
256 dict
3 index/Subtype get/TrueType eq{
1000
}{
1
}ifelse
4 index/MissingWidth//knownget exec not{
0
}if
5 index/FirstChar//knownget exec not{
0
}if
6 5 roll
dup 0 exch 1 exch length 1 sub{
2 copy get
exch 3 index add
7 index exch get
dup dup null ne exch/.notdef ne and{
6 index 3 1 roll exch
6 index div
3 copy pop//knownget exec{
0 eq
}{
true
}ifelse
{put
}{
pop pop pop
}ifelse
}{
pop pop
}ifelse
}for
pop pop pop pop exch pop
1 index exch/Metrics exch put
}{
dup/MissingWidth//knownget exec{
256 dict
2 index/Encoding get{
dup null ne{
3 copy 3 2 roll put
}if
pop
}forall
exch pop
1 index exch/Metrics exch put
}if
}ifelse
}bind def
/NotDef
{
FontMatrix aload pop pop pop exch pop exch pop
1 exch div exch
1 exch div exch
1 index 0 setcharwidth
0 setlinewidth
0 0 moveto
2 copy rlineto
1 index 0 rlineto
neg exch neg exch rlineto
closepath stroke
}bind def
/SaveResourcesToStack
{
[
//PDFReader/OldResources known{
//PDFReader/OldResources get
}{
null
}ifelse
//PDFReader/CurrentObject get/Context get/Resources get
]
//PDFReader/OldResources 3 -1 roll put
}bind def
/RestoreResourcesFromStack
{
//PDFReader/OldResources get dup
0 get//PDFReader/OldResources 3 -1 roll put
1 get//PDFReader/CurrentObject get/Context get/Resources 3 -1 roll put
}bind def
/BuildChar
{//PDFR_DEBUG{
(BuildChar )print dup//=only exec( )print
}if
exch begin
Encoding exch get
//PDFR_DEBUG{
dup =
}if
dup null eq{
pop//NotDef exec
}
{
CharProcs exch//knownget exec
{
currentfont/Font get/Resources//knownget exec{
exec
SaveResourcesToStack
//PDFReader/CurrentObject get/Context get
/Resources 3 -1 roll put
//RunDelayedStream exec
RestoreResourcesFromStack
}{
//RunDelayedStream exec
}ifelse
}
{
//NotDef exec
}ifelse
}ifelse
end
}bind def
/printdict
{(<<)=
{exch = ==}forall
(>>)=
}bind def
/printfont
{
dup{
exch dup =
dup/Encoding eq{
pop =
}{
dup/FontInfo eq exch/Private eq or{
//printdict exec
}{
==
}ifelse
}ifelse
}forall
}bind def
/ScaleMetrics
{1 index{
2 index div
3 index
3 1 roll put
}forall
pop
}bind def
/ResolveAndSetFontAux
{exch dup
//PDFReader/CurrentObject get/Context get/Resources get
/Font//DoNothing//ResolveD exec
exch//CheckFont//ResolveD exec
dup/Font//knownget exec{
exch pop exch pop
}{
{
dup/Subtype get dup dup/Type1 eq exch/TrueType eq or exch/MMType1 eq or{
exch pop
dup/BaseFont get
//RemoveFontNamePrefix exec
//PDFR_DEBUG{
(Font )print dup =
}if
1 index/FontDescriptor known{
//PDFR_DEBUG{
(Font from a font descriptor.)=
}if
1 index
/FontDescriptor//DoNothing//ResolveD exec
/Font//knownget exec{
exch pop
}{
//PDFR_DEBUG{
(Font descriptor has no Font resolved.)=
}if
//GetInstalledFont exec
}ifelse
}{
//GetInstalledFont exec
}ifelse
exch
dup/Encoding known not{
1 index/Encoding get 1 index exch/Encoding exch put
}if
//ObtainEncoding exec
//ObtainMetrics exec
exch
dup length dict copy
dup 2 index/Encoding get
/Encoding exch put
1 index/Metrics//knownget exec{
2 index/Subtype get/TrueType ne{
1 index/FontMatrix get 0 get
dup 0 eq{
pop
1 index/FontMatrix get 1 get
dup 0 eq{pop 1}if
}if
0.001 div
//ScaleMetrics exec
}{
1 index/sfnts known not{
1 index/FontMatrix get 0 get
dup 0 eq{
pop
1 index/FontMatrix get 1 get
dup 0 eq{pop 1}if
}if
//ScaleMetrics exec
}if
}ifelse
1 index exch/Metrics exch put
}if
1 index/BaseFont get
exch
dup/FID undef
dup/UniqueID undef
definefont
dup 3 1 roll
/Font exch put
exit
}if
dup/Subtype get/Type3 eq{
//ObtainEncoding exec
2 copy exch/FontName exch put
dup/CharProcs get//ResolveDict exec
dup/FontType 3 put
dup/BuildChar//BuildChar put
dup dup/Font exch put
dup 3 1 roll
definefont
2 copy ne{
2 copy/Font exch put
}if
exch pop
exit
}if
dup/Subtype get/Type0 eq{
}if
dup/Subtype get/CIDFontType0 eq{
}if
dup/Subtype get/CIDFontType2 eq{
}if
mark(Unknown font type )2 index/Subtype get//error exec
}loop
}ifelse
exch scalefont setfont
}bind def
/ResolveAndSetFont
{
//ResolveAndSetFontAux exec
}bind def
/.knownget
{2 copy known{
get true
}{
pop pop false
}ifelse
}bind def
/.min
{2 copy lt{
exch
}if
pop
}bind def
/.max
{2 copy gt{
exch
}if
pop
}bind def
/.dicttomark
{>>
}bind def
/getu16{
2 copy get 8 bitshift 3 1 roll 1 add get add
}bind def
/gets16{
getu16 16#8000 xor 16#8000 sub
}bind def
/getu32{
2 copy getu16 16 bitshift 3 1 roll 2 add getu16 add
}bind def
/gets32{
2 copy gets16 16 bitshift 3 1 roll 2 add getu16 add
}bind def
/cmapformats mark
0{
6 256 getinterval{}forall 256 packedarray
}bind
2{
/sHK_sz 2 def
/sH_sz 8 def
dup 2 getu16/cmapf2_tblen exch def
dup 4 getu16/cmapf2_lang exch def
dup 6 256 sHK_sz mul getinterval/sHKs exch def
0
0 1 255{
sHKs exch
2 mul getu16
1 index
1 index
lt{exch}if pop
}for
/sH_len exch def
dup 6 256 sHK_sz mul add
cmapf2_tblen 1 index sub getinterval
/sH_gIA exch def
/cmapf2_glyph_array 65535 array def
/.cmapf2_putGID{
/cmapf2_ch cmapf2_ch_hi 8 bitshift cmapf2_ch_lo add def
firstCode cmapf2_ch_lo le
cmapf2_ch_lo firstCode entryCount add lt
and{
sH_offset idRangeOffset add
cmapf2_ch_lo firstCode sub 2 mul
add 6 add
sH_gIA exch getu16
dup 0 gt{
idDelta add
cmapf2_glyph_array exch cmapf2_ch exch put
}{
pop
}ifelse
}{
}ifelse
}def
16#00 1 16#ff{
/cmapf2_ch_hi exch def
sHKs cmapf2_ch_hi sHK_sz mul getu16
/sH_offset exch def
sH_gIA sH_offset sH_sz getinterval
dup 0 getu16/firstCode exch def
dup 2 getu16/entryCount exch def
dup 4 gets16/idDelta exch def
dup 6 getu16/idRangeOffset exch def
pop
sH_offset 0 eq{
/cmapf2_ch_lo cmapf2_ch_hi def
/cmapf2_ch_hi 0 def
.cmapf2_putGID
}{
16#00 1 16#ff{
/cmapf2_ch_lo exch def
.cmapf2_putGID
}for
}ifelse
}for
pop
0 1 cmapf2_glyph_array length 1 sub{
dup cmapf2_glyph_array exch get
null eq{cmapf2_glyph_array exch 0 put}{pop}ifelse
}for
cmapf2_glyph_array
}bind
4{
/etab exch def
/nseg2 etab 6 getu16 def
14/endc etab 2 index nseg2 getinterval def
2 add
nseg2 add/startc etab 2 index nseg2 getinterval def
nseg2 add/iddelta etab 2 index nseg2 getinterval def
nseg2 add/idroff etab 2 index nseg2 getinterval def
pop
/firstcode startc 0 getu16 16#ff00 and dup 16#f000 ne{pop 0}if def
/lastcode firstcode def
/striptopbyte false def
/putglyph{
glyphs code 3 -1 roll put/code code 1 add def
}bind def
/numcodes 0 def/glyphs 0 0 2 nseg2 3 sub{
/i2 exch def
/scode startc i2 getu16 def
/ecode endc i2 getu16 def
ecode lastcode gt{
/lastcode ecode def
}if
}for pop
firstcode 16#f000 ge lastcode firstcode sub 255 le and{
lastcode 255 and
/striptopbyte true def
}{
lastcode
}ifelse
1 add
array def
glyphs length 1024 ge{
.array1024z 0 1024 glyphs length 1023 sub{glyphs exch 2 index putinterval}for
glyphs dup length 1024 sub 3 -1 roll
putinterval
}{
0 1 glyphs length 1 sub{glyphs exch 0 put}for
}ifelse
/numcodes 0 def/code 0 def
0 2 nseg2 3 sub{
/i2 exch def
/scode startc i2 getu16 def
/ecode endc i2 getu16 def
numcodes scode firstcode sub
exch sub 0 .max dup/code exch code exch add def
ecode scode sub 1 add add numcodes add/numcodes exch def
/delta iddelta i2 gets16 def
TTFDEBUG{
(scode=)print scode =only
( ecode=)print ecode =only
( delta=)print delta =only
( droff=)print idroff i2 getu16 =
}if
idroff i2 getu16 dup 0 eq{
pop scode delta add 65535 and 1 ecode delta add 65535 and
striptopbyte{
/code scode 255 and def
}{
/code scode def
}ifelse
{putglyph}for
}{
/gloff exch 14 nseg2 3 mul add 2 add i2 add add def
striptopbyte{
/code scode 255 and def
}{
/code scode def
}ifelse
0 1 ecode scode sub{
2 mul gloff add etab exch getu16
dup 0 ne{delta add 65535 and}if putglyph
}for
}ifelse
}for glyphs/glyphs null def
}bind
6{
dup 6 getu16/firstcode exch def dup 8 getu16/ng exch def
firstcode ng add array
0 1 firstcode 1 sub{2 copy 0 put pop}for
dup firstcode ng getinterval
0 1 ng 1 sub{
dup 2 mul 10 add 4 index exch getu16 3 copy put pop pop
}for pop exch pop
}bind
.dicttomark readonly def
/cmaparray{
dup 0 getu16 cmapformats exch .knownget{
TTFDEBUG{
(cmap: format )print 1 index 0 getu16 = flush
}if exec
}{
(Can't handle format )print 0 getu16 = flush
0 1 255{}for 256 packedarray
}ifelse
TTFDEBUG{
(cmap: length=)print dup length = dup ==
}if
}bind def
/postremap mark
/Cdot/Cdotaccent
/Edot/Edotaccent
/Eoverdot/Edotaccent
/Gdot/Gdotaccent
/Ldot/Ldotaccent
/Zdot/Zdotaccent
/cdot/cdotaccent
/edot/edotaccent
/eoverdot/edotaccent
/gdot/gdotaccent
/ldot/ldotaccent
/zdot/zdotaccent
.dicttomark readonly def
/get_from_stringarray
{1 index type/stringtype eq{
get
}{
exch{
2 copy length ge{
length sub
}{
exch get exit
}ifelse
}forall
}ifelse
}bind def
/getinterval_from_stringarray
{
2 index type/stringtype eq{
getinterval
}{
string exch 0
4 3 roll{
dup length
dup 4 index lt{
3 index exch sub
exch pop 3 1 roll exch pop
}{
dup 3 1 roll
4 index sub
5 index length 4 index sub
2 copy gt{exch}if pop
dup 3 1 roll
5 index exch getinterval
5 index 4 index 3 index
getinterval
copy pop
exch pop add exch pop 0 exch
dup 3 index length ge{exit}if
}ifelse
}forall
pop pop
}ifelse
}bind def
/string_array_size
{dup type/stringtype eq{
length
}{
0 exch{length add}forall
}ifelse
}bind def
/postformats mark
16#00010000{
pop MacGlyphEncoding
}
16#00020000{
dup dup type/arraytype eq{0 get}if length 36 lt{
TTFDEBUG{(post format 2.0 invalid.)= flush}if
pop[]
}{
/postglyphs exch def
/post_first postglyphs dup type/arraytype eq{0 get}if def
post_first 32 getu16/numglyphs exch def
/glyphnames numglyphs 2 mul 34 add def
/postpos glyphnames def
/total_length postglyphs//string_array_size exec def
numglyphs array 0 1 numglyphs 1 sub{
postpos total_length ge{
1 numglyphs 1 sub{1 index exch/.notdef put}for
exit
}if
postglyphs postpos//get_from_stringarray exec
postglyphs postpos 1 add 2 index//getinterval_from_stringarray exec cvn
exch postpos add 1 add/postpos exch def
2 index 3 1 roll
put
}for
/postnames exch def
numglyphs array 0 1 numglyphs 1 sub{
dup 2 mul 34 add postglyphs exch 2//getinterval_from_stringarray exec
dup 0 get 8 bitshift exch 1 get add dup 258 lt{
MacGlyphEncoding exch get
}{
dup 32768 ge{
pop/.notdef
}{
258 sub dup postnames length ge{
TTFDEBUG{(   *** warning: glyph index past end of 'post' table)= flush}if
pop
exit
}if
postnames exch get
postremap 1 index .knownget{exch pop}if
}ifelse
}ifelse
2 index 3 1 roll put
}for
}
ifelse
}bind
16#00030000{
pop[]
}bind
.dicttomark readonly def
/first_post_string
{
post dup type/arraytype eq{0 get}if
}bind def
/.getpost{
/glyphencoding post null eq{
TTFDEBUG{(post missing)= flush}if[]
}{
postformats first_post_string 0 getu32 .knownget{
TTFDEBUG{
(post: format )print
first_post_string
dup 0 getu16 =only(,)print 2 getu16 = flush
}if
post exch exec
}{
TTFDEBUG{(post: unknown format )print post 0 getu32 = flush}if[]
}ifelse
}ifelse def
}bind def
/MacRomanEncoding[
StandardEncoding 0 39 getinterval aload pop
/quotesingle
StandardEncoding 40 56 getinterval aload pop
/grave
StandardEncoding 97 31 getinterval aload pop
/Adieresis/Aring/Ccedilla/Eacute/Ntilde/Odieresis/Udieresis/aacute
/agrave/acircumflex/adieresis/atilde/aring/ccedilla/eacute/egrave
/ecircumflex/edieresis/iacute/igrave
/icircumflex/idieresis/ntilde/oacute
/ograve/ocircumflex/odieresis/otilde
/uacute/ugrave/ucircumflex/udieresis
/dagger/degree/cent/sterling/section/bullet/paragraph/germandbls
/registered/copyright/trademark/acute/dieresis/.notdef/AE/Oslash
/.notdef/plusminus/.notdef/.notdef/yen/mu/.notdef/.notdef
/.notdef/.notdef/.notdef/ordfeminine/ordmasculine/.notdef/ae/oslash
/questiondown/exclamdown/logicalnot/.notdef
/florin/.notdef/.notdef/guillemotleft
/guillemotright/ellipsis/space/Agrave/Atilde/Otilde/OE/oe
/endash/emdash/quotedblleft/quotedblright
/quoteleft/quoteright/divide/.notdef
/ydieresis/Ydieresis/fraction/currency
/guilsinglleft/guilsinglright/fi/fl
/daggerdbl/periodcentered/quotesinglbase/quotedblbase
/perthousand/Acircumflex/Ecircumflex/Aacute
/Edieresis/Egrave/Iacute/Icircumflex
/Idieresis/Igrave/Oacute/Ocircumflex
/.notdef/Ograve/Uacute/Ucircumflex
/Ugrave/dotlessi/circumflex/tilde
/macron/breve/dotaccent/ring/cedilla/hungarumlaut/ogonek/caron
]/Encoding defineresource pop
/TTParser<<
/Pos 0
/post null
>>def
/readu8
{read not{
mark(Insufficient data in the stream.)//error exec
}if
}bind def
/readu16
{dup//readu8 exec 8 bitshift exch//readu8 exec or
}bind def
/reads16
{//readu16 exec 16#8000 xor 16#8000 sub
}bind def
/readu32
{dup//readu16 exec 16 bitshift exch//readu16 exec or
}bind def
/reads32
{dup//reads16 exec 16 bitshift exch//readu16 exec or
}bind def
/SkipToPosition
{dup//TTParser/Pos get
exch//TTParser exch/Pos exch put
sub
//PDFR_DEBUG{
(Skipping )print dup//=only exec( bytes.)=
}if
dup 0 eq{
pop pop
}{
dup 3 1 roll
()/SubFileDecode filter
exch
{1 index//BlockBuffer readstring pop length
dup 0 eq{pop exch pop exit}if
sub
}loop
0 ne{
mark(Insufficient data in the stream for SkipToPosition.)//error exec
}if
}ifelse
}bind def
/TagBuffer 4 string def
/ParseTTTableDirectory
{//PDFR_DEBUG{
(ParseTTTableDirectory beg)=
}if
15 dict begin
dup//readu32 exec 16#00010000 ne{
mark(Unknown True Type version.)//error exec
}if
dup//readu16 exec/NumTables exch def
dup//readu16 exec/SearchRange exch def
dup//readu16 exec/EntrySelector exch def
dup//readu16 exec/RangeShift exch def
//PDFR_DEBUG{
(NumTables = )print NumTables =
}if
NumTables{
dup//TagBuffer readstring not{
mark(Could not read TT tag.)//error exec
}if
cvn
[2 index//readu32 exec pop
2 index//readu32 exec
3 index//readu32 exec
]
//PDFR_DEBUG{
2 copy exch//=only exec( )print ==
}if
def
}repeat
pop
//TTParser/Pos 12 NumTables 16 mul add put
currentdict end
//PDFR_DEBUG{
(ParseTTTableDirectory end)=
}if
}bind def
/ParseTTcmap
{//PDFR_DEBUG{
(ParseTTcmap beg)=
}if
/cmap get aload pop
3 1 roll
7 dict begin
//PDFR_DEBUG{
(Current position = )print//TTParser/Pos get =
(cmap position = )print dup =
}if
1 index exch//SkipToPosition exec
//TTParser/Pos get/TablePos exch def
dup//readu16 exec pop
dup//readu16 exec/NumEncodings exch def
//PDFR_DEBUG{
(NumEncodings = )print NumEncodings =
}if
null
NumEncodings{
1 index//readu32 exec
2 index//readu32 exec
3 array dup 3 2 roll 0 exch put
2 index null ne{
dup 0 get 3 index 0 get sub
3 index exch 1 exch put
}if
dup 4 3 roll pop 3 1 roll
def
}repeat
dup 0 get
4 3 roll exch sub
1 exch put
//PDFR_DEBUG{
currentdict{
exch dup type/integertype eq{
//PrintHex exec( )print ==
}{
pop pop
}ifelse
}forall
}if
4 NumEncodings 8 mul add/HeaderLength exch def
//TTParser/Pos//TTParser/Pos get HeaderLength add put
0
NumEncodings{
16#7FFFFFF null
currentdict{
1 index type/integertype eq{
exch pop dup 0 get
dup 5 index gt{
dup 4 index lt{
4 1 roll
exch pop exch pop
}{
pop pop
}ifelse
}{
pop pop
}ifelse
}{
pop pop
}ifelse
}forall
//PDFR_DEBUG{
(Obtaining subtable for )print dup ==
}if
3 2 roll pop
3 copy pop
TablePos add//SkipToPosition exec
3 copy exch pop 1 get
//TTParser/Pos//TTParser/Pos get 3 index add put
string
readstring not{
mark(Can't read a cmap subtable.)//error exec
}if
2 exch put
}repeat
pop pop
currentdict end
//PDFR_DEBUG{
(ParseTTcmap end)=
}if
}bind def
/GetTTEncoding
{//PDFR_DEBUG{
(GetTTEncoding beg)=
}if
get
exch pop
2 get
10 dict begin
/TTFDEBUG//PDFR_DEBUG def
//cmaparray exec
end
//PDFR_DEBUG{
(GetTTEncoding end)=
dup ==
}if
}bind def
/InverseEncoding
{
256 dict begin
dup length 1 sub -1 0{
2 copy get
exch
1 index currentdict exch//knownget exec{
dup type/arraytype eq{
aload length 1 add array astore
}{
2 array astore
}ifelse
}if
def
}for
pop
currentdict end
}bind def
/GetMacRomanEncodingInverse
{//PDFReader/MacRomanEncodingInverse get
dup null eq{
pop
MacRomanEncoding//InverseEncoding exec
dup//PDFReader exch/MacRomanEncodingInverse exch put
}if
}bind def
/PutCharStringSingle
{
dup 3 index length lt{
2 index exch get
dup 0 ne{
def
}{
pop pop
}ifelse
}{
pop pop
}ifelse
}bind def
/PutCharString
{1 index type/nametype ne{
mark(Bad charstring name)//error exec
}if
dup type/arraytype eq{
{
3 copy//PutCharStringSingle exec
pop pop
}forall
pop
}{
//PutCharStringSingle exec
}ifelse
}bind def
/ComposeCharStrings
{
//PDFR_DEBUG{
(ComposeCharStrings beg)=
}if
1 index length 1 add dict begin
/.notdef 0 def
exch
//TTParser/post get
dup null ne{
exch
1 index length 1 sub -1 0{
dup 3 index exch get exch
dup 0 eq 2 index/.notdef eq or{
pop pop
}{
def
}ifelse
}for
}if
exch pop exch
{
//PutCharString exec
}forall
pop
currentdict end
//PDFR_DEBUG{
(ComposeCharStrings end)=
}if
}bind def
/ParseTTpost
{
//PDFR_DEBUG{
(ParseTTpost beg)=
}if
/post get aload pop
3 1 roll
//PDFR_DEBUG{
(Current position = )print//TTParser/Pos get =
(post position = )print dup =
}if
1 index exch//SkipToPosition exec
//TTParser/Pos//TTParser/Pos get 4 index add put
exch dup 65535 le{
string
readstring not{
mark(Insufficient data in the stream for ParseTTpost.)//error exec
}if
}{
[3 1 roll
dup 16384 div floor cvi
exch 1 index 16384 mul
sub exch
1 sub 0 1 3 -1 roll
{
1 add index
16384 string readstring not{
mark(Insufficient data in the stream for ParseTTpost.)//error exec
}if
}for
counttomark -2 roll
string readstring not{
mark(Insufficient data in the stream for ParseTTpost.)//error exec
}if
]
}ifelse
1 dict begin
/post exch def
//.getpost exec
//TTParser/post glyphencoding put
//PDFR_DEBUG{
(ParseTTpost end)=
glyphencoding ==
}if
end
}bind def
/MakeTTCharStrings
{//MakeStreamReader exec
dup dup//ParseTTTableDirectory exec
//TTParser/post null put
dup/post//knownget exec{
0 get
1 index/cmap get 0 get
lt{
2 copy//ParseTTpost exec
//ParseTTcmap exec
}{
2 copy//ParseTTcmap exec
3 1 roll
//ParseTTpost exec
}ifelse
}{
//ParseTTcmap exec
}ifelse
{
dup 16#00030001 known{
//PDFR_DEBUG{
(Using the TT cmap encoding for Windows Unicode.)=
}if
16#00030001//GetTTEncoding exec
AdobeGlyphList//ComposeCharStrings exec
exit
}if
dup 16#00010000 known{
//PDFR_DEBUG{
(Using the TT cmap encoding for Macintosh Roman.)=
}if
16#00010000//GetTTEncoding exec
PDFEncoding dup null eq{
pop//GetMacRomanEncodingInverse exec
}{
//InverseEncoding exec
}ifelse
//ComposeCharStrings exec
exit
}if
dup 16#00030000 known{
//PDFR_DEBUG{
(Using the TT cmap encoding 3.0 - not sure why Ghostscript writes it since old versions.)=
}if
16#00030000//GetTTEncoding exec
PDFEncoding dup null eq{
pop//GetMacRomanEncodingInverse exec
}{
//InverseEncoding exec
}ifelse
//ComposeCharStrings exec
exit
}if
mark(True Type cmap has no useful encodings.)//error exec
}loop
//PDFR_DEBUG{
(CharStrings <<)=
dup{
exch
dup type/nametype eq{
//=only exec
}{
==
}ifelse
( )print ==
}forall
(>>)=
}if
}bind def
/ScaleVal
{
aload pop
1 index sub
3 2 roll mul add
}bind def
/ScaleArg
{
aload pop
1 index sub
3 1 roll
sub exch div
}bind def
/ScaleArgN
{
dup length 2 sub -2 0{
2
2 index 3 1 roll getinterval
3 2 roll
exch//ScaleArg exec
1 index length 2 idiv 1 add 1 roll
}for
pop
}bind def
/ComputeFunction_10
{
//PDFR_DEBUG{
(ComputeFunction_10 beg )print 1 index//=only exec( stack=)print count =
}if
exch
dup 1 eq{
pop dup length 1 sub get
}{
1 index length 1 sub mul
dup dup floor sub
dup 0 eq{
pop cvi get
}{
3 1 roll floor cvi
2 getinterval
aload pop
2 index mul 3 2 roll 1 exch sub 3 2 roll mul add
}ifelse
}ifelse
//PDFR_DEBUG{
(ComputeFunction_10 end )print dup//=only exec( stack=)print count =
}if
}bind def
/ComputeFunction_n0
{
//PDFR_DEBUG{
(ComputeFunction_n0 beg N=)print dup//=only exec( stack=)print count =
}if
dup 0 eq{
pop
}{
dup 2 add -1 roll
dup 3 index length 1 sub ge{
pop 1 sub
exch dup length 1 sub get exch
//PDFReader/ComputeFunction_n0 get exec
}{
dup floor cvi dup
4 index exch get
3 index dup
5 add copy
6 2 roll
pop pop pop pop
1 sub
//PDFReader/ComputeFunction_n0 get exec
3 2 roll pop
exch
4 3 roll exch
4 add 2 roll 1 add
3 2 roll exch get
exch 1 sub
//PDFReader/ComputeFunction_n0 get exec
1 index mul
3 1 roll
1 exch sub mul add
}ifelse
}ifelse
//PDFR_DEBUG{
(ComputeFunction_n0 end )print dup//=only exec( stack=)print count =
}if
}bind def
/FunctionToProc_x01
{
dup/Domain get exch
dup/Data get 0 get exch
/Size get length
[4 1 roll
//PDFR_DEBUG{
{(function beg, stack =)print count//=only exec(\n)print}/exec load
5 2 roll
}if
dup 1 gt{
{mark exch
3 add 2 roll
//ScaleArgN exec
counttomark dup
3 add -2 roll
pop exch
//ComputeFunction_n0 exec
}/exec load
}{
pop
3 1/roll load//ScaleArg/exec load
/exch load
//ComputeFunction_10/exec load
}ifelse
//PDFR_DEBUG{
(function end, stack =)/print load/count load//=only/exec load(\n)/print load
}if
]cvx
//PDFR_DEBUG{
(Made a procedure for the 1-result function :)=
dup ==
}if
}bind def
/FunctionProcDebugBeg
{(FunctionProcDebugBeg )print count =
}bind def
/FunctionProcDebugEnd
{(FunctionProcDebugEnd )print count =
}bind def
/FunctionToProc_x0n
{
PDFR_DEBUG{
(FunctionToProc_x0n beg m=)print dup =
}if
1 index/Size get length exch
dup 7 mul 2 add array
PDFR_DEBUG{
dup 0//FunctionProcDebugBeg put
}{
dup 0//DoNothing put
}ifelse
dup 1/exec load put
dup 2 5 index/Domain get put
2 index 1 eq{
dup 3//ScaleArg put
}{
dup 3//ScaleArgN put
}ifelse
dup 4/exec load put
1 index 1 sub 0 exch 1 exch{
dup 7 mul 5 add
1 index 4 index 1 sub ne{
dup 3 index exch 6 index put 1 add
dup 3 index exch/copy load put 1 add
}if
[
6 index/Data get 3 index get
6 index 1 eq{
//ComputeFunction_10/exec load
}{
6 index
//ComputeFunction_n0/exec load
}ifelse
]cvx
3 index exch 2 index exch put 1 add
2 index 1 index/exec load put 1 add
1 index 4 index 1 sub ne{
2 index 1 index 6 index 1 add put 1 add
2 index 1 index 1 put 1 add
2 index 1 index/roll load put
}if
pop pop
}for
PDFR_DEBUG{
dup dup length 2 sub//FunctionProcDebugEnd put
}{
dup dup length 2 sub//DoNothing put
}ifelse
dup dup length 1 sub/exec load put
cvx exch pop exch pop exch pop
//PDFR_DEBUG{
(Made a procedure for the n-argument function :)=
dup ==
}if
PDFR_DEBUG{
(FunctionToProc_x0n end)=
}if
}bind def
/MakeTableRec
{
0
exec
}bind def
/MakeTable
{//PDFR_DEBUG{
(MakeTable beg )print count =
}if
1 index/Size get exch
1 sub dup
3 1 roll
get
array
1 index 0 eq{
exch pop exch pop
}{
dup length 1 sub -1 0{
3 index 3 index//MakeTableRec exec
2 index 3 1 roll put
}for
exch pop exch pop
}ifelse
//PDFR_DEBUG{
(MakeTable end )print count =
}if
}bind def
//MakeTableRec 0//MakeTable put
/StoreSample
{
1 sub
dup 0 eq{
pop
}{
-1 1{
I exch get get
}for
}ifelse
I 0 get 3 2 roll put
}bind def
/ReadSample32
{
4{
File read not{
mark(Insufficient data for function.)//error exec
}if
}repeat
pop
3 1 roll exch
256 mul add 256 mul add
//1_24_bitshift_1_sub div
}bind def
/ReadSample
{
Buffer BitsLeft BitsPerSample
{2 copy ge{
exit
}if
3 1 roll
8 add 3 1 roll
256 mul File read not{
mark(Insufficient data for function.)//error exec
}if
add
3 1 roll
}loop
sub dup
2 index exch
neg bitshift
2 copy exch bitshift
4 3 roll exch sub
/Buffer exch def
exch/BitsLeft exch def
Div div
}bind def
/ReadSamplesRec
{0
exec
}bind def
/ReadSamples
{
//PDFR_DEBUG{
(ReadSamples beg )print count =
}if
dup 1 eq{
pop
0 1 Size 0 get 1 sub{
I exch 0 exch put
0 1 M 1 sub{
dup Range exch 2 mul 2 getinterval
//PDFR_DEBUG{
(Will read a sample ... )print
}if
BitsPerSample 32 eq{//ReadSample32}{//ReadSample}ifelse
exec exch//ScaleVal exec
//PDFR_DEBUG{
(value=)print dup =
}if
exch Table exch get
Size length//StoreSample exec
}for
}for
}{
1 sub
dup Size exch get 0 exch 1 exch 1 sub{
I exch 2 index exch put
dup//ReadSamplesRec exec
}for
pop
}ifelse
//PDFR_DEBUG{
(ReadSamples end )print count =
}if
}bind def
//ReadSamplesRec 0//ReadSamples put
/StreamToArray
{//PDFR_DEBUG{
(StreamToArray beg )print count =
}if
userdict/FuncDataReader get begin
dup/BitsPerSample get/BitsPerSample exch def
dup/Size get length/N exch def
dup/Range get length 2 idiv/M exch def
1 BitsPerSample bitshift 1 sub/Div exch def
/BitsLeft 0 def
/Buffer 0 def
dup/Size get/Size exch def
dup/Range get/Range exch def
/File 1 index//MakeStreamReader exec def
/I[N{0}repeat]def
M array
dup length 1 sub -1 0{
2 index N//MakeTable exec
2 index 3 1 roll put
}for
/Table exch def
N//ReadSamples exec
PDFR_DEBUG{
(Table = )print Table ==
}if
/Data Table put
end
//PDFR_DEBUG{
(StreamToArray end )print count =
}if
}bind def
/FunctionToProc10
{
PDFR_DEBUG{
(FunctionToProc10 beg, Range = )print dup/Range get ==
}if
dup/Order//knownget exec{
1 ne{
(Underimplemented function Type 0 Order 3.)=
}if
}if
dup//StreamToArray exec
dup/Range get length dup 2 eq{
pop//FunctionToProc_x01 exec
}{
2 idiv//FunctionToProc_x0n exec
}ifelse
PDFR_DEBUG{
(FunctionToProc10 end)=
}if
}bind def
/FunctionToProc12
{begin
currentdict/C0//knownget exec{length 1 eq}{true}ifelse{
N
currentdict/C0//knownget exec{
0 get
}{
0
}ifelse
currentdict/C1//knownget exec{
0 get
}{
1
}ifelse
1 index sub
[4 1 roll
{
4 2 roll
exp mul add
}aload pop
]cvx
}{
[
0 1 C0 length 1 sub{
N
C0 2 index get
C1 3 index get
4 3 roll pop
1 index sub
[/dup load
5 2 roll
{
4 2 roll
exp mul add
exch
}aload pop
]cvx
/exec load
}for
/pop load
]cvx
}ifelse
end
//PDFR_DEBUG{
(FunctionType2Proc : )print dup ==
}if
}bind def
/FunctionToProc14
{//MakeStreamReader exec cvx exec
//PDFR_DEBUG{
(FunctionType4Proc : )print dup ==
}if
}bind def
/FunctionToProc1
{
dup/FunctionType get
{dup 0 eq{
pop//FunctionToProc10 exec exit
}if
dup 2 eq{
pop//FunctionToProc12 exec exit
}if
dup 4 eq{
pop//FunctionToProc14 exec exit
}if
mark exch(Function type )exch( isn't implemented yet.)//error exec
}loop
}bind def
/FunctionToProc20
{
PDFR_DEBUG{
(FunctionToProc20, Range = )print dup/Range get ==
}if
dup/Order//knownget exec{
1 ne{
(Underimplemented function Type 0 Order 3.)=
}if
}if
dup//StreamToArray exec
dup/Range get length dup 2 eq{
pop//FunctionToProc_x01 exec
}{
2 idiv//FunctionToProc_x0n exec
}ifelse
}bind def
/FunctionToProc
{//PDFR_DEBUG{
(FunctionToProc beg )print count =
}if
dup type/dicttype eq{
dup/Domain get length 2 idiv
{
dup 1 eq{
pop//FunctionToProc1 exec exit
}if
dup 2 eq{
pop//FunctionToProc20 exec exit
}if
mark(Functions with many arguments aren't implemented yet.)//error exec
}loop
}{
//PDFR_DEBUG{(Not a function dict, assume already a procedure.)print}if
}ifelse
//PDFR_DEBUG{
(FunctionToProc end )print count =
}if
}bind def
/spotfunctions mark
/Round{
abs exch abs 2 copy add 1 le{
dup mul exch dup mul add 1 exch sub
}{
1 sub dup mul exch 1 sub dup mul add 1 sub
}ifelse
}
/Diamond{
abs exch abs 2 copy add .75 le{
dup mul exch dup mul add 1 exch sub
}{
2 copy add 1.23 le{
.85 mul add 1 exch sub
}{
1 sub dup mul exch 1 sub dup mul add 1 sub
}ifelse
}ifelse
}
/Ellipse{
abs exch abs 2 copy 3 mul exch 4 mul add 3 sub dup 0 lt{
pop dup mul exch .75 div dup mul add 4 div 1 exch sub
}{
dup 1 gt{
pop 1 exch sub dup mul exch 1 exch sub
.75 div dup mul add 4 div 1 sub
}{
.5 exch sub exch pop exch pop
}ifelse
}ifelse
}
/EllipseA{dup mul .9 mul exch dup mul add 1 exch sub}
/InvertedEllipseA{dup mul .9 mul exch dup mul add 1 sub}
/EllipseB{dup 5 mul 8 div mul exch dup mul exch add sqrt 1 exch sub}
/EllipseC{dup mul .9 mul exch dup mul add 1 exch sub}
/InvertedEllipseC{dup mul .9 mul exch dup mul add 1 sub}
/Line{exch pop abs neg}
/LineX{pop}
/LineY{exch pop}
/Square{abs exch abs 2 copy lt{exch}if pop neg}
/Cross{abs exch abs 2 copy gt{exch}if pop neg}
/Rhomboid{abs exch abs 0.9 mul add 2 div}
/DoubleDot{2{360 mul sin 2 div exch}repeat add}
/InvertedDoubleDot{2{360 mul sin 2 div exch}repeat add neg}
/SimpleDot{dup mul exch dup mul add 1 exch sub}
/InvertedSimpleDot{dup mul exch dup mul add 1 sub}
/CosineDot{180 mul cos exch 180 mul cos add 2 div}
/Double{exch 2 div exch 2{360 mul sin 2 div exch}repeat add}
/InvertedDouble{
exch 2 div exch 2{360 mul sin 2 div exch}repeat add neg
}
.dicttomark readonly def
/CheckColorSpace
{
dup type/arraytype ne{
mark(Resource )3 index( must be an array.)//error exec
}if
}bind def
/SubstitutePDFColorSpaceRec
{0
exec
}bind def
/SubstitutePDFColorSpace
{
{
dup 0 get/Pattern eq{
dup length 1 gt{
dup dup 1//CheckColorSpace//ResolveA exec
dup type/nametype ne{
//SubstitutePDFColorSpaceRec exec
}if
1 exch put
}if
exit
}if
dup 0 get/Indexed eq{
exit
}if
dup 0 get/Separation eq{
dup dup 2//CheckColorSpace//ResolveA exec
dup type/nametype ne{
//SubstitutePDFColorSpaceRec exec
}if
2 exch put
exit
}if
dup 0 get/CalGray eq{
1 get
dup/Gamma//knownget exec{
[exch[exch/exp load]cvx dup dup]
1 index exch/DecodeLMN exch put
}if
[exch/CIEBasedA exch]
exit
}if
dup 0 get/CalRGB eq{
1 get
dup/Matrix//knownget exec{
1 index exch/MatrixLMN exch put
}if
dup/Gamma//knownget exec{
aload pop
[exch/exp load]cvx
3 1 roll
[exch/exp load]cvx
3 1 roll
[exch/exp load]cvx
3 1 roll
3 array astore
1 index exch/DecodeLMN exch put
}if
[exch/CIEBasedABC exch]
exit
}if
dup 0 get/Lab eq{
1 get
begin
currentdict/Range//knownget exec{aload pop}{-100 100 -100 100}ifelse
0 100 6 2 roll 6 array astore
/RangeABC exch def
/DecodeABC[{16 add 116 div}bind{500 div}bind{200 div}bind]def
/MatrixABC[1 1 1 1 0 0 0 0 -1]def
{dup 6 29 div ge{dup dup mul mul}{4 29 div sub 108 841 div mul}ifelse}
/DecodeLMN[
[3 index aload pop WhitePoint 0 get/mul load]cvx
[4 index aload pop WhitePoint 1 get/mul load]cvx
[5 index aload pop WhitePoint 2 get/mul load]cvx
]def pop
//PDFR_DEBUG{
(Constructed from Lab <<)=
currentdict{exch = ==}forall
(>>)=
}if
[/CIEBasedABC currentdict]
end
exit
pop
}if
dup 0 get/CIEBasedA eq{exit}if
dup 0 get/CIEBasedABC eq{exit}if
mark exch(Unimplemented color space )exch//error exec
}loop
}bind def
//SubstitutePDFColorSpaceRec 0//SubstitutePDFColorSpace put
/ResolveArrayElement
{2 copy get
dup type dup/arraytype eq exch
/packedarraytype eq or{
dup length 1 ge exch xcheck and{
2 copy get
dup 0 get type/integertype eq
1 index 1 get type dup/arraytype
eq exch
/packedarraytype eq or
and{
exec
2 index 4 1 roll put
}{
pop pop
}ifelse
}{
pop
}ifelse
}{
pop pop
}ifelse
}bind def
/ResolveColorSpaceArrayRec
{0
exec
}bind def
/SetColorSpaceSafe
{
PDFR_DEBUG{
(SetColorSpaceSafe beg)=
}if
currentcolorspace dup type/arraytype eq{
1 index type/arraytype eq{
dup length 2 index length eq{
false exch
dup length 0 exch 1 exch 1 sub{
dup
4 index exch get exch
2 index exch get
ne{
exch pop true exch exit
}if
}for
pop
{
setcolorspace
}{
pop
}ifelse
}{
pop setcolorspace
}ifelse
}{
pop setcolorspace
}ifelse
}{
pop setcolorspace
}ifelse
PDFR_DEBUG{
(SetColorSpaceSafe end)=
}if
}bind def
/ResolveColorSpaceArray
{
//PDFR_DEBUG{
(ResolveColorSpaceArray beg )print dup ==
}if
dup 0 get/Indexed eq{
1//ResolveArrayElement exec
dup dup 1 get
dup type/arraytype eq{
//SubstitutePDFColorSpace exec
//ResolveColorSpaceArrayRec exec
1 exch put
}{
pop pop
}ifelse
}if
dup 0 get/Separation eq{
dup dup 1 get UnPDFEscape 1 exch put
3//ResolveArrayElement exec
dup 3 get//FunctionToProc exec
2 copy 3 exch put
pop
}if
dup 0 get/Pattern eq{
dup length 1 gt{
dup 1 get dup type/arraytype eq{
ResolveColorSpaceArray
1 index 1 3 -1 roll put
}{
pop
}ifelse
}if
}if
PDFR_DEBUG{
(Construcrted color space :)=
dup ==
}if
//PDFR_DEBUG{
(ResolveColorSpaceArray end )print dup ==
}if
}bind def
//ResolveColorSpaceArrayRec 0//ResolveColorSpaceArray put
/ResolveColorSpace
{
//PDFR_DEBUG{
(ResolveColorSpace beg )print dup =
}if
dup//SimpleColorSpaceNames exch known not{
dup//PDFColorSpaces exch//knownget exec{
exch pop
//PDFR_DEBUG{
(ResolveColorSpace known )=
}if
}{
dup
//PDFReader/CurrentObject get/Context get/Resources get
/ColorSpace//DoNothing//ResolveD exec
exch//CheckColorSpace//ResolveD exec
dup type/arraytype eq{
//SubstitutePDFColorSpace exec
//ResolveColorSpaceArray exec
dup//PDFColorSpaces 4 2 roll put
}if
}ifelse
}if
//PDFR_DEBUG{
(ResolveColorSpace end )print dup ==
}if
}bind def
/CheckPattern
{
dup/PatternType//knownget exec{
dup 1 ne{
mark(Resource )4 index( is a shading, which can't be handled at level 2. )//error exec
}if
pop
}if
dup/Type knownget{
/Pattern ne{
mark(Resource )4 index( must have /Type/Pattern .)//error exec
}if
}if
}bind def
/PaintProc
{/Context get
//RunDelayedStream exec
}bind def
/ResolvePattern
{
dup
userdict/PDFR_Patterns get
exch//knownget exec{
exch pop
}{
dup
//PDFReader/CurrentObject get/Context get/Resources get
/Pattern//DoNothing//ResolveD exec
exch//CheckPattern//ResolveD exec
dup dup/Context exch put
dup/Resources//DoNothing//ResolveD exec pop
dup/PaintProc//PaintProc put
gsave userdict/PDFR_InitialGS get setgstate
currentglobal exch false setglobal
dup/Matrix get
makepattern
exch setglobal
grestore
dup userdict/PDFR_Patterns get
4 2 roll
put
}ifelse
}bind def
/SetColor
{//PDFR_DEBUG{
(SetColor beg)=
}if
currentcolorspace dup type/nametype eq{
pop setcolor
}{
0 get/Pattern eq{
//ResolvePattern exec setpattern
}{
setcolor
}ifelse
}ifelse
//PDFR_DEBUG{
(SetColor end)=
}if
}bind def
/ImageKeys 15 dict begin
/BPC/BitsPerComponent def
/CS/ColorSpace def
/D/Decode def
/DP/DecodeParms def
/F/Filter def
/H/Height def
/IM/ImageMask def
/I/Interpolate def
/W/Width def
currentdict end readonly def
/ImageValues 15 dict begin
/G/DeviceGray def
/RGB/DeviceRGB def
/CMYK/DeviceCMYK def
/I/Indexed def
/AHx/ASCIIHexDecode def
/A85/ASCII85Decode def
/LZW/LZWDecode def
/Fl/FlateDecode def
/RL/RunLengthDecode def
/CCF/CCITTFaxDecode def
/DCT/DCTDecode def
currentdict end readonly def
/GetColorSpaceRange
{2 index/ColorSpace get
dup type/arraytype eq{
1 get
}if
exch//knownget exec{
exch pop
}if
}bind def
/DecodeArrays 15 dict begin
/DeviceGray{[0 1]}def
/DeviceRGB{[0 1 0 1 0 1]}def
/DeviceCMYK{[0 1 0 1 0 1 0 1]}def
/Indexed{
dup/BitsPerComponent get 1 exch bitshift 1 sub[exch 0 exch]
}def
/Separation{[0 1]}def
/CIEBasedA{[0 1]/RangeA//GetColorSpaceRange exec}def
/CIEBasedABC{[0 1 0 1 0 1]/RangeABC//GetColorSpaceRange exec}def
currentdict end readonly def
/Substitute
{1 index//knownget exec{
exch pop
}if
}bind def
/DebugImagePrinting
{
//PDFR_DEBUG{
(Image :)=
dup{exch//=only exec( )print ==
}forall
}if
}bind def
/CompleteImage
{
dup/ColorSpace known{
dup/ColorSpace//CheckColorSpace//ResolveD exec pop
}if
dup/Decode known not{
dup/ColorSpace//knownget exec{
dup type/arraytype eq{
0 get
}if
//DecodeArrays exch get exec
}{
[0 1]
}ifelse
1 index exch/Decode exch put
}if
dup/ImageMatrix[2 index/Width get 0 0 5 index/Height get neg
0 7 index/Height get]put
//DebugImagePrinting exec
}bind def
/CompleteInlineImage
{
//PDFR_DEBUG{
(CompleteInlineImage beg)=
}if
dup/ImageType known not{
dup/ImageType 1 put
}if
dup length dict exch{
exch//ImageKeys//Substitute exec
dup/Filter eq{
exch//ImageValues//Substitute exec exch
}if
dup/ColorSpace eq{
exch
dup//ImageValues exch//knownget exec{
exch pop
}{
//ResolveColorSpace exec
}ifelse
exch
}if
exch
2 index 3 1 roll put
}forall
//CompleteImage exec
dup/DataSource 2 copy get
2 index//AppendFilters exec put
//PDFR_DEBUG{
(CompleteInlineImage end)=
}if
}bind def
/CompleteOutlineImage
{
currentglobal exch dup gcheck setglobal
//PDFR_DEBUG{
(CompleteOutlineImage beg)=
}if
dup dup//MakeStreamReader exec/DataSource exch put
dup/ImageType known not{
//CompleteImage exec
dup/ImageType 1 put
dup/ColorSpace known{
dup/ColorSpace//CheckColorSpace//ResolveD exec
dup type/arraytype eq{
//ResolveColorSpaceArray exec
//SubstitutePDFColorSpace exec
1 index exch/ColorSpace exch put
}{
pop
}ifelse
}if
}if
//PDFR_DEBUG{
(CompleteOutlineImage end)=
}if
exch setglobal
}bind def
/DoImage
{
//PDFR_DEBUG{
(DoImage beg)=
}if
gsave
dup/ColorSpace//knownget exec{setcolorspace}if
dup/ImageMask//knownget exec not{false}if
{imagemask}{image}ifelse
grestore
//PDFR_DEBUG{
(DoImage end)=
}if
}bind def
/GSave
{
gsave
//PDFReader/GraphicStateStackPointer get
dup//GraphicStateStack exch get null eq{
dup//GraphicStateStack exch//InitialGraphicState length dict put
}if
dup//GraphicStateStack exch get
//GraphicState exch copy pop
1 add//PDFReader exch/GraphicStateStackPointer exch put
}bind def
/GRestore
{
grestore
//PDFReader/GraphicStateStackPointer get
1 sub dup
//PDFReader exch/GraphicStateStackPointer exch put
//GraphicStateStack exch get
//GraphicState copy pop
}bind def
/SetFont
{dup//GraphicState exch/FontSize exch put
//ResolveAndSetFont exec
//GraphicState/FontMatrixNonHV currentfont/FontMatrix get 1 get 0 ne put
}bind def
/ShowText
{
//GraphicState/TextRenderingMode get dup 0 eq
exch 3 eq not currentfont/FontType get 3 eq and or
{
//GraphicState/WordSpacing get 0
32
//GraphicState/CharacterSpacing get 0
6 5 roll
//GraphicState/FontMatrixNonHV get{
[
7 -2 roll pop
5 -2 roll pop
5 -1 roll
{
exch
pop
3 index add
exch 2 index eq{3 index add}if
4 1 roll
}
currentfont/FontMatrix get 0 get 0 ne{
1 1 index length 1 sub getinterval cvx
}if
5 index
cshow
pop pop pop]
xshow
}{
awidthshow
}ifelse
}{
//GraphicState/CharacterSpacing get 0 eq
//GraphicState/FontMatrixNonHV get not and
//GraphicState/WordSpacing get 0 eq and{
true charpath
}{
{
exch
pop 0
currentpoint 5 4 roll
( )dup 0 3 index put true charpath
5 1 roll
moveto rmoveto
//GraphicState/CharacterSpacing get 0 rmoveto
32 eq{
//GraphicState/WordSpacing get 0 rmoveto
}if
}
//GraphicState/FontMatrixNonHV get dup not exch{
pop currentfont/FontMatrix get 0 get 0 ne
}if{
1 1 index length 1 sub getinterval cvx
}if
exch cshow
}ifelse
}ifelse
}bind def
/ShowTextBeg
{
//GraphicState/TextRenderingMode get dup 0 ne
{
3 ne
currentfont/FontType get 3 eq not and{
currentpoint newpath moveto
}if
}
{
pop
}ifelse
}bind def
/ShowTextEnd
{
//GraphicState/TextRenderingMode get
currentfont/FontType get 3 eq{
dup 3 ne{
pop 0
}if
}if
{dup 1 eq{
stroke exit
}if
dup 2 eq{
gsave fill grestore stroke exit
}if
dup 3 eq{
currentpoint newpath moveto
}if
dup 4 eq{
gsave fill grestore clip exit
}if
dup 5 eq{
gsave stroke grestore clip exit
}if
dup 6 eq{
gsave fill grestore gsave stroke grestore fill exit
}if
dup 7 eq{
clip exit
}if
exit
}loop
pop
}bind def
/ShowTextWithGlyphPositioning
{//ShowTextBeg exec
{dup type/stringtype eq{
//ShowText exec
}{
neg 1000 div//GraphicState/FontSize get mul 0 rmoveto
}ifelse
}forall
//ShowTextEnd exec
}bind def
/CheckFont
{dup/Type get/ExtGState ne{
mark(Resource )3 index( must have /Type/ExtGState.)//error exec
}if
}bind def
/SetTransfer
{
//PDFR_DEBUG{(SetTransfer beg )print count =}if
dup type/arraytype eq 1 index xcheck not and{
0 4 getinterval aload pop
setcolortransfer
}{
settransfer
}ifelse
//PDFR_DEBUG{(SetTransfer end )print count =}if
}bind def
/CheckExtGState
{dup/Type get/ExtGState ne{
mark(Resource )3 index( must have /Type/ExtGState.)//error exec
}if
}bind def
/CheckHalftone
{dup/HalftoneType known not{
mark(Resource )3 index( must have /HalftoneType.)//error exec
}if
}bind def
/ResolveFunction
{
//PDFR_DEBUG{(ResolveFunction beg )print dup = count =}if
2 copy get//IsObjRef exec{
2 copy//DoNothing//ResolveD exec
3 copy put pop
}if
2 copy get dup type/arraytype eq exch xcheck and not{
2 copy get
dup type/arraytype eq 1 index xcheck not and{
dup length 1 sub -1 0{
2 copy//DoNothing ResolveA
dup/Identity eq{
pop 2 copy{}put
}{
//FunctionToProc exec
3 copy put pop
}ifelse
pop
}for
}{
dup/Default eq{
}{
dup/Identity eq{
pop{}
}{dup type/nametype eq{
//spotfunctions exch get
}{
//FunctionToProc exec
}ifelse
}ifelse
}ifelse
}ifelse
3 copy put
exch pop
}{
1 index exch get
}ifelse
//PDFR_DEBUG{(ResolveFunction end )print dup == count =}if
}bind def
/ResolveFunctionSafe
{2 copy known{
//ResolveFunction exec
}if
pop
}bind def
/CreateHalftoneThresholds
{
dup/Thresholds known not{
dup/HalftoneType get 10 eq{
dup dup//MakeStreamReader exec
/Thresholds exch put
}if
dup/HalftoneType get dup 3 eq exch 6 eq or{
dup dup//MakeStreamReader exec
//BlockBuffer readstring pop
dup length
dup 0 eq{
mark(Could not read Thresholds)//error exec
}if
string copy/Thresholds exch put
dup/HalftoneType 3 put
}if
}if
}bind def
/SetExtGState
{
//PDFReader/CurrentObject get/Context get/Resources get
/ExtGState//DoNothing//ResolveD exec
exch//CheckExtGState//ResolveD exec
dup/LW//knownget exec{
setlinewidth
}if
dup/LC//knownget exec{
setlinecap
}if
dup/LJ//knownget exec{
setlinejoin
}if
dup/ML//knownget exec{
setmeterlimit
}if
dup/D//knownget exec{
setdash
}if
dup/RI//knownget exec{
mark(Unimplemented ExtGState.RI)//error exec
}if
dup/OP//knownget exec{
setoverprint
}if
dup/op//knownget exec{
setoverprint
}if
dup/OPM//knownget exec{
mark(Unimplemented ExtGState.OPM)//error exec
}if
dup/Font//knownget exec{
mark(Unimplemented ExtGState.Font)//error exec
}if
dup/BG known{
/BG//ResolveFunction exec
setblackgeneration
}if
dup/BG2 known{
/BG2//ResolveFunction exec
dup/Default eq{
//InitialExtGState/BG2 get
}if
setblackgeneration
}if
dup/UCR known{
/UCR//ResolveFunction exec
setundercolorremoval
}if
dup/UCR2 known{
/UCR2//ResolveFunction exec
dup/Default eq{
//InitialExtGState/UCR2 get
}if
setundercolorremoval
}if
dup/TR known{
/TR//ResolveFunction exec
//SetTransfer exec
}if
dup/TR2 known{
/TR2//ResolveFunction exec
dup/Default eq{
pop//InitialExtGState/TR2 get
aload pop setcolortransfer
}{
//SetTransfer exec
}ifelse
}if
dup/HT//knownget exec{
dup/Default eq{
pop//InitialExtGState/HT get
sethalftone
}{
//PDFR_DEBUG{(Ht beg)=}if
pop dup/HT//CheckHalftone//ResolveD exec
/SpotFunction//ResolveFunctionSafe exec
/TransferFunction//ResolveFunctionSafe exec
null exch
dup/HalftoneType get dup 5 eq exch dup 4 eq exch 2 eq or or{
dup{
dup//IsObjRef exec{
pop
1 index exch//CheckHalftone ResolveD
}if
dup type/dicttype eq{
dup/SpotFunction//ResolveFunctionSafe exec
/TransferFunction//ResolveFunctionSafe exec
//CreateHalftoneThresholds exec
dup/HalftoneType get 5 gt{
4 3 roll pop
dup 4 1 roll
}if
}if
pop pop
}forall
}if
//CreateHalftoneThresholds exec
//PDFR_DEBUG{
(HT:)=
dup{
1 index/Default eq{
(Default <<)=
exch pop
{exch = ==}forall
(>>)=
}{
exch = ==
}ifelse
}forall
(HT end)= flush
}if
exch dup null ne{
(Warning: Ignoring a halftone with a Level 3 component halftone Type )print dup/HalftoneType get =
pop pop
}{
pop
dup/HalftoneType get 5 gt{
(Warning: Ignoring a Level 3 halftone Type )print dup/HalftoneType get =
pop
}{
sethalftone
}ifelse
}ifelse
//PDFR_DEBUG{(HT set)= flush}if
}ifelse
}if
dup/FL//knownget exec{
setflattness
}if
dup/SM//knownget exec{
setsmoothness
}if
dup/SA//knownget exec{
setstrokeadjust
}if
dup/BM//knownget exec{
mark(Unimplemented ExtGState.BM)//error exec
}if
dup/SMask//knownget exec{
mark(Unimplemented ExtGState.SMask)//error exec
}if
dup/CA//knownget exec{
mark(Unimplemented ExtGState.CA)//error exec
}if
dup/ca//knownget exec{
mark(Unimplemented ExtGState.ca)//error exec
}if
dup/AIS//knownget exec{
mark(Unimplemented ExtGState.AIS)//error exec
}if
dup/TK//knownget exec{
mark(Unimplemented ExtGState.TK)//error exec
}if
pop
}bind def
/CheckXObject
{dup/Subtype get dup/Image ne exch dup/Form ne exch/PS ne and and{
mark(Resource )3 index( must have /Subtype /Image or /Form or /PS.)//error exec
}if
}bind def
/DoXObject
{
//PDFReader/CurrentObject get/Context get/Resources get
/XObject//DoNothing//ResolveD exec
exch//CheckXObject//ResolveD exec
dup/Subtype get
dup/Image eq{
pop
//CompleteOutlineImage exec
//DoImage exec
}{
dup/PS eq{
PDFR_DEBUG{
(Executing a PS Xobject)=
}if
pop
//RunDelayedStream exec
}{
dup/Form eq{
pop
PDFR_DEBUG{
(Executing a Form XObject)=
}if
//PDFReader/CurrentObject get exch
dup//PDFReader exch<< exch/Context exch >>/CurrentObject exch put
dup/Matrix get concat
dup/BBox get aload pop exch 3 index sub exch 2 index sub rectclip
//RunDelayedStream exec
//PDFReader exch/CurrentObject exch put
}{
mark exch(unimplemented XObject type )exch//error exec
}ifelse
}ifelse
}ifelse
}bind def
/Operators 50 dict begin
/q{//GSave exec}bind def
/Q{//GRestore exec}bind def
/cm{//TempMatrix astore concat}bind def
/i{1 .min setflat}bind def
/J/setlinecap load def
/d/setdash load def
/j/setlinejoin load def
/w/setlinewidth load def
/M/setmiterlimit load def
/gs{SetExtGState}bind def
/g/setgray load def
/rg/setrgbcolor load def
/k/setcmykcolor load def
/cs{//ResolveColorSpace exec//SetColorSpaceSafe exec
}bind def
/sc/setcolor load def
/scn{//SetColor exec}bind def
/G/setgray load def
/RG/setrgbcolor load def
/K/setcmykcolor load def
/CS//cs def
/ri{SetColorRenderingIntent}bind def
/SC/setcolor load def
/SCN{//SetColor exec}bind def
/m/moveto load def
/l/lineto load def
/c/curveto load def
/v{currentpoint 6 2 roll curveto}bind def
/y{2 copy curveto}bind def
/re{
4 2 roll moveto exch dup 0 rlineto 0 3 -1 roll rlineto neg 0 rlineto
closepath
}def
/h/closepath load def
/n/newpath load def
/S/stroke load def
/s{closepath stroke}bind def
/f/fill load def
/f*/eofill load def
/B{gsave fill grestore stroke}bind def
/b{closepath gsave fill grestore stroke}bind def
/B*{gsave eofill grestore stroke}bind def
/b*{closepath gsave eofill grestore stroke}bind def
/W/clip load def
/W*/eoclip load def
/sh{
ResolveShading
dup/Background known{
gsave
dup/ColorSpace get setcolorspace
dup/Background get aload pop setcolor
pathbbox
2 index sub exch 3 index sub exch
rectfill
grestore
}if
shfill
}bind def
/Do{//DoXObject exec}bind def
/BI{currentglobal false setglobal<<}bind def
/ID{>>
dup/DataSource currentfile
2 index/F//knownget exec{
/A85 eq{
0(~>)/SubFileDecode filter
}if
}if
put
//CompleteInlineImage exec
exch setglobal
//DoImage exec
}bind def
/EI{}bind def
/BT{gsave//GraphicState/InitialTextMatrix get currentmatrix pop}bind def
/ET{grestore}bind def
/Tc{//GraphicState exch/CharacterSpacing exch put}bind def
/TL{//GraphicState exch/TextLeading exch put}bind def
/Tr{//GraphicState exch/TextRenderingMode exch put}bind def
/Ts{
mark(Unimplemented SetTextRise)//error exec
}bind def
/Tw{//GraphicState exch/WordSpacing exch put}bind def
/Tz{
mark(Unimplemented SetHorizontalTextScaling)//error exec
}bind def
/Td{translate 0 0 moveto}bind def
/TD{dup neg//TL exec//Td exec}bind def
/Tm{//GraphicState/InitialTextMatrix get setmatrix
//TempMatrix astore concat
0 0 moveto}bind def
/T*{0//GraphicState/TextLeading get neg//Td exec}bind def
/Tj{//ShowTextBeg exec//ShowText exec//ShowTextEnd exec}bind def
/'{//T* exec//ShowText exec//ShowTextEnd exec}bind def
/"{3 2 roll//Tw exec exch//Tc exec//' exec}bind def
/TJ//ShowTextWithGlyphPositioning def
/Tf//SetFont def
/d0/setcharwidth load def
/d1/setcachedevice load def
/BDC{pop pop}bind def
/BMC{pop}bind def
/EMC{}bind def
/BX{BeginCompatibilitySection}bind def
/EX{EndCompatibilitySection}bind def
/DP{DefineMarkedContentPointWithPropertyList}bind def
/MP{DefineMarkedContentPoint}bind def
/PS{cvx exec}bind def
currentdict end def
//PDFR_STREAM{
//Operators length dict begin
//Operators{
exch dup
[exch//=only/exec load
( )/print load
8 7 roll
dup type/arraytype eq{
/exec load
}if
( )/print load
]cvx
def
}forall
currentdict end/Operators exch def
}if
/.registerencoding
{pop pop
}bind def
/.defineencoding
{def
}bind def
/.findencoding
{load
}bind def
/currentglobal where
{pop currentglobal{setglobal}true setglobal}
{{}}
ifelse
/MacRomanEncoding
StandardEncoding 0 39 getinterval aload pop
/quotesingle
StandardEncoding 40 56 getinterval aload pop
/grave
StandardEncoding 97 31 getinterval aload pop
/Adieresis/Aring/Ccedilla/Eacute/Ntilde/Odieresis/Udieresis/aacute
/agrave/acircumflex/adieresis/atilde/aring/ccedilla/eacute/egrave
/ecircumflex/edieresis/iacute/igrave
/icircumflex/idieresis/ntilde/oacute
/ograve/ocircumflex/odieresis/otilde
/uacute/ugrave/ucircumflex/udieresis
/dagger/degree/cent/sterling/section/bullet/paragraph/germandbls
/registered/copyright/trademark/acute/dieresis/.notdef/AE/Oslash
/.notdef/plusminus/.notdef/.notdef/yen/mu/.notdef/.notdef
/.notdef/.notdef/.notdef/ordfeminine/ordmasculine/.notdef/ae/oslash
/questiondown/exclamdown/logicalnot/.notdef
/florin/.notdef/.notdef/guillemotleft
/guillemotright/ellipsis/space/Agrave/Atilde/Otilde/OE/oe
/endash/emdash/quotedblleft/quotedblright
/quoteleft/quoteright/divide/.notdef
/ydieresis/Ydieresis/fraction/currency
/guilsinglleft/guilsinglright/fi/fl
/daggerdbl/periodcentered/quotesinglbase/quotedblbase
/perthousand/Acircumflex/Ecircumflex/Aacute
/Edieresis/Egrave/Iacute/Icircumflex
/Idieresis/Igrave/Oacute/Ocircumflex
/.notdef/Ograve/Uacute/Ucircumflex
/Ugrave/dotlessi/circumflex/tilde
/macron/breve/dotaccent/ring/cedilla/hungarumlaut/ogonek/caron
256 packedarray
5 1 index .registerencoding
.defineencoding
exec
/AdobeGlyphList mark
/A 16#0041
/AE 16#00c6
/AEacute 16#01fc
/AEmacron 16#01e2
/AEsmall 16#f7e6
/Aacute 16#00c1
/Aacutesmall 16#f7e1
/Abreve 16#0102
/Abreveacute 16#1eae
/Abrevecyrillic 16#04d0
/Abrevedotbelow 16#1eb6
/Abrevegrave 16#1eb0
/Abrevehookabove 16#1eb2
/Abrevetilde 16#1eb4
/Acaron 16#01cd
/Acircle 16#24b6
/Acircumflex 16#00c2
/Acircumflexacute 16#1ea4
/Acircumflexdotbelow 16#1eac
/Acircumflexgrave 16#1ea6
/Acircumflexhookabove 16#1ea8
/Acircumflexsmall 16#f7e2
/Acircumflextilde 16#1eaa
/Acute 16#f6c9
/Acutesmall 16#f7b4
/Acyrillic 16#0410
/Adblgrave 16#0200
/Adieresis 16#00c4
/Adieresiscyrillic 16#04d2
/Adieresismacron 16#01de
/Adieresissmall 16#f7e4
/Adotbelow 16#1ea0
/Adotmacron 16#01e0
/Agrave 16#00c0
/Agravesmall 16#f7e0
/Ahookabove 16#1ea2
/Aiecyrillic 16#04d4
/Ainvertedbreve 16#0202
/Alpha 16#0391
/Alphatonos 16#0386
/Amacron 16#0100
/Amonospace 16#ff21
/Aogonek 16#0104
/Aring 16#00c5
/Aringacute 16#01fa
/Aringbelow 16#1e00
/Aringsmall 16#f7e5
/Asmall 16#f761
/Atilde 16#00c3
/Atildesmall 16#f7e3
/Aybarmenian 16#0531
/B 16#0042
/Bcircle 16#24b7
/Bdotaccent 16#1e02
/Bdotbelow 16#1e04
/Becyrillic 16#0411
/Benarmenian 16#0532
/Beta 16#0392
/Bhook 16#0181
/Blinebelow 16#1e06
/Bmonospace 16#ff22
/Brevesmall 16#f6f4
/Bsmall 16#f762
/Btopbar 16#0182
/C 16#0043
/Caarmenian 16#053e
/Cacute 16#0106
/Caron 16#f6ca
/Caronsmall 16#f6f5
/Ccaron 16#010c
/Ccedilla 16#00c7
/Ccedillaacute 16#1e08
/Ccedillasmall 16#f7e7
/Ccircle 16#24b8
/Ccircumflex 16#0108
/Cdot 16#010a
/Cdotaccent 16#010a
/Cedillasmall 16#f7b8
/Chaarmenian 16#0549
/Cheabkhasiancyrillic 16#04bc
/Checyrillic 16#0427
/Chedescenderabkhasiancyrillic 16#04be
/Chedescendercyrillic 16#04b6
/Chedieresiscyrillic 16#04f4
/Cheharmenian 16#0543
/Chekhakassiancyrillic 16#04cb
/Cheverticalstrokecyrillic 16#04b8
/Chi 16#03a7
/Chook 16#0187
/Circumflexsmall 16#f6f6
/Cmonospace 16#ff23
/Coarmenian 16#0551
/Csmall 16#f763
/D 16#0044
/DZ 16#01f1
/DZcaron 16#01c4
/Daarmenian 16#0534
/Dafrican 16#0189
/Dcaron 16#010e
/Dcedilla 16#1e10
/Dcircle 16#24b9
/Dcircumflexbelow 16#1e12
/Dcroat 16#0110
/Ddotaccent 16#1e0a
/Ddotbelow 16#1e0c
/Decyrillic 16#0414
/Deicoptic 16#03ee
/Delta 16#2206
/Deltagreek 16#0394
/Dhook 16#018a
/Dieresis 16#f6cb
/DieresisAcute 16#f6cc
/DieresisGrave 16#f6cd
/Dieresissmall 16#f7a8
/Digammagreek 16#03dc
/Djecyrillic 16#0402
/Dlinebelow 16#1e0e
/Dmonospace 16#ff24
/Dotaccentsmall 16#f6f7
/Dslash 16#0110
/Dsmall 16#f764
/Dtopbar 16#018b
/Dz 16#01f2
/Dzcaron 16#01c5
/Dzeabkhasiancyrillic 16#04e0
/Dzecyrillic 16#0405
/Dzhecyrillic 16#040f
/E 16#0045
/Eacute 16#00c9
/Eacutesmall 16#f7e9
/Ebreve 16#0114
/Ecaron 16#011a
/Ecedillabreve 16#1e1c
/Echarmenian 16#0535
/Ecircle 16#24ba
/Ecircumflex 16#00ca
/Ecircumflexacute 16#1ebe
/Ecircumflexbelow 16#1e18
/Ecircumflexdotbelow 16#1ec6
/Ecircumflexgrave 16#1ec0
/Ecircumflexhookabove 16#1ec2
/Ecircumflexsmall 16#f7ea
/Ecircumflextilde 16#1ec4
/Ecyrillic 16#0404
/Edblgrave 16#0204
/Edieresis 16#00cb
/Edieresissmall 16#f7eb
/Edot 16#0116
/Edotaccent 16#0116
/Edotbelow 16#1eb8
/Efcyrillic 16#0424
/Egrave 16#00c8
/Egravesmall 16#f7e8
/Eharmenian 16#0537
/Ehookabove 16#1eba
/Eightroman 16#2167
/Einvertedbreve 16#0206
/Eiotifiedcyrillic 16#0464
/Elcyrillic 16#041b
/Elevenroman 16#216a
/Emacron 16#0112
/Emacronacute 16#1e16
/Emacrongrave 16#1e14
/Emcyrillic 16#041c
/Emonospace 16#ff25
/Encyrillic 16#041d
/Endescendercyrillic 16#04a2
/Eng 16#014a
/Enghecyrillic 16#04a4
/Enhookcyrillic 16#04c7
/Eogonek 16#0118
/Eopen 16#0190
/Epsilon 16#0395
/Epsilontonos 16#0388
/Ercyrillic 16#0420
/Ereversed 16#018e
/Ereversedcyrillic 16#042d
/Escyrillic 16#0421
/Esdescendercyrillic 16#04aa
/Esh 16#01a9
/Esmall 16#f765
/Eta 16#0397
/Etarmenian 16#0538
/Etatonos 16#0389
/Eth 16#00d0
/Ethsmall 16#f7f0
/Etilde 16#1ebc
/Etildebelow 16#1e1a
/Euro 16#20ac
/Ezh 16#01b7
/Ezhcaron 16#01ee
/Ezhreversed 16#01b8
/F 16#0046
/Fcircle 16#24bb
/Fdotaccent 16#1e1e
/Feharmenian 16#0556
/Feicoptic 16#03e4
/Fhook 16#0191
/Fitacyrillic 16#0472
/Fiveroman 16#2164
/Fmonospace 16#ff26
/Fourroman 16#2163
/Fsmall 16#f766
/G 16#0047
/GBsquare 16#3387
/Gacute 16#01f4
/Gamma 16#0393
/Gammaafrican 16#0194
/Gangiacoptic 16#03ea
/Gbreve 16#011e
/Gcaron 16#01e6
/Gcedilla 16#0122
/Gcircle 16#24bc
/Gcircumflex 16#011c
/Gcommaaccent 16#0122
/Gdot 16#0120
/Gdotaccent 16#0120
/Gecyrillic 16#0413
/Ghadarmenian 16#0542
/Ghemiddlehookcyrillic 16#0494
/Ghestrokecyrillic 16#0492
/Gheupturncyrillic 16#0490
/Ghook 16#0193
/Gimarmenian 16#0533
/Gjecyrillic 16#0403
/Gmacron 16#1e20
/Gmonospace 16#ff27
/Grave 16#f6ce
/Gravesmall 16#f760
/Gsmall 16#f767
/Gsmallhook 16#029b
/Gstroke 16#01e4
/H 16#0048
/H18533 16#25cf
/H18543 16#25aa
/H18551 16#25ab
/H22073 16#25a1
/HPsquare 16#33cb
/Haabkhasiancyrillic 16#04a8
/Hadescendercyrillic 16#04b2
/Hardsigncyrillic 16#042a
/Hbar 16#0126
/Hbrevebelow 16#1e2a
/Hcedilla 16#1e28
/Hcircle 16#24bd
/Hcircumflex 16#0124
/Hdieresis 16#1e26
/Hdotaccent 16#1e22
/Hdotbelow 16#1e24
/Hmonospace 16#ff28
/Hoarmenian 16#0540
/Horicoptic 16#03e8
/Hsmall 16#f768
/Hungarumlaut 16#f6cf
/Hungarumlautsmall 16#f6f8
/Hzsquare 16#3390
/I 16#0049
/IAcyrillic 16#042f
/IJ 16#0132
/IUcyrillic 16#042e
/Iacute 16#00cd
/Iacutesmall 16#f7ed
/Ibreve 16#012c
/Icaron 16#01cf
/Icircle 16#24be
/Icircumflex 16#00ce
/Icircumflexsmall 16#f7ee
/Icyrillic 16#0406
/Idblgrave 16#0208
/Idieresis 16#00cf
/Idieresisacute 16#1e2e
/Idieresiscyrillic 16#04e4
/Idieresissmall 16#f7ef
/Idot 16#0130
/Idotaccent 16#0130
/Idotbelow 16#1eca
/Iebrevecyrillic 16#04d6
/Iecyrillic 16#0415
/Ifraktur 16#2111
/Igrave 16#00cc
/Igravesmall 16#f7ec
/Ihookabove 16#1ec8
/Iicyrillic 16#0418
/Iinvertedbreve 16#020a
/Iishortcyrillic 16#0419
/Imacron 16#012a
/Imacroncyrillic 16#04e2
/Imonospace 16#ff29
/Iniarmenian 16#053b
/Iocyrillic 16#0401
/Iogonek 16#012e
/Iota 16#0399
/Iotaafrican 16#0196
/Iotadieresis 16#03aa
/Iotatonos 16#038a
/Ismall 16#f769
/Istroke 16#0197
/Itilde 16#0128
/Itildebelow 16#1e2c
/Izhitsacyrillic 16#0474
/Izhitsadblgravecyrillic 16#0476
/J 16#004a
/Jaarmenian 16#0541
/Jcircle 16#24bf
/Jcircumflex 16#0134
/Jecyrillic 16#0408
/Jheharmenian 16#054b
/Jmonospace 16#ff2a
/Jsmall 16#f76a
/K 16#004b
/KBsquare 16#3385
/KKsquare 16#33cd
/Kabashkircyrillic 16#04a0
/Kacute 16#1e30
/Kacyrillic 16#041a
/Kadescendercyrillic 16#049a
/Kahookcyrillic 16#04c3
/Kappa 16#039a
/Kastrokecyrillic 16#049e
/Kaverticalstrokecyrillic 16#049c
/Kcaron 16#01e8
/Kcedilla 16#0136
/Kcircle 16#24c0
/Kcommaaccent 16#0136
/Kdotbelow 16#1e32
/Keharmenian 16#0554
/Kenarmenian 16#053f
/Khacyrillic 16#0425
/Kheicoptic 16#03e6
/Khook 16#0198
/Kjecyrillic 16#040c
/Klinebelow 16#1e34
/Kmonospace 16#ff2b
/Koppacyrillic 16#0480
/Koppagreek 16#03de
/Ksicyrillic 16#046e
/Ksmall 16#f76b
/L 16#004c
/LJ 16#01c7
/LL 16#f6bf
/Lacute 16#0139
/Lambda 16#039b
/Lcaron 16#013d
/Lcedilla 16#013b
/Lcircle 16#24c1
/Lcircumflexbelow 16#1e3c
/Lcommaaccent 16#013b
/Ldot 16#013f
/Ldotaccent 16#013f
/Ldotbelow 16#1e36
/Ldotbelowmacron 16#1e38
/Liwnarmenian 16#053c
/Lj 16#01c8
/Ljecyrillic 16#0409
/Llinebelow 16#1e3a
/Lmonospace 16#ff2c
/Lslash 16#0141
/Lslashsmall 16#f6f9
/Lsmall 16#f76c
/M 16#004d
/MBsquare 16#3386
/Macron 16#f6d0
/Macronsmall 16#f7af
/Macute 16#1e3e
/Mcircle 16#24c2
/Mdotaccent 16#1e40
/Mdotbelow 16#1e42
/Menarmenian 16#0544
/Mmonospace 16#ff2d
/Msmall 16#f76d
/Mturned 16#019c
/Mu 16#039c
/N 16#004e
/NJ 16#01ca
/Nacute 16#0143
/Ncaron 16#0147
/Ncedilla 16#0145
/Ncircle 16#24c3
/Ncircumflexbelow 16#1e4a
/Ncommaaccent 16#0145
/Ndotaccent 16#1e44
/Ndotbelow 16#1e46
/Nhookleft 16#019d
/Nineroman 16#2168
/Nj 16#01cb
/Njecyrillic 16#040a
/Nlinebelow 16#1e48
/Nmonospace 16#ff2e
/Nowarmenian 16#0546
/Nsmall 16#f76e
/Ntilde 16#00d1
/Ntildesmall 16#f7f1
/Nu 16#039d
/O 16#004f
/OE 16#0152
/OEsmall 16#f6fa
/Oacute 16#00d3
/Oacutesmall 16#f7f3
/Obarredcyrillic 16#04e8
/Obarreddieresiscyrillic 16#04ea
/Obreve 16#014e
/Ocaron 16#01d1
/Ocenteredtilde 16#019f
/Ocircle 16#24c4
/Ocircumflex 16#00d4
/Ocircumflexacute 16#1ed0
/Ocircumflexdotbelow 16#1ed8
/Ocircumflexgrave 16#1ed2
/Ocircumflexhookabove 16#1ed4
/Ocircumflexsmall 16#f7f4
/Ocircumflextilde 16#1ed6
/Ocyrillic 16#041e
/Odblacute 16#0150
/Odblgrave 16#020c
/Odieresis 16#00d6
/Odieresiscyrillic 16#04e6
/Odieresissmall 16#f7f6
/Odotbelow 16#1ecc
/Ogoneksmall 16#f6fb
/Ograve 16#00d2
/Ogravesmall 16#f7f2
/Oharmenian 16#0555
/Ohm 16#2126
/Ohookabove 16#1ece
/Ohorn 16#01a0
/Ohornacute 16#1eda
/Ohorndotbelow 16#1ee2
/Ohorngrave 16#1edc
/Ohornhookabove 16#1ede
/Ohorntilde 16#1ee0
/Ohungarumlaut 16#0150
/Oi 16#01a2
/Oinvertedbreve 16#020e
/Omacron 16#014c
/Omacronacute 16#1e52
/Omacrongrave 16#1e50
/Omega 16#2126
/Omegacyrillic 16#0460
/Omegagreek 16#03a9
/Omegaroundcyrillic 16#047a
/Omegatitlocyrillic 16#047c
/Omegatonos 16#038f
/Omicron 16#039f
/Omicrontonos 16#038c
/Omonospace 16#ff2f
/Oneroman 16#2160
/Oogonek 16#01ea
/Oogonekmacron 16#01ec
/Oopen 16#0186
/Oslash 16#00d8
/Oslashacute 16#01fe
/Oslashsmall 16#f7f8
/Osmall 16#f76f
/Ostrokeacute 16#01fe
/Otcyrillic 16#047e
/Otilde 16#00d5
/Otildeacute 16#1e4c
/Otildedieresis 16#1e4e
/Otildesmall 16#f7f5
/P 16#0050
/Pacute 16#1e54
/Pcircle 16#24c5
/Pdotaccent 16#1e56
/Pecyrillic 16#041f
/Peharmenian 16#054a
/Pemiddlehookcyrillic 16#04a6
/Phi 16#03a6
/Phook 16#01a4
/Pi 16#03a0
/Piwrarmenian 16#0553
/Pmonospace 16#ff30
/Psi 16#03a8
/Psicyrillic 16#0470
/Psmall 16#f770
/Q 16#0051
/Qcircle 16#24c6
/Qmonospace 16#ff31
/Qsmall 16#f771
/R 16#0052
/Raarmenian 16#054c
/Racute 16#0154
/Rcaron 16#0158
/Rcedilla 16#0156
/Rcircle 16#24c7
/Rcommaaccent 16#0156
/Rdblgrave 16#0210
/Rdotaccent 16#1e58
/Rdotbelow 16#1e5a
/Rdotbelowmacron 16#1e5c
/Reharmenian 16#0550
/Rfraktur 16#211c
/Rho 16#03a1
/Ringsmall 16#f6fc
/Rinvertedbreve 16#0212
/Rlinebelow 16#1e5e
/Rmonospace 16#ff32
/Rsmall 16#f772
/Rsmallinverted 16#0281
/Rsmallinvertedsuperior 16#02b6
/S 16#0053
/SF010000 16#250c
/SF020000 16#2514
/SF030000 16#2510
/SF040000 16#2518
/SF050000 16#253c
/SF060000 16#252c
/SF070000 16#2534
/SF080000 16#251c
/SF090000 16#2524
/SF100000 16#2500
/SF110000 16#2502
/SF190000 16#2561
/SF200000 16#2562
/SF210000 16#2556
/SF220000 16#2555
/SF230000 16#2563
/SF240000 16#2551
/SF250000 16#2557
/SF260000 16#255d
/SF270000 16#255c
/SF280000 16#255b
/SF360000 16#255e
/SF370000 16#255f
/SF380000 16#255a
/SF390000 16#2554
/SF400000 16#2569
/SF410000 16#2566
/SF420000 16#2560
/SF430000 16#2550
/SF440000 16#256c
/SF450000 16#2567
/SF460000 16#2568
/SF470000 16#2564
/SF480000 16#2565
/SF490000 16#2559
/SF500000 16#2558
/SF510000 16#2552
/SF520000 16#2553
/SF530000 16#256b
/SF540000 16#256a
/Sacute 16#015a
/Sacutedotaccent 16#1e64
/Sampigreek 16#03e0
/Scaron 16#0160
/Scarondotaccent 16#1e66
/Scaronsmall 16#f6fd
/Scedilla 16#015e
/Schwa 16#018f
/Schwacyrillic 16#04d8
/Schwadieresiscyrillic 16#04da
/Scircle 16#24c8
/Scircumflex 16#015c
/Scommaaccent 16#0218
/Sdotaccent 16#1e60
/Sdotbelow 16#1e62
/Sdotbelowdotaccent 16#1e68
/Seharmenian 16#054d
/Sevenroman 16#2166
/Shaarmenian 16#0547
/Shacyrillic 16#0428
/Shchacyrillic 16#0429
/Sheicoptic 16#03e2
/Shhacyrillic 16#04ba
/Shimacoptic 16#03ec
/Sigma 16#03a3
/Sixroman 16#2165
/Smonospace 16#ff33
/Softsigncyrillic 16#042c
/Ssmall 16#f773
/Stigmagreek 16#03da
/T 16#0054
/Tau 16#03a4
/Tbar 16#0166
/Tcaron 16#0164
/Tcedilla 16#0162
/Tcircle 16#24c9
/Tcircumflexbelow 16#1e70
/Tcommaaccent 16#0162
/Tdotaccent 16#1e6a
/Tdotbelow 16#1e6c
/Tecyrillic 16#0422
/Tedescendercyrillic 16#04ac
/Tenroman 16#2169
/Tetsecyrillic 16#04b4
/Theta 16#0398
/Thook 16#01ac
/Thorn 16#00de
/Thornsmall 16#f7fe
/Threeroman 16#2162
/Tildesmall 16#f6fe
/Tiwnarmenian 16#054f
/Tlinebelow 16#1e6e
/Tmonospace 16#ff34
/Toarmenian 16#0539
/Tonefive 16#01bc
/Tonesix 16#0184
/Tonetwo 16#01a7
/Tretroflexhook 16#01ae
/Tsecyrillic 16#0426
/Tshecyrillic 16#040b
/Tsmall 16#f774
/Twelveroman 16#216b
/Tworoman 16#2161
/U 16#0055
/Uacute 16#00da
/Uacutesmall 16#f7fa
/Ubreve 16#016c
/Ucaron 16#01d3
/Ucircle 16#24ca
/Ucircumflex 16#00db
/Ucircumflexbelow 16#1e76
/Ucircumflexsmall 16#f7fb
/Ucyrillic 16#0423
/Udblacute 16#0170
/Udblgrave 16#0214
/Udieresis 16#00dc
/Udieresisacute 16#01d7
/Udieresisbelow 16#1e72
/Udieresiscaron 16#01d9
/Udieresiscyrillic 16#04f0
/Udieresisgrave 16#01db
/Udieresismacron 16#01d5
/Udieresissmall 16#f7fc
/Udotbelow 16#1ee4
/Ugrave 16#00d9
/Ugravesmall 16#f7f9
/Uhookabove 16#1ee6
/Uhorn 16#01af
/Uhornacute 16#1ee8
/Uhorndotbelow 16#1ef0
/Uhorngrave 16#1eea
/Uhornhookabove 16#1eec
/Uhorntilde 16#1eee
/Uhungarumlaut 16#0170
/Uhungarumlautcyrillic 16#04f2
/Uinvertedbreve 16#0216
/Ukcyrillic 16#0478
/Umacron 16#016a
/Umacroncyrillic 16#04ee
/Umacrondieresis 16#1e7a
/Umonospace 16#ff35
/Uogonek 16#0172
/Upsilon 16#03a5
/Upsilon1 16#03d2
/Upsilonacutehooksymbolgreek 16#03d3
/Upsilonafrican 16#01b1
/Upsilondieresis 16#03ab
/Upsilondieresishooksymbolgreek 16#03d4
/Upsilonhooksymbol 16#03d2
/Upsilontonos 16#038e
/Uring 16#016e
/Ushortcyrillic 16#040e
/Usmall 16#f775
/Ustraightcyrillic 16#04ae
/Ustraightstrokecyrillic 16#04b0
/Utilde 16#0168
/Utildeacute 16#1e78
/Utildebelow 16#1e74
/V 16#0056
/Vcircle 16#24cb
/Vdotbelow 16#1e7e
/Vecyrillic 16#0412
/Vewarmenian 16#054e
/Vhook 16#01b2
/Vmonospace 16#ff36
/Voarmenian 16#0548
/Vsmall 16#f776
/Vtilde 16#1e7c
/W 16#0057
/Wacute 16#1e82
/Wcircle 16#24cc
/Wcircumflex 16#0174
/Wdieresis 16#1e84
/Wdotaccent 16#1e86
/Wdotbelow 16#1e88
/Wgrave 16#1e80
/Wmonospace 16#ff37
/Wsmall 16#f777
/X 16#0058
/Xcircle 16#24cd
/Xdieresis 16#1e8c
/Xdotaccent 16#1e8a
/Xeharmenian 16#053d
/Xi 16#039e
/Xmonospace 16#ff38
/Xsmall 16#f778
/Y 16#0059
/Yacute 16#00dd
/Yacutesmall 16#f7fd
/Yatcyrillic 16#0462
/Ycircle 16#24ce
/Ycircumflex 16#0176
/Ydieresis 16#0178
/Ydieresissmall 16#f7ff
/Ydotaccent 16#1e8e
/Ydotbelow 16#1ef4
/Yericyrillic 16#042b
/Yerudieresiscyrillic 16#04f8
/Ygrave 16#1ef2
/Yhook 16#01b3
/Yhookabove 16#1ef6
/Yiarmenian 16#0545
/Yicyrillic 16#0407
/Yiwnarmenian 16#0552
/Ymonospace 16#ff39
/Ysmall 16#f779
/Ytilde 16#1ef8
/Yusbigcyrillic 16#046a
/Yusbigiotifiedcyrillic 16#046c
/Yuslittlecyrillic 16#0466
/Yuslittleiotifiedcyrillic 16#0468
/Z 16#005a
/Zaarmenian 16#0536
/Zacute 16#0179
/Zcaron 16#017d
/Zcaronsmall 16#f6ff
/Zcircle 16#24cf
/Zcircumflex 16#1e90
/Zdot 16#017b
/Zdotaccent 16#017b
/Zdotbelow 16#1e92
/Zecyrillic 16#0417
/Zedescendercyrillic 16#0498
/Zedieresiscyrillic 16#04de
/Zeta 16#0396
/Zhearmenian 16#053a
/Zhebrevecyrillic 16#04c1
/Zhecyrillic 16#0416
/Zhedescendercyrillic 16#0496
/Zhedieresiscyrillic 16#04dc
/Zlinebelow 16#1e94
/Zmonospace 16#ff3a
/Zsmall 16#f77a
/Zstroke 16#01b5
/a 16#0061
/aabengali 16#0986
/aacute 16#00e1
/aadeva 16#0906
/aagujarati 16#0a86
/aagurmukhi 16#0a06
/aamatragurmukhi 16#0a3e
/aarusquare 16#3303
/aavowelsignbengali 16#09be
/aavowelsigndeva 16#093e
/aavowelsigngujarati 16#0abe
/abbreviationmarkarmenian 16#055f
/abbreviationsigndeva 16#0970
/abengali 16#0985
/abopomofo 16#311a
/abreve 16#0103
/abreveacute 16#1eaf
/abrevecyrillic 16#04d1
/abrevedotbelow 16#1eb7
/abrevegrave 16#1eb1
/abrevehookabove 16#1eb3
/abrevetilde 16#1eb5
/acaron 16#01ce
/acircle 16#24d0
/acircumflex 16#00e2
/acircumflexacute 16#1ea5
/acircumflexdotbelow 16#1ead
/acircumflexgrave 16#1ea7
/acircumflexhookabove 16#1ea9
/acircumflextilde 16#1eab
/acute 16#00b4
/acutebelowcmb 16#0317
/acutecmb 16#0301
/acutecomb 16#0301
/acutedeva 16#0954
/acutelowmod 16#02cf
/acutetonecmb 16#0341
/acyrillic 16#0430
/adblgrave 16#0201
/addakgurmukhi 16#0a71
/adeva 16#0905
/adieresis 16#00e4
/adieresiscyrillic 16#04d3
/adieresismacron 16#01df
/adotbelow 16#1ea1
/adotmacron 16#01e1
/ae 16#00e6
/aeacute 16#01fd
/aekorean 16#3150
/aemacron 16#01e3
/afii00208 16#2015
/afii08941 16#20a4
/afii10017 16#0410
/afii10018 16#0411
/afii10019 16#0412
/afii10020 16#0413
/afii10021 16#0414
/afii10022 16#0415
/afii10023 16#0401
/afii10024 16#0416
/afii10025 16#0417
/afii10026 16#0418
/afii10027 16#0419
/afii10028 16#041a
/afii10029 16#041b
/afii10030 16#041c
/afii10031 16#041d
/afii10032 16#041e
/afii10033 16#041f
/afii10034 16#0420
/afii10035 16#0421
/afii10036 16#0422
/afii10037 16#0423
/afii10038 16#0424
/afii10039 16#0425
/afii10040 16#0426
/afii10041 16#0427
/afii10042 16#0428
/afii10043 16#0429
/afii10044 16#042a
/afii10045 16#042b
/afii10046 16#042c
/afii10047 16#042d
/afii10048 16#042e
/afii10049 16#042f
/afii10050 16#0490
/afii10051 16#0402
/afii10052 16#0403
/afii10053 16#0404
/afii10054 16#0405
/afii10055 16#0406
/afii10056 16#0407
/afii10057 16#0408
/afii10058 16#0409
/afii10059 16#040a
/afii10060 16#040b
/afii10061 16#040c
/afii10062 16#040e
/afii10063 16#f6c4
/afii10064 16#f6c5
/afii10065 16#0430
/afii10066 16#0431
/afii10067 16#0432
/afii10068 16#0433
/afii10069 16#0434
/afii10070 16#0435
/afii10071 16#0451
/afii10072 16#0436
/afii10073 16#0437
/afii10074 16#0438
/afii10075 16#0439
/afii10076 16#043a
/afii10077 16#043b
/afii10078 16#043c
/afii10079 16#043d
/afii10080 16#043e
/afii10081 16#043f
/afii10082 16#0440
/afii10083 16#0441
/afii10084 16#0442
/afii10085 16#0443
/afii10086 16#0444
/afii10087 16#0445
/afii10088 16#0446
/afii10089 16#0447
/afii10090 16#0448
/afii10091 16#0449
/afii10092 16#044a
/afii10093 16#044b
/afii10094 16#044c
/afii10095 16#044d
/afii10096 16#044e
/afii10097 16#044f
/afii10098 16#0491
/afii10099 16#0452
/afii10100 16#0453
/afii10101 16#0454
/afii10102 16#0455
/afii10103 16#0456
/afii10104 16#0457
/afii10105 16#0458
/afii10106 16#0459
/afii10107 16#045a
/afii10108 16#045b
/afii10109 16#045c
/afii10110 16#045e
/afii10145 16#040f
/afii10146 16#0462
/afii10147 16#0472
/afii10148 16#0474
/afii10192 16#f6c6
/afii10193 16#045f
/afii10194 16#0463
/afii10195 16#0473
/afii10196 16#0475
/afii10831 16#f6c7
/afii10832 16#f6c8
/afii10846 16#04d9
/afii299 16#200e
/afii300 16#200f
/afii301 16#200d
/afii57381 16#066a
/afii57388 16#060c
/afii57392 16#0660
/afii57393 16#0661
/afii57394 16#0662
/afii57395 16#0663
/afii57396 16#0664
/afii57397 16#0665
/afii57398 16#0666
/afii57399 16#0667
/afii57400 16#0668
/afii57401 16#0669
/afii57403 16#061b
/afii57407 16#061f
/afii57409 16#0621
/afii57410 16#0622
/afii57411 16#0623
/afii57412 16#0624
/afii57413 16#0625
/afii57414 16#0626
/afii57415 16#0627
/afii57416 16#0628
/afii57417 16#0629
/afii57418 16#062a
/afii57419 16#062b
/afii57420 16#062c
/afii57421 16#062d
/afii57422 16#062e
/afii57423 16#062f
/afii57424 16#0630
/afii57425 16#0631
/afii57426 16#0632
/afii57427 16#0633
/afii57428 16#0634
/afii57429 16#0635
/afii57430 16#0636
/afii57431 16#0637
/afii57432 16#0638
/afii57433 16#0639
/afii57434 16#063a
/afii57440 16#0640
/afii57441 16#0641
/afii57442 16#0642
/afii57443 16#0643
/afii57444 16#0644
/afii57445 16#0645
/afii57446 16#0646
/afii57448 16#0648
/afii57449 16#0649
/afii57450 16#064a
/afii57451 16#064b
/afii57452 16#064c
/afii57453 16#064d
/afii57454 16#064e
/afii57455 16#064f
/afii57456 16#0650
/afii57457 16#0651
/afii57458 16#0652
/afii57470 16#0647
/afii57505 16#06a4
/afii57506 16#067e
/afii57507 16#0686
/afii57508 16#0698
/afii57509 16#06af
/afii57511 16#0679
/afii57512 16#0688
/afii57513 16#0691
/afii57514 16#06ba
/afii57519 16#06d2
/afii57534 16#06d5
/afii57636 16#20aa
/afii57645 16#05be
/afii57658 16#05c3
/afii57664 16#05d0
/afii57665 16#05d1
/afii57666 16#05d2
/afii57667 16#05d3
/afii57668 16#05d4
/afii57669 16#05d5
/afii57670 16#05d6
/afii57671 16#05d7
/afii57672 16#05d8
/afii57673 16#05d9
/afii57674 16#05da
/afii57675 16#05db
/afii57676 16#05dc
/afii57677 16#05dd
/afii57678 16#05de
/afii57679 16#05df
/afii57680 16#05e0
/afii57681 16#05e1
/afii57682 16#05e2
/afii57683 16#05e3
/afii57684 16#05e4
/afii57685 16#05e5
/afii57686 16#05e6
/afii57687 16#05e7
/afii57688 16#05e8
/afii57689 16#05e9
/afii57690 16#05ea
/afii57694 16#fb2a
/afii57695 16#fb2b
/afii57700 16#fb4b
/afii57705 16#fb1f
/afii57716 16#05f0
/afii57717 16#05f1
/afii57718 16#05f2
/afii57723 16#fb35
/afii57793 16#05b4
/afii57794 16#05b5
/afii57795 16#05b6
/afii57796 16#05bb
/afii57797 16#05b8
/afii57798 16#05b7
/afii57799 16#05b0
/afii57800 16#05b2
/afii57801 16#05b1
/afii57802 16#05b3
/afii57803 16#05c2
/afii57804 16#05c1
/afii57806 16#05b9
/afii57807 16#05bc
/afii57839 16#05bd
/afii57841 16#05bf
/afii57842 16#05c0
/afii57929 16#02bc
/afii61248 16#2105
/afii61289 16#2113
/afii61352 16#2116
/afii61573 16#202c
/afii61574 16#202d
/afii61575 16#202e
/afii61664 16#200c
/afii63167 16#066d
/afii64937 16#02bd
/agrave 16#00e0
/agujarati 16#0a85
/agurmukhi 16#0a05
/ahiragana 16#3042
/ahookabove 16#1ea3
/aibengali 16#0990
/aibopomofo 16#311e
/aideva 16#0910
/aiecyrillic 16#04d5
/aigujarati 16#0a90
/aigurmukhi 16#0a10
/aimatragurmukhi 16#0a48
/ainarabic 16#0639
/ainfinalarabic 16#feca
/aininitialarabic 16#fecb
/ainmedialarabic 16#fecc
/ainvertedbreve 16#0203
/aivowelsignbengali 16#09c8
/aivowelsigndeva 16#0948
/aivowelsigngujarati 16#0ac8
/akatakana 16#30a2
/akatakanahalfwidth 16#ff71
/akorean 16#314f
/alef 16#05d0
/alefarabic 16#0627
/alefdageshhebrew 16#fb30
/aleffinalarabic 16#fe8e
/alefhamzaabovearabic 16#0623
/alefhamzaabovefinalarabic 16#fe84
/alefhamzabelowarabic 16#0625
/alefhamzabelowfinalarabic 16#fe88
/alefhebrew 16#05d0
/aleflamedhebrew 16#fb4f
/alefmaddaabovearabic 16#0622
/alefmaddaabovefinalarabic 16#fe82
/alefmaksuraarabic 16#0649
/alefmaksurafinalarabic 16#fef0
/alefmaksurainitialarabic 16#fef3
/alefmaksuramedialarabic 16#fef4
/alefpatahhebrew 16#fb2e
/alefqamatshebrew 16#fb2f
/aleph 16#2135
/allequal 16#224c
/alpha 16#03b1
/alphatonos 16#03ac
/amacron 16#0101
/amonospace 16#ff41
/ampersand 16#0026
/ampersandmonospace 16#ff06
/ampersandsmall 16#f726
/amsquare 16#33c2
/anbopomofo 16#3122
/angbopomofo 16#3124
/angkhankhuthai 16#0e5a
/angle 16#2220
/anglebracketleft 16#3008
/anglebracketleftvertical 16#fe3f
/anglebracketright 16#3009
/anglebracketrightvertical 16#fe40
/angleleft 16#2329
/angleright 16#232a
/angstrom 16#212b
/anoteleia 16#0387
/anudattadeva 16#0952
/anusvarabengali 16#0982
/anusvaradeva 16#0902
/anusvaragujarati 16#0a82
/aogonek 16#0105
/apaatosquare 16#3300
/aparen 16#249c
/apostrophearmenian 16#055a
/apostrophemod 16#02bc
/apple 16#f8ff
/approaches 16#2250
/approxequal 16#2248
/approxequalorimage 16#2252
/approximatelyequal 16#2245
/araeaekorean 16#318e
/araeakorean 16#318d
/arc 16#2312
/arighthalfring 16#1e9a
/aring 16#00e5
/aringacute 16#01fb
/aringbelow 16#1e01
/arrowboth 16#2194
/arrowdashdown 16#21e3
/arrowdashleft 16#21e0
/arrowdashright 16#21e2
/arrowdashup 16#21e1
/arrowdblboth 16#21d4
/arrowdbldown 16#21d3
/arrowdblleft 16#21d0
/arrowdblright 16#21d2
/arrowdblup 16#21d1
/arrowdown 16#2193
/arrowdownleft 16#2199
/arrowdownright 16#2198
/arrowdownwhite 16#21e9
/arrowheaddownmod 16#02c5
/arrowheadleftmod 16#02c2
/arrowheadrightmod 16#02c3
/arrowheadupmod 16#02c4
/arrowhorizex 16#f8e7
/arrowleft 16#2190
/arrowleftdbl 16#21d0
/arrowleftdblstroke 16#21cd
/arrowleftoverright 16#21c6
/arrowleftwhite 16#21e6
/arrowright 16#2192
/arrowrightdblstroke 16#21cf
/arrowrightheavy 16#279e
/arrowrightoverleft 16#21c4
/arrowrightwhite 16#21e8
/arrowtableft 16#21e4
/arrowtabright 16#21e5
/arrowup 16#2191
/arrowupdn 16#2195
/arrowupdnbse 16#21a8
/arrowupdownbase 16#21a8
/arrowupleft 16#2196
/arrowupleftofdown 16#21c5
/arrowupright 16#2197
/arrowupwhite 16#21e7
/arrowvertex 16#f8e6
/asciicircum 16#005e
/asciicircummonospace 16#ff3e
/asciitilde 16#007e
/asciitildemonospace 16#ff5e
/ascript 16#0251
/ascriptturned 16#0252
/asmallhiragana 16#3041
/asmallkatakana 16#30a1
/asmallkatakanahalfwidth 16#ff67
/asterisk 16#002a
/asteriskaltonearabic 16#066d
/asteriskarabic 16#066d
/asteriskmath 16#2217
/asteriskmonospace 16#ff0a
/asterisksmall 16#fe61
/asterism 16#2042
/asuperior 16#f6e9
/asymptoticallyequal 16#2243
/at 16#0040
/atilde 16#00e3
/atmonospace 16#ff20
/atsmall 16#fe6b
/aturned 16#0250
/aubengali 16#0994
/aubopomofo 16#3120
/audeva 16#0914
/augujarati 16#0a94
/augurmukhi 16#0a14
/aulengthmarkbengali 16#09d7
/aumatragurmukhi 16#0a4c
/auvowelsignbengali 16#09cc
/auvowelsigndeva 16#094c
/auvowelsigngujarati 16#0acc
/avagrahadeva 16#093d
/aybarmenian 16#0561
/ayin 16#05e2
/ayinaltonehebrew 16#fb20
/ayinhebrew 16#05e2
/b 16#0062
/babengali 16#09ac
/backslash 16#005c
/backslashmonospace 16#ff3c
/badeva 16#092c
/bagujarati 16#0aac
/bagurmukhi 16#0a2c
/bahiragana 16#3070
/bahtthai 16#0e3f
/bakatakana 16#30d0
/bar 16#007c
/barmonospace 16#ff5c
/bbopomofo 16#3105
/bcircle 16#24d1
/bdotaccent 16#1e03
/bdotbelow 16#1e05
/beamedsixteenthnotes 16#266c
/because 16#2235
/becyrillic 16#0431
/beharabic 16#0628
/behfinalarabic 16#fe90
/behinitialarabic 16#fe91
/behiragana 16#3079
/behmedialarabic 16#fe92
/behmeeminitialarabic 16#fc9f
/behmeemisolatedarabic 16#fc08
/behnoonfinalarabic 16#fc6d
/bekatakana 16#30d9
/benarmenian 16#0562
/bet 16#05d1
/beta 16#03b2
/betasymbolgreek 16#03d0
/betdagesh 16#fb31
/betdageshhebrew 16#fb31
/bethebrew 16#05d1
/betrafehebrew 16#fb4c
/bhabengali 16#09ad
/bhadeva 16#092d
/bhagujarati 16#0aad
/bhagurmukhi 16#0a2d
/bhook 16#0253
/bihiragana 16#3073
/bikatakana 16#30d3
/bilabialclick 16#0298
/bindigurmukhi 16#0a02
/birusquare 16#3331
/blackcircle 16#25cf
/blackdiamond 16#25c6
/blackdownpointingtriangle 16#25bc
/blackleftpointingpointer 16#25c4
/blackleftpointingtriangle 16#25c0
/blacklenticularbracketleft 16#3010
/blacklenticularbracketleftvertical 16#fe3b
/blacklenticularbracketright 16#3011
/blacklenticularbracketrightvertical 16#fe3c
/blacklowerlefttriangle 16#25e3
/blacklowerrighttriangle 16#25e2
/blackrectangle 16#25ac
/blackrightpointingpointer 16#25ba
/blackrightpointingtriangle 16#25b6
/blacksmallsquare 16#25aa
/blacksmilingface 16#263b
/blacksquare 16#25a0
/blackstar 16#2605
/blackupperlefttriangle 16#25e4
/blackupperrighttriangle 16#25e5
/blackuppointingsmalltriangle 16#25b4
/blackuppointingtriangle 16#25b2
/blank 16#2423
/blinebelow 16#1e07
/block 16#2588
/bmonospace 16#ff42
/bobaimaithai 16#0e1a
/bohiragana 16#307c
/bokatakana 16#30dc
/bparen 16#249d
/bqsquare 16#33c3
/braceex 16#f8f4
/braceleft 16#007b
/braceleftbt 16#f8f3
/braceleftmid 16#f8f2
/braceleftmonospace 16#ff5b
/braceleftsmall 16#fe5b
/bracelefttp 16#f8f1
/braceleftvertical 16#fe37
/braceright 16#007d
/bracerightbt 16#f8fe
/bracerightmid 16#f8fd
/bracerightmonospace 16#ff5d
/bracerightsmall 16#fe5c
/bracerighttp 16#f8fc
/bracerightvertical 16#fe38
/bracketleft 16#005b
/bracketleftbt 16#f8f0
/bracketleftex 16#f8ef
/bracketleftmonospace 16#ff3b
/bracketlefttp 16#f8ee
/bracketright 16#005d
/bracketrightbt 16#f8fb
/bracketrightex 16#f8fa
/bracketrightmonospace 16#ff3d
/bracketrighttp 16#f8f9
/breve 16#02d8
/brevebelowcmb 16#032e
/brevecmb 16#0306
/breveinvertedbelowcmb 16#032f
/breveinvertedcmb 16#0311
/breveinverteddoublecmb 16#0361
/bridgebelowcmb 16#032a
/bridgeinvertedbelowcmb 16#033a
/brokenbar 16#00a6
/bstroke 16#0180
/bsuperior 16#f6ea
/btopbar 16#0183
/buhiragana 16#3076
/bukatakana 16#30d6
/bullet 16#2022
/bulletinverse 16#25d8
/bulletoperator 16#2219
/bullseye 16#25ce
/c 16#0063
/caarmenian 16#056e
/cabengali 16#099a
/cacute 16#0107
/cadeva 16#091a
/cagujarati 16#0a9a
/cagurmukhi 16#0a1a
/calsquare 16#3388
/candrabindubengali 16#0981
/candrabinducmb 16#0310
/candrabindudeva 16#0901
/candrabindugujarati 16#0a81
/capslock 16#21ea
/careof 16#2105
/caron 16#02c7
/caronbelowcmb 16#032c
/caroncmb 16#030c
/carriagereturn 16#21b5
/cbopomofo 16#3118
/ccaron 16#010d
/ccedilla 16#00e7
/ccedillaacute 16#1e09
/ccircle 16#24d2
/ccircumflex 16#0109
/ccurl 16#0255
/cdot 16#010b
/cdotaccent 16#010b
/cdsquare 16#33c5
/cedilla 16#00b8
/cedillacmb 16#0327
/cent 16#00a2
/centigrade 16#2103
/centinferior 16#f6df
/centmonospace 16#ffe0
/centoldstyle 16#f7a2
/centsuperior 16#f6e0
/chaarmenian 16#0579
/chabengali 16#099b
/chadeva 16#091b
/chagujarati 16#0a9b
/chagurmukhi 16#0a1b
/chbopomofo 16#3114
/cheabkhasiancyrillic 16#04bd
/checkmark 16#2713
/checyrillic 16#0447
/chedescenderabkhasiancyrillic 16#04bf
/chedescendercyrillic 16#04b7
/chedieresiscyrillic 16#04f5
/cheharmenian 16#0573
/chekhakassiancyrillic 16#04cc
/cheverticalstrokecyrillic 16#04b9
/chi 16#03c7
/chieuchacirclekorean 16#3277
/chieuchaparenkorean 16#3217
/chieuchcirclekorean 16#3269
/chieuchkorean 16#314a
/chieuchparenkorean 16#3209
/chochangthai 16#0e0a
/chochanthai 16#0e08
/chochingthai 16#0e09
/chochoethai 16#0e0c
/chook 16#0188
/cieucacirclekorean 16#3276
/cieucaparenkorean 16#3216
/cieuccirclekorean 16#3268
/cieuckorean 16#3148
/cieucparenkorean 16#3208
/cieucuparenkorean 16#321c
/circle 16#25cb
/circlemultiply 16#2297
/circleot 16#2299
/circleplus 16#2295
/circlepostalmark 16#3036
/circlewithlefthalfblack 16#25d0
/circlewithrighthalfblack 16#25d1
/circumflex 16#02c6
/circumflexbelowcmb 16#032d
/circumflexcmb 16#0302
/clear 16#2327
/clickalveolar 16#01c2
/clickdental 16#01c0
/clicklateral 16#01c1
/clickretroflex 16#01c3
/club 16#2663
/clubsuitblack 16#2663
/clubsuitwhite 16#2667
/cmcubedsquare 16#33a4
/cmonospace 16#ff43
/cmsquaredsquare 16#33a0
/coarmenian 16#0581
/colon 16#003a
/colonmonetary 16#20a1
/colonmonospace 16#ff1a
/colonsign 16#20a1
/colonsmall 16#fe55
/colontriangularhalfmod 16#02d1
/colontriangularmod 16#02d0
/comma 16#002c
/commaabovecmb 16#0313
/commaaboverightcmb 16#0315
/commaaccent 16#f6c3
/commaarabic 16#060c
/commaarmenian 16#055d
/commainferior 16#f6e1
/commamonospace 16#ff0c
/commareversedabovecmb 16#0314
/commareversedmod 16#02bd
/commasmall 16#fe50
/commasuperior 16#f6e2
/commaturnedabovecmb 16#0312
/commaturnedmod 16#02bb
/compass 16#263c
/congruent 16#2245
/contourintegral 16#222e
/control 16#2303
/controlACK 16#0006
/controlBEL 16#0007
/controlBS 16#0008
/controlCAN 16#0018
/controlCR 16#000d
/controlDC1 16#0011
/controlDC2 16#0012
/controlDC3 16#0013
/controlDC4 16#0014
/controlDEL 16#007f
/controlDLE 16#0010
/controlEM 16#0019
/controlENQ 16#0005
/controlEOT 16#0004
/controlESC 16#001b
/controlETB 16#0017
/controlETX 16#0003
/controlFF 16#000c
/controlFS 16#001c
/controlGS 16#001d
/controlHT 16#0009
/controlLF 16#000a
/controlNAK 16#0015
/controlRS 16#001e
/controlSI 16#000f
/controlSO 16#000e
/controlSOT 16#0002
/controlSTX 16#0001
/controlSUB 16#001a
/controlSYN 16#0016
/controlUS 16#001f
/controlVT 16#000b
/copyright 16#00a9
/copyrightsans 16#f8e9
/copyrightserif 16#f6d9
/cornerbracketleft 16#300c
/cornerbracketlefthalfwidth 16#ff62
/cornerbracketleftvertical 16#fe41
/cornerbracketright 16#300d
/cornerbracketrighthalfwidth 16#ff63
/cornerbracketrightvertical 16#fe42
/corporationsquare 16#337f
/cosquare 16#33c7
/coverkgsquare 16#33c6
/cparen 16#249e
/cruzeiro 16#20a2
/cstretched 16#0297
/curlyand 16#22cf
/curlyor 16#22ce
/currency 16#00a4
/cyrBreve 16#f6d1
/cyrFlex 16#f6d2
/cyrbreve 16#f6d4
/cyrflex 16#f6d5
/d 16#0064
/daarmenian 16#0564
/dabengali 16#09a6
/dadarabic 16#0636
/dadeva 16#0926
/dadfinalarabic 16#febe
/dadinitialarabic 16#febf
/dadmedialarabic 16#fec0
/dagesh 16#05bc
/dageshhebrew 16#05bc
/dagger 16#2020
/daggerdbl 16#2021
/dagujarati 16#0aa6
/dagurmukhi 16#0a26
/dahiragana 16#3060
/dakatakana 16#30c0
/dalarabic 16#062f
/dalet 16#05d3
/daletdagesh 16#fb33
/daletdageshhebrew 16#fb33
/dalethebrew 16#05d3
/dalfinalarabic 16#feaa
/dammaarabic 16#064f
/dammalowarabic 16#064f
/dammatanaltonearabic 16#064c
/dammatanarabic 16#064c
/danda 16#0964
/dargahebrew 16#05a7
/dargalefthebrew 16#05a7
/dasiapneumatacyrilliccmb 16#0485
/dblGrave 16#f6d3
/dblanglebracketleft 16#300a
/dblanglebracketleftvertical 16#fe3d
/dblanglebracketright 16#300b
/dblanglebracketrightvertical 16#fe3e
/dblarchinvertedbelowcmb 16#032b
/dblarrowleft 16#21d4
/dblarrowright 16#21d2
/dbldanda 16#0965
/dblgrave 16#f6d6
/dblgravecmb 16#030f
/dblintegral 16#222c
/dbllowline 16#2017
/dbllowlinecmb 16#0333
/dbloverlinecmb 16#033f
/dblprimemod 16#02ba
/dblverticalbar 16#2016
/dblverticallineabovecmb 16#030e
/dbopomofo 16#3109
/dbsquare 16#33c8
/dcaron 16#010f
/dcedilla 16#1e11
/dcircle 16#24d3
/dcircumflexbelow 16#1e13
/dcroat 16#0111
/ddabengali 16#09a1
/ddadeva 16#0921
/ddagujarati 16#0aa1
/ddagurmukhi 16#0a21
/ddalarabic 16#0688
/ddalfinalarabic 16#fb89
/dddhadeva 16#095c
/ddhabengali 16#09a2
/ddhadeva 16#0922
/ddhagujarati 16#0aa2
/ddhagurmukhi 16#0a22
/ddotaccent 16#1e0b
/ddotbelow 16#1e0d
/decimalseparatorarabic 16#066b
/decimalseparatorpersian 16#066b
/decyrillic 16#0434
/degree 16#00b0
/dehihebrew 16#05ad
/dehiragana 16#3067
/deicoptic 16#03ef
/dekatakana 16#30c7
/deleteleft 16#232b
/deleteright 16#2326
/delta 16#03b4
/deltaturned 16#018d
/denominatorminusonenumeratorbengali 16#09f8
/dezh 16#02a4
/dhabengali 16#09a7
/dhadeva 16#0927
/dhagujarati 16#0aa7
/dhagurmukhi 16#0a27
/dhook 16#0257
/dialytikatonos 16#0385
/dialytikatonoscmb 16#0344
/diamond 16#2666
/diamondsuitwhite 16#2662
/dieresis 16#00a8
/dieresisacute 16#f6d7
/dieresisbelowcmb 16#0324
/dieresiscmb 16#0308
/dieresisgrave 16#f6d8
/dieresistonos 16#0385
/dihiragana 16#3062
/dikatakana 16#30c2
/dittomark 16#3003
/divide 16#00f7
/divides 16#2223
/divisionslash 16#2215
/djecyrillic 16#0452
/dkshade 16#2593
/dlinebelow 16#1e0f
/dlsquare 16#3397
/dmacron 16#0111
/dmonospace 16#ff44
/dnblock 16#2584
/dochadathai 16#0e0e
/dodekthai 16#0e14
/dohiragana 16#3069
/dokatakana 16#30c9
/dollar 16#0024
/dollarinferior 16#f6e3
/dollarmonospace 16#ff04
/dollaroldstyle 16#f724
/dollarsmall 16#fe69
/dollarsuperior 16#f6e4
/dong 16#20ab
/dorusquare 16#3326
/dotaccent 16#02d9
/dotaccentcmb 16#0307
/dotbelowcmb 16#0323
/dotbelowcomb 16#0323
/dotkatakana 16#30fb
/dotlessi 16#0131
/dotlessj 16#f6be
/dotlessjstrokehook 16#0284
/dotmath 16#22c5
/dottedcircle 16#25cc
/doubleyodpatah 16#fb1f
/doubleyodpatahhebrew 16#fb1f
/downtackbelowcmb 16#031e
/downtackmod 16#02d5
/dparen 16#249f
/dsuperior 16#f6eb
/dtail 16#0256
/dtopbar 16#018c
/duhiragana 16#3065
/dukatakana 16#30c5
/dz 16#01f3
/dzaltone 16#02a3
/dzcaron 16#01c6
/dzcurl 16#02a5
/dzeabkhasiancyrillic 16#04e1
/dzecyrillic 16#0455
/dzhecyrillic 16#045f
/e 16#0065
/eacute 16#00e9
/earth 16#2641
/ebengali 16#098f
/ebopomofo 16#311c
/ebreve 16#0115
/ecandradeva 16#090d
/ecandragujarati 16#0a8d
/ecandravowelsigndeva 16#0945
/ecandravowelsigngujarati 16#0ac5
/ecaron 16#011b
/ecedillabreve 16#1e1d
/echarmenian 16#0565
/echyiwnarmenian 16#0587
/ecircle 16#24d4
/ecircumflex 16#00ea
/ecircumflexacute 16#1ebf
/ecircumflexbelow 16#1e19
/ecircumflexdotbelow 16#1ec7
/ecircumflexgrave 16#1ec1
/ecircumflexhookabove 16#1ec3
/ecircumflextilde 16#1ec5
/ecyrillic 16#0454
/edblgrave 16#0205
/edeva 16#090f
/edieresis 16#00eb
/edot 16#0117
/edotaccent 16#0117
/edotbelow 16#1eb9
/eegurmukhi 16#0a0f
/eematragurmukhi 16#0a47
/efcyrillic 16#0444
/egrave 16#00e8
/egujarati 16#0a8f
/eharmenian 16#0567
/ehbopomofo 16#311d
/ehiragana 16#3048
/ehookabove 16#1ebb
/eibopomofo 16#311f
/eight 16#0038
/eightarabic 16#0668
/eightbengali 16#09ee
/eightcircle 16#2467
/eightcircleinversesansserif 16#2791
/eightdeva 16#096e
/eighteencircle 16#2471
/eighteenparen 16#2485
/eighteenperiod 16#2499
/eightgujarati 16#0aee
/eightgurmukhi 16#0a6e
/eighthackarabic 16#0668
/eighthangzhou 16#3028
/eighthnotebeamed 16#266b
/eightideographicparen 16#3227
/eightinferior 16#2088
/eightmonospace 16#ff18
/eightoldstyle 16#f738
/eightparen 16#247b
/eightperiod 16#248f
/eightpersian 16#06f8
/eightroman 16#2177
/eightsuperior 16#2078
/eightthai 16#0e58
/einvertedbreve 16#0207
/eiotifiedcyrillic 16#0465
/ekatakana 16#30a8
/ekatakanahalfwidth 16#ff74
/ekonkargurmukhi 16#0a74
/ekorean 16#3154
/elcyrillic 16#043b
/element 16#2208
/elevencircle 16#246a
/elevenparen 16#247e
/elevenperiod 16#2492
/elevenroman 16#217a
/ellipsis 16#2026
/ellipsisvertical 16#22ee
/emacron 16#0113
/emacronacute 16#1e17
/emacrongrave 16#1e15
/emcyrillic 16#043c
/emdash 16#2014
/emdashvertical 16#fe31
/emonospace 16#ff45
/emphasismarkarmenian 16#055b
/emptyset 16#2205
/enbopomofo 16#3123
/encyrillic 16#043d
/endash 16#2013
/endashvertical 16#fe32
/endescendercyrillic 16#04a3
/eng 16#014b
/engbopomofo 16#3125
/enghecyrillic 16#04a5
/enhookcyrillic 16#04c8
/enspace 16#2002
/eogonek 16#0119
/eokorean 16#3153
/eopen 16#025b
/eopenclosed 16#029a
/eopenreversed 16#025c
/eopenreversedclosed 16#025e
/eopenreversedhook 16#025d
/eparen 16#24a0
/epsilon 16#03b5
/epsilontonos 16#03ad
/equal 16#003d
/equalmonospace 16#ff1d
/equalsmall 16#fe66
/equalsuperior 16#207c
/equivalence 16#2261
/erbopomofo 16#3126
/ercyrillic 16#0440
/ereversed 16#0258
/ereversedcyrillic 16#044d
/escyrillic 16#0441
/esdescendercyrillic 16#04ab
/esh 16#0283
/eshcurl 16#0286
/eshortdeva 16#090e
/eshortvowelsigndeva 16#0946
/eshreversedloop 16#01aa
/eshsquatreversed 16#0285
/esmallhiragana 16#3047
/esmallkatakana 16#30a7
/esmallkatakanahalfwidth 16#ff6a
/estimated 16#212e
/esuperior 16#f6ec
/eta 16#03b7
/etarmenian 16#0568
/etatonos 16#03ae
/eth 16#00f0
/etilde 16#1ebd
/etildebelow 16#1e1b
/etnahtafoukhhebrew 16#0591
/etnahtafoukhlefthebrew 16#0591
/etnahtahebrew 16#0591
/etnahtalefthebrew 16#0591
/eturned 16#01dd
/eukorean 16#3161
/euro 16#20ac
/evowelsignbengali 16#09c7
/evowelsigndeva 16#0947
/evowelsigngujarati 16#0ac7
/exclam 16#0021
/exclamarmenian 16#055c
/exclamdbl 16#203c
/exclamdown 16#00a1
/exclamdownsmall 16#f7a1
/exclammonospace 16#ff01
/exclamsmall 16#f721
/existential 16#2203
/ezh 16#0292
/ezhcaron 16#01ef
/ezhcurl 16#0293
/ezhreversed 16#01b9
/ezhtail 16#01ba
/f 16#0066
/fadeva 16#095e
/fagurmukhi 16#0a5e
/fahrenheit 16#2109
/fathaarabic 16#064e
/fathalowarabic 16#064e
/fathatanarabic 16#064b
/fbopomofo 16#3108
/fcircle 16#24d5
/fdotaccent 16#1e1f
/feharabic 16#0641
/feharmenian 16#0586
/fehfinalarabic 16#fed2
/fehinitialarabic 16#fed3
/fehmedialarabic 16#fed4
/feicoptic 16#03e5
/female 16#2640
/ff 16#fb00
/ffi 16#fb03
/ffl 16#fb04
/fi 16#fb01
/fifteencircle 16#246e
/fifteenparen 16#2482
/fifteenperiod 16#2496
/figuredash 16#2012
/filledbox 16#25a0
/filledrect 16#25ac
/finalkaf 16#05da
/finalkafdagesh 16#fb3a
/finalkafdageshhebrew 16#fb3a
/finalkafhebrew 16#05da
/finalmem 16#05dd
/finalmemhebrew 16#05dd
/finalnun 16#05df
/finalnunhebrew 16#05df
/finalpe 16#05e3
/finalpehebrew 16#05e3
/finaltsadi 16#05e5
/finaltsadihebrew 16#05e5
/firsttonechinese 16#02c9
/fisheye 16#25c9
/fitacyrillic 16#0473
/five 16#0035
/fivearabic 16#0665
/fivebengali 16#09eb
/fivecircle 16#2464
/fivecircleinversesansserif 16#278e
/fivedeva 16#096b
/fiveeighths 16#215d
/fivegujarati 16#0aeb
/fivegurmukhi 16#0a6b
/fivehackarabic 16#0665
/fivehangzhou 16#3025
/fiveideographicparen 16#3224
/fiveinferior 16#2085
/fivemonospace 16#ff15
/fiveoldstyle 16#f735
/fiveparen 16#2478
/fiveperiod 16#248c
/fivepersian 16#06f5
/fiveroman 16#2174
/fivesuperior 16#2075
/fivethai 16#0e55
/fl 16#fb02
/florin 16#0192
/fmonospace 16#ff46
/fmsquare 16#3399
/fofanthai 16#0e1f
/fofathai 16#0e1d
/fongmanthai 16#0e4f
/forall 16#2200
/four 16#0034
/fourarabic 16#0664
/fourbengali 16#09ea
/fourcircle 16#2463
/fourcircleinversesansserif 16#278d
/fourdeva 16#096a
/fourgujarati 16#0aea
/fourgurmukhi 16#0a6a
/fourhackarabic 16#0664
/fourhangzhou 16#3024
/fourideographicparen 16#3223
/fourinferior 16#2084
/fourmonospace 16#ff14
/fournumeratorbengali 16#09f7
/fouroldstyle 16#f734
/fourparen 16#2477
/fourperiod 16#248b
/fourpersian 16#06f4
/fourroman 16#2173
/foursuperior 16#2074
/fourteencircle 16#246d
/fourteenparen 16#2481
/fourteenperiod 16#2495
/fourthai 16#0e54
/fourthtonechinese 16#02cb
/fparen 16#24a1
/fraction 16#2044
/franc 16#20a3
/g 16#0067
/gabengali 16#0997
/gacute 16#01f5
/gadeva 16#0917
/gafarabic 16#06af
/gaffinalarabic 16#fb93
/gafinitialarabic 16#fb94
/gafmedialarabic 16#fb95
/gagujarati 16#0a97
/gagurmukhi 16#0a17
/gahiragana 16#304c
/gakatakana 16#30ac
/gamma 16#03b3
/gammalatinsmall 16#0263
/gammasuperior 16#02e0
/gangiacoptic 16#03eb
/gbopomofo 16#310d
/gbreve 16#011f
/gcaron 16#01e7
/gcedilla 16#0123
/gcircle 16#24d6
/gcircumflex 16#011d
/gcommaaccent 16#0123
/gdot 16#0121
/gdotaccent 16#0121
/gecyrillic 16#0433
/gehiragana 16#3052
/gekatakana 16#30b2
/geometricallyequal 16#2251
/gereshaccenthebrew 16#059c
/gereshhebrew 16#05f3
/gereshmuqdamhebrew 16#059d
/germandbls 16#00df
/gershayimaccenthebrew 16#059e
/gershayimhebrew 16#05f4
/getamark 16#3013
/ghabengali 16#0998
/ghadarmenian 16#0572
/ghadeva 16#0918
/ghagujarati 16#0a98
/ghagurmukhi 16#0a18
/ghainarabic 16#063a
/ghainfinalarabic 16#fece
/ghaininitialarabic 16#fecf
/ghainmedialarabic 16#fed0
/ghemiddlehookcyrillic 16#0495
/ghestrokecyrillic 16#0493
/gheupturncyrillic 16#0491
/ghhadeva 16#095a
/ghhagurmukhi 16#0a5a
/ghook 16#0260
/ghzsquare 16#3393
/gihiragana 16#304e
/gikatakana 16#30ae
/gimarmenian 16#0563
/gimel 16#05d2
/gimeldagesh 16#fb32
/gimeldageshhebrew 16#fb32
/gimelhebrew 16#05d2
/gjecyrillic 16#0453
/glottalinvertedstroke 16#01be
/glottalstop 16#0294
/glottalstopinverted 16#0296
/glottalstopmod 16#02c0
/glottalstopreversed 16#0295
/glottalstopreversedmod 16#02c1
/glottalstopreversedsuperior 16#02e4
/glottalstopstroke 16#02a1
/glottalstopstrokereversed 16#02a2
/gmacron 16#1e21
/gmonospace 16#ff47
/gohiragana 16#3054
/gokatakana 16#30b4
/gparen 16#24a2
/gpasquare 16#33ac
/gradient 16#2207
/grave 16#0060
/gravebelowcmb 16#0316
/gravecmb 16#0300
/gravecomb 16#0300
/gravedeva 16#0953
/gravelowmod 16#02ce
/gravemonospace 16#ff40
/gravetonecmb 16#0340
/greater 16#003e
/greaterequal 16#2265
/greaterequalorless 16#22db
/greatermonospace 16#ff1e
/greaterorequivalent 16#2273
/greaterorless 16#2277
/greateroverequal 16#2267
/greatersmall 16#fe65
/gscript 16#0261
/gstroke 16#01e5
/guhiragana 16#3050
/guillemotleft 16#00ab
/guillemotright 16#00bb
/guilsinglleft 16#2039
/guilsinglright 16#203a
/gukatakana 16#30b0
/guramusquare 16#3318
/gysquare 16#33c9
/h 16#0068
/haabkhasiancyrillic 16#04a9
/haaltonearabic 16#06c1
/habengali 16#09b9
/hadescendercyrillic 16#04b3
/hadeva 16#0939
/hagujarati 16#0ab9
/hagurmukhi 16#0a39
/haharabic 16#062d
/hahfinalarabic 16#fea2
/hahinitialarabic 16#fea3
/hahiragana 16#306f
/hahmedialarabic 16#fea4
/haitusquare 16#332a
/hakatakana 16#30cf
/hakatakanahalfwidth 16#ff8a
/halantgurmukhi 16#0a4d
/hamzaarabic 16#0621
/hamzalowarabic 16#0621
/hangulfiller 16#3164
/hardsigncyrillic 16#044a
/harpoonleftbarbup 16#21bc
/harpoonrightbarbup 16#21c0
/hasquare 16#33ca
/hatafpatah 16#05b2
/hatafpatah16 16#05b2
/hatafpatah23 16#05b2
/hatafpatah2f 16#05b2
/hatafpatahhebrew 16#05b2
/hatafpatahnarrowhebrew 16#05b2
/hatafpatahquarterhebrew 16#05b2
/hatafpatahwidehebrew 16#05b2
/hatafqamats 16#05b3
/hatafqamats1b 16#05b3
/hatafqamats28 16#05b3
/hatafqamats34 16#05b3
/hatafqamatshebrew 16#05b3
/hatafqamatsnarrowhebrew 16#05b3
/hatafqamatsquarterhebrew 16#05b3
/hatafqamatswidehebrew 16#05b3
/hatafsegol 16#05b1
/hatafsegol17 16#05b1
/hatafsegol24 16#05b1
/hatafsegol30 16#05b1
/hatafsegolhebrew 16#05b1
/hatafsegolnarrowhebrew 16#05b1
/hatafsegolquarterhebrew 16#05b1
/hatafsegolwidehebrew 16#05b1
/hbar 16#0127
/hbopomofo 16#310f
/hbrevebelow 16#1e2b
/hcedilla 16#1e29
/hcircle 16#24d7
/hcircumflex 16#0125
/hdieresis 16#1e27
/hdotaccent 16#1e23
/hdotbelow 16#1e25
/he 16#05d4
/heart 16#2665
/heartsuitblack 16#2665
/heartsuitwhite 16#2661
/hedagesh 16#fb34
/hedageshhebrew 16#fb34
/hehaltonearabic 16#06c1
/heharabic 16#0647
/hehebrew 16#05d4
/hehfinalaltonearabic 16#fba7
/hehfinalalttwoarabic 16#feea
/hehfinalarabic 16#feea
/hehhamzaabovefinalarabic 16#fba5
/hehhamzaaboveisolatedarabic 16#fba4
/hehinitialaltonearabic 16#fba8
/hehinitialarabic 16#feeb
/hehiragana 16#3078
/hehmedialaltonearabic 16#fba9
/hehmedialarabic 16#feec
/heiseierasquare 16#337b
/hekatakana 16#30d8
/hekatakanahalfwidth 16#ff8d
/hekutaarusquare 16#3336
/henghook 16#0267
/herutusquare 16#3339
/het 16#05d7
/hethebrew 16#05d7
/hhook 16#0266
/hhooksuperior 16#02b1
/hieuhacirclekorean 16#327b
/hieuhaparenkorean 16#321b
/hieuhcirclekorean 16#326d
/hieuhkorean 16#314e
/hieuhparenkorean 16#320d
/hihiragana 16#3072
/hikatakana 16#30d2
/hikatakanahalfwidth 16#ff8b
/hiriq 16#05b4
/hiriq14 16#05b4
/hiriq21 16#05b4
/hiriq2d 16#05b4
/hiriqhebrew 16#05b4
/hiriqnarrowhebrew 16#05b4
/hiriqquarterhebrew 16#05b4
/hiriqwidehebrew 16#05b4
/hlinebelow 16#1e96
/hmonospace 16#ff48
/hoarmenian 16#0570
/hohipthai 16#0e2b
/hohiragana 16#307b
/hokatakana 16#30db
/hokatakanahalfwidth 16#ff8e
/holam 16#05b9
/holam19 16#05b9
/holam26 16#05b9
/holam32 16#05b9
/holamhebrew 16#05b9
/holamnarrowhebrew 16#05b9
/holamquarterhebrew 16#05b9
/holamwidehebrew 16#05b9
/honokhukthai 16#0e2e
/hookabovecomb 16#0309
/hookcmb 16#0309
/hookpalatalizedbelowcmb 16#0321
/hookretroflexbelowcmb 16#0322
/hoonsquare 16#3342
/horicoptic 16#03e9
/horizontalbar 16#2015
/horncmb 16#031b
/hotsprings 16#2668
/house 16#2302
/hparen 16#24a3
/hsuperior 16#02b0
/hturned 16#0265
/huhiragana 16#3075
/huiitosquare 16#3333
/hukatakana 16#30d5
/hukatakanahalfwidth 16#ff8c
/hungarumlaut 16#02dd
/hungarumlautcmb 16#030b
/hv 16#0195
/hyphen 16#002d
/hypheninferior 16#f6e5
/hyphenmonospace 16#ff0d
/hyphensmall 16#fe63
/hyphensuperior 16#f6e6
/hyphentwo 16#2010
/i 16#0069
/iacute 16#00ed
/iacyrillic 16#044f
/ibengali 16#0987
/ibopomofo 16#3127
/ibreve 16#012d
/icaron 16#01d0
/icircle 16#24d8
/icircumflex 16#00ee
/icyrillic 16#0456
/idblgrave 16#0209
/ideographearthcircle 16#328f
/ideographfirecircle 16#328b
/ideographicallianceparen 16#323f
/ideographiccallparen 16#323a
/ideographiccentrecircle 16#32a5
/ideographicclose 16#3006
/ideographiccomma 16#3001
/ideographiccommaleft 16#ff64
/ideographiccongratulationparen 16#3237
/ideographiccorrectcircle 16#32a3
/ideographicearthparen 16#322f
/ideographicenterpriseparen 16#323d
/ideographicexcellentcircle 16#329d
/ideographicfestivalparen 16#3240
/ideographicfinancialcircle 16#3296
/ideographicfinancialparen 16#3236
/ideographicfireparen 16#322b
/ideographichaveparen 16#3232
/ideographichighcircle 16#32a4
/ideographiciterationmark 16#3005
/ideographiclaborcircle 16#3298
/ideographiclaborparen 16#3238
/ideographicleftcircle 16#32a7
/ideographiclowcircle 16#32a6
/ideographicmedicinecircle 16#32a9
/ideographicmetalparen 16#322e
/ideographicmoonparen 16#322a
/ideographicnameparen 16#3234
/ideographicperiod 16#3002
/ideographicprintcircle 16#329e
/ideographicreachparen 16#3243
/ideographicrepresentparen 16#3239
/ideographicresourceparen 16#323e
/ideographicrightcircle 16#32a8
/ideographicsecretcircle 16#3299
/ideographicselfparen 16#3242
/ideographicsocietyparen 16#3233
/ideographicspace 16#3000
/ideographicspecialparen 16#3235
/ideographicstockparen 16#3231
/ideographicstudyparen 16#323b
/ideographicsunparen 16#3230
/ideographicsuperviseparen 16#323c
/ideographicwaterparen 16#322c
/ideographicwoodparen 16#322d
/ideographiczero 16#3007
/ideographmetalcircle 16#328e
/ideographmooncircle 16#328a
/ideographnamecircle 16#3294
/ideographsuncircle 16#3290
/ideographwatercircle 16#328c
/ideographwoodcircle 16#328d
/ideva 16#0907
/idieresis 16#00ef
/idieresisacute 16#1e2f
/idieresiscyrillic 16#04e5
/idotbelow 16#1ecb
/iebrevecyrillic 16#04d7
/iecyrillic 16#0435
/ieungacirclekorean 16#3275
/ieungaparenkorean 16#3215
/ieungcirclekorean 16#3267
/ieungkorean 16#3147
/ieungparenkorean 16#3207
/igrave 16#00ec
/igujarati 16#0a87
/igurmukhi 16#0a07
/ihiragana 16#3044
/ihookabove 16#1ec9
/iibengali 16#0988
/iicyrillic 16#0438
/iideva 16#0908
/iigujarati 16#0a88
/iigurmukhi 16#0a08
/iimatragurmukhi 16#0a40
/iinvertedbreve 16#020b
/iishortcyrillic 16#0439
/iivowelsignbengali 16#09c0
/iivowelsigndeva 16#0940
/iivowelsigngujarati 16#0ac0
/ij 16#0133
/ikatakana 16#30a4
/ikatakanahalfwidth 16#ff72
/ikorean 16#3163
/ilde 16#02dc
/iluyhebrew 16#05ac
/imacron 16#012b
/imacroncyrillic 16#04e3
/imageorapproximatelyequal 16#2253
/imatragurmukhi 16#0a3f
/imonospace 16#ff49
/increment 16#2206
/infinity 16#221e
/iniarmenian 16#056b
/integral 16#222b
/integralbottom 16#2321
/integralbt 16#2321
/integralex 16#f8f5
/integraltop 16#2320
/integraltp 16#2320
/intersection 16#2229
/intisquare 16#3305
/invbullet 16#25d8
/invcircle 16#25d9
/invsmileface 16#263b
/iocyrillic 16#0451
/iogonek 16#012f
/iota 16#03b9
/iotadieresis 16#03ca
/iotadieresistonos 16#0390
/iotalatin 16#0269
/iotatonos 16#03af
/iparen 16#24a4
/irigurmukhi 16#0a72
/ismallhiragana 16#3043
/ismallkatakana 16#30a3
/ismallkatakanahalfwidth 16#ff68
/issharbengali 16#09fa
/istroke 16#0268
/isuperior 16#f6ed
/iterationhiragana 16#309d
/iterationkatakana 16#30fd
/itilde 16#0129
/itildebelow 16#1e2d
/iubopomofo 16#3129
/iucyrillic 16#044e
/ivowelsignbengali 16#09bf
/ivowelsigndeva 16#093f
/ivowelsigngujarati 16#0abf
/izhitsacyrillic 16#0475
/izhitsadblgravecyrillic 16#0477
/j 16#006a
/jaarmenian 16#0571
/jabengali 16#099c
/jadeva 16#091c
/jagujarati 16#0a9c
/jagurmukhi 16#0a1c
/jbopomofo 16#3110
/jcaron 16#01f0
/jcircle 16#24d9
/jcircumflex 16#0135
/jcrossedtail 16#029d
/jdotlessstroke 16#025f
/jecyrillic 16#0458
/jeemarabic 16#062c
/jeemfinalarabic 16#fe9e
/jeeminitialarabic 16#fe9f
/jeemmedialarabic 16#fea0
/jeharabic 16#0698
/jehfinalarabic 16#fb8b
/jhabengali 16#099d
/jhadeva 16#091d
/jhagujarati 16#0a9d
/jhagurmukhi 16#0a1d
/jheharmenian 16#057b
/jis 16#3004
/jmonospace 16#ff4a
/jparen 16#24a5
/jsuperior 16#02b2
/k 16#006b
/kabashkircyrillic 16#04a1
/kabengali 16#0995
/kacute 16#1e31
/kacyrillic 16#043a
/kadescendercyrillic 16#049b
/kadeva 16#0915
/kaf 16#05db
/kafarabic 16#0643
/kafdagesh 16#fb3b
/kafdageshhebrew 16#fb3b
/kaffinalarabic 16#feda
/kafhebrew 16#05db
/kafinitialarabic 16#fedb
/kafmedialarabic 16#fedc
/kafrafehebrew 16#fb4d
/kagujarati 16#0a95
/kagurmukhi 16#0a15
/kahiragana 16#304b
/kahookcyrillic 16#04c4
/kakatakana 16#30ab
/kakatakanahalfwidth 16#ff76
/kappa 16#03ba
/kappasymbolgreek 16#03f0
/kapyeounmieumkorean 16#3171
/kapyeounphieuphkorean 16#3184
/kapyeounpieupkorean 16#3178
/kapyeounssangpieupkorean 16#3179
/karoriisquare 16#330d
/kashidaautoarabic 16#0640
/kashidaautonosidebearingarabic 16#0640
/kasmallkatakana 16#30f5
/kasquare 16#3384
/kasraarabic 16#0650
/kasratanarabic 16#064d
/kastrokecyrillic 16#049f
/katahiraprolongmarkhalfwidth 16#ff70
/kaverticalstrokecyrillic 16#049d
/kbopomofo 16#310e
/kcalsquare 16#3389
/kcaron 16#01e9
/kcedilla 16#0137
/kcircle 16#24da
/kcommaaccent 16#0137
/kdotbelow 16#1e33
/keharmenian 16#0584
/kehiragana 16#3051
/kekatakana 16#30b1
/kekatakanahalfwidth 16#ff79
/kenarmenian 16#056f
/kesmallkatakana 16#30f6
/kgreenlandic 16#0138
/khabengali 16#0996
/khacyrillic 16#0445
/khadeva 16#0916
/khagujarati 16#0a96
/khagurmukhi 16#0a16
/khaharabic 16#062e
/khahfinalarabic 16#fea6
/khahinitialarabic 16#fea7
/khahmedialarabic 16#fea8
/kheicoptic 16#03e7
/khhadeva 16#0959
/khhagurmukhi 16#0a59
/khieukhacirclekorean 16#3278
/khieukhaparenkorean 16#3218
/khieukhcirclekorean 16#326a
/khieukhkorean 16#314b
/khieukhparenkorean 16#320a
/khokhaithai 16#0e02
/khokhonthai 16#0e05
/khokhuatthai 16#0e03
/khokhwaithai 16#0e04
/khomutthai 16#0e5b
/khook 16#0199
/khorakhangthai 16#0e06
/khzsquare 16#3391
/kihiragana 16#304d
/kikatakana 16#30ad
/kikatakanahalfwidth 16#ff77
/kiroguramusquare 16#3315
/kiromeetorusquare 16#3316
/kirosquare 16#3314
/kiyeokacirclekorean 16#326e
/kiyeokaparenkorean 16#320e
/kiyeokcirclekorean 16#3260
/kiyeokkorean 16#3131
/kiyeokparenkorean 16#3200
/kiyeoksioskorean 16#3133
/kjecyrillic 16#045c
/klinebelow 16#1e35
/klsquare 16#3398
/kmcubedsquare 16#33a6
/kmonospace 16#ff4b
/kmsquaredsquare 16#33a2
/kohiragana 16#3053
/kohmsquare 16#33c0
/kokaithai 16#0e01
/kokatakana 16#30b3
/kokatakanahalfwidth 16#ff7a
/kooposquare 16#331e
/koppacyrillic 16#0481
/koreanstandardsymbol 16#327f
/koroniscmb 16#0343
/kparen 16#24a6
/kpasquare 16#33aa
/ksicyrillic 16#046f
/ktsquare 16#33cf
/kturned 16#029e
/kuhiragana 16#304f
/kukatakana 16#30af
/kukatakanahalfwidth 16#ff78
/kvsquare 16#33b8
/kwsquare 16#33be
/l 16#006c
/labengali 16#09b2
/lacute 16#013a
/ladeva 16#0932
/lagujarati 16#0ab2
/lagurmukhi 16#0a32
/lakkhangyaothai 16#0e45
/lamaleffinalarabic 16#fefc
/lamalefhamzaabovefinalarabic 16#fef8
/lamalefhamzaaboveisolatedarabic 16#fef7
/lamalefhamzabelowfinalarabic 16#fefa
/lamalefhamzabelowisolatedarabic 16#fef9
/lamalefisolatedarabic 16#fefb
/lamalefmaddaabovefinalarabic 16#fef6
/lamalefmaddaaboveisolatedarabic 16#fef5
/lamarabic 16#0644
/lambda 16#03bb
/lambdastroke 16#019b
/lamed 16#05dc
/lameddagesh 16#fb3c
/lameddageshhebrew 16#fb3c
/lamedhebrew 16#05dc
/lamfinalarabic 16#fede
/lamhahinitialarabic 16#fcca
/laminitialarabic 16#fedf
/lamjeeminitialarabic 16#fcc9
/lamkhahinitialarabic 16#fccb
/lamlamhehisolatedarabic 16#fdf2
/lammedialarabic 16#fee0
/lammeemhahinitialarabic 16#fd88
/lammeeminitialarabic 16#fccc
/largecircle 16#25ef
/lbar 16#019a
/lbelt 16#026c
/lbopomofo 16#310c
/lcaron 16#013e
/lcedilla 16#013c
/lcircle 16#24db
/lcircumflexbelow 16#1e3d
/lcommaaccent 16#013c
/ldot 16#0140
/ldotaccent 16#0140
/ldotbelow 16#1e37
/ldotbelowmacron 16#1e39
/leftangleabovecmb 16#031a
/lefttackbelowcmb 16#0318
/less 16#003c
/lessequal 16#2264
/lessequalorgreater 16#22da
/lessmonospace 16#ff1c
/lessorequivalent 16#2272
/lessorgreater 16#2276
/lessoverequal 16#2266
/lesssmall 16#fe64
/lezh 16#026e
/lfblock 16#258c
/lhookretroflex 16#026d
/lira 16#20a4
/liwnarmenian 16#056c
/lj 16#01c9
/ljecyrillic 16#0459
/ll 16#f6c0
/lladeva 16#0933
/llagujarati 16#0ab3
/llinebelow 16#1e3b
/llladeva 16#0934
/llvocalicbengali 16#09e1
/llvocalicdeva 16#0961
/llvocalicvowelsignbengali 16#09e3
/llvocalicvowelsigndeva 16#0963
/lmiddletilde 16#026b
/lmonospace 16#ff4c
/lmsquare 16#33d0
/lochulathai 16#0e2c
/logicaland 16#2227
/logicalnot 16#00ac
/logicalnotreversed 16#2310
/logicalor 16#2228
/lolingthai 16#0e25
/longs 16#017f
/lowlinecenterline 16#fe4e
/lowlinecmb 16#0332
/lowlinedashed 16#fe4d
/lozenge 16#25ca
/lparen 16#24a7
/lslash 16#0142
/lsquare 16#2113
/lsuperior 16#f6ee
/ltshade 16#2591
/luthai 16#0e26
/lvocalicbengali 16#098c
/lvocalicdeva 16#090c
/lvocalicvowelsignbengali 16#09e2
/lvocalicvowelsigndeva 16#0962
/lxsquare 16#33d3
/m 16#006d
/mabengali 16#09ae
/macron 16#00af
/macronbelowcmb 16#0331
/macroncmb 16#0304
/macronlowmod 16#02cd
/macronmonospace 16#ffe3
/macute 16#1e3f
/madeva 16#092e
/magujarati 16#0aae
/magurmukhi 16#0a2e
/mahapakhhebrew 16#05a4
/mahapakhlefthebrew 16#05a4
/mahiragana 16#307e
/maichattawalowleftthai 16#f895
/maichattawalowrightthai 16#f894
/maichattawathai 16#0e4b
/maichattawaupperleftthai 16#f893
/maieklowleftthai 16#f88c
/maieklowrightthai 16#f88b
/maiekthai 16#0e48
/maiekupperleftthai 16#f88a
/maihanakatleftthai 16#f884
/maihanakatthai 16#0e31
/maitaikhuleftthai 16#f889
/maitaikhuthai 16#0e47
/maitholowleftthai 16#f88f
/maitholowrightthai 16#f88e
/maithothai 16#0e49
/maithoupperleftthai 16#f88d
/maitrilowleftthai 16#f892
/maitrilowrightthai 16#f891
/maitrithai 16#0e4a
/maitriupperleftthai 16#f890
/maiyamokthai 16#0e46
/makatakana 16#30de
/makatakanahalfwidth 16#ff8f
/male 16#2642
/mansyonsquare 16#3347
/maqafhebrew 16#05be
/mars 16#2642
/masoracirclehebrew 16#05af
/masquare 16#3383
/mbopomofo 16#3107
/mbsquare 16#33d4
/mcircle 16#24dc
/mcubedsquare 16#33a5
/mdotaccent 16#1e41
/mdotbelow 16#1e43
/meemarabic 16#0645
/meemfinalarabic 16#fee2
/meeminitialarabic 16#fee3
/meemmedialarabic 16#fee4
/meemmeeminitialarabic 16#fcd1
/meemmeemisolatedarabic 16#fc48
/meetorusquare 16#334d
/mehiragana 16#3081
/meizierasquare 16#337e
/mekatakana 16#30e1
/mekatakanahalfwidth 16#ff92
/mem 16#05de
/memdagesh 16#fb3e
/memdageshhebrew 16#fb3e
/memhebrew 16#05de
/menarmenian 16#0574
/merkhahebrew 16#05a5
/merkhakefulahebrew 16#05a6
/merkhakefulalefthebrew 16#05a6
/merkhalefthebrew 16#05a5
/mhook 16#0271
/mhzsquare 16#3392
/middledotkatakanahalfwidth 16#ff65
/middot 16#00b7
/mieumacirclekorean 16#3272
/mieumaparenkorean 16#3212
/mieumcirclekorean 16#3264
/mieumkorean 16#3141
/mieumpansioskorean 16#3170
/mieumparenkorean 16#3204
/mieumpieupkorean 16#316e
/mieumsioskorean 16#316f
/mihiragana 16#307f
/mikatakana 16#30df
/mikatakanahalfwidth 16#ff90
/minus 16#2212
/minusbelowcmb 16#0320
/minuscircle 16#2296
/minusmod 16#02d7
/minusplus 16#2213
/minute 16#2032
/miribaarusquare 16#334a
/mirisquare 16#3349
/mlonglegturned 16#0270
/mlsquare 16#3396
/mmcubedsquare 16#33a3
/mmonospace 16#ff4d
/mmsquaredsquare 16#339f
/mohiragana 16#3082
/mohmsquare 16#33c1
/mokatakana 16#30e2
/mokatakanahalfwidth 16#ff93
/molsquare 16#33d6
/momathai 16#0e21
/moverssquare 16#33a7
/moverssquaredsquare 16#33a8
/mparen 16#24a8
/mpasquare 16#33ab
/mssquare 16#33b3
/msuperior 16#f6ef
/mturned 16#026f
/mu 16#00b5
/mu1 16#00b5
/muasquare 16#3382
/muchgreater 16#226b
/muchless 16#226a
/mufsquare 16#338c
/mugreek 16#03bc
/mugsquare 16#338d
/muhiragana 16#3080
/mukatakana 16#30e0
/mukatakanahalfwidth 16#ff91
/mulsquare 16#3395
/multiply 16#00d7
/mumsquare 16#339b
/munahhebrew 16#05a3
/munahlefthebrew 16#05a3
/musicalnote 16#266a
/musicalnotedbl 16#266b
/musicflatsign 16#266d
/musicsharpsign 16#266f
/mussquare 16#33b2
/muvsquare 16#33b6
/muwsquare 16#33bc
/mvmegasquare 16#33b9
/mvsquare 16#33b7
/mwmegasquare 16#33bf
/mwsquare 16#33bd
/n 16#006e
/nabengali 16#09a8
/nabla 16#2207
/nacute 16#0144
/nadeva 16#0928
/nagujarati 16#0aa8
/nagurmukhi 16#0a28
/nahiragana 16#306a
/nakatakana 16#30ca
/nakatakanahalfwidth 16#ff85
/napostrophe 16#0149
/nasquare 16#3381
/nbopomofo 16#310b
/nbspace 16#00a0
/ncaron 16#0148
/ncedilla 16#0146
/ncircle 16#24dd
/ncircumflexbelow 16#1e4b
/ncommaaccent 16#0146
/ndotaccent 16#1e45
/ndotbelow 16#1e47
/nehiragana 16#306d
/nekatakana 16#30cd
/nekatakanahalfwidth 16#ff88
/newsheqelsign 16#20aa
/nfsquare 16#338b
/ngabengali 16#0999
/ngadeva 16#0919
/ngagujarati 16#0a99
/ngagurmukhi 16#0a19
/ngonguthai 16#0e07
/nhiragana 16#3093
/nhookleft 16#0272
/nhookretroflex 16#0273
/nieunacirclekorean 16#326f
/nieunaparenkorean 16#320f
/nieuncieuckorean 16#3135
/nieuncirclekorean 16#3261
/nieunhieuhkorean 16#3136
/nieunkorean 16#3134
/nieunpansioskorean 16#3168
/nieunparenkorean 16#3201
/nieunsioskorean 16#3167
/nieuntikeutkorean 16#3166
/nihiragana 16#306b
/nikatakana 16#30cb
/nikatakanahalfwidth 16#ff86
/nikhahitleftthai 16#f899
/nikhahitthai 16#0e4d
/nine 16#0039
/ninearabic 16#0669
/ninebengali 16#09ef
/ninecircle 16#2468
/ninecircleinversesansserif 16#2792
/ninedeva 16#096f
/ninegujarati 16#0aef
/ninegurmukhi 16#0a6f
/ninehackarabic 16#0669
/ninehangzhou 16#3029
/nineideographicparen 16#3228
/nineinferior 16#2089
/ninemonospace 16#ff19
/nineoldstyle 16#f739
/nineparen 16#247c
/nineperiod 16#2490
/ninepersian 16#06f9
/nineroman 16#2178
/ninesuperior 16#2079
/nineteencircle 16#2472
/nineteenparen 16#2486
/nineteenperiod 16#249a
/ninethai 16#0e59
/nj 16#01cc
/njecyrillic 16#045a
/nkatakana 16#30f3
/nkatakanahalfwidth 16#ff9d
/nlegrightlong 16#019e
/nlinebelow 16#1e49
/nmonospace 16#ff4e
/nmsquare 16#339a
/nnabengali 16#09a3
/nnadeva 16#0923
/nnagujarati 16#0aa3
/nnagurmukhi 16#0a23
/nnnadeva 16#0929
/nohiragana 16#306e
/nokatakana 16#30ce
/nokatakanahalfwidth 16#ff89
/nonbreakingspace 16#00a0
/nonenthai 16#0e13
/nonuthai 16#0e19
/noonarabic 16#0646
/noonfinalarabic 16#fee6
/noonghunnaarabic 16#06ba
/noonghunnafinalarabic 16#fb9f
/nooninitialarabic 16#fee7
/noonjeeminitialarabic 16#fcd2
/noonjeemisolatedarabic 16#fc4b
/noonmedialarabic 16#fee8
/noonmeeminitialarabic 16#fcd5
/noonmeemisolatedarabic 16#fc4e
/noonnoonfinalarabic 16#fc8d
/notcontains 16#220c
/notelement 16#2209
/notelementof 16#2209
/notequal 16#2260
/notgreater 16#226f
/notgreaternorequal 16#2271
/notgreaternorless 16#2279
/notidentical 16#2262
/notless 16#226e
/notlessnorequal 16#2270
/notparallel 16#2226
/notprecedes 16#2280
/notsubset 16#2284
/notsucceeds 16#2281
/notsuperset 16#2285
/nowarmenian 16#0576
/nparen 16#24a9
/nssquare 16#33b1
/nsuperior 16#207f
/ntilde 16#00f1
/nu 16#03bd
/nuhiragana 16#306c
/nukatakana 16#30cc
/nukatakanahalfwidth 16#ff87
/nuktabengali 16#09bc
/nuktadeva 16#093c
/nuktagujarati 16#0abc
/nuktagurmukhi 16#0a3c
/numbersign 16#0023
/numbersignmonospace 16#ff03
/numbersignsmall 16#fe5f
/numeralsigngreek 16#0374
/numeralsignlowergreek 16#0375
/numero 16#2116
/nun 16#05e0
/nundagesh 16#fb40
/nundageshhebrew 16#fb40
/nunhebrew 16#05e0
/nvsquare 16#33b5
/nwsquare 16#33bb
/nyabengali 16#099e
/nyadeva 16#091e
/nyagujarati 16#0a9e
/nyagurmukhi 16#0a1e
/o 16#006f
/oacute 16#00f3
/oangthai 16#0e2d
/obarred 16#0275
/obarredcyrillic 16#04e9
/obarreddieresiscyrillic 16#04eb
/obengali 16#0993
/obopomofo 16#311b
/obreve 16#014f
/ocandradeva 16#0911
/ocandragujarati 16#0a91
/ocandravowelsigndeva 16#0949
/ocandravowelsigngujarati 16#0ac9
/ocaron 16#01d2
/ocircle 16#24de
/ocircumflex 16#00f4
/ocircumflexacute 16#1ed1
/ocircumflexdotbelow 16#1ed9
/ocircumflexgrave 16#1ed3
/ocircumflexhookabove 16#1ed5
/ocircumflextilde 16#1ed7
/ocyrillic 16#043e
/odblacute 16#0151
/odblgrave 16#020d
/odeva 16#0913
/odieresis 16#00f6
/odieresiscyrillic 16#04e7
/odotbelow 16#1ecd
/oe 16#0153
/oekorean 16#315a
/ogonek 16#02db
/ogonekcmb 16#0328
/ograve 16#00f2
/ogujarati 16#0a93
/oharmenian 16#0585
/ohiragana 16#304a
/ohookabove 16#1ecf
/ohorn 16#01a1
/ohornacute 16#1edb
/ohorndotbelow 16#1ee3
/ohorngrave 16#1edd
/ohornhookabove 16#1edf
/ohorntilde 16#1ee1
/ohungarumlaut 16#0151
/oi 16#01a3
/oinvertedbreve 16#020f
/okatakana 16#30aa
/okatakanahalfwidth 16#ff75
/okorean 16#3157
/olehebrew 16#05ab
/omacron 16#014d
/omacronacute 16#1e53
/omacrongrave 16#1e51
/omdeva 16#0950
/omega 16#03c9
/omega1 16#03d6
/omegacyrillic 16#0461
/omegalatinclosed 16#0277
/omegaroundcyrillic 16#047b
/omegatitlocyrillic 16#047d
/omegatonos 16#03ce
/omgujarati 16#0ad0
/omicron 16#03bf
/omicrontonos 16#03cc
/omonospace 16#ff4f
/one 16#0031
/onearabic 16#0661
/onebengali 16#09e7
/onecircle 16#2460
/onecircleinversesansserif 16#278a
/onedeva 16#0967
/onedotenleader 16#2024
/oneeighth 16#215b
/onefitted 16#f6dc
/onegujarati 16#0ae7
/onegurmukhi 16#0a67
/onehackarabic 16#0661
/onehalf 16#00bd
/onehangzhou 16#3021
/oneideographicparen 16#3220
/oneinferior 16#2081
/onemonospace 16#ff11
/onenumeratorbengali 16#09f4
/oneoldstyle 16#f731
/oneparen 16#2474
/oneperiod 16#2488
/onepersian 16#06f1
/onequarter 16#00bc
/oneroman 16#2170
/onesuperior 16#00b9
/onethai 16#0e51
/onethird 16#2153
/oogonek 16#01eb
/oogonekmacron 16#01ed
/oogurmukhi 16#0a13
/oomatragurmukhi 16#0a4b
/oopen 16#0254
/oparen 16#24aa
/openbullet 16#25e6
/option 16#2325
/ordfeminine 16#00aa
/ordmasculine 16#00ba
/orthogonal 16#221f
/oshortdeva 16#0912
/oshortvowelsigndeva 16#094a
/oslash 16#00f8
/oslashacute 16#01ff
/osmallhiragana 16#3049
/osmallkatakana 16#30a9
/osmallkatakanahalfwidth 16#ff6b
/ostrokeacute 16#01ff
/osuperior 16#f6f0
/otcyrillic 16#047f
/otilde 16#00f5
/otildeacute 16#1e4d
/otildedieresis 16#1e4f
/oubopomofo 16#3121
/overline 16#203e
/overlinecenterline 16#fe4a
/overlinecmb 16#0305
/overlinedashed 16#fe49
/overlinedblwavy 16#fe4c
/overlinewavy 16#fe4b
/overscore 16#00af
/ovowelsignbengali 16#09cb
/ovowelsigndeva 16#094b
/ovowelsigngujarati 16#0acb
/p 16#0070
/paampssquare 16#3380
/paasentosquare 16#332b
/pabengali 16#09aa
/pacute 16#1e55
/padeva 16#092a
/pagedown 16#21df
/pageup 16#21de
/pagujarati 16#0aaa
/pagurmukhi 16#0a2a
/pahiragana 16#3071
/paiyannoithai 16#0e2f
/pakatakana 16#30d1
/palatalizationcyrilliccmb 16#0484
/palochkacyrillic 16#04c0
/pansioskorean 16#317f
/paragraph 16#00b6
/parallel 16#2225
/parenleft 16#0028
/parenleftaltonearabic 16#fd3e
/parenleftbt 16#f8ed
/parenleftex 16#f8ec
/parenleftinferior 16#208d
/parenleftmonospace 16#ff08
/parenleftsmall 16#fe59
/parenleftsuperior 16#207d
/parenlefttp 16#f8eb
/parenleftvertical 16#fe35
/parenright 16#0029
/parenrightaltonearabic 16#fd3f
/parenrightbt 16#f8f8
/parenrightex 16#f8f7
/parenrightinferior 16#208e
/parenrightmonospace 16#ff09
/parenrightsmall 16#fe5a
/parenrightsuperior 16#207e
/parenrighttp 16#f8f6
/parenrightvertical 16#fe36
/partialdiff 16#2202
/paseqhebrew 16#05c0
/pashtahebrew 16#0599
/pasquare 16#33a9
/patah 16#05b7
/patah11 16#05b7
/patah1d 16#05b7
/patah2a 16#05b7
/patahhebrew 16#05b7
/patahnarrowhebrew 16#05b7
/patahquarterhebrew 16#05b7
/patahwidehebrew 16#05b7
/pazerhebrew 16#05a1
/pbopomofo 16#3106
/pcircle 16#24df
/pdotaccent 16#1e57
/pe 16#05e4
/pecyrillic 16#043f
/pedagesh 16#fb44
/pedageshhebrew 16#fb44
/peezisquare 16#333b
/pefinaldageshhebrew 16#fb43
/peharabic 16#067e
/peharmenian 16#057a
/pehebrew 16#05e4
/pehfinalarabic 16#fb57
/pehinitialarabic 16#fb58
/pehiragana 16#307a
/pehmedialarabic 16#fb59
/pekatakana 16#30da
/pemiddlehookcyrillic 16#04a7
/perafehebrew 16#fb4e
/percent 16#0025
/percentarabic 16#066a
/percentmonospace 16#ff05
/percentsmall 16#fe6a
/period 16#002e
/periodarmenian 16#0589
/periodcentered 16#00b7
/periodhalfwidth 16#ff61
/periodinferior 16#f6e7
/periodmonospace 16#ff0e
/periodsmall 16#fe52
/periodsuperior 16#f6e8
/perispomenigreekcmb 16#0342
/perpendicular 16#22a5
/perthousand 16#2030
/peseta 16#20a7
/pfsquare 16#338a
/phabengali 16#09ab
/phadeva 16#092b
/phagujarati 16#0aab
/phagurmukhi 16#0a2b
/phi 16#03c6
/phi1 16#03d5
/phieuphacirclekorean 16#327a
/phieuphaparenkorean 16#321a
/phieuphcirclekorean 16#326c
/phieuphkorean 16#314d
/phieuphparenkorean 16#320c
/philatin 16#0278
/phinthuthai 16#0e3a
/phisymbolgreek 16#03d5
/phook 16#01a5
/phophanthai 16#0e1e
/phophungthai 16#0e1c
/phosamphaothai 16#0e20
/pi 16#03c0
/pieupacirclekorean 16#3273
/pieupaparenkorean 16#3213
/pieupcieuckorean 16#3176
/pieupcirclekorean 16#3265
/pieupkiyeokkorean 16#3172
/pieupkorean 16#3142
/pieupparenkorean 16#3205
/pieupsioskiyeokkorean 16#3174
/pieupsioskorean 16#3144
/pieupsiostikeutkorean 16#3175
/pieupthieuthkorean 16#3177
/pieuptikeutkorean 16#3173
/pihiragana 16#3074
/pikatakana 16#30d4
/pisymbolgreek 16#03d6
/piwrarmenian 16#0583
/plus 16#002b
/plusbelowcmb 16#031f
/pluscircle 16#2295
/plusminus 16#00b1
/plusmod 16#02d6
/plusmonospace 16#ff0b
/plussmall 16#fe62
/plussuperior 16#207a
/pmonospace 16#ff50
/pmsquare 16#33d8
/pohiragana 16#307d
/pointingindexdownwhite 16#261f
/pointingindexleftwhite 16#261c
/pointingindexrightwhite 16#261e
/pointingindexupwhite 16#261d
/pokatakana 16#30dd
/poplathai 16#0e1b
/postalmark 16#3012
/postalmarkface 16#3020
/pparen 16#24ab
/precedes 16#227a
/prescription 16#211e
/primemod 16#02b9
/primereversed 16#2035
/product 16#220f
/projective 16#2305
/prolongedkana 16#30fc
/propellor 16#2318
/propersubset 16#2282
/propersuperset 16#2283
/proportion 16#2237
/proportional 16#221d
/psi 16#03c8
/psicyrillic 16#0471
/psilipneumatacyrilliccmb 16#0486
/pssquare 16#33b0
/puhiragana 16#3077
/pukatakana 16#30d7
/pvsquare 16#33b4
/pwsquare 16#33ba
/q 16#0071
/qadeva 16#0958
/qadmahebrew 16#05a8
/qafarabic 16#0642
/qaffinalarabic 16#fed6
/qafinitialarabic 16#fed7
/qafmedialarabic 16#fed8
/qamats 16#05b8
/qamats10 16#05b8
/qamats1a 16#05b8
/qamats1c 16#05b8
/qamats27 16#05b8
/qamats29 16#05b8
/qamats33 16#05b8
/qamatsde 16#05b8
/qamatshebrew 16#05b8
/qamatsnarrowhebrew 16#05b8
/qamatsqatanhebrew 16#05b8
/qamatsqatannarrowhebrew 16#05b8
/qamatsqatanquarterhebrew 16#05b8
/qamatsqatanwidehebrew 16#05b8
/qamatsquarterhebrew 16#05b8
/qamatswidehebrew 16#05b8
/qarneyparahebrew 16#059f
/qbopomofo 16#3111
/qcircle 16#24e0
/qhook 16#02a0
/qmonospace 16#ff51
/qof 16#05e7
/qofdagesh 16#fb47
/qofdageshhebrew 16#fb47
/qofhebrew 16#05e7
/qparen 16#24ac
/quarternote 16#2669
/qubuts 16#05bb
/qubuts18 16#05bb
/qubuts25 16#05bb
/qubuts31 16#05bb
/qubutshebrew 16#05bb
/qubutsnarrowhebrew 16#05bb
/qubutsquarterhebrew 16#05bb
/qubutswidehebrew 16#05bb
/question 16#003f
/questionarabic 16#061f
/questionarmenian 16#055e
/questiondown 16#00bf
/questiondownsmall 16#f7bf
/questiongreek 16#037e
/questionmonospace 16#ff1f
/questionsmall 16#f73f
/quotedbl 16#0022
/quotedblbase 16#201e
/quotedblleft 16#201c
/quotedblmonospace 16#ff02
/quotedblprime 16#301e
/quotedblprimereversed 16#301d
/quotedblright 16#201d
/quoteleft 16#2018
/quoteleftreversed 16#201b
/quotereversed 16#201b
/quoteright 16#2019
/quoterightn 16#0149
/quotesinglbase 16#201a
/quotesingle 16#0027
/quotesinglemonospace 16#ff07
/r 16#0072
/raarmenian 16#057c
/rabengali 16#09b0
/racute 16#0155
/radeva 16#0930
/radical 16#221a
/radicalex 16#f8e5
/radoverssquare 16#33ae
/radoverssquaredsquare 16#33af
/radsquare 16#33ad
/rafe 16#05bf
/rafehebrew 16#05bf
/ragujarati 16#0ab0
/ragurmukhi 16#0a30
/rahiragana 16#3089
/rakatakana 16#30e9
/rakatakanahalfwidth 16#ff97
/ralowerdiagonalbengali 16#09f1
/ramiddlediagonalbengali 16#09f0
/ramshorn 16#0264
/ratio 16#2236
/rbopomofo 16#3116
/rcaron 16#0159
/rcedilla 16#0157
/rcircle 16#24e1
/rcommaaccent 16#0157
/rdblgrave 16#0211
/rdotaccent 16#1e59
/rdotbelow 16#1e5b
/rdotbelowmacron 16#1e5d
/referencemark 16#203b
/reflexsubset 16#2286
/reflexsuperset 16#2287
/registered 16#00ae
/registersans 16#f8e8
/registerserif 16#f6da
/reharabic 16#0631
/reharmenian 16#0580
/rehfinalarabic 16#feae
/rehiragana 16#308c
/rekatakana 16#30ec
/rekatakanahalfwidth 16#ff9a
/resh 16#05e8
/reshdageshhebrew 16#fb48
/reshhebrew 16#05e8
/reversedtilde 16#223d
/reviahebrew 16#0597
/reviamugrashhebrew 16#0597
/revlogicalnot 16#2310
/rfishhook 16#027e
/rfishhookreversed 16#027f
/rhabengali 16#09dd
/rhadeva 16#095d
/rho 16#03c1
/rhook 16#027d
/rhookturned 16#027b
/rhookturnedsuperior 16#02b5
/rhosymbolgreek 16#03f1
/rhotichookmod 16#02de
/rieulacirclekorean 16#3271
/rieulaparenkorean 16#3211
/rieulcirclekorean 16#3263
/rieulhieuhkorean 16#3140
/rieulkiyeokkorean 16#313a
/rieulkiyeoksioskorean 16#3169
/rieulkorean 16#3139
/rieulmieumkorean 16#313b
/rieulpansioskorean 16#316c
/rieulparenkorean 16#3203
/rieulphieuphkorean 16#313f
/rieulpieupkorean 16#313c
/rieulpieupsioskorean 16#316b
/rieulsioskorean 16#313d
/rieulthieuthkorean 16#313e
/rieultikeutkorean 16#316a
/rieulyeorinhieuhkorean 16#316d
/rightangle 16#221f
/righttackbelowcmb 16#0319
/righttriangle 16#22bf
/rihiragana 16#308a
/rikatakana 16#30ea
/rikatakanahalfwidth 16#ff98
/ring 16#02da
/ringbelowcmb 16#0325
/ringcmb 16#030a
/ringhalfleft 16#02bf
/ringhalfleftarmenian 16#0559
/ringhalfleftbelowcmb 16#031c
/ringhalfleftcentered 16#02d3
/ringhalfright 16#02be
/ringhalfrightbelowcmb 16#0339
/ringhalfrightcentered 16#02d2
/rinvertedbreve 16#0213
/rittorusquare 16#3351
/rlinebelow 16#1e5f
/rlongleg 16#027c
/rlonglegturned 16#027a
/rmonospace 16#ff52
/rohiragana 16#308d
/rokatakana 16#30ed
/rokatakanahalfwidth 16#ff9b
/roruathai 16#0e23
/rparen 16#24ad
/rrabengali 16#09dc
/rradeva 16#0931
/rragurmukhi 16#0a5c
/rreharabic 16#0691
/rrehfinalarabic 16#fb8d
/rrvocalicbengali 16#09e0
/rrvocalicdeva 16#0960
/rrvocalicgujarati 16#0ae0
/rrvocalicvowelsignbengali 16#09c4
/rrvocalicvowelsigndeva 16#0944
/rrvocalicvowelsigngujarati 16#0ac4
/rsuperior 16#f6f1
/rtblock 16#2590
/rturned 16#0279
/rturnedsuperior 16#02b4
/ruhiragana 16#308b
/rukatakana 16#30eb
/rukatakanahalfwidth 16#ff99
/rupeemarkbengali 16#09f2
/rupeesignbengali 16#09f3
/rupiah 16#f6dd
/ruthai 16#0e24
/rvocalicbengali 16#098b
/rvocalicdeva 16#090b
/rvocalicgujarati 16#0a8b
/rvocalicvowelsignbengali 16#09c3
/rvocalicvowelsigndeva 16#0943
/rvocalicvowelsigngujarati 16#0ac3
/s 16#0073
/sabengali 16#09b8
/sacute 16#015b
/sacutedotaccent 16#1e65
/sadarabic 16#0635
/sadeva 16#0938
/sadfinalarabic 16#feba
/sadinitialarabic 16#febb
/sadmedialarabic 16#febc
/sagujarati 16#0ab8
/sagurmukhi 16#0a38
/sahiragana 16#3055
/sakatakana 16#30b5
/sakatakanahalfwidth 16#ff7b
/sallallahoualayhewasallamarabic 16#fdfa
/samekh 16#05e1
/samekhdagesh 16#fb41
/samekhdageshhebrew 16#fb41
/samekhhebrew 16#05e1
/saraaathai 16#0e32
/saraaethai 16#0e41
/saraaimaimalaithai 16#0e44
/saraaimaimuanthai 16#0e43
/saraamthai 16#0e33
/saraathai 16#0e30
/saraethai 16#0e40
/saraiileftthai 16#f886
/saraiithai 16#0e35
/saraileftthai 16#f885
/saraithai 16#0e34
/saraothai 16#0e42
/saraueeleftthai 16#f888
/saraueethai 16#0e37
/saraueleftthai 16#f887
/sarauethai 16#0e36
/sarauthai 16#0e38
/sarauuthai 16#0e39
/sbopomofo 16#3119
/scaron 16#0161
/scarondotaccent 16#1e67
/scedilla 16#015f
/schwa 16#0259
/schwacyrillic 16#04d9
/schwadieresiscyrillic 16#04db
/schwahook 16#025a
/scircle 16#24e2
/scircumflex 16#015d
/scommaaccent 16#0219
/sdotaccent 16#1e61
/sdotbelow 16#1e63
/sdotbelowdotaccent 16#1e69
/seagullbelowcmb 16#033c
/second 16#2033
/secondtonechinese 16#02ca
/section 16#00a7
/seenarabic 16#0633
/seenfinalarabic 16#feb2
/seeninitialarabic 16#feb3
/seenmedialarabic 16#feb4
/segol 16#05b6
/segol13 16#05b6
/segol1f 16#05b6
/segol2c 16#05b6
/segolhebrew 16#05b6
/segolnarrowhebrew 16#05b6
/segolquarterhebrew 16#05b6
/segoltahebrew 16#0592
/segolwidehebrew 16#05b6
/seharmenian 16#057d
/sehiragana 16#305b
/sekatakana 16#30bb
/sekatakanahalfwidth 16#ff7e
/semicolon 16#003b
/semicolonarabic 16#061b
/semicolonmonospace 16#ff1b
/semicolonsmall 16#fe54
/semivoicedmarkkana 16#309c
/semivoicedmarkkanahalfwidth 16#ff9f
/sentisquare 16#3322
/sentosquare 16#3323
/seven 16#0037
/sevenarabic 16#0667
/sevenbengali 16#09ed
/sevencircle 16#2466
/sevencircleinversesansserif 16#2790
/sevendeva 16#096d
/seveneighths 16#215e
/sevengujarati 16#0aed
/sevengurmukhi 16#0a6d
/sevenhackarabic 16#0667
/sevenhangzhou 16#3027
/sevenideographicparen 16#3226
/seveninferior 16#2087
/sevenmonospace 16#ff17
/sevenoldstyle 16#f737
/sevenparen 16#247a
/sevenperiod 16#248e
/sevenpersian 16#06f7
/sevenroman 16#2176
/sevensuperior 16#2077
/seventeencircle 16#2470
/seventeenparen 16#2484
/seventeenperiod 16#2498
/seventhai 16#0e57
/sfthyphen 16#00ad
/shaarmenian 16#0577
/shabengali 16#09b6
/shacyrillic 16#0448
/shaddaarabic 16#0651
/shaddadammaarabic 16#fc61
/shaddadammatanarabic 16#fc5e
/shaddafathaarabic 16#fc60
/shaddakasraarabic 16#fc62
/shaddakasratanarabic 16#fc5f
/shade 16#2592
/shadedark 16#2593
/shadelight 16#2591
/shademedium 16#2592
/shadeva 16#0936
/shagujarati 16#0ab6
/shagurmukhi 16#0a36
/shalshelethebrew 16#0593
/shbopomofo 16#3115
/shchacyrillic 16#0449
/sheenarabic 16#0634
/sheenfinalarabic 16#feb6
/sheeninitialarabic 16#feb7
/sheenmedialarabic 16#feb8
/sheicoptic 16#03e3
/sheqel 16#20aa
/sheqelhebrew 16#20aa
/sheva 16#05b0
/sheva115 16#05b0
/sheva15 16#05b0
/sheva22 16#05b0
/sheva2e 16#05b0
/shevahebrew 16#05b0
/shevanarrowhebrew 16#05b0
/shevaquarterhebrew 16#05b0
/shevawidehebrew 16#05b0
/shhacyrillic 16#04bb
/shimacoptic 16#03ed
/shin 16#05e9
/shindagesh 16#fb49
/shindageshhebrew 16#fb49
/shindageshshindot 16#fb2c
/shindageshshindothebrew 16#fb2c
/shindageshsindot 16#fb2d
/shindageshsindothebrew 16#fb2d
/shindothebrew 16#05c1
/shinhebrew 16#05e9
/shinshindot 16#fb2a
/shinshindothebrew 16#fb2a
/shinsindot 16#fb2b
/shinsindothebrew 16#fb2b
/shook 16#0282
/sigma 16#03c3
/sigma1 16#03c2
/sigmafinal 16#03c2
/sigmalunatesymbolgreek 16#03f2
/sihiragana 16#3057
/sikatakana 16#30b7
/sikatakanahalfwidth 16#ff7c
/siluqhebrew 16#05bd
/siluqlefthebrew 16#05bd
/similar 16#223c
/sindothebrew 16#05c2
/siosacirclekorean 16#3274
/siosaparenkorean 16#3214
/sioscieuckorean 16#317e
/sioscirclekorean 16#3266
/sioskiyeokkorean 16#317a
/sioskorean 16#3145
/siosnieunkorean 16#317b
/siosparenkorean 16#3206
/siospieupkorean 16#317d
/siostikeutkorean 16#317c
/six 16#0036
/sixarabic 16#0666
/sixbengali 16#09ec
/sixcircle 16#2465
/sixcircleinversesansserif 16#278f
/sixdeva 16#096c
/sixgujarati 16#0aec
/sixgurmukhi 16#0a6c
/sixhackarabic 16#0666
/sixhangzhou 16#3026
/sixideographicparen 16#3225
/sixinferior 16#2086
/sixmonospace 16#ff16
/sixoldstyle 16#f736
/sixparen 16#2479
/sixperiod 16#248d
/sixpersian 16#06f6
/sixroman 16#2175
/sixsuperior 16#2076
/sixteencircle 16#246f
/sixteencurrencydenominatorbengali 16#09f9
/sixteenparen 16#2483
/sixteenperiod 16#2497
/sixthai 16#0e56
/slash 16#002f
/slashmonospace 16#ff0f
/slong 16#017f
/slongdotaccent 16#1e9b
/smileface 16#263a
/smonospace 16#ff53
/sofpasuqhebrew 16#05c3
/softhyphen 16#00ad
/softsigncyrillic 16#044c
/sohiragana 16#305d
/sokatakana 16#30bd
/sokatakanahalfwidth 16#ff7f
/soliduslongoverlaycmb 16#0338
/solidusshortoverlaycmb 16#0337
/sorusithai 16#0e29
/sosalathai 16#0e28
/sosothai 16#0e0b
/sosuathai 16#0e2a
/space 16#0020
/spacehackarabic 16#0020
/spade 16#2660
/spadesuitblack 16#2660
/spadesuitwhite 16#2664
/sparen 16#24ae
/squarebelowcmb 16#033b
/squarecc 16#33c4
/squarecm 16#339d
/squarediagonalcrosshatchfill 16#25a9
/squarehorizontalfill 16#25a4
/squarekg 16#338f
/squarekm 16#339e
/squarekmcapital 16#33ce
/squareln 16#33d1
/squarelog 16#33d2
/squaremg 16#338e
/squaremil 16#33d5
/squaremm 16#339c
/squaremsquared 16#33a1
/squareorthogonalcrosshatchfill 16#25a6
/squareupperlefttolowerrightfill 16#25a7
/squareupperrighttolowerleftfill 16#25a8
/squareverticalfill 16#25a5
/squarewhitewithsmallblack 16#25a3
/srsquare 16#33db
/ssabengali 16#09b7
/ssadeva 16#0937
/ssagujarati 16#0ab7
/ssangcieuckorean 16#3149
/ssanghieuhkorean 16#3185
/ssangieungkorean 16#3180
/ssangkiyeokkorean 16#3132
/ssangnieunkorean 16#3165
/ssangpieupkorean 16#3143
/ssangsioskorean 16#3146
/ssangtikeutkorean 16#3138
/ssuperior 16#f6f2
/sterling 16#00a3
/sterlingmonospace 16#ffe1
/strokelongoverlaycmb 16#0336
/strokeshortoverlaycmb 16#0335
/subset 16#2282
/subsetnotequal 16#228a
/subsetorequal 16#2286
/succeeds 16#227b
/suchthat 16#220b
/suhiragana 16#3059
/sukatakana 16#30b9
/sukatakanahalfwidth 16#ff7d
/sukunarabic 16#0652
/summation 16#2211
/sun 16#263c
/superset 16#2283
/supersetnotequal 16#228b
/supersetorequal 16#2287
/svsquare 16#33dc
/syouwaerasquare 16#337c
/t 16#0074
/tabengali 16#09a4
/tackdown 16#22a4
/tackleft 16#22a3
/tadeva 16#0924
/tagujarati 16#0aa4
/tagurmukhi 16#0a24
/taharabic 16#0637
/tahfinalarabic 16#fec2
/tahinitialarabic 16#fec3
/tahiragana 16#305f
/tahmedialarabic 16#fec4
/taisyouerasquare 16#337d
/takatakana 16#30bf
/takatakanahalfwidth 16#ff80
/tatweelarabic 16#0640
/tau 16#03c4
/tav 16#05ea
/tavdages 16#fb4a
/tavdagesh 16#fb4a
/tavdageshhebrew 16#fb4a
/tavhebrew 16#05ea
/tbar 16#0167
/tbopomofo 16#310a
/tcaron 16#0165
/tccurl 16#02a8
/tcedilla 16#0163
/tcheharabic 16#0686
/tchehfinalarabic 16#fb7b
/tchehinitialarabic 16#fb7c
/tchehmedialarabic 16#fb7d
/tcircle 16#24e3
/tcircumflexbelow 16#1e71
/tcommaaccent 16#0163
/tdieresis 16#1e97
/tdotaccent 16#1e6b
/tdotbelow 16#1e6d
/tecyrillic 16#0442
/tedescendercyrillic 16#04ad
/teharabic 16#062a
/tehfinalarabic 16#fe96
/tehhahinitialarabic 16#fca2
/tehhahisolatedarabic 16#fc0c
/tehinitialarabic 16#fe97
/tehiragana 16#3066
/tehjeeminitialarabic 16#fca1
/tehjeemisolatedarabic 16#fc0b
/tehmarbutaarabic 16#0629
/tehmarbutafinalarabic 16#fe94
/tehmedialarabic 16#fe98
/tehmeeminitialarabic 16#fca4
/tehmeemisolatedarabic 16#fc0e
/tehnoonfinalarabic 16#fc73
/tekatakana 16#30c6
/tekatakanahalfwidth 16#ff83
/telephone 16#2121
/telephoneblack 16#260e
/telishagedolahebrew 16#05a0
/telishaqetanahebrew 16#05a9
/tencircle 16#2469
/tenideographicparen 16#3229
/tenparen 16#247d
/tenperiod 16#2491
/tenroman 16#2179
/tesh 16#02a7
/tet 16#05d8
/tetdagesh 16#fb38
/tetdageshhebrew 16#fb38
/tethebrew 16#05d8
/tetsecyrillic 16#04b5
/tevirhebrew 16#059b
/tevirlefthebrew 16#059b
/thabengali 16#09a5
/thadeva 16#0925
/thagujarati 16#0aa5
/thagurmukhi 16#0a25
/thalarabic 16#0630
/thalfinalarabic 16#feac
/thanthakhatlowleftthai 16#f898
/thanthakhatlowrightthai 16#f897
/thanthakhatthai 16#0e4c
/thanthakhatupperleftthai 16#f896
/theharabic 16#062b
/thehfinalarabic 16#fe9a
/thehinitialarabic 16#fe9b
/thehmedialarabic 16#fe9c
/thereexists 16#2203
/therefore 16#2234
/theta 16#03b8
/theta1 16#03d1
/thetasymbolgreek 16#03d1
/thieuthacirclekorean 16#3279
/thieuthaparenkorean 16#3219
/thieuthcirclekorean 16#326b
/thieuthkorean 16#314c
/thieuthparenkorean 16#320b
/thirteencircle 16#246c
/thirteenparen 16#2480
/thirteenperiod 16#2494
/thonangmonthothai 16#0e11
/thook 16#01ad
/thophuthaothai 16#0e12
/thorn 16#00fe
/thothahanthai 16#0e17
/thothanthai 16#0e10
/thothongthai 16#0e18
/thothungthai 16#0e16
/thousandcyrillic 16#0482
/thousandsseparatorarabic 16#066c
/thousandsseparatorpersian 16#066c
/three 16#0033
/threearabic 16#0663
/threebengali 16#09e9
/threecircle 16#2462
/threecircleinversesansserif 16#278c
/threedeva 16#0969
/threeeighths 16#215c
/threegujarati 16#0ae9
/threegurmukhi 16#0a69
/threehackarabic 16#0663
/threehangzhou 16#3023
/threeideographicparen 16#3222
/threeinferior 16#2083
/threemonospace 16#ff13
/threenumeratorbengali 16#09f6
/threeoldstyle 16#f733
/threeparen 16#2476
/threeperiod 16#248a
/threepersian 16#06f3
/threequarters 16#00be
/threequartersemdash 16#f6de
/threeroman 16#2172
/threesuperior 16#00b3
/threethai 16#0e53
/thzsquare 16#3394
/tihiragana 16#3061
/tikatakana 16#30c1
/tikatakanahalfwidth 16#ff81
/tikeutacirclekorean 16#3270
/tikeutaparenkorean 16#3210
/tikeutcirclekorean 16#3262
/tikeutkorean 16#3137
/tikeutparenkorean 16#3202
/tilde 16#02dc
/tildebelowcmb 16#0330
/tildecmb 16#0303
/tildecomb 16#0303
/tildedoublecmb 16#0360
/tildeoperator 16#223c
/tildeoverlaycmb 16#0334
/tildeverticalcmb 16#033e
/timescircle 16#2297
/tipehahebrew 16#0596
/tipehalefthebrew 16#0596
/tippigurmukhi 16#0a70
/titlocyrilliccmb 16#0483
/tiwnarmenian 16#057f
/tlinebelow 16#1e6f
/tmonospace 16#ff54
/toarmenian 16#0569
/tohiragana 16#3068
/tokatakana 16#30c8
/tokatakanahalfwidth 16#ff84
/tonebarextrahighmod 16#02e5
/tonebarextralowmod 16#02e9
/tonebarhighmod 16#02e6
/tonebarlowmod 16#02e8
/tonebarmidmod 16#02e7
/tonefive 16#01bd
/tonesix 16#0185
/tonetwo 16#01a8
/tonos 16#0384
/tonsquare 16#3327
/topatakthai 16#0e0f
/tortoiseshellbracketleft 16#3014
/tortoiseshellbracketleftsmall 16#fe5d
/tortoiseshellbracketleftvertical 16#fe39
/tortoiseshellbracketright 16#3015
/tortoiseshellbracketrightsmall 16#fe5e
/tortoiseshellbracketrightvertical 16#fe3a
/totaothai 16#0e15
/tpalatalhook 16#01ab
/tparen 16#24af
/trademark 16#2122
/trademarksans 16#f8ea
/trademarkserif 16#f6db
/tretroflexhook 16#0288
/triagdn 16#25bc
/triaglf 16#25c4
/triagrt 16#25ba
/triagup 16#25b2
/ts 16#02a6
/tsadi 16#05e6
/tsadidagesh 16#fb46
/tsadidageshhebrew 16#fb46
/tsadihebrew 16#05e6
/tsecyrillic 16#0446
/tsere 16#05b5
/tsere12 16#05b5
/tsere1e 16#05b5
/tsere2b 16#05b5
/tserehebrew 16#05b5
/tserenarrowhebrew 16#05b5
/tserequarterhebrew 16#05b5
/tserewidehebrew 16#05b5
/tshecyrillic 16#045b
/tsuperior 16#f6f3
/ttabengali 16#099f
/ttadeva 16#091f
/ttagujarati 16#0a9f
/ttagurmukhi 16#0a1f
/tteharabic 16#0679
/ttehfinalarabic 16#fb67
/ttehinitialarabic 16#fb68
/ttehmedialarabic 16#fb69
/tthabengali 16#09a0
/tthadeva 16#0920
/tthagujarati 16#0aa0
/tthagurmukhi 16#0a20
/tturned 16#0287
/tuhiragana 16#3064
/tukatakana 16#30c4
/tukatakanahalfwidth 16#ff82
/tusmallhiragana 16#3063
/tusmallkatakana 16#30c3
/tusmallkatakanahalfwidth 16#ff6f
/twelvecircle 16#246b
/twelveparen 16#247f
/twelveperiod 16#2493
/twelveroman 16#217b
/twentycircle 16#2473
/twentyhangzhou 16#5344
/twentyparen 16#2487
/twentyperiod 16#249b
/two 16#0032
/twoarabic 16#0662
/twobengali 16#09e8
/twocircle 16#2461
/twocircleinversesansserif 16#278b
/twodeva 16#0968
/twodotenleader 16#2025
/twodotleader 16#2025
/twodotleadervertical 16#fe30
/twogujarati 16#0ae8
/twogurmukhi 16#0a68
/twohackarabic 16#0662
/twohangzhou 16#3022
/twoideographicparen 16#3221
/twoinferior 16#2082
/twomonospace 16#ff12
/twonumeratorbengali 16#09f5
/twooldstyle 16#f732
/twoparen 16#2475
/twoperiod 16#2489
/twopersian 16#06f2
/tworoman 16#2171
/twostroke 16#01bb
/twosuperior 16#00b2
/twothai 16#0e52
/twothirds 16#2154
/u 16#0075
/uacute 16#00fa
/ubar 16#0289
/ubengali 16#0989
/ubopomofo 16#3128
/ubreve 16#016d
/ucaron 16#01d4
/ucircle 16#24e4
/ucircumflex 16#00fb
/ucircumflexbelow 16#1e77
/ucyrillic 16#0443
/udattadeva 16#0951
/udblacute 16#0171
/udblgrave 16#0215
/udeva 16#0909
/udieresis 16#00fc
/udieresisacute 16#01d8
/udieresisbelow 16#1e73
/udieresiscaron 16#01da
/udieresiscyrillic 16#04f1
/udieresisgrave 16#01dc
/udieresismacron 16#01d6
/udotbelow 16#1ee5
/ugrave 16#00f9
/ugujarati 16#0a89
/ugurmukhi 16#0a09
/uhiragana 16#3046
/uhookabove 16#1ee7
/uhorn 16#01b0
/uhornacute 16#1ee9
/uhorndotbelow 16#1ef1
/uhorngrave 16#1eeb
/uhornhookabove 16#1eed
/uhorntilde 16#1eef
/uhungarumlaut 16#0171
/uhungarumlautcyrillic 16#04f3
/uinvertedbreve 16#0217
/ukatakana 16#30a6
/ukatakanahalfwidth 16#ff73
/ukcyrillic 16#0479
/ukorean 16#315c
/umacron 16#016b
/umacroncyrillic 16#04ef
/umacrondieresis 16#1e7b
/umatragurmukhi 16#0a41
/umonospace 16#ff55
/underscore 16#005f
/underscoredbl 16#2017
/underscoremonospace 16#ff3f
/underscorevertical 16#fe33
/underscorewavy 16#fe4f
/union 16#222a
/universal 16#2200
/uogonek 16#0173
/uparen 16#24b0
/upblock 16#2580
/upperdothebrew 16#05c4
/upsilon 16#03c5
/upsilondieresis 16#03cb
/upsilondieresistonos 16#03b0
/upsilonlatin 16#028a
/upsilontonos 16#03cd
/uptackbelowcmb 16#031d
/uptackmod 16#02d4
/uragurmukhi 16#0a73
/uring 16#016f
/ushortcyrillic 16#045e
/usmallhiragana 16#3045
/usmallkatakana 16#30a5
/usmallkatakanahalfwidth 16#ff69
/ustraightcyrillic 16#04af
/ustraightstrokecyrillic 16#04b1
/utilde 16#0169
/utildeacute 16#1e79
/utildebelow 16#1e75
/uubengali 16#098a
/uudeva 16#090a
/uugujarati 16#0a8a
/uugurmukhi 16#0a0a
/uumatragurmukhi 16#0a42
/uuvowelsignbengali 16#09c2
/uuvowelsigndeva 16#0942
/uuvowelsigngujarati 16#0ac2
/uvowelsignbengali 16#09c1
/uvowelsigndeva 16#0941
/uvowelsigngujarati 16#0ac1
/v 16#0076
/vadeva 16#0935
/vagujarati 16#0ab5
/vagurmukhi 16#0a35
/vakatakana 16#30f7
/vav 16#05d5
/vavdagesh 16#fb35
/vavdagesh65 16#fb35
/vavdageshhebrew 16#fb35
/vavhebrew 16#05d5
/vavholam 16#fb4b
/vavholamhebrew 16#fb4b
/vavvavhebrew 16#05f0
/vavyodhebrew 16#05f1
/vcircle 16#24e5
/vdotbelow 16#1e7f
/vecyrillic 16#0432
/veharabic 16#06a4
/vehfinalarabic 16#fb6b
/vehinitialarabic 16#fb6c
/vehmedialarabic 16#fb6d
/vekatakana 16#30f9
/venus 16#2640
/verticalbar 16#007c
/verticallineabovecmb 16#030d
/verticallinebelowcmb 16#0329
/verticallinelowmod 16#02cc
/verticallinemod 16#02c8
/vewarmenian 16#057e
/vhook 16#028b
/vikatakana 16#30f8
/viramabengali 16#09cd
/viramadeva 16#094d
/viramagujarati 16#0acd
/visargabengali 16#0983
/visargadeva 16#0903
/visargagujarati 16#0a83
/vmonospace 16#ff56
/voarmenian 16#0578
/voicediterationhiragana 16#309e
/voicediterationkatakana 16#30fe
/voicedmarkkana 16#309b
/voicedmarkkanahalfwidth 16#ff9e
/vokatakana 16#30fa
/vparen 16#24b1
/vtilde 16#1e7d
/vturned 16#028c
/vuhiragana 16#3094
/vukatakana 16#30f4
/w 16#0077
/wacute 16#1e83
/waekorean 16#3159
/wahiragana 16#308f
/wakatakana 16#30ef
/wakatakanahalfwidth 16#ff9c
/wakorean 16#3158
/wasmallhiragana 16#308e
/wasmallkatakana 16#30ee
/wattosquare 16#3357
/wavedash 16#301c
/wavyunderscorevertical 16#fe34
/wawarabic 16#0648
/wawfinalarabic 16#feee
/wawhamzaabovearabic 16#0624
/wawhamzaabovefinalarabic 16#fe86
/wbsquare 16#33dd
/wcircle 16#24e6
/wcircumflex 16#0175
/wdieresis 16#1e85
/wdotaccent 16#1e87
/wdotbelow 16#1e89
/wehiragana 16#3091
/weierstrass 16#2118
/wekatakana 16#30f1
/wekorean 16#315e
/weokorean 16#315d
/wgrave 16#1e81
/whitebullet 16#25e6
/whitecircle 16#25cb
/whitecircleinverse 16#25d9
/whitecornerbracketleft 16#300e
/whitecornerbracketleftvertical 16#fe43
/whitecornerbracketright 16#300f
/whitecornerbracketrightvertical 16#fe44
/whitediamond 16#25c7
/whitediamondcontainingblacksmalldiamond 16#25c8
/whitedownpointingsmalltriangle 16#25bf
/whitedownpointingtriangle 16#25bd
/whiteleftpointingsmalltriangle 16#25c3
/whiteleftpointingtriangle 16#25c1
/whitelenticularbracketleft 16#3016
/whitelenticularbracketright 16#3017
/whiterightpointingsmalltriangle 16#25b9
/whiterightpointingtriangle 16#25b7
/whitesmallsquare 16#25ab
/whitesmilingface 16#263a
/whitesquare 16#25a1
/whitestar 16#2606
/whitetelephone 16#260f
/whitetortoiseshellbracketleft 16#3018
/whitetortoiseshellbracketright 16#3019
/whiteuppointingsmalltriangle 16#25b5
/whiteuppointingtriangle 16#25b3
/wihiragana 16#3090
/wikatakana 16#30f0
/wikorean 16#315f
/wmonospace 16#ff57
/wohiragana 16#3092
/wokatakana 16#30f2
/wokatakanahalfwidth 16#ff66
/won 16#20a9
/wonmonospace 16#ffe6
/wowaenthai 16#0e27
/wparen 16#24b2
/wring 16#1e98
/wsuperior 16#02b7
/wturned 16#028d
/wynn 16#01bf
/x 16#0078
/xabovecmb 16#033d
/xbopomofo 16#3112
/xcircle 16#24e7
/xdieresis 16#1e8d
/xdotaccent 16#1e8b
/xeharmenian 16#056d
/xi 16#03be
/xmonospace 16#ff58
/xparen 16#24b3
/xsuperior 16#02e3
/y 16#0079
/yaadosquare 16#334e
/yabengali 16#09af
/yacute 16#00fd
/yadeva 16#092f
/yaekorean 16#3152
/yagujarati 16#0aaf
/yagurmukhi 16#0a2f
/yahiragana 16#3084
/yakatakana 16#30e4
/yakatakanahalfwidth 16#ff94
/yakorean 16#3151
/yamakkanthai 16#0e4e
/yasmallhiragana 16#3083
/yasmallkatakana 16#30e3
/yasmallkatakanahalfwidth 16#ff6c
/yatcyrillic 16#0463
/ycircle 16#24e8
/ycircumflex 16#0177
/ydieresis 16#00ff
/ydotaccent 16#1e8f
/ydotbelow 16#1ef5
/yeharabic 16#064a
/yehbarreearabic 16#06d2
/yehbarreefinalarabic 16#fbaf
/yehfinalarabic 16#fef2
/yehhamzaabovearabic 16#0626
/yehhamzaabovefinalarabic 16#fe8a
/yehhamzaaboveinitialarabic 16#fe8b
/yehhamzaabovemedialarabic 16#fe8c
/yehinitialarabic 16#fef3
/yehmedialarabic 16#fef4
/yehmeeminitialarabic 16#fcdd
/yehmeemisolatedarabic 16#fc58
/yehnoonfinalarabic 16#fc94
/yehthreedotsbelowarabic 16#06d1
/yekorean 16#3156
/yen 16#00a5
/yenmonospace 16#ffe5
/yeokorean 16#3155
/yeorinhieuhkorean 16#3186
/yerahbenyomohebrew 16#05aa
/yerahbenyomolefthebrew 16#05aa
/yericyrillic 16#044b
/yerudieresiscyrillic 16#04f9
/yesieungkorean 16#3181
/yesieungpansioskorean 16#3183
/yesieungsioskorean 16#3182
/yetivhebrew 16#059a
/ygrave 16#1ef3
/yhook 16#01b4
/yhookabove 16#1ef7
/yiarmenian 16#0575
/yicyrillic 16#0457
/yikorean 16#3162
/yinyang 16#262f
/yiwnarmenian 16#0582
/ymonospace 16#ff59
/yod 16#05d9
/yoddagesh 16#fb39
/yoddageshhebrew 16#fb39
/yodhebrew 16#05d9
/yodyodhebrew 16#05f2
/yodyodpatahhebrew 16#fb1f
/yohiragana 16#3088
/yoikorean 16#3189
/yokatakana 16#30e8
/yokatakanahalfwidth 16#ff96
/yokorean 16#315b
/yosmallhiragana 16#3087
/yosmallkatakana 16#30e7
/yosmallkatakanahalfwidth 16#ff6e
/yotgreek 16#03f3
/yoyaekorean 16#3188
/yoyakorean 16#3187
/yoyakthai 16#0e22
/yoyingthai 16#0e0d
/yparen 16#24b4
/ypogegrammeni 16#037a
/ypogegrammenigreekcmb 16#0345
/yr 16#01a6
/yring 16#1e99
/ysuperior 16#02b8
/ytilde 16#1ef9
/yturned 16#028e
/yuhiragana 16#3086
/yuikorean 16#318c
/yukatakana 16#30e6
/yukatakanahalfwidth 16#ff95
/yukorean 16#3160
/yusbigcyrillic 16#046b
/yusbigiotifiedcyrillic 16#046d
/yuslittlecyrillic 16#0467
/yuslittleiotifiedcyrillic 16#0469
/yusmallhiragana 16#3085
/yusmallkatakana 16#30e5
/yusmallkatakanahalfwidth 16#ff6d
/yuyekorean 16#318b
/yuyeokorean 16#318a
/yyabengali 16#09df
/yyadeva 16#095f
/z 16#007a
/zaarmenian 16#0566
/zacute 16#017a
/zadeva 16#095b
/zagurmukhi 16#0a5b
/zaharabic 16#0638
/zahfinalarabic 16#fec6
/zahinitialarabic 16#fec7
/zahiragana 16#3056
/zahmedialarabic 16#fec8
/zainarabic 16#0632
/zainfinalarabic 16#feb0
/zakatakana 16#30b6
/zaqefgadolhebrew 16#0595
/zaqefqatanhebrew 16#0594
/zarqahebrew 16#0598
/zayin 16#05d6
/zayindagesh 16#fb36
/zayindageshhebrew 16#fb36
/zayinhebrew 16#05d6
/zbopomofo 16#3117
/zcaron 16#017e
/zcircle 16#24e9
/zcircumflex 16#1e91
/zcurl 16#0291
/zdot 16#017c
/zdotaccent 16#017c
/zdotbelow 16#1e93
/zecyrillic 16#0437
/zedescendercyrillic 16#0499
/zedieresiscyrillic 16#04df
/zehiragana 16#305c
/zekatakana 16#30bc
/zero 16#0030
/zeroarabic 16#0660
/zerobengali 16#09e6
/zerodeva 16#0966
/zerogujarati 16#0ae6
/zerogurmukhi 16#0a66
/zerohackarabic 16#0660
/zeroinferior 16#2080
/zeromonospace 16#ff10
/zerooldstyle 16#f730
/zeropersian 16#06f0
/zerosuperior 16#2070
/zerothai 16#0e50
/zerowidthjoiner 16#feff
/zerowidthnonjoiner 16#200c
/zerowidthspace 16#200b
/zeta 16#03b6
/zhbopomofo 16#3113
/zhearmenian 16#056a
/zhebrevecyrillic 16#04c2
/zhecyrillic 16#0436
/zhedescendercyrillic 16#0497
/zhedieresiscyrillic 16#04dd
/zihiragana 16#3058
/zikatakana 16#30b8
/zinorhebrew 16#05ae
/zlinebelow 16#1e95
/zmonospace 16#ff5a
/zohiragana 16#305e
/zokatakana 16#30be
/zparen 16#24b5
/zretroflexhook 16#0290
/zstroke 16#01b6
/zuhiragana 16#305a
/zukatakana 16#30ba
.dicttomark readonly def
/currentglobal where
{pop currentglobal{setglobal}true setglobal}
{{}}
ifelse
/MacRomanEncoding .findencoding
/MacGlyphEncoding
/.notdef/.null/CR
4 index 32 95 getinterval aload pop
99 index 128 45 getinterval aload pop
/notequal/AE
/Oslash/infinity/plusminus/lessequal/greaterequal
/yen/mu1/partialdiff/summation/product
/pi/integral/ordfeminine/ordmasculine/Ohm
/ae/oslash/questiondown/exclamdown/logicalnot
/radical/florin/approxequal/increment/guillemotleft
/guillemotright/ellipsis/nbspace
174 index 203 12 getinterval aload pop
/lozenge
187 index 216 24 getinterval aload pop
/applelogo
212 index 241 7 getinterval aload pop
/overscore
220 index 249 7 getinterval aload pop
/Lslash/lslash/Scaron/scaron
/Zcaron/zcaron/brokenbar/Eth/eth
/Yacute/yacute/Thorn/thorn/minus
/multiply/onesuperior/twosuperior/threesuperior/onehalf
/onequarter/threequarters/franc/Gbreve/gbreve
/Idotaccent/Scedilla/scedilla/Cacute/cacute
/Ccaron/ccaron/dmacron
260 -1 roll pop
258 packedarray
7 1 index .registerencoding
.defineencoding
exec

%%BeginResource: procset (PDF Font obj_11)
11 0 obj
<</R9
9 0 R/R7
7 0 R>>
endobj
%%EndResource
%%BeginResource: file (PDF CharProc obj_6)
6 0 obj
<</Filter[/ASCII85Decode
/LZWDecode]/Length 32>>stream
J.)Pl,9Xc90Gb-%0K<Se'b(]kc(M!@~>
endstream
endobj
%%EndResource
%%BeginResource: procset (PDF Font obj_9)
9 0 obj
<</BaseFont/YBDAWZ+CMSY5/FontDescriptor 10 0 R/Type/Font
/FirstChar 0/LastChar 0/Widths[
1083]
/Encoding 14 0 R/Subtype/Type1>>
endobj
%%EndResource
%%BeginResource: encoding (PDF Encoding obj_14)
14 0 obj
<</Type/Encoding/Differences[
0/minus]>>
endobj
%%EndResource
%%BeginResource: procset (PDF Font obj_7)
7 0 obj
<</BaseFont/DHFNQQ+CMR5/FontDescriptor 8 0 R/Type/Font
/FirstChar 48/LastChar 56/Widths[
680 680 680 0 680 0 680 0 680]
/Subtype/Type1>>
endobj
%%EndResource
%%BeginResource: file (PDF FontDescriptor obj_10)
10 0 obj
<</Type/FontDescriptor/FontName/YBDAWZ+CMSY5/FontBBox[0 0 923 278]/Flags 4
/Ascent 278
/CapHeight 278
/Descent 0
/ItalicAngle 0
/StemV 138
/MissingWidth 500
/CharSet(/minus)/FontFile 12 0 R>>
endobj
%%EndResource
%%BeginResource: file (PDF FontFile obj_12)
12 0 obj
<</Filter[/ASCII85Decode
/LZWDecode]
/Length1 551
/Length2 1618
/Length3 533/Length 2168>>stream
J-eUh@sPTJ<'6ZZUCh1X>VI0(#\;AOA82GgN'mk;8W;jU0c<[IPAhpW"G7=]`aa7DCE&;Sb_K,M
JjgrK'920Gl'+F]+e-a^PEDZdBTJJ`"u>,45VX7=7cM5aaC$\AL^5?V/JQ352[B_e';;)AD6cZ*
-G]^VhIAC1:5=r8.!\?E*!\h#H/AHO0UkgHj*A_k5,Qj?k*mcLUh)R<:<$3TJl*U;?t[kHW75*J
1)Wd6@Q"A;CcpiqPPPTmi,q(^Ce"@0aPEN:Bb%#XAu^8EMFFum1F]pcE\a"UOBk7mF!lR12o5TE
E\5SaMo_dU*n!SlWf9Kb!t!'0@6B-r'`STSpDT3.:BCVIi5THq$]WWK$(+!0Ne?O\Q4?/XAt$DA
GhS;jURV88!DZ"Hjt;_\8Ed-bU5Jc)@=fO;,>VQ5&&]Uk?nR*DPEn-fYd:!T#XT*Zq2\sI1_tii
TP17D6,5`)LEVT['gdr!/qY;Q;qVro!t%SOTdfTb(CG-.hDL7d\C*,eZC>khNit6r3[9IPki2\B
"N5p$brU5-^8!*r"UMr3MAqAfaCZY&Tn%e6LgdfF*!ZLpE#VRr^gnSmJe2Z8LdE5;(^Rs>NCV;l
Y\k<_U*+Lg#bdVt)[8-LE#a\.&;N=QJ5Cn<7m\&G,).8FL)U?5R"u[d!N:#V#2u52(^OS!A0TC[
.#0tl+ioT]es(3N(^M:@AP36dW/;SC@AmQX#gqjD,(WN$@nkVkI@;:5iLR+q#6CAR)+R"Ms$'O"
:ifkGQe`u.#XSfL:tsUSEHBLV=CNK/G+]RTfN#^t+GPGtAfB9"W,#Rm@?)fT?9uA@G6g$5@Nl"U
/h.]>eIZG@#6G`W)@41sAOHf'^2"^>6,\9u=/$Og8-m'A(L76r*IK$V68C>\#,/^<NomnWL*?n<
]shoJ!QDm"fiRT>,$]]4MjjnX[.+ug+iTZj>*p.?)e?L>F.ie1NHn'.dLO86ck]o3)i0'BNGO>O
m=5?G];8m[#/V)D*/HK7gLp#,L+?&1(N+%5cQSU0M?^P$(J4nbAK?;UJZTV0"uLH^+b;qNNO4LN
(hl7iF6nJsLjAZqDdXGG,=15""f%mW_4Y:BdZk)pPMM^!BBNekL_Z@n_DRs9<YTrQ`nhcO7E4@r
^<@NGe3.ajEWM_<euBSC'jZP/`W]$&f-[fm(`L59^ua"K(E*bb$i,,,h--RbD!XJ22c1!?B"80Y
kaoC>E$\q#WPtm7h4gZH9/mfAPs!Q`l@oR=[HFnnCp2>$Z==7*+K=!<!0eToAIQp([ub'megfof
XNZVj<DjC$U@<C8*LqctDQqVphLUPaWIoA7gsmYEKl%\jK4'S4(lN89CK[*Z&/5]*)Nn:hqAt;d
BW9\UqcL#S+?YC_e6.-D"p!IH/p>ISe<6b<:"`1Pokq9Gds4pN`Kj$4K")go14\ITd5mU4hGt;T
<@bp:3>Q5>*33_3c:(k\V.dEU_mNUI=^ifq(?AEjr<A,cT._K1dTt;k7Nhps/[uW+=jno+Do$cO
7?jL@F&S>RfucAk,+V`.p1_r(TM-XCSX9d6@*6nJ3+Zb4<2u:#00Q,1<Gm&Se>Za`_.5D@P3#kn
WJ@NSd0(#?%\S^IGTdi!U]t/.e,kM5MP]7HQ+Y2hMmRcPaW.fZljR='\<EMAIL><gE=UM$7
<EMAIL>`-<WN>P+7dhbu@,D/u0Rt<X+I2>/83[OB$ml=%[J%ZSghI<V&tZl92PgjSInB
b/4F%D-JP[j"D/,p1+Ya?:!\h/7;FaS-jO=.#?6CQL_S"!>TQ7f-;<h9AWF$OkhU&FOjC>/73.W
H7+FMdUCW.Kl7lK1K>^"g.ZmRcfSB1V)Yk0)b"b+32kK^r2s+`=He_R.m\<VpQcb1T92!BKGsk3
h:akgY.8%<B&LVR:dfWt'l0j=7-:6?HYkU=Ft,A<UZ?4%CT;*9X7\PLTX;1?c:O#/V]]!\b;j6O
S^PCZ?OH!1*`&e!MF(gkj]P7P<_c(\.p?o](BjJG$Ec07ZuiX?K8"J-R7fm>Lp5A9oMn0`S#;)9
`MeB^NQV2q0@mf&>"cXo7:-+(>Rc8R7L?n50*P`G)2%F5&]+6($"7'jKJ98>_cUgY@@T?5n?*hN
(lnl1/4k]f4sn5cX;E:GfQ'?@F:I?)3<l]%5X5~>
endstream
endobj
%%EndResource
%%BeginResource: file (PDF FontDescriptor obj_8)
8 0 obj
<</Type/FontDescriptor/FontName/DHFNQQ+CMR5/FontBBox[0 -22 617 677]/Flags 65568
/Ascent 677
/CapHeight 677
/Descent -22
/ItalicAngle 0
/StemV 92
/MissingWidth 500
/CharSet(/eight/four/one/six/two/zero)/FontFile 13 0 R>>
endobj
%%EndResource
%%BeginResource: file (PDF FontFile obj_13)
13 0 obj
<</Filter[/ASCII85Decode
/LZWDecode]
/Length1 631
/Length2 3258
/Length3 533/Length 3582>>stream
J-eUh@sPTJ<'6ZZUCh1X>VI0(#\;AFe.aVSL^]$%):1WX17?B6i)%,o"sf[s`pj&[2&qYp%]9U1
L.<O;)pXS`."G,]2k1C+,>"EQ*\Id.A\gof@Q>Ef`I\,P^gK/6&5B$*+cO9U&/._b6U?sk@u_C#
Jc_@Q`b@3!%jD?Onr@fO%Y`p6^%>Q<2Oqb+o9#Pk9W=T-2F.@7b&a^+N2AlDSifc`:L\<@FYq0N
1J(-i"%f`gR'"]5#1J.Ql!1H^oB.?AHU4AeRgkgCm/ACjgLs9Xm%MMC@5:,jdB1'M$?Y,1"o5]P
ImPe4RuD\C)4mca)[Hj",E[ZM$5c^Ro&JM;8"VmL%&\^qUSKM$::e:OC!c\f-"E#NR3mKXE9Rm*
i`Q2qBp_.(.fjr'T#$L4>4dO/T^N&lct[54L/*n43f["s3su7dUSKo008qCd4:[IqU8[T9kQNrk
6^5-mbnH\R%Z2Lj/VXOU@#VcS9T:;#!J%`m7kR?fji=NE$m>mDHf@2^Y=Mh7#-UR,)28`"D_Srs
N-=>Em'71V4]85O0ViAC&tZclW2Cbtj&jGSN?]Yc4LX8i8E1i6g,f$hJ5m$K3+JW*d\iijjt_m'
:lH9JTt$%$L12io(^NEpL)Ypl^i1+q!OY*2#!rl7+U4X=KH^[fJ;b?OeZD(S"sLXp+pTk_MA_.W
Y[^lB!Q^-A#^#!\"UIDuBGq.c&;h<Z_AI).#%A]k*!W+XA/g8T+CH@KTob15LnYnX+GV-pDf+'_
LgV?[+dI=/LnW(70Sk<<Y>pCO;o)kt_8WI$#U-]jIgCV@B42!F\;aD9QF5]lg[K`1>iEO*K4E+c
!-)DsQ&&lni&B:'E*YBsA0?VXJ7]_T1tZS]#[ra6).MR?Bo.X'OCBC<<F0\_>dcn3N/%HS;BlHU
Q!>J6]"i>)!C=M>HJF)hM-C8&%:a@3f2]s:e,hB[;$N`-=.nj\0QFFTJf:bAbVDBV]i0M&+f:'_
88iZe+bHao#XOa:)2C7DLN6^Q*_fO>GE$C3<d&ZZc!FJ24c"nO-6sGbfJVB@kF$R2+P.AoKjrqk
%?_[i>0(brl=%B<aTaZ,=lP69*P7hOG.qC/;dk35UkX4"K@u#<c':*LJh0iq?[A8W:3sBu"<%L<
-^F</27B4;B*'^IH7C)5_7loL<Rk+9Et)lmPl"B^9NA9rV.fgn.dU;30p/pK:_#f:_HkCuF"7fZ
mZhS0#,'lX1j?7_Q.Rd,@2'X_=>$Tl'..$9q0.K,:=g6_f36-V>V-[c\K@$7H9g/\_Bl2oJhROb
JDl\Xj]^2>)n/HIn09dpT[(BS8S7DT;(>Co%A.DNBBjbj[o]m=+=eCK9ncKX<LaVe]@R<^X;JCD
db`:OVfM(pW&[bY\Qo9p?r"u)/XEIHg>a<2M]rY@[a#<hH^AZdacHT.X:=2Wl%G`BWIm*)'L@V\
9WOkqWsJ?uUdFBWCcT!JB)2qg:c23;d@<"HD"V%h3+I!.fq&@,g/aC^KT*`u2sDg!@"PCtc"SE(
Q]eM'$pg$'!M(e8Wi^i67!4B4Q)ekYiIWS:EAM09VDZf!eFI!L$?g:h8?jEVX%Yhl2Bb"kjV([q
?Z&NN1dMAVct-Zr>X68AK*Y#uSCCZ$;"7H%7*_r@jO7`l?*Ldap(8'3(^B:Y#V<uK[bXQdGq12_
.lF!36(9B-.^@jF-I1M;A:%g(cklb[L^fUd3F^7tCbOEng63GW=GiAc^LpHfeME\8M34`)$2D7I
!qnn&ELfYYKE@aFh,[2=Wii@0m/aNe>4+jOX#7W<;^8)Cb;lbY:l+bs4Hs'mM8$4PC>j`20`b.K
'2<U5j/@S?9T,HIcP>0f<9hG%P@tf@#5erS,I`7R*Jsr47&M1Y04?KuBiaC0ZYOeE8F;u%/QLl8
>Vis?b>iX8Kqd$t,E?r,eK_NRM#F"$F($RJCb&'2A!ZDO;jG$%/seE>g0LG?D"09_Br(T7C7ou+
Jpo2JY-X[fabWSI[`JntXJ2/QK'MTb9#])\eS=GR19n0b`pF.GP\ZBhJul6:UFMnb0:r]26VAl'
#c[__>I%-N4B@p4di/*pE'[Ym7Q.U%*$en7opn:8k-o!1/!8"<hCGF%s-A;%Ab5FdYNR-'(E0f:
Kg@Gq`5+1Xl7%TJ\c"dJP,j0C;0a[3>HLk&Wa;2$>7U1.4BIFdf*dUDmr<P)],\;O/9hBgG54;@
%9_;@'90#c=A-aYm]^6M._*=t>IUE4>O&Y?D<[1n-PI?TgLcu!/UKKZFR!>Q'40+dE\->"J!k-+
<`>Gi1mQYuUrp/P%:q:Q2Q!@A]7"?N`Ih(p70_rOUNm99E\p77es6a#P1feT\E]=4>)Pt3AR56e
LjHY(SQSmnfom-i1pL+u92uh2GtA==,qRMW>]$<[0P+kjQT?R0[EVl3f6#C^9\4o3fqlCDZWp=Z
'699h'.Lo0F($R[E7(*f'l)$I+M.)HMKSeLG')hV79*[f,?.6G@bkU+cZnbE:/0mqVegDDL(^Jf
2J'`6J5e.)$i8;OG+di2Fbje4MM^N8<8QWp9iid+FKF2tRFn*DBU#BM/_XJ@(&I!Wa+I\tf`9<L
1M:T"SMIYsKYJlii,VGq/%h-8n)]6'=o?t*/%XTgJYRO;CPK=VXH:jJnuJ'KNNoX/3AOTua_o;h
?7QgEn(bT`!DCsa0ma8;<mkVola=pKQqJhFe<VQKH-45d7YDu_HeV9^N6(-9Etm.PjXn;tZ4>CH
S$#<MX"JTf"_amo3k8sag8sX`e4(T.Y@DD4M_,*3Z;g-i>U;nL\Yg6;(K/Oj$YA9Y[H03s.rAl.
=f-4d$G;`Ug'Dgf!3@FSZlsPS>d'hPBND4;Ji-\i]b.,f"dXrf,DfAO8S>*6J>f>ChIu2bAus_G
Qk9S=>1=A?e&>6D=(Rh;/XjU.H!%LU]c:e94g`&HRn=DC(2/Xea"%Gr\(E:OT:NgXY(eK-L'ih$
%-?WQ>u&='`V=7[YQlc^AL?JO.5G1hJpM8*o4TcCBn5OQ_PAj/;i)&b"cA<j<3bgG@Rb<Q-AZ$,
DJW!UM5a`b-?<b]Qu>C<?cB$M#2k;nRT=#M?jBAojd^*T=t1i4K'!c-c;KMaL0\BU.].ZTB%2Tn
c;uBhK.21U`B[GBp6*CXfk-P'Ur^76:l=B0C'T33LT5%,AErBoo^<iHjt>jE+Bt[E]PW8*l=,Fm
GZJO7AnI-6Bqge;Y.7&[=X^Li"kHr_DHhhKhhECfCf4=<m!0mHG(p?KWUeeYjEW)POiiJb-.NB7
:j7ZZXN(+q-B>1h`,X)4KZ.D#V<G^m;HF%'ROGD5`km)G13kL(;l/Q7%t\c".&DNeAB':Y"tF>c
8p_rZAuQ@2Lc3Op/o0qnP:JL%APtrCDW$,JMQ+Trk)1-)G)`Js=R!88`XF?eDG14[Q:4F"C0Y&%
XSN,dVlfRqNE/U3bmJ1/>V$M$V,CTPb%8_GF%<m=.U[<m;JSm1+/YK3,L@pg,pI@Y,-idEnhD@E
0d8A1,I0HWniJ(Z5pUJB,LT"+Z;Do',p`%]-*g#eP$KCi2'rrI-F,-KP&2Qr=q"fk38ij-JDMj8
j:,K2+<U~>
endstream
endobj
%%EndResource
%%BeginResource: file (PDF object obj_1)
1 0 obj
<<>>endobj
%%EndResource
%%EndProlog
%%Page: 1 1
%%BeginPageSetup
4 0 obj
<</Type/Page/MediaBox [0 0 387.31 292.54]
/Parent 3 0 R
/Resources<</ProcSet[/PDF]
/Font 11 0 R
>>
/Contents 5 0 R
>>
endobj
%%EndPageSetup
5 0 obj
<</Length 32484>>stream
q 0.012 0 0 0.012 0 0 cm
q
0 24337 32276 -24337 re
W n
1 G
1 g
0 -42 32275.6 24378.2 re
f
1915.55 21594.2 29760 2092.08 re
f
0 G
0 g
3268.27 21594.3 m
3268.27 21302.6 l
f
false setstrokeadjust
66.6667 w
1 j
3268.27 21594.3 m
3268.27 21302.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 21594.3 m
8733.83 21302.6 l
f
false setstrokeadjust
66.6667 w
1 j
8733.83 21594.3 m
8733.83 21302.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 21594.3 m
14199.4 21302.6 l
f
false setstrokeadjust
66.6667 w
1 j
14199.4 21594.3 m
14199.4 21302.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 21594.3 m
19665 21302.6 l
f
false setstrokeadjust
66.6667 w
1 j
19665 21594.3 m
19665 21302.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 21594.3 m
25130.5 21302.6 l
f
false setstrokeadjust
66.6667 w
1 j
25130.5 21594.3 m
25130.5 21302.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 21594.3 m
30596.1 21302.6 l
f
false setstrokeadjust
66.6667 w
1 j
30596.1 21594.3 m
30596.1 21302.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 21684.3 m
1623.88 21684.3 l
f
false setstrokeadjust
66.6667 w
1 j
1915.55 21684.3 m
1623.88 21684.3 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 22638.2 m
1623.88 22638.2 l
f
false setstrokeadjust
66.6667 w
1 j
1915.55 22638.2 m
1623.88 22638.2 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 23592 m
1623.88 23592 l
f
false setstrokeadjust
66.6667 w
1 j
1915.55 23592 m
1623.88 23592 l
S
Q
false setstrokeadjust
q
1915.58 21594.3 29760 2092.08 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 21693.8 m
3541.55 23571.7 l
3814.84 21696.6 l
4088.11 23591.2 l
4361.39 21691.2 l
4634.67 23573.2 l
4907.95 21698.8 l
5181.23 23589.1 l
5454.5 21689.6 l
5727.78 23575.8 l
6001.06 21699.8 l
6274.34 23586.3 l
6547.62 21689.3 l
6820.89 23578.5 l
7094.17 21699.3 l
7367.45 23583.8 l
7640.73 21690.4 l
7914.01 23580.6 l
8187.29 21697.8 l
8460.57 23582.2 l
8733.84 21692.1 l
9007.12 23581.7 l
9280.4 21696 l
9553.68 23581.6 l
9826.96 21693.8 l
10100.2 23581.9 l
10373.5 21694.5 l
10646.8 23581.9 l
10920.1 21695 l
11193.3 23581.4 l
11466.6 21693.7 l
11739.9 23582.4 l
12013.2 21695.4 l
12286.5 23580.8 l
12559.7 21693.7 l
12833 23582.9 l
13106.3 21695.2 l
13379.6 23580.6 l
13652.9 21694 l
13926.1 23582.9 l
14199.4 21694.8 l
14472.7 23580.8 l
14746 21694.4 l
15019.2 23582.5 l
15292.5 21694.5 l
15565.8 23581.3 l
15839.1 21694.6 l
16112.4 23581.9 l
16385.6 21694.5 l
16658.9 23581.8 l
16932.2 21694.4 l
17205.5 23581.5 l
17478.7 21694.7 l
17752 23582.1 l
18025.3 21694.2 l
18298.6 23581.4 l
18571.9 21695 l
18845.1 23582.1 l
19118.4 21693.9 l
19391.7 23581.5 l
19665 21695.1 l
19938.3 23581.9 l
20211.5 21693.9 l
20484.8 23581.7 l
20758.1 21695 l
21031.4 23581.8 l
21304.6 21694.2 l
21577.9 23581.8 l
21851.2 21694.7 l
22124.5 23581.7 l
22397.8 21694.5 l
22671 23581.8 l
22944.3 21694.4 l
23217.6 23581.8 l
23490.9 21694.7 l
23764.1 23581.6 l
24037.4 21694.2 l
24310.7 23582 l
24584 21694.8 l
24857.3 23581.4 l
25130.5 21694.2 l
25403.8 23582.2 l
25677.1 21694.8 l
25950.4 23581.3 l
26223.6 21694.3 l
26496.9 23582.3 l
26770.2 21694.7 l
27043.5 23581.2 l
27316.8 21694.4 l
27590 23582.3 l
27863.3 21694.6 l
28136.6 23581.2 l
28409.9 21694.4 l
28683.2 23582.3 l
28956.4 21694.5 l
29229.7 23581.1 l
29503 21694.5 l
29776.3 23582.5 l
30049.5 21694.4 l
30322.8 23580.9 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 21594.2 m
1915.55 23686.3 l
S
31675.6 21594.2 m
31675.6 23686.3 l
S
1915.55 21594.2 m
31675.6 21594.2 l
S
1915.55 23686.3 m
31675.6 23686.3 l
S
1 G
1 g
1915.55 19083.8 29760 2092.07 re
f
0 G
0 g
3268.27 19083.8 m
3268.27 18792.1 l
f
0 J
1 j
3268.27 19083.8 m
3268.27 18792.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 19083.8 m
8733.83 18792.1 l
f
66.6667 w
1 j
8733.83 19083.8 m
8733.83 18792.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 19083.8 m
14199.4 18792.1 l
f
66.6667 w
1 j
14199.4 19083.8 m
14199.4 18792.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 19083.8 m
19665 18792.1 l
f
66.6667 w
1 j
19665 19083.8 m
19665 18792.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 19083.8 m
25130.5 18792.1 l
f
66.6667 w
1 j
25130.5 19083.8 m
25130.5 18792.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 19083.8 m
30596.1 18792.1 l
f
66.6667 w
1 j
30596.1 19083.8 m
30596.1 18792.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 19169.9 m
1623.88 19169.9 l
f
66.6667 w
1 j
1915.55 19169.9 m
1623.88 19169.9 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 20125.6 m
1623.88 20125.6 l
f
66.6667 w
1 j
1915.55 20125.6 m
1623.88 20125.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 21081.3 m
1623.88 21081.3 l
f
66.6667 w
1 j
1915.55 21081.3 m
1623.88 21081.3 l
S
Q
q
1915.58 19083.8 29760 2092.08 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 19179.5 m
3541.55 21080.7 l
3814.84 19179.6 l
4088.11 21079.4 l
4361.39 19178.9 l
4634.67 21077.6 l
4907.95 19179.4 l
5181.23 21075.1 l
5454.5 19178.8 l
5727.78 21073.3 l
6001.06 19180 l
6274.34 21070.6 l
6547.62 19180 l
6820.89 21069.8 l
7094.17 19181.5 l
7367.45 21067.7 l
7640.73 19182 l
7914.01 21068 l
8187.29 19183.2 l
8460.57 21066.5 l
8733.84 19183.8 l
9007.12 21067.2 l
9280.4 19184.3 l
9553.68 21066.2 l
9826.96 19184.6 l
10100.2 21066.9 l
10373.5 19184.5 l
10646.8 21066.3 l
10920.1 19184.7 l
11193.3 21066.8 l
11466.6 19184.4 l
11739.9 21066.6 l
12013.2 19184.6 l
12286.5 21066.8 l
12559.7 19184.5 l
12833 21066.8 l
13106.3 19184.5 l
13379.6 21066.8 l
13652.9 19184.6 l
13926.1 21066.8 l
14199.4 19184.5 l
14472.7 21066.8 l
14746 19184.9 l
15019.2 21066.8 l
15292.5 19184.7 l
15565.8 21066.9 l
15839.1 19185.2 l
16112.4 21067 l
16385.6 19185.1 l
16658.9 21067.2 l
16932.2 19185.4 l
17205.5 21067.1 l
17478.7 19185.3 l
17752 21067.2 l
18025.3 19185.3 l
18298.6 21066.8 l
18571.9 19184.9 l
18845.1 21066.4 l
19118.4 19184.3 l
19391.7 21065.7 l
19665 19183.6 l
19938.3 21064.7 l
20211.5 19182.7 l
20484.8 21063.9 l
20758.1 19181.9 l
21031.4 21062.9 l
21304.6 19181.2 l
21577.9 21062.5 l
21851.2 19180.9 l
22124.5 21062.2 l
22397.8 19181 l
22671 21062.7 l
22944.3 19181.5 l
23217.6 21063.3 l
23490.9 19182.4 l
23764.1 21064.4 l
24037.4 19183.3 l
24310.7 21065.3 l
24584 19184.3 l
24857.3 21066.3 l
25130.5 19185 l
25403.8 21067 l
25677.1 19185.5 l
25950.4 21067.3 l
26223.6 19185.6 l
26496.9 21067.3 l
26770.2 19185.4 l
27043.5 21067.2 l
27316.8 19185.2 l
27590 21067 l
27863.3 19184.9 l
28136.6 21066.8 l
28409.9 19184.7 l
28683.2 21066.6 l
28956.4 19184.6 l
29229.7 21066.6 l
29503 19184.5 l
29776.3 21066.6 l
30049.5 19184.7 l
30322.8 21066.6 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 19083.8 m
1915.55 21175.8 l
S
31675.6 19083.8 m
31675.6 21175.8 l
S
1915.55 19083.8 m
31675.6 19083.8 l
S
1915.55 21175.8 m
31675.6 21175.8 l
S
1 G
1 g
1915.55 16573.3 29760 2092.07 re
f
0 G
0 g
3268.27 16573.3 m
3268.27 16281.6 l
f
0 J
1 j
3268.27 16573.3 m
3268.27 16281.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 16573.3 m
8733.83 16281.6 l
f
66.6667 w
1 j
8733.83 16573.3 m
8733.83 16281.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 16573.3 m
14199.4 16281.6 l
f
66.6667 w
1 j
14199.4 16573.3 m
14199.4 16281.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 16573.3 m
19665 16281.6 l
f
66.6667 w
1 j
19665 16573.3 m
19665 16281.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 16573.3 m
25130.5 16281.6 l
f
66.6667 w
1 j
25130.5 16573.3 m
25130.5 16281.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 16573.3 m
30596.1 16281.6 l
f
66.6667 w
1 j
30596.1 16573.3 m
30596.1 16281.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 16660.1 m
1623.88 16660.1 l
f
66.6667 w
1 j
1915.55 16660.1 m
1623.88 16660.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 17615.4 m
1623.88 17615.4 l
f
66.6667 w
1 j
1915.55 17615.4 m
1623.88 17615.4 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 18570.8 m
1623.88 18570.8 l
f
66.6667 w
1 j
1915.55 18570.8 m
1623.88 18570.8 l
S
Q
q
1915.58 16573.3 29760 2092.08 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 16669.6 m
3541.55 18570.2 l
3814.84 16669.3 l
4088.11 18568.7 l
4361.39 16668.8 l
4634.67 18566.6 l
4907.95 16668.4 l
5181.23 18564.3 l
5454.5 16668.4 l
5727.78 18562.5 l
6001.06 16669 l
6274.34 18561.1 l
6547.62 16670.2 l
6820.89 18560.3 l
7094.17 16671.7 l
7367.45 18559.7 l
7640.73 16673.5 l
7914.01 18559.2 l
8187.29 16675.1 l
8460.57 18558.3 l
8733.84 16676.5 l
9007.12 18557.2 l
9280.4 16677.4 l
9553.68 18555.8 l
9826.96 16678.1 l
10100.2 18554.4 l
10373.5 16678.5 l
10646.8 18553.3 l
10920.1 16678.9 l
11193.3 18552.3 l
11466.6 16679 l
11739.9 18551.9 l
12013.2 16679.3 l
12286.5 18551.6 l
12559.7 16679.4 l
12833 18551.5 l
13106.3 16679.4 l
13379.6 18551.5 l
13652.9 16679.2 l
13926.1 18551.4 l
14199.4 16679 l
14472.7 18551.3 l
14746 16678.8 l
15019.2 18551.2 l
15292.5 16678.7 l
15565.8 18551.1 l
15839.1 16678.7 l
16112.4 18551.1 l
16385.6 16678.9 l
16658.9 18551.3 l
16932.2 16679.1 l
17205.5 18551.5 l
17478.7 16679.4 l
17752 18551.8 l
18025.3 16679.5 l
18298.6 18552 l
18571.9 16679.3 l
18845.1 18552.2 l
19118.4 16678.7 l
19391.7 18552.1 l
19665 16677.7 l
19938.3 18551.9 l
20211.5 16676.3 l
20484.8 18551.4 l
20758.1 16674.8 l
21031.4 18551 l
21304.6 16673.4 l
21577.9 18550.7 l
21851.2 16672.5 l
22124.5 18550.7 l
22397.8 16672.4 l
22671 18551 l
22944.3 16673.1 l
23217.6 18551.6 l
23490.9 16674.4 l
23764.1 18552.3 l
24037.4 16676 l
24310.7 18552.7 l
24584 16677.5 l
24857.3 18552.8 l
25130.5 16678.6 l
25403.8 18552.4 l
25677.1 16679 l
25950.4 18551.5 l
26223.6 16678.8 l
26496.9 18550.5 l
26770.2 16678.2 l
27043.5 18549.5 l
27316.8 16677.5 l
27590 18548.8 l
27863.3 16677.2 l
28136.6 18548.7 l
28409.9 16677.3 l
28683.2 18549 l
28956.4 16677.7 l
29229.7 18549.7 l
29503 16678.3 l
29776.3 18550.4 l
30049.5 16678.9 l
30322.8 18551.1 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 16573.3 m
1915.55 18665.3 l
S
31675.6 16573.3 m
31675.6 18665.3 l
S
1915.55 16573.3 m
31675.6 16573.3 l
S
1915.55 18665.3 m
31675.6 18665.3 l
S
1 G
1 g
1915.55 14062.8 29760 2092.07 re
f
0 G
0 g
3268.27 14062.8 m
3268.27 13771.1 l
f
0 J
1 j
3268.27 14062.8 m
3268.27 13771.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 14062.8 m
8733.83 13771.1 l
f
66.6667 w
1 j
8733.83 14062.8 m
8733.83 13771.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 14062.8 m
14199.4 13771.1 l
f
66.6667 w
1 j
14199.4 14062.8 m
14199.4 13771.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 14062.8 m
19665 13771.1 l
f
66.6667 w
1 j
19665 14062.8 m
19665 13771.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 14062.8 m
25130.5 13771.1 l
f
66.6667 w
1 j
25130.5 14062.8 m
25130.5 13771.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 14062.8 m
30596.1 13771.1 l
f
66.6667 w
1 j
30596.1 14062.8 m
30596.1 13771.1 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 14149.5 m
1623.88 14149.5 l
f
66.6667 w
1 j
1915.55 14149.5 m
1623.88 14149.5 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 15104.9 m
1623.88 15104.9 l
f
66.6667 w
1 j
1915.55 15104.9 m
1623.88 15104.9 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 16060.3 m
1623.88 16060.3 l
f
66.6667 w
1 j
1915.55 16060.3 m
1623.88 16060.3 l
S
Q
q
1915.58 14062.8 29760 2092.08 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 14159.1 m
3541.55 16059.8 l
3814.84 14158.7 l
4088.11 16058.2 l
4361.39 14158.2 l
4634.67 16056.1 l
4907.95 14157.9 l
5181.23 16053.9 l
5454.5 14157.9 l
5727.78 16052.1 l
6001.06 14158.5 l
6274.34 16050.7 l
6547.62 14159.6 l
6820.89 16049.7 l
7094.17 14160.9 l
7367.45 16048.8 l
7640.73 14162.2 l
7914.01 16047.7 l
8187.29 14163.3 l
8460.57 16046.4 l
8733.84 14164.2 l
9007.12 16044.9 l
9280.4 14164.8 l
9553.68 16043.3 l
9826.96 14165.6 l
10100.2 16041.8 l
10373.5 14166.5 l
10646.8 16040.6 l
10920.1 14167.6 l
11193.3 16039.8 l
11466.6 14169 l
11739.9 16039.1 l
12013.2 14170.5 l
12286.5 16038.6 l
12559.7 14171.7 l
12833 16038.2 l
13106.3 14172.7 l
13379.6 16037.8 l
13652.9 14173.4 l
13926.1 16037.4 l
14199.4 14173.9 l
14472.7 16037.3 l
14746 14174.1 l
15019.2 16037.3 l
15292.5 14174.3 l
15565.8 16037.5 l
15839.1 14174.3 l
16112.4 16037.8 l
16385.6 14174.1 l
16658.9 16038.2 l
16932.2 14173.7 l
17205.5 16038.5 l
17478.7 14173.1 l
17752 16038.8 l
18025.3 14172.3 l
18298.6 16039 l
18571.9 14171.2 l
18845.1 16039.1 l
19118.4 14170 l
19391.7 16039.1 l
19665 14168.5 l
19938.3 16039.1 l
20211.5 14166.9 l
20484.8 16038.9 l
20758.1 14165.4 l
21031.4 16038.7 l
21304.6 14164.1 l
21577.9 16038.6 l
21851.2 14163.2 l
22124.5 16038.7 l
22397.8 14163.1 l
22671 16039.1 l
22944.3 14163.8 l
23217.6 16039.8 l
23490.9 14165.1 l
23764.1 16040.5 l
24037.4 14166.7 l
24310.7 16040.9 l
24584 14168.3 l
24857.3 16041 l
25130.5 14169.4 l
25403.8 16040.4 l
25677.1 14169.9 l
25950.4 16039.4 l
26223.6 14169.9 l
26496.9 16038.1 l
26770.2 14169.7 l
27043.5 16037 l
27316.8 14169.7 l
27590 16036.3 l
27863.3 14170 l
28136.6 16036.1 l
28409.9 14170.7 l
28683.2 16036.5 l
28956.4 14171.7 l
29229.7 16037.2 l
29503 14172.5 l
29776.3 16037.8 l
30049.5 14172.7 l
30322.8 16038.1 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 14062.8 m
1915.55 16154.8 l
S
31675.6 14062.8 m
31675.6 16154.8 l
S
1915.55 14062.8 m
31675.6 14062.8 l
S
1915.55 16154.8 m
31675.6 16154.8 l
S
1 G
1 g
1915.55 11552.3 29760 2092.07 re
f
0 G
0 g
3268.27 11552.3 m
3268.27 11260.6 l
f
0 J
1 j
3268.27 11552.3 m
3268.27 11260.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 11552.3 m
8733.83 11260.6 l
f
66.6667 w
1 j
8733.83 11552.3 m
8733.83 11260.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 11552.3 m
14199.4 11260.6 l
f
66.6667 w
1 j
14199.4 11552.3 m
14199.4 11260.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 11552.3 m
19665 11260.6 l
f
66.6667 w
1 j
19665 11552.3 m
19665 11260.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 11552.3 m
25130.5 11260.6 l
f
66.6667 w
1 j
25130.5 11552.3 m
25130.5 11260.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 11552.3 m
30596.1 11260.6 l
f
66.6667 w
1 j
30596.1 11552.3 m
30596.1 11260.6 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 11639 m
1623.88 11639 l
f
66.6667 w
1 j
1915.55 11639 m
1623.88 11639 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 12594.4 m
1623.88 12594.4 l
f
66.6667 w
1 j
1915.55 12594.4 m
1623.88 12594.4 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 13549.8 m
1623.88 13549.8 l
f
66.6667 w
1 j
1915.55 13549.8 m
1623.88 13549.8 l
S
Q
q
1915.58 11552.3 29760 2092.08 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 11648.6 m
3541.55 13549.3 l
3814.84 11648.2 l
4088.11 13547.7 l
4361.39 11647.7 l
4634.67 13545.6 l
4907.95 11647.4 l
5181.23 13543.4 l
5454.5 11647.4 l
5727.78 13541.6 l
6001.06 11648 l
6274.34 13540.2 l
6547.62 11649.1 l
6820.89 13539.2 l
7094.17 11650.4 l
7367.45 13538.4 l
7640.73 11651.9 l
7914.01 13537.5 l
8187.29 11653.1 l
8460.57 13536.4 l
8733.84 11654.3 l
9007.12 13535.2 l
9280.4 11655.3 l
9553.68 13533.9 l
9826.96 11656.4 l
10100.2 13532.7 l
10373.5 11657.6 l
10646.8 13531.6 l
10920.1 11658.8 l
11193.3 13530.5 l
11466.6 11659.9 l
11739.9 13529.3 l
12013.2 11660.9 l
12286.5 13527.9 l
12559.7 11661.5 l
12833 13526.3 l
13106.3 11661.9 l
13379.6 13524.7 l
13652.9 11662.2 l
13926.1 13523.3 l
14199.4 11662.5 l
14472.7 13522.5 l
14746 11663 l
15019.2 13522.2 l
15292.5 11663.7 l
15565.8 13522.7 l
15839.1 11664.2 l
16112.4 13523.6 l
16385.6 11664.5 l
16658.9 13524.8 l
16932.2 11664.3 l
17205.5 13526 l
17478.7 11663.5 l
17752 13527.2 l
18025.3 11662.3 l
18298.6 13528.2 l
18571.9 11660.8 l
18845.1 13528.9 l
19118.4 11659.2 l
19391.7 13529.3 l
19665 11657.5 l
19938.3 13529.4 l
20211.5 11656 l
20484.8 13529.3 l
20758.1 11654.7 l
21031.4 13528.9 l
21304.6 11653.9 l
21577.9 13528.5 l
21851.2 11653.6 l
22124.5 13528.2 l
22397.8 11653.9 l
22671 13528.2 l
22944.3 11654.9 l
23217.6 13528.5 l
23490.9 11656.3 l
23764.1 13528.9 l
24037.4 11657.9 l
24310.7 13529.3 l
24584 11659.2 l
24857.3 13529.3 l
25130.5 11659.9 l
25403.8 13528.9 l
25677.1 11659.9 l
25950.4 13528 l
26223.6 11659.4 l
26496.9 13526.8 l
26770.2 11658.7 l
27043.5 13525.8 l
27316.8 11658.2 l
27590 13525 l
27863.3 11658.3 l
28136.6 13524.8 l
28409.9 11659 l
28683.2 13525 l
28956.4 11660.1 l
29229.7 13525.5 l
29503 11661.3 l
29776.3 13525.9 l
30049.5 11662.1 l
30322.8 13526.1 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 11552.3 m
1915.55 13644.4 l
S
31675.6 11552.3 m
31675.6 13644.4 l
S
1915.55 11552.3 m
31675.6 11552.3 l
S
1915.55 13644.4 m
31675.6 13644.4 l
S
1 G
1 g
1915.55 9041.79 29760 2092.07 re
f
0 G
0 g
3268.27 9041.75 m
3268.27 8750.08 l
f
0 J
1 j
3268.27 9041.75 m
3268.27 8750.08 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 9041.75 m
8733.83 8750.08 l
f
66.6667 w
1 j
8733.83 9041.75 m
8733.83 8750.08 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 9041.75 m
14199.4 8750.08 l
f
66.6667 w
1 j
14199.4 9041.75 m
14199.4 8750.08 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 9041.75 m
19665 8750.08 l
f
66.6667 w
1 j
19665 9041.75 m
19665 8750.08 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 9041.75 m
25130.5 8750.08 l
f
66.6667 w
1 j
25130.5 9041.75 m
25130.5 8750.08 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 9041.75 m
30596.1 8750.08 l
f
66.6667 w
1 j
30596.1 9041.75 m
30596.1 8750.08 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 9128.5 m
1623.88 9128.5 l
f
66.6667 w
1 j
1915.55 9128.5 m
1623.88 9128.5 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 10083.9 m
1623.88 10083.9 l
f
66.6667 w
1 j
1915.55 10083.9 m
1623.88 10083.9 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 11039.3 m
1623.88 11039.3 l
f
66.6667 w
1 j
1915.55 11039.3 m
1623.88 11039.3 l
S
Q
q
1915.58 9041.75 29760 2092.08 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 9138.1 m
3541.55 11038.8 l
3814.84 9137.75 l
4088.11 11037.2 l
4361.39 9137.25 l
4634.67 11035.1 l
4907.95 9136.88 l
5181.23 11032.9 l
5454.5 9136.93 l
5727.78 11031.1 l
6001.06 9137.53 l
6274.34 11029.7 l
6547.62 9138.6 l
6820.89 11028.7 l
7094.17 9139.93 l
7367.45 11027.8 l
7640.73 9141.25 l
7914.01 11026.7 l
8187.29 9142.35 l
8460.57 11025.4 l
8733.84 9143.19 l
9007.12 11023.9 l
9280.4 9143.87 l
9553.68 11022.3 l
9826.96 9144.57 l
10100.2 11020.8 l
10373.5 9145.49 l
10646.8 11019.7 l
10920.1 9146.67 l
11193.3 11018.8 l
11466.6 9148.05 l
11739.9 11018.2 l
12013.2 9149.47 l
12286.5 11017.7 l
12559.7 9150.75 l
12833 11017.2 l
13106.3 9151.76 l
13379.6 11016.8 l
13652.9 9152.46 l
13926.1 11016.5 l
14199.4 9152.91 l
14472.7 11016.3 l
14746 9153.16 l
15019.2 11016.3 l
15292.5 9153.28 l
15565.8 11016.5 l
15839.1 9153.27 l
16112.4 11016.8 l
16385.6 9153.11 l
16658.9 11017.2 l
16932.2 9152.74 l
17205.5 11017.5 l
17478.7 9152.14 l
17752 11017.8 l
18025.3 9151.31 l
18298.6 11018 l
18571.9 9150.25 l
18845.1 11018.1 l
19118.4 9148.98 l
19391.7 11018.2 l
19665 9147.52 l
19938.3 11018.1 l
20211.5 9145.94 l
20484.8 11017.9 l
20758.1 9144.39 l
21031.4 11017.7 l
21304.6 9143.07 l
21577.9 11017.6 l
21851.2 9142.25 l
22124.5 11017.7 l
22397.8 9142.14 l
22671 11018.1 l
22944.3 9142.8 l
23217.6 11018.8 l
23490.9 9144.12 l
23764.1 11019.5 l
24037.4 9145.77 l
24310.7 11020 l
24584 9147.33 l
24857.3 11020 l
25130.5 9148.45 l
25403.8 11019.4 l
25677.1 9148.96 l
25950.4 11018.4 l
26223.6 9148.96 l
26496.9 11017.1 l
26770.2 9148.74 l
27043.5 11016 l
27316.8 9148.68 l
27590 11015.3 l
27863.3 9149 l
28136.6 11015.2 l
28409.9 9149.75 l
28683.2 11015.5 l
28956.4 9150.7 l
29229.7 11016.2 l
29503 9151.48 l
29776.3 11016.8 l
30049.5 9151.75 l
30322.8 11017.1 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 9041.79 m
1915.55 11133.9 l
S
31675.6 9041.79 m
31675.6 11133.9 l
S
1915.55 9041.79 m
31675.6 9041.79 l
S
1915.55 11133.9 m
31675.6 11133.9 l
S
1 G
1 g
1915.55 6531.3 29760 2092.07 re
f
0 G
0 g
3268.27 6531.3 m
3268.27 6239.63 l
f
0 J
1 j
3268.27 6531.3 m
3268.27 6239.63 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 6531.3 m
8733.83 6239.63 l
f
66.6667 w
1 j
8733.83 6531.3 m
8733.83 6239.63 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 6531.3 m
14199.4 6239.63 l
f
66.6667 w
1 j
14199.4 6531.3 m
14199.4 6239.63 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 6531.3 m
19665 6239.63 l
f
66.6667 w
1 j
19665 6531.3 m
19665 6239.63 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 6531.3 m
25130.5 6239.63 l
f
66.6667 w
1 j
25130.5 6531.3 m
25130.5 6239.63 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 6531.3 m
30596.1 6239.63 l
f
66.6667 w
1 j
30596.1 6531.3 m
30596.1 6239.63 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 6618.09 m
1623.88 6618.09 l
f
66.6667 w
1 j
1915.55 6618.09 m
1623.88 6618.09 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 7573.46 m
1623.88 7573.46 l
f
66.6667 w
1 j
1915.55 7573.46 m
1623.88 7573.46 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 8528.83 m
1623.88 8528.83 l
f
66.6667 w
1 j
1915.55 8528.83 m
1623.88 8528.83 l
S
Q
q
1915.58 6531.33 29760 2092.09 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 6627.65 m
3541.55 8528.28 l
3814.84 6627.3 l
4088.11 8526.73 l
4361.39 6626.79 l
4634.67 8524.61 l
4907.95 6626.39 l
5181.23 8522.38 l
5454.5 6626.41 l
5727.78 8520.51 l
6001.06 6627 l
6274.34 8519.15 l
6547.62 6628.21 l
6820.89 8518.35 l
7094.17 6629.78 l
7367.45 8517.76 l
7640.73 6631.59 l
7914.01 8517.21 l
8187.29 6633.17 l
8460.57 8516.37 l
8733.84 6634.57 l
9007.12 8515.24 l
9280.4 6635.46 l
9553.68 8513.88 l
9826.96 6636.17 l
10100.2 8512.48 l
10373.5 6636.52 l
10646.8 8511.3 l
10920.1 6636.89 l
11193.3 8510.38 l
11466.6 6637.08 l
11739.9 8509.89 l
12013.2 6637.33 l
12286.5 8509.6 l
12559.7 6637.4 l
12833 8509.56 l
13106.3 6637.42 l
13379.6 8509.5 l
13652.9 6637.28 l
13926.1 8509.48 l
14199.4 6637.08 l
14472.7 8509.36 l
14746 6636.88 l
15019.2 8509.25 l
15292.5 6636.75 l
15565.8 8509.18 l
15839.1 6636.76 l
16112.4 8509.18 l
16385.6 6636.92 l
16658.9 8509.32 l
16932.2 6637.17 l
17205.5 8509.54 l
17478.7 6637.43 l
17752 8509.85 l
18025.3 6637.53 l
18298.6 8510.09 l
18571.9 6637.37 l
18845.1 8510.23 l
19118.4 6636.76 l
19391.7 8510.16 l
19665 6635.75 l
19938.3 8509.89 l
20211.5 6634.35 l
20484.8 8509.46 l
20758.1 6632.83 l
21031.4 8509.02 l
21304.6 6631.46 l
21577.9 8508.71 l
21851.2 6630.58 l
22124.5 8508.7 l
22397.8 6630.44 l
22671 8509.03 l
22944.3 6631.09 l
23217.6 8509.63 l
23490.9 6632.41 l
23764.1 8510.29 l
24037.4 6634.04 l
24310.7 8510.79 l
24584 6635.57 l
24857.3 8510.86 l
25130.5 6636.62 l
25403.8 8510.44 l
25677.1 6637.02 l
25950.4 8509.57 l
26223.6 6636.79 l
26496.9 8508.49 l
26770.2 6636.2 l
27043.5 8507.5 l
27316.8 6635.59 l
27590 8506.85 l
27863.3 6635.25 l
28136.6 8506.73 l
28409.9 6635.33 l
28683.2 8507.07 l
28956.4 6635.78 l
29229.7 8507.76 l
29503 6636.38 l
29776.3 8508.49 l
30049.5 6636.91 l
30322.8 8509.16 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 6531.3 m
1915.55 8623.38 l
S
31675.6 6531.3 m
31675.6 8623.38 l
S
1915.55 6531.3 m
31675.6 6531.3 l
S
1915.55 8623.38 m
31675.6 8623.38 l
S
1 G
1 g
1915.55 4020.81 29760 2092.08 re
f
0 G
0 g
3268.27 4020.81 m
3268.27 3729.14 l
f
0 J
1 j
3268.27 4020.81 m
3268.27 3729.14 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 4020.81 m
8733.83 3729.14 l
f
66.6667 w
1 j
8733.83 4020.81 m
8733.83 3729.14 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 4020.81 m
14199.4 3729.14 l
f
66.6667 w
1 j
14199.4 4020.81 m
14199.4 3729.14 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 4020.81 m
19665 3729.14 l
f
66.6667 w
1 j
19665 4020.81 m
19665 3729.14 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 4020.81 m
25130.5 3729.14 l
f
66.6667 w
1 j
25130.5 4020.81 m
25130.5 3729.14 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 4020.81 m
30596.1 3729.14 l
f
66.6667 w
1 j
30596.1 4020.81 m
30596.1 3729.14 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 4106.95 m
1623.88 4106.95 l
f
66.6667 w
1 j
1915.55 4106.95 m
1623.88 4106.95 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 5062.66 m
1623.88 5062.66 l
f
66.6667 w
1 j
1915.55 5062.66 m
1623.88 5062.66 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 6018.36 m
1623.88 6018.36 l
f
66.6667 w
1 j
1915.55 6018.36 m
1623.88 6018.36 l
S
Q
q
1915.58 4020.83 29760 2092.09 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 4116.52 m
3541.55 6017.79 l
3814.84 4116.61 l
4088.11 6016.51 l
4361.39 4116 l
4634.67 6014.66 l
4907.95 4116.49 l
5181.23 6012.14 l
5454.5 4115.9 l
5727.78 6010.34 l
6001.06 4117.04 l
6274.34 6007.68 l
6547.62 4117.04 l
6820.89 6006.9 l
7094.17 4118.57 l
7367.45 6004.78 l
7640.73 4119.07 l
7914.01 6005.05 l
8187.29 4120.3 l
8460.57 6003.54 l
8733.84 4120.85 l
9007.12 6004.29 l
9280.4 4121.35 l
9553.68 6003.25 l
9826.96 4121.7 l
10100.2 6003.97 l
10373.5 4121.57 l
10646.8 6003.36 l
10920.1 4121.77 l
11193.3 6003.86 l
11466.6 4121.49 l
11739.9 6003.62 l
12013.2 4121.63 l
12286.5 6003.84 l
12559.7 4121.51 l
12833 6003.82 l
13106.3 4121.54 l
13379.6 6003.82 l
13652.9 4121.68 l
13926.1 6003.87 l
14199.4 4121.59 l
14472.7 6003.82 l
14746 4121.93 l
15019.2 6003.88 l
15292.5 4121.79 l
15565.8 6003.96 l
15839.1 4122.23 l
16112.4 6004.01 l
16385.6 4122.12 l
16658.9 6004.21 l
16932.2 4122.48 l
17205.5 6004.17 l
17478.7 4122.33 l
17752 6004.22 l
18025.3 4122.33 l
18298.6 6003.88 l
18571.9 4121.93 l
18845.1 6003.45 l
19118.4 4121.39 l
19391.7 6002.72 l
19665 4120.64 l
19938.3 6001.79 l
20211.5 4119.76 l
20484.8 6000.93 l
20758.1 4118.95 l
21031.4 5999.98 l
21304.6 4118.3 l
21577.9 5999.58 l
21851.2 4117.97 l
22124.5 5999.28 l
22397.8 4118.11 l
22671 5999.74 l
22944.3 4118.56 l
23217.6 6000.31 l
23490.9 4119.46 l
23764.1 6001.41 l
24037.4 4120.38 l
24310.7 6002.38 l
24584 4121.38 l
24857.3 6003.38 l
25130.5 4122.07 l
25403.8 6004.02 l
25677.1 4122.52 l
25950.4 6004.39 l
26223.6 4122.62 l
26496.9 6004.41 l
26770.2 4122.47 l
27043.5 6004.28 l
27316.8 4122.22 l
27590 6004.01 l
27863.3 4121.94 l
28136.6 6003.85 l
28409.9 4121.73 l
28683.2 6003.69 l
28956.4 4121.7 l
29229.7 6003.7 l
29503 4121.59 l
29776.3 6003.7 l
30049.5 4121.73 l
30322.8 6003.69 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 4020.81 m
1915.55 6112.89 l
S
31675.6 4020.81 m
31675.6 6112.89 l
S
1915.55 4020.81 m
31675.6 4020.81 l
S
1915.55 6112.89 m
31675.6 6112.89 l
S
1 G
1 g
1915.55 1510.32 29760 2092.07 re
f
0 G
0 g
3268.27 1510.32 m
3268.27 1218.65 l
f
0 J
1 j
3268.27 1510.32 m
3268.27 1218.65 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
8733.83 1510.32 m
8733.83 1218.65 l
f
66.6667 w
1 j
8733.83 1510.32 m
8733.83 1218.65 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
14199.4 1510.32 m
14199.4 1218.65 l
f
66.6667 w
1 j
14199.4 1510.32 m
14199.4 1218.65 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
19665 1510.32 m
19665 1218.65 l
f
66.6667 w
1 j
19665 1510.32 m
19665 1218.65 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
25130.5 1510.32 m
25130.5 1218.65 l
f
66.6667 w
1 j
25130.5 1510.32 m
25130.5 1218.65 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
30596.1 1510.32 m
30596.1 1218.65 l
f
66.6667 w
1 j
30596.1 1510.32 m
30596.1 1218.65 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 1600.32 m
1623.88 1600.32 l
f
66.6667 w
1 j
1915.55 1600.32 m
1623.88 1600.32 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 2554.21 m
1623.88 2554.21 l
f
66.6667 w
1 j
1915.55 2554.21 m
1623.88 2554.21 l
S
Q
q
0 24337 32276 -24337 re
W n
0 G
0 g
1915.55 3508.1 m
1623.88 3508.1 l
f
66.6667 w
1 j
1915.55 3508.1 m
1623.88 3508.1 l
S
Q
q
1915.58 1510.33 29760 2092.09 re W n
125 w
2 J
1 j
0.12207 0.467041 0.705078 rg
0.12207 0.467041 0.705078 RG
3268.28 1609.86 m
3541.55 3487.76 l
3814.84 1612.71 l
4088.11 3507.3 l
4361.39 1607.24 l
4634.67 3489.29 l
4907.95 1614.91 l
5181.23 3505.17 l
5454.5 1605.63 l
5727.78 3491.84 l
6001.06 1615.83 l
6274.34 3502.43 l
6547.62 1605.41 l
6820.89 3494.57 l
7094.17 1615.37 l
7367.45 3499.91 l
7640.73 1606.46 l
7914.01 3496.71 l
8187.29 1613.89 l
8460.57 3498.26 l
8733.84 1608.2 l
9007.12 3497.82 l
9280.4 1612.08 l
9553.68 3497.68 l
9826.96 1609.91 l
10100.2 3497.93 l
10373.5 1610.59 l
10646.8 3497.93 l
10920.1 1611.07 l
11193.3 3497.44 l
11466.6 1609.82 l
11739.9 3498.53 l
12013.2 1611.46 l
12286.5 3496.87 l
12559.7 1609.76 l
12833 3498.96 l
13106.3 1611.27 l
13379.6 3496.64 l
13652.9 1610.12 l
13926.1 3498.97 l
14199.4 1610.84 l
14472.7 3496.85 l
14746 1610.51 l
15019.2 3498.57 l
15292.5 1610.55 l
15565.8 3497.36 l
15839.1 1610.65 l
16112.4 3498.02 l
16385.6 1610.56 l
16658.9 3497.88 l
16932.2 1610.51 l
17205.5 3497.59 l
17478.7 1610.8 l
17752 3498.19 l
18025.3 1610.23 l
18298.6 3497.43 l
18571.9 1611.07 l
18845.1 3498.2 l
19118.4 1610.02 l
19391.7 3497.54 l
19665 1611.19 l
19938.3 3498.02 l
20211.5 1610.02 l
20484.8 3497.74 l
20758.1 1611.07 l
21031.4 3497.84 l
21304.6 1610.24 l
21577.9 3497.87 l
21851.2 1610.79 l
22124.5 3497.79 l
22397.8 1610.55 l
22671 3497.83 l
22944.3 1610.49 l
23217.6 3497.9 l
23490.9 1610.8 l
23764.1 3497.67 l
24037.4 1610.31 l
24310.7 3498.09 l
24584 1610.89 l
24857.3 3497.47 l
25130.5 1610.29 l
25403.8 3498.26 l
25677.1 1610.86 l
25950.4 3497.35 l
26223.6 1610.36 l
26496.9 3498.34 l
26770.2 1610.77 l
27043.5 3497.31 l
27316.8 1610.45 l
27590 3498.35 l
27863.3 1610.69 l
28136.6 3497.29 l
28409.9 1610.51 l
28683.2 3498.41 l
28956.4 1610.62 l
29229.7 3497.18 l
29503 1610.62 l
29776.3 3498.57 l
30049.5 1610.45 l
30322.8 3496.96 l
S
Q
q
0 24337 32276 -24337 re
W n
66.6667 w
2 J
0 g
0 G
1915.55 1510.32 m
1915.55 3602.39 l
S
31675.6 1510.32 m
31675.6 3602.39 l
S
1915.55 1510.32 m
31675.6 1510.32 l
S
1915.55 3602.39 m
31675.6 3602.39 l
S
Q
q
0 0 m
W n
0 0 0 1 K
0 0 0 1 k
q 7410 0 0 -40 -7401 80727 cm
BI
/IM true
/W 1
/H 1
/BPC 1
/F/A85
ID
!!~>
EI Q
Q
0 0 0 RG
0 0 0 rg
q
83.3333 0 0 83.3333 0 0 cm BT
/R7 4.98132 Tf
1 0 0 1 37.524 248.672 Tm
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.649 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.6071 Td
[(0)5.47995]TJ
0 11.447 Td
[(1)5.47995]TJ
24.928 -62.829 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.603 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.629 Td
[(0)5.47995]TJ
0 11.468 Td
[(1)5.47995]TJ
24.928 -62.8261 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.611 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.625 Td
[(0)5.47995]TJ
0 11.464 Td
[(1)5.47995]TJ
24.928 -62.826 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.611 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.624 Td
[(0)5.47995]TJ
0 11.465 Td
[(1)5.47995]TJ
24.928 -62.826 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.611 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.625 Td
[(0)5.47995]TJ
0 11.464 Td
[(1)5.47995]TJ
24.928 -62.826 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.611 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.625 Td
[(0)5.47995]TJ
0 11.464 Td
[(1)5.47995]TJ
24.928 -62.826 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.611 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.625 Td
[(0)5.47995]TJ
0 11.465 Td
[(1)5.47995]TJ
24.928 -62.827 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.604 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.628 Td
[(0)5.47995]TJ
0 11.469 Td
[(1)5.47995]TJ
24.928 -62.826 Td
[(0)-12146.3(2)5.47906(0)-11812.1(4)5.47906(0)-11812.1(6)5.48142(0)-11811.9(8)5.48142(0)-11471.8(1)5.48142(0)5.48142(0)5.48142]TJ
/R9 4.98132 Tf
-30.324 10.649 Td
[<00>-1.05011]TJ
/R7 4.98132 Tf
5.4 0 Td
[(1)5.47995]TJ
-0.00398438 10.607 Td
[(0)5.47995]TJ
0 11.447 Td
[(1)5.47995]TJ
ET
Q
Q

endstream
endobj
%%PageTrailer
%%Trailer
end
cleartomark
countdictstack
exch sub { end } repeat
restore
showpage
%%EOF
