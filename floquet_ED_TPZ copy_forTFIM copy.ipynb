{"cells": [{"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy\n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["N = 4\n", "#T=t1+t2\n", "t_1 = 1\n", "t_2 = 1\n", "lam_h = 0.01\n", "lam_J = 0.1\n", "h_x=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J_z=np.pi/4 - lam_J#J*t2# 0.75"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def translate(N, state, steps):\n", "    \"\"\"平移操作（示例：循环左移steps位）\"\"\"\n", "    # 实际实现需与checkstate中的平移逻辑一致\n", "    bits = [(state >> i) & 1 for i in range(N)]\n", "    steps = steps % N\n", "    shifted_bits = bits[steps:] + bits[:steps]\n", "    return sum(bit << i for i, bit in enumerate(shifted_bits))\n", "\n", "def spin_flip_state(N, rep):\n", "    \"\"\"自旋翻转（逐位取反）\"\"\"\n", "    return rep ^ ((1 << N) - 1)\n", "\n", "def reverseBits(n, N):\n", "    \"\"\"反演操作（比特反转）\"\"\"\n", "    reversed_num = 0\n", "    for i in range(N):\n", "        reversed_num = (reversed_num << 1) | (n & 1)\n", "        n >>= 1\n", "    return reversed_num"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def translate(L, state, n_translation_sites):\\n    new_state = 0\\n    for site in range(L):\\n        site_value = get_site_value(state, site)\\n        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\\n    return new_state\\ndef spin_flip_state(N: int, rep: int) -> int:\\n    \"\"\"\\n    对N维系统中的量子态rep执行自旋翻转操作\\n    \\n    参数:\\n        N: 系统维度（自旋数量）\\n        rep: 待翻转的量子态（整数表示，二进制编码）\\n        \\n    返回:\\n        自旋翻转后的量子态（整数表示）\\n        \\n    异常:\\n        ValueError: 当rep超出N维系统的可能状态范围时抛出\\n    \"\"\"\\n    # 验证输入态的有效性\\n    max_state = (1 << N) - 1  # N个自旋的最大可能状态\\n    if rep < 0 or rep > max_state:\\n        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\\n    \\n    # 自旋翻转逻辑：翻转每个比特位（0<->1）\\n    # 构造N位全1的掩码，用于异或操作实现翻转\\n    mask = (1 << N) - 1\\n    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\\n    \\n    return flipped_rep\\ndef reverseBits(s,N):\\n    bin_chars = \"\"\\n    temp = s\\n    for i in range(N):\\n        bin_char = bin(temp % 2)[-1]\\n        temp = temp // 2\\n        bin_chars = bin_char + bin_chars\\n    bits =  bin_chars.upper()\\n    return int(bits[::-1], 2)'"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "'''def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def spin_flip_state(N: int, rep: int) -> int:\n", "    \"\"\"\n", "    对N维系统中的量子态rep执行自旋翻转操作\n", "    \n", "    参数:\n", "        N: 系统维度（自旋数量）\n", "        rep: 待翻转的量子态（整数表示，二进制编码）\n", "        \n", "    返回:\n", "        自旋翻转后的量子态（整数表示）\n", "        \n", "    异常:\n", "        ValueError: 当rep超出N维系统的可能状态范围时抛出\n", "    \"\"\"\n", "    # 验证输入态的有效性\n", "    max_state = (1 << N) - 1  # N个自旋的最大可能状态\n", "    if rep < 0 or rep > max_state:\n", "        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\n", "    \n", "    # 自旋翻转逻辑：翻转每个比特位（0<->1）\n", "    # 构造N位全1的掩码，用于异或操作实现翻转\n", "    mask = (1 << N) - 1\n", "    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\n", "    \n", "    return flipped_rep\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)'''"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def ffun(t,s,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    if t == -1:\n", "        if s == -1:\n", "            f = np.cos(kk*l)\n", "        elif s==1:\n", "            f = -np.sin(kk*l)\n", "    elif t ==1:\n", "        if s == -1:\n", "            f = np.sin(kk*l)\n", "        elif s==1:\n", "            f = np.cos(kk*l)\n", "    return f\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    g=0\n", "    if t == -1:\n", "        g = 1-np.cos(kk*l)\n", "    elif t==1:\n", "        g = 1+np.cos(kk*l) \n", "    return g"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["def block_direct_sum(blocks):\n", "    \"\"\"\n", "    沿对角线构造块直和矩阵（块对角矩阵）\n", "    \n", "    参数:\n", "        blocks: 子矩阵列表，每个元素为numpy数组（方阵）\n", "    返回:\n", "        块直和矩阵（对角线上是输入的子矩阵，其余为0）\n", "    \"\"\"\n", "    # 检查输入是否为空\n", "    if not blocks:\n", "        return np.array([], dtype=float)\n", "    \n", "    # 检查所有子矩阵是否为方阵\n", "    for i, b in enumerate(blocks):\n", "        if b.ndim != 2 or b.shape[0] != b.shape[1]:\n", "            raise ValueError(f\"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）\")\n", "    \n", "    # 初始化结果矩阵（从空矩阵开始）\n", "    result = np.array([], dtype=blocks[0].dtype)\n", "    \n", "    for block in blocks:\n", "        m = block.shape[0]  # 当前块的维度\n", "        if result.size == 0:\n", "            # 第一次拼接：直接用当前块作为初始矩阵\n", "            result = block.copy()\n", "        else:\n", "            # 非第一次拼接：构造新的块对角矩阵\n", "            n = result.shape[0]  # 现有矩阵的维度\n", "            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）\n", "            new_result = np.zeros((n + m, n + m), dtype=result.dtype)\n", "            # 填充左上角为现有矩阵\n", "            new_result[:n, :n] = result\n", "            # 填充右下角为当前块\n", "            new_result[n:, n:] = block\n", "            result = new_result\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["def checkstate(s,k,N):\n", "    R=-1\n", "    tz=-1\n", "    tp = -1\n", "    tpz = -1\n", "    smax = 2**N-1\n", "    #Sum_m = 0\n", "    #for i in range(N):\n", "    #    Sum_m += get_site_value(s,i)\n", "    #if Sum_m != N//2:\n", "    #    return R,tp,tz,tpz\n", "    t=s\n", "    for i in range(1,N+1):\n", "        t = translate(N,t,1)\n", "        az = smax -t\n", "        #print(t,s,az)\n", "        if t<s or az<s:\n", "            break\n", "        if t==s:\n", "            if k%(N/i)!=0:\n", "                break\n", "            R=i\n", "            break\n", "        if az==s:\n", "            tz=i\n", "    t = reverseBits(s,N)\n", "    az = smax-t\n", "    for i in range(R):\n", "        if t<s or az<s:\n", "            R=-1\n", "            break\n", "        if t==s:\n", "            tp=i\n", "        if az==s:\n", "            tpz=i\n", "        t = translate(N,t,1)\n", "        az = smax-t\n", "    return R,tp,tz,tpz"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p, z):\n", "    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数\n", "    repr_list = []\n", "    typee = []# 分类标签\n", "    peri = []# 平移对称性\n", "    mtrf = []# 反演对称性\n", "    ntrf = []# 自旋翻转对称性\n", "    capr = []# 联合对称性\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 显式列出sigma值（替代range，更清晰）\n", "        for sigma in (-1, 1):\n", "            # 3. 每次迭代初始化m、n，避免跨状态污染\n", "            m, n = None, None\n", "            ca = None  # 显式初始化分类标签\n", "            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\n", "            \n", "            # 基础过滤：仅处理有效平移对称性的状态\n", "            if R <= -1:\n", "                continue\n", "            \n", "            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\n", "            if (k == 0 or k == N // 2) and (sigma == -1):\n", "                R = -1  # 标记为无效状态\n", "            \n", "            # 5. 仅处理R仍有效的状态\n", "            if R > 0:\n", "                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\n", "                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\n", "                if tp == -1 and tz == -1 and tpz == -1:\n", "                    ca = 1\n", "                    m, n = None, None  # 明确赋值，避免未定义\n", "                \n", "                # 分支2：tp≠-1、tz=-1（反演-平移对称）\n", "                elif tp != -1 and tz == -1:\n", "                    ca = 2\n", "                    m = tp\n", "                    # 7. 浮点数比较改用np.isclose，增强稳健性\n", "                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\n", "                elif tp == -1 and tz != -1:\n", "                    ca = 3\n", "                    n = tz\n", "                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\n", "                elif tp == -1 and tz == -1 and tpz != -1:\n", "                    ca = 4\n", "                    m = tpz\n", "                    n = None  # 明确n未定义\n", "                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\n", "                elif tp != -1 and tz != -1:\n", "                    ca = 5\n", "                    m, n = tp, tz\n", "                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 8. 捕获未覆盖的状态组合（调试用）\n", "                else:\n", "                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\n", "                    continue\n", "                \n", "                # 9. 最终检查：ca必须已定义且R仍有效\n", "                if ca is not None and R > 0:\n", "                    repr_list.append(s)\n", "                    typee.append(2 * ca + (sigma + 1) / 2)\n", "                    capr.append(ca)\n", "                    peri.append(R)\n", "                    mtrf.append(m)\n", "                    ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 10. 简化打印，避免输出过载\n", "    #print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            g=1    \n", "    at = reverseBits(a0,L)\n", "    for t in range(L):\n", "        if at<a:\n", "            a=at\n", "            l=t\n", "            q=1\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            q=1\n", "            g=1\n", "        at = translate(L, at, 1)\n", "    return a,l,q,g"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["from collections import deque\n", "def generate_symmetry_orbit_for_state(N, state, k, p, z):\n", "    \"\"\"\n", "    生成给定状态在所有对称性操作下的轨道\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        state: 初始状态\n", "        k: 波矢\n", "        p: 反演对称性参数\n", "        z: 自旋翻转对称性参数\n", "        \n", "    返回:\n", "        orbit: 包含所有对称操作生成的状态列表\n", "        symmetry_info: 每个状态对应的对称操作信息\n", "    \"\"\"\n", "    # 首先检查状态的对称性分类\n", "    R, tp, tz, tpz = checkstate(state, k, N)\n", "    \n", "    if R <= -1:\n", "        raise ValueError(\"状态不具有有效的平移对称性\")\n", "    \n", "    # 确定对称性分类\n", "    ca = None\n", "    m, n = None, None\n", "    \n", "    if tp == -1 and tz == -1 and tpz == -1:\n", "        ca = 1\n", "    elif tp != -1 and tz == -1:\n", "        ca = 2\n", "        m = tp\n", "    elif tp == -1 and tz != -1:\n", "        ca = 3\n", "        n = tz\n", "    elif tp == -1 and tz == -1 and tpz != -1:\n", "        ca = 4\n", "        m = tpz\n", "    elif tp != -1 and tz != -1:\n", "        ca = 5\n", "        m, n = tp, tz\n", "    \n", "    # 生成包含所有对称操作的状态轨道\n", "    orbit = []\n", "    symmetry_info = {}  # 存储每个状态对应的对称操作\n", "    visited = set()\n", "    queue = deque([(state, \"identity\")])  # (状态, 操作序列)\n", "    \n", "    while queue:\n", "        current_state, operations = queue.popleft()\n", "        if current_state in visited:\n", "            continue\n", "        \n", "        visited.add(current_state)\n", "        orbit.append(current_state)\n", "        symmetry_info[current_state] = operations\n", "        \n", "        # 添加平移操作生成的状态\n", "        translated = translate(N, current_state, 1)\n", "        if translated not in visited:\n", "            queue.append((translated, f\"{operations}->T\"))\n", "        \n", "        # 添加反演操作生成的状态（如果适用）\n", "        if ca in [2, 4, 5] and m is not None:\n", "            inverted = invert_state_k(N, current_state, m)\n", "            if inverted not in visited:\n", "                queue.append((inverted, f\"{operations}->P\"))\n", "        \n", "        # 添加自旋翻转操作生成的状态（如果适用）\n", "        if ca in [3, 4, 5] and n is not None:\n", "            flipped = flip_all_spins_k(N, current_state)\n", "            if flipped not in visited:\n", "                queue.append((flipped, f\"{operations}->Z\"))\n", "    \n", "    return orbit, symmetry_info, ca, m, n, R\n", "\n", "\n", "\n", "def invert_state_k(N, state, m):\n", "    \"\"\"\n", "    空间反演操作：以位置m为中心进行反演\n", "    \"\"\"\n", "    result = 0\n", "    for i in range(N):\n", "        # 计算反演后的位置\n", "        j = (2 * m - i) % N\n", "        if j < 0:\n", "            j += N\n", "        # 获取原位置i的比特并放到位置j\n", "        bit = get_site_value(state, i)\n", "        result = set_site_value(result, j, bit)\n", "    return result\n", "\n", "\n", "def flip_all_spins_k(N, state):\n", "    \"\"\"\n", "    自旋翻转操作：将所有自旋翻转\n", "    \"\"\"\n", "    # 将所有自旋翻转import numpy as np\n", "from collections import deque\n", "\n", "def build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, p, z, k):\n", "    \"\"\"\n", "    严格按照公式构建投影算符：\n", "    |a^σ(k,p,z)> = 1/√(N_a^σ) Σ_{r=0}^{R-1} c_k^σ(r) (1 + pP)(1 + zZ) T^r |a>\n", "    \n", "    参数:\n", "        N: 系统大小（自旋数）\n", "        reps: 代表元列表\n", "        peri: 平移周期列表\n", "        mtrf: 反演-平移参数tp\n", "        ntrf: 反演-自旋翻转参数tz\n", "        capr: 对称性分类（ca=1~5）\n", "        p, z: 对称性约束参数（±1）\n", "        k: 波矢（0 ≤ k < N）\n", "    返回:\n", "        V: 投影矩阵（列向量为对称化基矢）\n", "    \"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    \n", "    # 输入验证\n", "    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\n", "        raise ValueError(\"参数长度必须与代表元数量一致\")\n", "    if not (0 <= k < N):\n", "        raise ValueError(f\"波矢k={k}超出范围[0, {N-1}]\")\n", "    \n", "    # 初始化投影矩阵\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    \n", "    for col in range(nrep):\n", "        # 提取当前代表元的对称性参数\n", "        rep = reps[col]\n", "        R = peri[col]  # 理论轨道长度\n", "        ca = capr[col]\n", "        m = mtrf[col] if mtrf[col] != -1 else None\n", "        n = ntrf[col] if ntrf[col] != -1 else None\n", "        \n", "        # 生成平移轨道\n", "        orbit = generate_translation_orbit(N, rep, R)\n", "        \n", "        # 对于情况2)、4)和5)，选择使归一化因子为正的sigma\n", "        if ca in [2, 4, 5]:\n", "            # 计算两种sigma的归一化因子\n", "            if ca == 2:\n", "                norm1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\n", "                norm_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\n", "            elif ca == 4:\n", "                norm1 = 1 + 1 * p * z * np.cos(2 * np.pi * k * m / N)\n", "                norm_minus1 = 1 + (-1 * p * z) * np.cos(2 * np.pi * k * m / N)\n", "            else:  # ca == 5\n", "                term1_1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\n", "                term1_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\n", "                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm1 = term1_1 * term2\n", "                norm_minus1 = term1_minus1 * term2\n", "            \n", "            # 选择使归一化因子为正的sigma\n", "            if norm1 > 0:\n", "                sigma = 1\n", "            elif norm_minus1 > 0:\n", "                sigma = -1\n", "            else:\n", "                raise ValueError(f\"代表元{col}的两种sigma归一化因子都不为正\")\n", "        else:\n", "            # 对于情况1)和3)，我们处理两种sigma\n", "            sigma = None\n", "        \n", "        # 计算归一化因子\n", "        if ca == 1:\n", "            # 仅平移对称\n", "            norm_factor = np.sqrt(R) / N\n", "        elif ca == 2:\n", "            # 平移+反演\n", "            norm_expr = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\n", "            norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "        elif ca == 3:\n", "            # 平移+自旋翻转\n", "            norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "            norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "        elif ca == 4:\n", "            # 平移+反演+自旋翻转（联合）\n", "            norm_expr = 1 + sigma * p * z * np.cos(2 * np.pi * k * m / N)\n", "            norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "        elif ca == 5:\n", "            # 平移+反演+自旋翻转（叠加）\n", "            term1 = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\n", "            term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "            norm_factor = np.sqrt(R / (2 * N**2 * term1 * term2))\n", "        else:\n", "            raise ValueError(f\"代表元{col}的对称性分类ca={ca}无效\")\n", "        \n", "        # 计算相位因子\n", "        for r, s in enumerate(orbit):\n", "            # 平移相位\n", "            trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "            \n", "            # 对称性操作相位\n", "            sym_phase = 1.0\n", "            \n", "            # 计算反演操作相位（如果适用）\n", "            if ca in [2, 4, 5] and m is not None:\n", "                inverted = invert_state_k(N, s, m)\n", "                if inverted in orbit:\n", "                    idx = orbit.index(inverted)\n", "                    inv_phase = np.exp(1j * 2 * np.pi * k * idx / R)\n", "                    # 如果状态在反演下不变，添加额外因子\n", "                    if inverted == s:\n", "                        sym_phase *= (1 + sigma * p * inv_phase)\n", "                    else:\n", "                        sym_phase *= inv_phase\n", "            \n", "            # 计算自旋翻转操作相位（如果适用）\n", "            if ca in [3, 4, 5] and n is not None:\n", "                flipped = flip_all_spins_k(N, s)\n", "                if flipped in orbit:\n", "                    idx = orbit.index(flipped)\n", "                    flip_phase = np.exp(1j * 2 * np.pi * k * idx / R)\n", "                    # 如果状态在自旋翻转下不变，添加额外因子\n", "                    if flipped == s:\n", "                        sym_phase *= (1 + z * flip_phase)\n", "                    else:\n", "                        sym_phase *= flip_phase\n", "            \n", "            # 填充投影矩阵\n", "            V[s, col] += norm_factor * trans_phase * sym_phase\n", "    \n", "    # 确保每一列都是归一化的（平方和为1）\n", "    for col in range(nrep):\n", "        col_norm = np.linalg.norm(V[:, col])\n", "        if col_norm > 0:\n", "            V[:, col] /= col_norm\n", "    \n", "    return V\n", "\n", "\n", "def generate_translation_orbit(N, rep, R):\n", "    \"\"\"\n", "    生成平移轨道（只包含平移操作生成的状态）\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        rep: 代表元\n", "        R: 平移周期\n", "    返回:\n", "        orbit: 包含平移操作生成的状态列表\n", "    \"\"\"\n", "    orbit = []\n", "    current = rep\n", "    \n", "    for _ in range(R):\n", "        if current in orbit:\n", "            break\n", "        orbit.append(current)\n", "        current = translate(N, current, 1)  # 平移一个位置\n", "    \n", "    if len(orbit) != R:\n", "        raise RuntimeError(f\"平移轨道生成错误: 期望长度{R}, 实际长度{len(orbit)}\")\n", "    \n", "    return orbit\n", "\n", "\n", "def translate(L, state, n_translation_sites):\n", "    \"\"\"\n", "    平移操作\n", "    \"\"\"\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites) % L, site_value)\n", "    return new_state\n", "\n", "\n", "def invert_state_k(N, state, m):\n", "    \"\"\"\n", "    空间反演操作：以位置m为中心进行反演\n", "    \"\"\"\n", "    result = 0\n", "    for i in range(N):\n", "        # 计算反演后的位置\n", "        j = (2 * m - i) % N\n", "        if j < 0:\n", "            j += N\n", "        # 获取原位置i的比特并放到位置j\n", "        bit = get_site_value(state, i)\n", "        result = set_site_value(result, j, bit)\n", "    return result\n", "\n", "\n", "def flip_all_spins_k(N, state):\n", "    \"\"\"\n", "    自旋翻转操作：将所有自旋翻转\n", "    \"\"\"\n", "    # 将所有自旋翻转\n", "    return state ^ ((1 << N) - 1)\n", "\n", "\n", "    return state ^ ((1 << N) - 1)\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ndef build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, p, z, k):\\n    \"\"\"\\n    严格按照公式构建投影算符：\\n    |a^σ(k,p,z)> = 1/√(N_a^σ) Σ_{r=0}^{R-1} c_k^σ(r) (1 + pP)(1 + zZ) T^r |a>\\n    \\n    参数:\\n        N: 系统大小（自旋数）\\n        reps: 代表元列表\\n        peri: 平移周期列表\\n        mtrf: 反演-平移参数tp\\n        ntrf: 反演-自旋翻转参数tz\\n        capr: 对称性分类（ca=1~5）\\n        p, z: 对称性约束参数（±1）\\n        k: 波矢（0 ≤ k < N）\\n    返回:\\n        V: 投影矩阵（列向量为对称化基矢）\\n    \"\"\"\\n    dim_full = 2 ** N\\n    nrep = len(reps)\\n    \\n    # 输入验证\\n    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\\n        raise ValueError(\"参数长度必须与代表元数量一致\")\\n    if not (0 <= k < N):\\n        raise ValueError(f\"波矢k={k}超出范围[0, {N-1}]\")\\n    \\n    # 初始化投影矩阵\\n    V = np.zeros((dim_full, nrep), dtype=complex)\\n    \\n    for col in range(nrep):\\n        # 提取当前代表元的对称性参数\\n        rep = reps[col]\\n        R = peri[col]  # 理论轨道长度\\n        ca = capr[col]\\n        m = mtrf[col] if mtrf[col] != -1 else None\\n        n = ntrf[col] if ntrf[col] != -1 else None\\n        \\n        # 生成平移轨道\\n        orbit, symmetry_info, ca, m, n, R = generate_symmetry_orbit_for_state(N, rep, k, p, z)\\n        \\n        # 对于情况2)、4)和5)，选择使归一化因子为正的sigma\\n        if ca in [2, 4, 5]:\\n            # 计算两种sigma的归一化因子\\n            if ca == 2:\\n                norm1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\\n                norm_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\\n            elif ca == 4:\\n                norm1 = 1 + 1 * p * z * np.cos(2 * np.pi * k * m / N)\\n                norm_minus1 = 1 + (-1) * p * z * np.cos(2 * np.pi * k * m / N)\\n            else:  # ca == 5\\n                term1_1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\\n                term1_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\\n                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                norm1 = term1_1 * term2\\n                norm_minus1 = term1_minus1 * term2\\n            \\n            # 选择使归一化因子为正的sigma\\n            if norm1 > 0:\\n                sigma = 1\\n            elif norm_minus1 > 0:\\n                sigma = -1\\n            else:\\n                raise ValueError(f\"代表元{col}的两种sigma归一化因子都不为正\")\\n        else:\\n            # 对于情况1)和3)，我们处理两种sigma\\n            sigma = None\\n        \\n        # 处理不同的对称性分类\\n        if sigma is None:\\n            # 情况1)和3)：处理两种sigma\\n            for sigma_val in [-1, 1]:\\n                # 计算归一化因子\\n                if ca == 1:\\n                    norm_factor = np.sqrt(R) / N\\n                elif ca == 3:\\n                    norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                    norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n                \\n                # 计算相位因子\\n                for r, s in enumerate(orbit):\\n                    # 平移相位\\n                    trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\\n                    \\n                    # 对称性操作相位\\n                    sym_phase = 1.0\\n                    if ca == 3:\\n                        # 自旋翻转操作相位\\n                        flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\\n                        sym_phase *= flip_phase\\n                    \\n                    # 填充投影矩阵\\n                    V[s, col] += norm_factor * trans_phase * sym_phase\\n        else:\\n            # 计算归一化因子\\n            if ca == 1:\\n                norm_factor = np.sqrt(R) / N\\n            elif ca == 2:\\n                norm_expr = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n            elif ca == 3:\\n                norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n            elif ca == 4:\\n                norm_expr = 1 + sigma * p * z * np.cos(2 * np.pi * k * m / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n            elif ca == 5:\\n                term1 = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\\n                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * term1 * term2))\\n            else:\\n                raise ValueError(f\"代表元{col}的对称性分类ca={ca}无效\")\\n            \\n            # 计算相位因子\\n            for r, s in enumerate(orbit):\\n                # 平移相位\\n                trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\\n                \\n                # 对称性操作相位\\n                sym_phase = 1.0\\n                if ca in [2, 4, 5]:\\n                    # 反演操作相位\\n                    inv_phase = calculate_symmetry_phase(N, s, orbit, m, k, R, p, \"inversion\")\\n                    sym_phase *= inv_phase\\n                \\n                if ca in [3, 4, 5]:\\n                    # 自旋翻转操作相位\\n                    flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\\n                    sym_phase *= flip_phase\\n                \\n                # 填充投影矩阵\\n                if k ==0 or k == N//2:#g_k因子\\n                    V[s, col] += norm_factor * trans_phase * sym_phase *np.sqrt(2)\\n                else:\\n                    V[s, col] += norm_factor * trans_phase * sym_phase \\n    \\n    return V\\n\\n\\ndef calculate_symmetry_phase(N, state, orbit, param, k, R, factor, sym_type):\\n    \"\"\"\\n    计算对称操作的相位因子\\n    \\n    参数:\\n        N: 系统大小\\n        state: 当前状态\\n        orbit: 轨道\\n        param: 对称操作参数（m或n）\\n        k: 波矢\\n        R: 轨道长度\\n        factor: 对称性因子（p, z等）\\n        sym_type: 对称操作类型（\\'inversion\\'或\\'flip\\'）\\n    返回:\\n        相位因子\\n    \"\"\"\\n    if sym_type == \"inversion\":\\n        transformed = invert_state_k(N, state, param)\\n    elif sym_type == \"flip\":\\n        transformed = flip_all_spins_k(N, state)\\n    else:\\n        raise ValueError(f\"未知的对称操作类型: {sym_type}\")\\n    \\n    # 找到变换后状态在轨道中的位置\\n    try:\\n        idx = orbit.index(transformed)\\n        # 如果状态在对称操作下不变，添加额外因子\\n        if transformed == state:\\n            return 1.0 + factor\\n        else:\\n            return np.exp(1j * 2 * np.pi * k * idx / R)\\n    except ValueError:\\n        # 如果变换后状态不在轨道中，返回0\\n        return 0.0\\n'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from collections import deque\n", "'''\n", "def build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, p, z, k):\n", "    \"\"\"\n", "    严格按照公式构建投影算符：\n", "    |a^σ(k,p,z)> = 1/√(N_a^σ) Σ_{r=0}^{R-1} c_k^σ(r) (1 + pP)(1 + zZ) T^r |a>\n", "    \n", "    参数:\n", "        N: 系统大小（自旋数）\n", "        reps: 代表元列表\n", "        peri: 平移周期列表\n", "        mtrf: 反演-平移参数tp\n", "        ntrf: 反演-自旋翻转参数tz\n", "        capr: 对称性分类（ca=1~5）\n", "        p, z: 对称性约束参数（±1）\n", "        k: 波矢（0 ≤ k < N）\n", "    返回:\n", "        V: 投影矩阵（列向量为对称化基矢）\n", "    \"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    \n", "    # 输入验证\n", "    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\n", "        raise ValueError(\"参数长度必须与代表元数量一致\")\n", "    if not (0 <= k < N):\n", "        raise ValueError(f\"波矢k={k}超出范围[0, {N-1}]\")\n", "    \n", "    # 初始化投影矩阵\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    \n", "    for col in range(nrep):\n", "        # 提取当前代表元的对称性参数\n", "        rep = reps[col]\n", "        R = peri[col]  # 理论轨道长度\n", "        ca = capr[col]\n", "        m = mtrf[col] if mtrf[col] != -1 else None\n", "        n = ntrf[col] if ntrf[col] != -1 else None\n", "        \n", "        # 生成平移轨道\n", "        orbit, symmetry_info, ca, m, n, R = generate_symmetry_orbit_for_state(N, rep, k, p, z)\n", "        \n", "        # 对于情况2)、4)和5)，选择使归一化因子为正的sigma\n", "        if ca in [2, 4, 5]:\n", "            # 计算两种sigma的归一化因子\n", "            if ca == 2:\n", "                norm1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\n", "                norm_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\n", "            elif ca == 4:\n", "                norm1 = 1 + 1 * p * z * np.cos(2 * np.pi * k * m / N)\n", "                norm_minus1 = 1 + (-1) * p * z * np.cos(2 * np.pi * k * m / N)\n", "            else:  # ca == 5\n", "                term1_1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\n", "                term1_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\n", "                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm1 = term1_1 * term2\n", "                norm_minus1 = term1_minus1 * term2\n", "            \n", "            # 选择使归一化因子为正的sigma\n", "            if norm1 > 0:\n", "                sigma = 1\n", "            elif norm_minus1 > 0:\n", "                sigma = -1\n", "            else:\n", "                raise ValueError(f\"代表元{col}的两种sigma归一化因子都不为正\")\n", "        else:\n", "            # 对于情况1)和3)，我们处理两种sigma\n", "            sigma = None\n", "        \n", "        # 处理不同的对称性分类\n", "        if sigma is None:\n", "            # 情况1)和3)：处理两种sigma\n", "            for sigma_val in [-1, 1]:\n", "                # 计算归一化因子\n", "                if ca == 1:\n", "                    norm_factor = np.sqrt(R) / N\n", "                elif ca == 3:\n", "                    norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                    norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "                \n", "                # 计算相位因子\n", "                for r, s in enumerate(orbit):\n", "                    # 平移相位\n", "                    trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "                    \n", "                    # 对称性操作相位\n", "                    sym_phase = 1.0\n", "                    if ca == 3:\n", "                        # 自旋翻转操作相位\n", "                        flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\n", "                        sym_phase *= flip_phase\n", "                    \n", "                    # 填充投影矩阵\n", "                    V[s, col] += norm_factor * trans_phase * sym_phase\n", "        else:\n", "            # 计算归一化因子\n", "            if ca == 1:\n", "                norm_factor = np.sqrt(R) / N\n", "            elif ca == 2:\n", "                norm_expr = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "            elif ca == 3:\n", "                norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "            elif ca == 4:\n", "                norm_expr = 1 + sigma * p * z * np.cos(2 * np.pi * k * m / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "            elif ca == 5:\n", "                term1 = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\n", "                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * term1 * term2))\n", "            else:\n", "                raise ValueError(f\"代表元{col}的对称性分类ca={ca}无效\")\n", "            \n", "            # 计算相位因子\n", "            for r, s in enumerate(orbit):\n", "                # 平移相位\n", "                trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "                \n", "                # 对称性操作相位\n", "                sym_phase = 1.0\n", "                if ca in [2, 4, 5]:\n", "                    # 反演操作相位\n", "                    inv_phase = calculate_symmetry_phase(N, s, orbit, m, k, R, p, \"inversion\")\n", "                    sym_phase *= inv_phase\n", "                \n", "                if ca in [3, 4, 5]:\n", "                    # 自旋翻转操作相位\n", "                    flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\n", "                    sym_phase *= flip_phase\n", "                \n", "                # 填充投影矩阵\n", "                if k ==0 or k == N//2:#g_k因子\n", "                    V[s, col] += norm_factor * trans_phase * sym_phase *np.sqrt(2)\n", "                else:\n", "                    V[s, col] += norm_factor * trans_phase * sym_phase \n", "    \n", "    return V\n", "\n", "\n", "def calculate_symmetry_phase(N, state, orbit, param, k, R, factor, sym_type):\n", "    \"\"\"\n", "    计算对称操作的相位因子\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        state: 当前状态\n", "        orbit: 轨道\n", "        param: 对称操作参数（m或n）\n", "        k: 波矢\n", "        R: 轨道长度\n", "        factor: 对称性因子（p, z等）\n", "        sym_type: 对称操作类型（'inversion'或'flip'）\n", "    返回:\n", "        相位因子\n", "    \"\"\"\n", "    if sym_type == \"inversion\":\n", "        transformed = invert_state_k(N, state, param)\n", "    elif sym_type == \"flip\":\n", "        transformed = flip_all_spins_k(N, state)\n", "    else:\n", "        raise ValueError(f\"未知的对称操作类型: {sym_type}\")\n", "    \n", "    # 找到变换后状态在轨道中的位置\n", "    try:\n", "        idx = orbit.index(transformed)\n", "        # 如果状态在对称操作下不变，添加额外因子\n", "        if transformed == state:\n", "            return 1.0 + factor\n", "        else:\n", "            return np.exp(1j * 2 * np.pi * k * idx / R)\n", "    except ValueError:\n", "        # 如果变换后状态不在轨道中，返回0\n", "        return 0.0\n", "'''\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = 2*(typee[a]%2)-1\n", "    t = 2*(typee[b]%2)-1\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)\n", "    return matelement"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def Ham_total_TPZ(J,h,N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J\n", "            else:\n", "                Ez -= J\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            #横场项\n", "            #sb = flip_state(sa, i)\n", "            #if ai == 1:\n", "            sb = flip_state(sa, i)\n", "            #else:\n", "                #sb = flip_state(flip_state(sa, j), i)\n", "            representative, l, q, g = represent(N, sb)\n", "            if representative in repr:\n", "                ib = repr.index(representative)\n", "                if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                    ib = ib - 1\n", "                    nb = 2\n", "                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                    nb = 2\n", "                else:\n", "                    nb = 1\n", "                for ii in range(ia, ia + na):\n", "                    for jj in range(ib, ib + nb):\n", "                        try:\n", "                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)\n", "                            if np.isfinite(elem):\n", "                                Hk[ii, jj] += elem\n", "                        except Exception as e:\n", "                            print(f\"helement error at ii={ii}, jj={jj}: {e}\")\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["def sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia>1 and sa==repr[ia-1]):\n", "            continue\n", "        elif (ia<nrep-1 and sa==repr[ia+1]):\n", "            na=2\n", "        else:\n", "            na=1\n", "        for a in range(ia,ia+na):\n", "            Hk[a,a] += (1/2 )*N    \n", "        for i in range(N):\n", "            for j in range(i+1,N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                if ai != aj:\n", "                    if  ai == 1:   \n", "                        sb = flip_state(flip_state(sa,i),j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa,j),i)\n", "                    representative, l,q,g = represent(N,sb)\n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        if ib >1 and repr[ib]==repr[ib-1]:\n", "                            ib = ib-1\n", "                            nb=2\n", "                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\n", "                            nb=2\n", "                        else:\n", "                            nb=1\n", "                        for ii in range(ia,ia+na):\n", "                            for jj in range(ib,ib+nb):\n", "                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    "]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["def fullspectrum(J,h,N):\n", "    E=[]\n", "    #k=0\n", "    k_min = []\n", "    p_min = []\n", "    z_min = []\n", "    E_min = []\n", "    spi_min = []\n", "\n", "    v_full = []\n", "    Hk_full = []\n", "    new_basis_matrix = []\n", "    for k in range(N):\n", "        if k==0 or k==N//2:\n", "            p1=-1\n", "            p2=1\n", "        else:\n", "            p1=1\n", "            p2=1\n", "        for p in range(p1,p2+1,2):\n", "            for z in [-1,1]:\n", "                nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)\n", "                if nrep != 0:\n", "                    Hk_1 = Ham_total_TPZ(0*J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    Hk_2 = Ham_total_TPZ(J,0*h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    H_f = expm(-1j*t_1*Hk_1) @ expm(-1j*t_2*Hk_2)\n", "                    # 拼接为完整矩阵\n", "                    if len(Hk_full) == 0:\n", "                        Hk_full = H_f\n", "                    else:\n", "                        Hk_full = block_direct_sum([Hk_full,H_f])#np.block(block_structure)\n", "\n", "                    eigenvalue, featurevector =np.linalg.eig(H_f)\n", "                    Hk_spin = sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    spn = transform(nrep,Hk_spin,featurevector)\n", "                    spin = []\n", "                    for spin_i in range(len(spn)):\n", "                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\n", "                    E1 = eigenvalue.tolist()\n", "                    #print(E1)\n", "                    #print(k,p,z,nrep,repr)\n", "                    <PERSON>.extend(eigenvalue.tolist())\n", "                    \n", "                    if len(v_full) == 0:\n", "                        v_full = featurevector\n", "                    else:\n", "                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)\n", "                    # 构造投影矩阵 V_k\n", "                    V_k = build_projection_matrix(N, repr, peri, mtrf, ntrf, capr, p, z, k)\n", "    \n", "                    #V_k = build_projection_matrix_for_k_test(N, repr, peri, mtrf, ntrf, capr, p, z, k, sigma=1)\n", "                    #矩阵按列直接拼接\n", "                    if len(new_basis_matrix) == 0:\n", "                        new_basis_matrix = V_k\n", "                    else:\n", "                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）\n", "                    #print(new_basis_matrix.shape)\n", "                if len(E1) != 0:\n", "                    for i in range(len(E1)):\n", "                        idx = E1.index(np.min(E1))\n", "                        k_min.append(k)\n", "                        p_min.append(p)\n", "                        z_min.append(z)\n", "                        E_min.append(E1[idx])\n", "                        spi_min.append(spin[idx])\n", "                        #print(len(E1))\n", "                        #print(np.min(E1),E1.index(np.min(E1)))\n", "                        E1.pop(idx)\n", "                        spin.pop(idx)   \n", "    return E,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_full"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["H1 = Ham_total(N,0*J_z,h_x)\n", "H2 = Ham_total(N,J_z,0*h_x)\n", "H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)\n", "\n", "eigvals_all, v_full = np.linalg.eig(H_F)\n", "#eigvals_all= np.linalg.eigvalsh(H_tot)\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["#eigvals_all"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["#min_n =8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,0)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"ename": "UnboundLocalError", "evalue": "cannot access local variable 'E1' where it is not associated with a value", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnboundLocalError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[51], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform ,Hk_block_full\u001b[38;5;241m=\u001b[39m fullspectrum(J_z,h_x,N)\n", "Cell \u001b[0;32mIn[47], line 58\u001b[0m, in \u001b[0;36mfullspectrum\u001b[0;34m(J, h, N)\u001b[0m\n\u001b[1;32m     56\u001b[0m         new_basis_matrix \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mhstack((new_basis_matrix, V_k))  \u001b[38;5;66;03m# 收集新基（线性组合形式）\u001b[39;00m\n\u001b[1;32m     57\u001b[0m     \u001b[38;5;66;03m#print(new_basis_matrix.shape)\u001b[39;00m\n\u001b[0;32m---> 58\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(E1) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m     59\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mlen\u001b[39m(E1)):\n\u001b[1;32m     60\u001b[0m         idx \u001b[38;5;241m=\u001b[39m E1\u001b[38;5;241m.\u001b[39mindex(np\u001b[38;5;241m.\u001b[39mmin(E1))\n", "\u001b[0;31mUnboundLocalError\u001b[0m: cannot access local variable 'E1' where it is not associated with a value"]}], "source": ["E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform ,Hk_block_full= fullspectrum(J_z,h_x,N)\n", "    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))\n", "    #print(np.array(eigvals))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "#print(k_all,E_full)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["64 64\n"]}], "source": ["print (len(E_full),len(eigvals_all))\n", "for x in E_full:\n", "    index = np.abs(eigvals_all - x).argmin()\n", "    if np.isclose(eigvals_all[index], x) != True:\n", "        print(np.isclose(eigvals_all[index], x), eigvals_all[index], x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.12/site-packages/matplotlib/cbook.py:1762: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return math.isfinite(val)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/matplotlib/collections.py:197: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  offsets = np.asanyarray(offsets, float)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(eigvals_all)), np.sort(eigvals_all), label='full',s = 50)\n", "plt.scatter(range(len(E_full)), np.sort(E_full), label='block',s=10,marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'file = open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') \\nlines = [\"\"] \\nwith open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') as file: \\n    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\\n    for i in range(len(k_min)):\\n        if i%(min_n)==0:\\n            file.write(\"----------------------------------------\"+\"\\n\")\\n        file.write(str(k_min[i])+\"\\t\")\\n        file.write(str(p_min[i])+\"\\t\")\\n        file.write(str(z_min[i])+\"\\t\")\\n        file.write(str(E_min[i])+\"\\t\")\\n        file.write(str(spi_min[i])+\"\\t\")\\n        file.write(\"\\n\")\\nfile.close()'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["'''file = open('ED_TPZ_1D_N=16.txt', 'w') \n", "lines = [\"\"] \n", "with open('ED_TPZ_1D_N=16.txt', 'w') as file: \n", "    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\n", "    for i in range(len(k_min)):\n", "        if i%(min_n)==0:\n", "            file.write(\"----------------------------------------\"+\"\\n\")\n", "        file.write(str(k_min[i])+\"\\t\")\n", "        file.write(str(p_min[i])+\"\\t\")\n", "        file.write(str(z_min[i])+\"\\t\")\n", "        file.write(str(E_min[i])+\"\\t\")\n", "        file.write(str(spi_min[i])+\"\\t\")\n", "        file.write(\"\\n\")\n", "file.close()'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[62, 1, 7, 6, 24, 62, 0, 8, 20, 9, 2, 4, 5, 11, 13, 40, 10, 32, 31, 50, 53, 38, 35, 18, 16, 28, 25, 62, 55, 59, 54, 35, 38, 47, 23, 21, 3, 15, 40, 59, 47, 40, 59, 47, 19, 16, 28, 26, 60, 49, 59, 54, 35, 22, 47, 13, 40, 10, 32, 30, 50, 53, 22, 39]\n", "归一化后绝对值误差：1.71e+00\n", "最小内积绝对值：3.53e-15\n"]}], "source": ["# 1. 直接对角化全空间Floquet算子 F\n", "#evals_direct, evecs_direct = np.linalg.eig(F)\n", "# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列\n", "matched_indices = []\n", "for eval_block in E_full:  # energies是分块对角化得到的本征值（Hk的本征值）\n", "    # 找到直接对角化中最接近的本征值索引\n", "    idx = np.argmin(np.abs(eigvals_all - eval_block))\n", "    matched_indices.append(idx)\n", "# 3. 按匹配顺序重新排列直接对角化的本征矢\n", "print(matched_indices)\n", "evecs_direct_matched = v_full[:, matched_indices]\n", "# 4. 验证一致性（忽略相位，对比绝对值或内积）\n", "# 方法1：归一化后对比绝对值\n", "full_evecs_norm = U_transform @ v_block_full \n", "direct_evecs_norm = evecs_direct_matched \n", "abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))\n", "print(f\"归一化后绝对值误差：{abs_error:.2e}\")  # 正常应<1e-6\n", "\n", "# 方法2：计算内积绝对值（应为1，说明是同一本征矢）\n", "inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) \n", "                  for i in range(2**N)]\n", "print(f\"最小内积绝对值：{min(inner_products):.2e}\")  # 正常应>0.999"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), \n", "                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))\n", "IPR_block_full = np.sum(ipr1, axis=0)\n", "ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), \n", "                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))\n", "IPR_full = np.sum(ipr2, axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 假设分块计算的本征值和IPR为 evals_block, ipr_block\n", "# 整体计算的为 evals_full, ipr_full\n", "matched_ipr = []\n", "for e_block, ipr_b in zip(E_full, IPR_block_full):\n", "    # 找到整体计算中与e_block接近的本征值\n", "    idx = np.argmin(np.abs(eigvals_all - e_block))\n", "    matched_ipr.append((ipr_b, IPR_full[idx]))\n", "# 查看匹配后的IPR是否一致\n", "print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')\n", "plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_51737/165616371.py:41: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def plot_matrix(matrix, title=\"矩阵热图\", cmap=\"viridis\", annot=True, figsize=(8, 6)):\n", "    \"\"\"\n", "    可视化矩阵，用颜色深浅表示元素值差异\n", "    \n", "    参数:\n", "        matrix: 待可视化的NumPy矩阵\n", "        title: 图表标题\n", "        cmap: 颜色映射（如\"viridis\", \"coolwarm\", \"RdBu\"）\n", "        annot: 是否在单元格中显示数值\n", "        figsize: 图表尺寸\n", "    \"\"\"\n", "    # 设置绘图风格\n", "    plt.style.use(\"default\")\n", "    \n", "    # 创建画布\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # 绘制热图\n", "    sns.heatmap(\n", "        matrix,\n", "        annot=annot,          # 显示数值\n", "        fmt=\".2f\",            # 数值格式（保留2位小数）\n", "        cmap=cmap,            # 颜色映射\n", "        cbar=True,            # 显示颜色条\n", "        square=True,          # 单元格为正方形\n", "        ax=ax,                # 子图对象\n", "        linewidths=0.5,       # 单元格边框宽度\n", "        linecolor=\"gray\"      # 单元格边框颜色\n", "    )\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=14, pad=10)\n", "    ax.set_xlabel(\"列索引\", fontsize=12, labelpad=10)\n", "    ax.set_ylabel(\"行索引\", fontsize=12, labelpad=10)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig\n", "\n", "fig = plot_matrix(\n", "        #np.abs(U_transform @ v_block_full ),\n", "        #np.abs((U_transform @ v_block_full ) - v_full),\n", "        #np.abs(v_full),\n", "        np.abs(U_transform @ U_transform.T.conj()),\n", "        #np.abs(full_vecs_eig),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(V @ np.linalg.inv(V)),\n", "        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),\n", "        #np.abs(H_F),\n", "        #np.abs(V ** 4),\n", "        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(H_F),\n", "        #np.abs(np.linalg.inv(v_block_full) @ np.linalg.inv(U_transform) @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  - np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ H_F @ U_transform @ v_block_full  ),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  ),\n", "        #np.abs(np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(U_transform)),\n", "        #np.abs(Hk_full),\n", "        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),\n", "        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data - E )),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data) - np.diag(E)),\n", "        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),\n", "        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),\n", "        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),\n", "        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),\n", "        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),\n", "        title=\"示例矩阵热图（颜色表示元素值大小）\",\n", "        cmap=\"coolwarm\"  # 冷暖色映射（正值暖色，负值冷色）\n", "    )\n", "    \n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 2.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j, ...,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j,  0.        +0.00000000e+00j],\n", "       [ 0.        +0.00000000e+00j,  1.58333333+7.06063774e-19j,\n", "        -0.58333333-3.58024774e-18j, ...,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j,  0.        +0.00000000e+00j],\n", "       [ 0.        +0.00000000e+00j, -0.58333333-2.36358437e-17j,\n", "         3.83333333-2.43607396e-18j, ...,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j,  0.        +0.00000000e+00j],\n", "       ...,\n", "       [ 0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j, ...,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j,  0.        +0.00000000e+00j],\n", "       [ 0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j, ...,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j,  0.        +0.00000000e+00j],\n", "       [ 0.        +0.00000000e+00j,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j, ...,  0.        +0.00000000e+00j,\n", "         0.        +0.00000000e+00j,  0.        +0.00000000e+00j]])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["U_transform @ U_transform.T.conj()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "nikola": {"category": "Strongly Correlated Systems", "date": "2020-12-20 13:18:31 UTC+08:00", "description": "", "link": "", "slug": "exact-diagonalization", "tags": "Correlated Electronic Systems, Numerical Method, Exact Diagonalization", "title": "Exact Diagonalization", "type": "text"}, "toc-autonumbering": true}, "nbformat": 4, "nbformat_minor": 4}