import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import expm
from scipy.sparse.linalg import eigsh
from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定
from math import * 
import pandas as pd
import cmath
import os #导入os库
import random
import time
import seaborn as sns
import cmath
import scipy
import functools
import matplotlib
import matplotlib.pyplot as plt


J=1
h=1
N=8

# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state
def reverseBits(s,N):
    bin_chars = ""
    temp = s
    for i in range(N):
        bin_char = bin(temp % 2)[-1]
        temp = temp // 2
        bin_chars = bin_char + bin_chars
    bits =  bin_chars.upper()
    return int(bits[::-1], 2)

def ffun(t,s,l,k,N):
    kk = 2*np.pi*k/N
    if t == -1:
        if s == -1:
            f = np.cos(kk*l)
        elif s==1:
            f = -np.sin(kk*l)
    elif t ==1:
        if s == -1:
            f = np.sin(kk*l)
        elif s==1:
            f = np.cos(kk*l)
    return f
def ggun(t,l,k,N):
    kk = 2*np.pi*k/N
    g=0
    if t == -1:
        g = 1-np.cos(kk*l)
    elif t==1:
        g = 1+np.cos(kk*l) 
    return g

def block_direct_sum(blocks):
    """
    沿对角线构造块直和矩阵（块对角矩阵）
    
    参数:
        blocks: 子矩阵列表，每个元素为numpy数组（方阵）
    返回:
        块直和矩阵（对角线上是输入的子矩阵，其余为0）
    """
    # 检查输入是否为空
    if not blocks:
        return np.array([], dtype=float)
    
    # 检查所有子矩阵是否为方阵
    for i, b in enumerate(blocks):
        if b.ndim != 2 or b.shape[0] != b.shape[1]:
            raise ValueError(f"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）")
    
    # 初始化结果矩阵（从空矩阵开始）
    result = np.array([], dtype=blocks[0].dtype)
    
    for block in blocks:
        m = block.shape[0]  # 当前块的维度
        if result.size == 0:
            # 第一次拼接：直接用当前块作为初始矩阵
            result = block.copy()
        else:
            # 非第一次拼接：构造新的块对角矩阵
            n = result.shape[0]  # 现有矩阵的维度
            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）
            new_result = np.zeros((n + m, n + m), dtype=result.dtype)
            # 填充左上角为现有矩阵
            new_result[:n, :n] = result
            # 填充右下角为当前块
            new_result[n:, n:] = block
            result = new_result
    
    return result

def checkstate(s,k,N):
    R=-1
    tz=-1
    tp = -1
    tpz = -1
    smax = 2**N-1
    #Sum_m = 0
    #for i in range(N):
    #    Sum_m += get_site_value(s,i)
    #if Sum_m != N//2:
    #    return R,tp,tz,tpz
    t=s
    for i in range(1,N+1):
        t = translate(N,t,1)
        az = smax -t
        #print(t,s,az)
        if t<s or az<s:
            break
        if t==s:
            if k%(N/i)!=0:
                break
            R=i
            break
        if az==s:
            tz=i
    t = reverseBits(s,N)
    az = smax-t
    for i in range(R):
        if t<s or az<s:
            R=-1
            break
        if t==s:
            tp=i
        if az==s:
            tpz=i
        t = translate(N,t,1)
        az = smax-t
    return R,tp,tz,tpz


import numpy as np

reprcount = []  # 全局记录列表（按需保留）

def findbasis(N, k, p, z):
    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数
    repr_list = []
    typee = []# 分类标签
    peri = []# 平移对称性
    mtrf = []# 反演对称性
    ntrf = []# 自旋翻转对称性
    capr = []# 联合对称性
    
    for s in range(2 **N):
        # 2. 显式列出sigma值（替代range，更清晰）
        for sigma in (-1, 1):
            # 3. 每次迭代初始化m、n，避免跨状态污染
            m, n = None, None
            ca = None  # 显式初始化分类标签
            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数
            
            # 基础过滤：仅处理有效平移对称性的状态
            if R <= -1:
                continue
            
            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）
            if (k == 0 or k == N // 2) and (sigma == -1):
                R = -1  # 标记为无效状态
            
            # 5. 仅处理R仍有效的状态
            if R > 0:
                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类
                # 分支1：tp、tz、tpz均为-1（无反演相关对称）
                if tp == -1 and tz == -1 and tpz == -1:
                    ca = 1
                    m, n = None, None  # 明确赋值，避免未定义
                
                # 分支2：tp≠-1、tz=-1（反演-平移对称）
                elif tp != -1 and tz == -1:
                    ca = 2
                    m = tp
                    # 7. 浮点数比较改用np.isclose，增强稳健性
                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                
                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）
                elif tp == -1 and tz != -1:
                    ca = 3
                    n = tz
                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):
                        R = -1
                
                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）
                elif tp == -1 and tz == -1 and tpz != -1:
                    ca = 4
                    m = tpz
                    n = None  # 明确n未定义
                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):
                        R = -1
                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):
                        R = -1
                
                # 分支5：tp≠-1、tz≠-1（多重对称叠加）
                elif tp != -1 and tz != -1:
                    ca = 5
                    m, n = tp, tz
                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                
                # 8. 捕获未覆盖的状态组合（调试用）
                else:
                    print(f"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})")
                    continue
                
                # 9. 最终检查：ca必须已定义且R仍有效
                if ca is not None and R > 0:
                    repr_list.append(s)
                    typee.append(2 * ca + (sigma + 1) / 2)
                    capr.append(ca)
                    peri.append(R)
                    mtrf.append(m)
                    ntrf.append(n)
    
    nrep = len(repr_list)
    reprcount.append(repr_list)
    # 10. 简化打印，避免输出过载
    #print(f"k={k}, p={p}, z={z}: 有效状态数={nrep}")
    return nrep, repr_list, typee, peri, mtrf, ntrf, capr

def Ham_total(N,J,h):
    H = np.zeros((2 ** N, 2 ** N))

    # a is a basis
    for a in range(2 ** N):
        # i is position of this basis
        for i in range(N):
            # j is the nearest neighbor, mod N for periodic boundary conditions
            j = (i + 1) % N
            ai = get_site_value(a,i)
            aj = get_site_value(a,j)

            # Sz
            b = a
            if ai == aj:
                H[a, b] += J
            else:
                H[a, b] += -J

            # SxSx + SySy
            #if ai != aj:
            b = flip_state(a, i)
            #b = flip_state(b, j)
            H[a, b] += h
    return H

def represent(L,a0):
    at = a0
    a = a0
    l=0
    q=0
    g=0
    smax = 2**L-1
    for t in range(1,L+1):
        at = translate(L,a0,t)
        if at < a:
            a = at
            l=t
            g=0
        az=smax-at
        if az<a:
            a=az
            l=t
            g=1    
    at = reverseBits(a0,L)
    for t in range(L):
        if at<a:
            a=at
            l=t
            q=1
            g=0
        az=smax-at
        if az<a:
            a=az
            l=t
            q=1
            g=1
        at = translate(L, at, 1)
    return a,l,q,g

import numpy as np

def build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, p, z, k):
    """
    重构的投影算符构建函数（严格遵循对称性分类与正交性要求）
    
    参数:
        N: 系统大小（自旋数）
        reps: 代表元列表（findbasis输出）
        peri: 平移周期列表（每个代表元的轨道长度R）
        mtrf: 反演-平移参数tp
        ntrf: 反演-自旋翻转参数tz
        capr: 对称性分类（ca=1~5）
        p, z: 对称性约束参数（±1）
        k: 波矢（0 ≤ k < N）
    返回:
        V: 投影矩阵（列向量为对称化基矢）
    """
    dim_full = 2 ** N
    nrep = len(reps)
    
    # 输入验证
    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):
        raise ValueError("参数长度必须与代表元数量一致")
    if not (0 <= k < N):
        raise ValueError(f"波矢k={k}超出范围[0, {N-1}]")
    
    V = np.zeros((dim_full, nrep), dtype=complex)
    
    for col in range(nrep):
        # 提取当前代表元的对称性参数
        rep = reps[col]
        R = peri[col]  # 理论轨道长度
        ca = capr[col]
        m = mtrf[col] if mtrf[col] != -1 else None
        n = ntrf[col] if ntrf[col] != -1 else None
        
        # 生成平移轨道
        orbit = generate_translation_orbit(N, rep, R)
        
        # 对于情况2)、4)和5)，选择使归一化因子为正的sigma
        if ca in [2, 4, 5]:
            # 计算两种sigma的归一化因子
            if ca == 2:
                norm1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)
                norm_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)
            elif ca == 4:
                norm1 = 1 + 1 * p * z * np.cos(2 * np.pi * k * m / N)
                norm_minus1 = 1 + (-1) * p * z * np.cos(2 * np.pi * k * m / N)
            else:  # ca == 5
                term1_1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)
                term1_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)
                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)
                norm1 = term1_1 * term2
                norm_minus1 = term1_minus1 * term2
            
            # 选择使归一化因子为正的sigma
            if norm1 > 0:
                sigma = 1
            elif norm_minus1 > 0:
                sigma = -1
            else:
                raise ValueError(f"代表元{col}的两种sigma归一化因子都不为正")
        else:
            # 对于情况1)和3)，我们处理两种sigma
            sigma = None
        
        # 处理不同的对称性分类
        if sigma is None:
            # 情况1)和3)：处理两种sigma
            for sigma_val in [-1, 1]:
                # 计算归一化因子
                if ca == 1:
                    norm_factor = np.sqrt(R) / N
                elif ca == 3:
                    norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)
                    norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))
                
                # 计算相位因子
                for r, s in enumerate(orbit):
                    # 平移相位
                    trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)
                    
                    # 对称性操作相位
                    sym_phase = 1.0
                    if ca == 3:
                        # 自旋翻转操作相位
                        flip_phase = calculate_flip_phase(N, s, orbit, n, k, R, z)
                        sym_phase *= flip_phase
                    
                    # 填充投影矩阵
                    V[s, col] += norm_factor * trans_phase * sym_phase
        else:
            # 计算归一化因子
            if ca == 1:
                norm_factor = np.sqrt(R) / N
            elif ca == 2:
                norm_expr = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)
                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))
            elif ca == 3:
                norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)
                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))
            elif ca == 4:
                norm_expr = 1 + sigma * p * z * np.cos(2 * np.pi * k * m / N)
                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))
            elif ca == 5:
                term1 = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)
                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)
                norm_factor = np.sqrt(R / (2 * N**2 * term1 * term2))
            else:
                raise ValueError(f"代表元{col}的对称性分类ca={ca}无效")
            
            # 计算相位因子
            for r, s in enumerate(orbit):
                # 平移相位
                trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)
                
                # 对称性操作相位
                sym_phase = 1.0
                if ca in [2, 4, 5]:
                    # 反演操作相位
                    inv_phase = calculate_inversion_phase(N, s, orbit, m, k, R, sigma, p)
                    sym_phase *= inv_phase
                
                if ca in [3, 4, 5]:
                    # 自旋翻转操作相位
                    flip_phase = calculate_flip_phase(N, s, orbit, n, k, R, z)
                    sym_phase *= flip_phase
                
                # 填充投影矩阵
                V[s, col] += norm_factor * trans_phase * sym_phase
    
    # 确保投影矩阵是幺正的
    V_dag = V.conj().T
    identity = np.eye(nrep)
    ortho_error = np.linalg.norm(V_dag @ V - identity, ord='fro')
    
    if ortho_error > 1e-10:
        print(f"警告：投影矩阵不是严格幺正的，误差为 {ortho_error}")
        # 进行QR分解以确保正交性
        Q, R_mat = np.linalg.qr(V)
        V = Q[:, :nrep]
    
    return V


def generate_translation_orbit(N, rep, R):
    """
    生成平移轨道（只包含平移操作生成的状态）
    
    参数:
        N: 系统大小
        rep: 代表元
        R: 平移周期
    返回:
        orbit: 包含平移操作生成的状态列表
    """
    orbit = []
    current = rep
    
    for _ in range(R):
        if current in orbit:
            break
        orbit.append(current)
        current = translate_k(N, current, 1)  # 平移一个位置
    
    if len(orbit) != R:
        raise RuntimeError(f"平移轨道生成错误: 期望长度{R}, 实际长度{len(orbit)}")
    
    return orbit


def calculate_inversion_phase(N, state, orbit, m, k, R, sigma, p):
    """
    计算反演操作的相位因子
    """
    inverted = invert_state_k(N, state, m)
    
    # 找到反演后状态在轨道中的位置
    try:
        idx = orbit.index(inverted)
        # 如果状态在反演下不变，添加额外因子
        if inverted == state:
            return 1.0 + sigma * p
        else:
            return np.exp(1j * 2 * np.pi * k * idx / R)
    except ValueError:
        # 如果反演状态不在轨道中，返回0
        return 0.0


def calculate_flip_phase(N, state, orbit, n, k, R, z):
    """
    计算自旋翻转操作的相位因子
    """
    flipped = flip_all_spins_k(N, state)
    
    # 找到自旋翻转后状态在轨道中的位置
    try:
        idx = orbit.index(flipped)
        # 如果状态在自旋翻转下不变，添加额外因子
        if flipped == state:
            return 1.0 + z
        else:
            return np.exp(1j * 2 * np.pi * k * idx / R)
    except ValueError:
        # 如果自旋翻转状态不在轨道中，返回0
        return 0.0


def translate_k(L, state, n_translation_sites):
    """
    平移操作
    """
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites) % L, site_value)
    return new_state


def invert_state_k(N, state, m):
    """
    空间反演操作：以位置m为中心进行反演
    """
    result = 0
    for i in range(N):
        # 计算反演后的位置
        j = (2 * m - i) % N
        if j < 0:
            j += N
        # 获取原位置i的比特并放到位置j
        bit = get_site_value(state, i)
        result = set_site_value(result, j, bit)
    return result


def flip_all_spins_k(N, state):
    """
    自旋翻转操作：将所有自旋翻转
    """
    # 将所有自旋翻转
    return state ^ ((1 << N) - 1)


def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):
    ca = capr[a]
    cb = capr[b]
    s = 2*(typee[a]%2)-1
    t = 2*(typee[b]%2)-1
    matelement = peri[a]/peri[b]
    if ca==2 or ca==5:
        matelement = matelement/ggun(s*p,mtrf[a],k,N)
    if ca==3 or ca==5:
        matelement = matelement/ggun(z,ntrf[a],k,N)
    if ca==4:
        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)
    if cb==2 or cb==5:
        matelement = matelement*ggun(t*p,mtrf[b],k,N)
    if cb==3 or cb==5:
        matelement = matelement*ggun(z,ntrf[b],k,N)
    if cb==4:
        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)
    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)
    if cb==1 or cb==3:
        matelement = matelement*ffun(t,s,l,k,N)
    elif cb==2 or cb==5:
        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)
    elif cb==4:
        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)
    return matelement

def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):
    Hk = np.zeros((nrep,) * 2).astype(complex)
    for ia in range(nrep):
        sa = repr[ia]
        if (ia > 1 and sa == repr[ia - 1]):
            continue
        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1
        Ez = 0
        for i in range(N):
            j = (i + 1) % N
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            if ai == aj:
                Ez += J
            else:
                Ez -= J
        for a in range(ia, ia + na):
            Hk[a, a] += Ez
        for i in range(N):
            #j = (i + 1) % N
            #ai = get_site_value(sa, i)
            #aj = get_site_value(sa, j)
            #if ai != aj:
            #横场项
            #sb = flip_state(sa, i)
            #if ai == 1:
            sb = flip_state(sa, i)
            #else:
                #sb = flip_state(flip_state(sa, j), i)
            representative, l, q, g = represent(N, sb)
            if representative in repr:
                ib = repr.index(representative)
                if ib > 1 and repr[ib] == repr[ib - 1]:
                    ib = ib - 1
                    nb = 2
                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:
                    nb = 2
                else:
                    nb = 1
                for ii in range(ia, ia + na):
                    for jj in range(ib, ib + nb):
                        try:
                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)
                            if np.isfinite(elem):
                                Hk[ii, jj] += elem
                        except Exception as e:
                            print(f"helement error at ii={ii}, jj={jj}: {e}")
    return Hk


def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):
    Hk = np.zeros((nrep,) * 2).astype(complex)
    for ia in range(nrep):
        sa = repr[ia]
        if (ia>1 and sa==repr[ia-1]):
            continue
        elif (ia<nrep-1 and sa==repr[ia+1]):
            na=2
        else:
            na=1
        for a in range(ia,ia+na):
            Hk[a,a] += (1/2 )*N    
        for i in range(N):
            for j in range(i+1,N):
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                if ai != aj:
                    if  ai == 1:   
                        sb = flip_state(flip_state(sa,i),j)
                    else:
                        sb = flip_state(flip_state(sa,j),i)
                    representative, l,q,g = represent(N,sb)
                    if representative in repr:
                        ib = repr.index(representative)
                        if ib >1 and repr[ib]==repr[ib-1]:
                            ib = ib-1
                            nb=2
                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:
                            nb=2
                        else:
                            nb=1
                        for ii in range(ia,ia+na):
                            for jj in range(ib,ib+nb):
                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)
    return Hk

def transform(nrep,mat,vec):
    Hk = []
    a = np.transpose(vec) @ (mat @ vec)
    for i in range(nrep):
        Hk.append(a[i,i])
    return Hk    

def fullspectrum(N):
    E=[]
    #k=0
    k_min = []
    p_min = []
    z_min = []
    E_min = []
    spi_min = []

    v_full = []
    Hk_block_full = []
    new_basis_matrix = []
    for k in range(N):
        if k==0 or k==N//2:
            p1=-1
            p2=1
        else:
            p1=1
            p2=1
        for p in range(p1,p2+1,2):
            for z in [-1,1]:
                nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)
                if nrep != 0:
                    Hk = Ham_total_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)
                    eigenvalue, featurevector =np.linalg.eig(Hk)
                    # 拼接为完整矩阵
                    if len(Hk_block_full) == 0:
                        Hk_block_full = Hk
                    else:
                        Hk_block_full = block_direct_sum([Hk_block_full,Hk])#np.block(block_structure)
                    Hk_spin = sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)
                    spn = transform(nrep,Hk_spin,featurevector)
                    spin = []
                    for spin_i in range(len(spn)):
                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))
                    E1 = eigenvalue.tolist()
                    #print(E1)
                    print(k,p,z,nrep,repr)
                    E.extend(eigenvalue.tolist())
                    
                    if len(v_full) == 0:
                        v_full = featurevector
                    else:
                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)
                    # 构造投影矩阵 V_k
                    #V_k = build_projection_matrix_for_k(N, repr, peri,mtrf,ntrf,capr,p,z,k )
                    V_k = build_projection_matrix(N, repr, peri, mtrf, ntrf, capr, p, z, k)
                    #矩阵按列直接拼接
                    if len(new_basis_matrix) == 0:
                        new_basis_matrix = V_k
                    else:
                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）
                    print(new_basis_matrix.shape)
                for i in range(len(E1)):
                    idx = E1.index(np.min(E1))
                    k_min.append(k)
                    p_min.append(p)
                    z_min.append(z)
                    E_min.append(E1[idx])
                    spi_min.append(spin[idx])
                    #print(len(E1))
                    #print(np.min(E1),E1.index(np.min(E1)))
                    E1.pop(idx)
                    spin.pop(idx)   
    return E,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_block_full

H_tot = Ham_total(N,J,h)
eigvals_all, v_full = np.linalg.eig(H_tot)
#eigvals_all= np.linalg.eigvalsh(H_tot)

E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform,Hk_blockfull = fullspectrum(N)
    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))
    #print(np.array(eigvals))


# 1. 直接对角化全空间Floquet算子 F
#evals_direct, evecs_direct = np.linalg.eig(F)
# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列
matched_indices = []
for eval_block in E_full:  # energies是分块对角化得到的本征值（Hk的本征值）
    # 找到直接对角化中最接近的本征值索引
    idx = np.argmin(np.abs(eigvals_all - eval_block))
    matched_indices.append(idx)
# 3. 按匹配顺序重新排列直接对角化的本征矢
print(matched_indices)
evecs_direct_matched = v_full[:, matched_indices]
# 4. 验证一致性（忽略相位，对比绝对值或内积）
# 方法1：归一化后对比绝对值
full_evecs_norm = U_transform @ v_block_full 
direct_evecs_norm = evecs_direct_matched 
abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))
print(f"归一化后绝对值误差：{abs_error:.2e}")  # 正常应<1e-6

# 方法2：计算内积绝对值（应为1，说明是同一本征矢）
inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) 
                  for i in range(2**N)]
print(f"最小内积绝对值：{min(inner_products):.2e}")  # 正常应>0.999

ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), 
                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))
IPR_block_full = np.sum(ipr1, axis=0)
ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), 
                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))
IPR_full = np.sum(ipr2, axis=0)

# 假设分块计算的本征值和IPR为 evals_block, ipr_block
# 整体计算的为 evals_full, ipr_full
matched_ipr = []
for e_block, ipr_b in zip(E_full, IPR_block_full):
    # 找到整体计算中与e_block接近的本征值
    idx = np.argmin(np.abs(eigvals_all - e_block))
    matched_ipr.append((ipr_b, IPR_full[idx]))
# 查看匹配后的IPR是否一致
print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))

# 绘制匹配后的IPR对比
plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')
plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker="x")
plt.legend()
plt.show()

print(U_transform @ U_transform.T.conj())
