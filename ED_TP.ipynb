{"cells": [{"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy.sparse.linalg import eigsh"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["J=1\n", "h=1\n", "N=8"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def ffun(t,s,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    if t == -1:\n", "        if s == -1:\n", "            f = np.cos(kk*l)\n", "        elif s==1:\n", "            f = -np.sin(kk*l)\n", "    elif t ==1:\n", "        if s == -1:\n", "            f = np.sin(kk*l)\n", "        elif s==1:\n", "            f = np.cos(kk*l)\n", "    return f\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    g=0\n", "    if t == -1:\n", "        g = 1-np.cos(kk*l)\n", "    elif t==1:\n", "        g = 1+np.cos(kk*l) \n", "    return g"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["def checkstate(s,k,N):\n", "    R=-1\n", "    tz=-1\n", "    tp = -1\n", "    tpz = -1\n", "    smax = 2**N-1\n", "    Sum_m = 0\n", "    for i in range(N):\n", "        Sum_m += get_site_value(s,i)\n", "    if Sum_m != N//2:\n", "        return R,tp,tz,tpz\n", "    t=s\n", "    for i in range(1,N+1):\n", "        t = translate(N,t,1)\n", "        az = smax -t\n", "        #print(t,s,az)\n", "        if t<s or az<s:\n", "            break\n", "        if t==s:\n", "            if k%(N/i)!=0:\n", "                break\n", "            R=i\n", "            break\n", "        #if az==s:\n", "        #   tz=i\n", "    '''t = reverseBits(s,N)\n", "    az = smax-t\n", "    for i in range(R):\n", "        if t<s or az<s:\n", "            R=-1\n", "            break\n", "        if t==s:\n", "            tp=i\n", "        if az==s:\n", "            tpz=i\n", "        t = translate(N,t,1)\n", "        az = smax-t'''\n", "    return R,tp,tz,tpz"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["(-1, -1, -1, -1)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["checkstate(297,10,16)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nreprcount = []  # 全局记录列表（按需保留）\\n\\ndef findbasis(N, k, p, z):\\n    # 1. 重命名\\'repr\\'为\\'repr_list\\'，避免覆盖内置函数\\n    repr_list = []\\n    typee = []# 分类标签\\n    peri = []# 平移对称性\\n    mtrf = []# 反演对称性\\n    ntrf = []# 自旋翻转对称性\\n    capr = []# 联合对称性\\n    \\n    for s in range(2 **N):\\n        # 2. 显式列出sigma值（替代range，更清晰）\\n        for sigma in (-1, 1):\\n            # 3. 每次迭代初始化m、n，避免跨状态污染\\n            m, n = None, None\\n            ca = None  # 显式初始化分类标签\\n            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\\n            \\n            # 基础过滤：仅处理有效平移对称性的状态\\n            if R <= -1:\\n                continue\\n            \\n            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\\n            if (k == 0 or k == N // 2) and (sigma == -1):\\n                R = -1  # 标记为无效状态\\n            \\n            # 5. 仅处理R仍有效的状态\\n            if R > 0:\\n                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\\n                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\\n                if tp == -1 and tz == -1 and tpz == -1:\\n                    ca = 1\\n                    m, n = None, None  # 明确赋值，避免未定义\\n                \\n                # 分支2：tp≠-1、tz=-1（反演-平移对称）\\n                elif tp != -1 and tz == -1:\\n                    ca = 2\\n                    m = tp\\n                    # 7. 浮点数比较改用np.isclose，增强稳健性\\n                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\\n                        R = -1\\n                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\\n                        R = -1\\n                \\n                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\\n                elif tp == -1 and tz != -1:\\n                    ca = 3\\n                    n = tz\\n                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\\n                        R = -1\\n                \\n                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\\n                elif tp == -1 and tz == -1 and tpz != -1:\\n                    ca = 4\\n                    m = tpz\\n                    n = None  # 明确n未定义\\n                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):\\n                        R = -1\\n                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):\\n                        R = -1\\n                \\n                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\\n                elif tp != -1 and tz != -1:\\n                    ca = 5\\n                    m, n = tp, tz\\n                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\\n                        R = -1\\n                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\\n                        R = -1\\n                \\n                # 8. 捕获未覆盖的状态组合（调试用）\\n                else:\\n                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\\n                    continue\\n                \\n                # 9. 最终检查：ca必须已定义且R仍有效\\n                if ca is not None and R > 0:\\n                    repr_list.append(s)\\n                    typee.append(2 * ca + (sigma + 1) / 2)\\n                    capr.append(ca)\\n                    peri.append(R)\\n                    mtrf.append(m)\\n                    ntrf.append(n)\\n    \\n    nrep = len(repr_list)\\n    reprcount.append(repr_list)\\n    # 10. 简化打印，避免输出过载\\n    print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\\n    return nrep, repr_list, typee, peri, mtrf, ntrf, capr\\n    '"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "'''\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p, z):\n", "    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数\n", "    repr_list = []\n", "    typee = []# 分类标签\n", "    peri = []# 平移对称性\n", "    mtrf = []# 反演对称性\n", "    ntrf = []# 自旋翻转对称性\n", "    capr = []# 联合对称性\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 显式列出sigma值（替代range，更清晰）\n", "        for sigma in (-1, 1):\n", "            # 3. 每次迭代初始化m、n，避免跨状态污染\n", "            m, n = None, None\n", "            ca = None  # 显式初始化分类标签\n", "            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\n", "            \n", "            # 基础过滤：仅处理有效平移对称性的状态\n", "            if R <= -1:\n", "                continue\n", "            \n", "            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\n", "            if (k == 0 or k == N // 2) and (sigma == -1):\n", "                R = -1  # 标记为无效状态\n", "            \n", "            # 5. 仅处理R仍有效的状态\n", "            if R > 0:\n", "                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\n", "                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\n", "                if tp == -1 and tz == -1 and tpz == -1:\n", "                    ca = 1\n", "                    m, n = None, None  # 明确赋值，避免未定义\n", "                \n", "                # 分支2：tp≠-1、tz=-1（反演-平移对称）\n", "                elif tp != -1 and tz == -1:\n", "                    ca = 2\n", "                    m = tp\n", "                    # 7. 浮点数比较改用np.isclose，增强稳健性\n", "                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\n", "                elif tp == -1 and tz != -1:\n", "                    ca = 3\n", "                    n = tz\n", "                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\n", "                elif tp == -1 and tz == -1 and tpz != -1:\n", "                    ca = 4\n", "                    m = tpz\n", "                    n = None  # 明确n未定义\n", "                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\n", "                elif tp != -1 and tz != -1:\n", "                    ca = 5\n", "                    m, n = tp, tz\n", "                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 8. 捕获未覆盖的状态组合（调试用）\n", "                else:\n", "                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\n", "                    continue\n", "                \n", "                # 9. 最终检查：ca必须已定义且R仍有效\n", "                if ca is not None and R > 0:\n", "                    repr_list.append(s)\n", "                    typee.append(2 * ca + (sigma + 1) / 2)\n", "                    capr.append(ca)\n", "                    peri.append(R)\n", "                    mtrf.append(m)\n", "                    ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 10. 简化打印，避免输出过载\n", "    print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr\n", "    '''"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "#仅保留平移对称性\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p=None, z=None):  # 1. 非平移参数p/z设为默认None（无需传入）\n", "    repr_list = []\n", "    typee = []\n", "    peri = []\n", "    mtrf = []  # 反演相关参数，后续无赋值（可保留占位）\n", "    ntrf = []  # 自旋翻转相关参数，后续无赋值（可保留占位）\n", "    capr = []  # 联合对称类标签，后续简化（可保留占位）\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 注释/删除sigma循环（自旋宇称与平移无关，无需遍历）\n", "        # for sigma in (-1, 1):\n", "        sigma = 1  # 固定sigma=1，避免无意义循环（仅保留形式，不影响逻辑）\n", "        m, n = None, None\n", "        ca = 1  # 3. 简化对称类：仅保留1类（无反演/自旋翻转对称）\n", "        R, tp, tz, tpz = checkstate(s, k, N)  # 获取平移周期R（核心）\n", "        \n", "        # 4. 仅保留平移对称性的基础过滤：R>0表示满足平移对称\n", "        if R <= -1:\n", "            continue\n", "        \n", "        # 5. 注释/删除特殊k值的sigma约束（sigma已固定，该逻辑无效）\n", "        # if (k == 0 or k == N // 2) and (sigma == -1):\n", "        #     R = -1  # 标记为无效状态\n", "        \n", "        if R > 0:\n", "            # 6. 注释/删除所有反演/自旋翻转相关的分支逻辑（ca已固定为1）\n", "            # 原ca=2~5的分支全删除，仅保留ca=1的基础赋值\n", "            m, n = None, None  # 反演/自旋翻转参数无意义，设为None\n", "            \n", "            # 7. 注释/删除反演/自旋翻转的兼容性验证（ggun与平移无关）\n", "            # if np.isclose(ggun(...), 0, atol=1e-8):\n", "            #     R = -1\n", "            \n", "            # 最终筛选：仅需ca已定义（固定为1）且R>0（平移对称有效）\n", "            if ca is not None and R > 0:\n", "                repr_list.append(s)\n", "                # 8. 简化typee：仅体现ca=1和sigma=1（无需复杂计算）\n", "                typee.append(2 * ca + (sigma + 1) / 2)\n", "                capr.append(ca)  # 固定为1，标记仅平移对称\n", "                peri.append(R)   # 记录平移周期（核心输出，体现平移对称性）\n", "                mtrf.append(m)\n", "                ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 9. 简化打印：仅输出k和有效状态数（p/z已无关）\n", "    print(f\"k={k}: 有效状态数={nrep}（仅平移对称）\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            if ai != aj:\n", "                b = flip_state(a, i)\n", "                b = flip_state(b, j)\n", "                H[a, b] += h\n", "    return H"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        #az=smax-at\n", "        #if az<a:\n", "        #    a=az\n", "        #   l=t\n", "        #    g=1    \n", "    '''at = reverseBits(a0,L)\n", "    for t in range(L):\n", "        if at<a:\n", "            a=at\n", "            l=t\n", "            q=1\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            q=1\n", "            g=1\n", "        at = translate(L, at, 1)'''\n", "    return a,l,q,g"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = 2*(typee[a]%2)-1\n", "    t = 2*(typee[b]%2)-1\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)\n", "    return matelement"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):\\n    Hk = np.zeros((nrep,) * 2).astype(complex)\\n    for ia in range(nrep):\\n        sa = repr[ia]\\n        if (ia > 1 and sa == repr[ia - 1]):\\n            continue\\n        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\\n        Ez = 0\\n        for i in range(N):\\n            j = (i + 1) % N\\n            ai = get_site_value(sa, i)\\n            aj = get_site_value(sa, j)\\n            if ai == aj:\\n                Ez += J /4\\n            else:\\n                Ez -= J /4\\n        for a in range(ia, ia + na):\\n            Hk[a, a] += Ez\\n        for i in range(N):\\n            j = (i + 1) % N\\n            ai = get_site_value(sa, i)\\n            aj = get_site_value(sa, j)\\n            if ai != aj:\\n                #横场项\\n                #sb = flip_state(sa, i)\\n                if ai == 1:\\n                    sb = flip_state(flip_state(sa, i), j)\\n                else:\\n                    sb = flip_state(flip_state(sa, j), i)\\n                representative, l, q, g = represent(N, sb)\\n                if representative in repr:\\n                    ib = repr.index(representative)\\n                    if ib > 1 and repr[ib] == repr[ib - 1]:\\n                        ib = ib - 1\\n                        nb = 2\\n                    elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\\n                        nb = 2\\n                    else:\\n                        nb = 1\\n                    for ii in range(ia, ia + na):\\n                        for jj in range(ib, ib + nb):\\n                            try:\\n                                elem = h/2 * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)\\n                                if np.isfinite(elem):\\n                                    Hk[ii, jj] += elem\\n                            except Exception as e:\\n                                print(f\"helement error at ii={ii}, jj={jj}: {e}\")\\n    return Hk'"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["'''def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J /4\n", "            else:\n", "                Ez -= J /4\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai != aj:\n", "                #横场项\n", "                #sb = flip_state(sa, i)\n", "                if ai == 1:\n", "                    sb = flip_state(flip_state(sa, i), j)\n", "                else:\n", "                    sb = flip_state(flip_state(sa, j), i)\n", "                representative, l, q, g = represent(N, sb)\n", "                if representative in repr:\n", "                    ib = repr.index(representative)\n", "                    if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                        ib = ib - 1\n", "                        nb = 2\n", "                    elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                        nb = 2\n", "                    else:\n", "                        nb = 1\n", "                    for ii in range(ia, ia + na):\n", "                        for jj in range(ib, ib + nb):\n", "                            try:\n", "                                elem = h/2 * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)\n", "                                if np.isfinite(elem):\n", "                                    Hk[ii, jj] += elem\n", "                            except Exception as e:\n", "                                print(f\"helement error at ii={ii}, jj={jj}: {e}\")\n", "    return Hk'''"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, k):\n", "    # 1. 删除与反演/自旋翻转相关的参数p、z（不再传入）\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        # 跳过重复基矢（平移对称下可能出现的简并）\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        # 确定当前基矢的简并度（na=1或2）\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        \n", "        # 2. 计算自旋-自旋相互作用对角项（仅与平移对称兼容）\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N  # 周期边界条件（平移对称性核心）\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J / 4\n", "            else:\n", "                Ez -= J / 4\n", "        # 填充对角元（覆盖简并基矢）\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        \n", "        # 3. 计算横场项（仅保留平移对称相关逻辑）\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai != aj:\n", "                # 生成自旋翻转后的状态sb（仅与平移相关的翻转）\n", "                if ai == 1:\n", "                    sb = flip_state(flip_state(sa, i), j)\n", "                else:\n", "                    sb = flip_state(flip_state(sa, j), i)\n", "                \n", "                # 4. 仅用平移对称找代表态（忽略反演/自旋翻转参数）\n", "                representative, l, _, _ = represent(N, sb)  # 忽略q、g（反演/翻转标记）\n", "                \n", "                if representative in repr:\n", "                    ib = repr.index(representative)\n", "                    # 确定目标基矢的简并度\n", "                    if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                        ib = ib - 1\n", "                        nb = 2\n", "                    elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                        nb = 2\n", "                    else:\n", "                        nb = 1\n", "                    \n", "                    # 5. 计算矩阵元时仅保留平移相关参数（删除反演/翻转参数）\n", "                    for ii in range(ia, ia + na):\n", "                        for jj in range(ib, ib + nb):\n", "                            try:\n", "                                # 简化helement调用：仅传入平移相关参数（l是平移步数）\n", "                                elem = h/2 * helement_translation(ii, jj, typee, peri, mtrf, ntrf, capr,l,k,N)\n", "                                if np.isfinite(elem):\n", "                                    Hk[ii, jj] += elem\n", "                            except Exception as e:\n", "                                print(f\"矩阵元计算错误 at ii={ii}, jj={jj}: {e}\")\n", "    return Hk\n", "\n", "\n", "# 6. 简化的矩阵元函数（仅考虑平移对称性）\n", "def helement_translation(a, b, typee, peri, mtrf, ntrf, capr, l,k, N):\n", "    # 1. 删除与反演/自旋翻转相关的参数：p, z, q, g\n", "    # 2. 对称类简化：仅关注平移对称性（忽略ca/cb的反演/翻转分类）\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    \n", "    # 3. 自旋宇称简化：仅保留平移相关的自旋标记（或直接固定为1）\n", "    s = 2 * (typee[a] % 2) - 1  # 可简化为 s = 1（若自旋宇称与平移无关）\n", "    t = 2 * (typee[b] % 2) - 1  # 可简化为 t = 1\n", "    \n", "    # 4. 初始化矩阵元：仅保留平移周期比（核心平移对称参数）\n", "    matelement = peri[a] / peri[b]\n", "    \n", "    # 5. 注释/删除所有反演/自旋翻转相关的振幅修正（基于ggun的部分）\n", "    # 原ca=2/3/4/5的修正逻辑全部删除\n", "    # if ca==2 or ca==5: ...\n", "    # if ca==3 or ca==5: ...\n", "    # if ca==4: ...\n", "    # if cb==2 or cb==5: ...\n", "    # if cb==3 or cb==5: ...\n", "    # if cb==4: ...\n", "    \n", "    # 6. 注释/删除反演+自旋翻转的相位修正\n", "    # 仅保留平移相关的振幅开方（若需要）\n", "    matelement = np.sqrt(matelement)  # 若平移对称无需开方，可直接删除此步\n", "    \n", "    # 7. 仅保留平移跃迁的相位修正（基于ffun），删除反演/翻转相关的叠加项\n", "    # 统一使用基础平移相位，忽略所有反演相关的term2\n", "    matelement = matelement * ffun(t, s, l, k, N)  # l为平移步数（需从参数传入）\n", "    \n", "    return matelement\n", "\n", "\n", "# 辅助函数：仅考虑平移对称性的ffun（示例实现）\n", "def ffun(t, s, l, k, N):\n", "    \"\"\"仅计算平移l步在动量k扇区的相位（布洛赫相位）\"\"\"\n", "    # 核心：平移l步的相位因子 e^(i * 2πk l / N)\n", "    return np.exp(1j * 2 * np.pi * k * l / N)\n", "\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):\\n    Hk = np.zeros((nrep,) * 2).astype(complex)\\n    for ia in range(nrep):\\n        sa = repr[ia]\\n        if (ia>1 and sa==repr[ia-1]):\\n            continue\\n        elif (ia<nrep-1 and sa==repr[ia+1]):\\n            na=2\\n        else:\\n            na=1\\n        for a in range(ia,ia+na):\\n            Hk[a,a] += (1/2 )*N    \\n        for i in range(N):\\n            for j in range(i+1,N):\\n                ai = get_site_value(sa, i)\\n                aj = get_site_value(sa, j)\\n                if ai != aj:\\n                    if  ai == 1:   \\n                        sb = flip_state(flip_state(sa,i),j)\\n                    else:\\n                        sb = flip_state(flip_state(sa,j),i)\\n                    representative, l,q,g = represent(N,sb)\\n                    if representative in repr:\\n                        ib = repr.index(representative)\\n                        if ib >1 and repr[ib]==repr[ib-1]:\\n                            ib = ib-1\\n                            nb=2\\n                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\\n                            nb=2\\n                        else:\\n                            nb=1\\n                        for ii in range(ia,ia+na):\\n                            for jj in range(ib,ib+nb):\\n                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)\\n    return Hk'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["'''def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia>1 and sa==repr[ia-1]):\n", "            continue\n", "        elif (ia<nrep-1 and sa==repr[ia+1]):\n", "            na=2\n", "        else:\n", "            na=1\n", "        for a in range(ia,ia+na):\n", "            Hk[a,a] += (1/2 )*N    \n", "        for i in range(N):\n", "            for j in range(i+1,N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                if ai != aj:\n", "                    if  ai == 1:   \n", "                        sb = flip_state(flip_state(sa,i),j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa,j),i)\n", "                    representative, l,q,g = represent(N,sb)\n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        if ib >1 and repr[ib]==repr[ib-1]:\n", "                            ib = ib-1\n", "                            nb=2\n", "                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\n", "                            nb=2\n", "                        else:\n", "                            nb=1\n", "                        for ii in range(ia,ia+na):\n", "                            for jj in range(ib,ib+nb):\n", "                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)\n", "    return Hk'''"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,k):\n", "    # 1. 删除与反演/自旋翻转相关的参数：p, z\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        # 跳过重复基矢（平移对称下的简并态）\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        # 确定当前基矢的简并度（na=1或2）\n", "        elif (ia < nrep - 1 and sa == repr[ia + 1]):\n", "            na = 2\n", "        else:\n", "            na = 1\n", "        \n", "        # 填充对角元（常数项，与平移对称兼容）\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += (1/2) * N    \n", "        \n", "        # 遍历所有长程格点对（i < j）\n", "        for i in range(N):\n", "            for j in range(i + 1, N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                \n", "                if ai != aj:  # 仅反向自旋有非零跃迁\n", "                    # 生成自旋交换后的状态sb\n", "                    if ai == 1:   \n", "                        sb = flip_state(flip_state(sa, i), j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa, j), i)\n", "                    \n", "                    # 2. 仅保留平移相关的代表态参数（忽略反演/自旋翻转标记q、g）\n", "                    representative, l, _, _ = represent(N, sb)  # 丢弃q、g\n", "                    \n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        # 确定目标基矢的简并度（nb=1或2）\n", "                        if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                            ib = ib - 1\n", "                            nb = 2\n", "                        elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                            nb = 2\n", "                        else:\n", "                            nb = 1\n", "                        \n", "                        # 计算非对角矩阵元（仅保留平移对称修正）\n", "                        for ii in range(ia, ia + na):\n", "                            for jj in range(ib, ib + nb):\n", "                                # 3. 调用简化的helement（仅传入平移相关参数）\n", "                                Hk[ii, jj] += J * helement_translation(ii, jj, typee, peri, mtrf, ntrf, capr,l,k,N)\n", "    return Hk\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    "]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def fullspectrum(N,min_n,k):\\n    E=[]\\n    #k=0\\n    k_min = []\\n    p_min = []\\n    z_min = []\\n    E_min = []\\n    spi_min = []\\n    if k==0 or k==N//2:\\n        p1=-1\\n        p2=1\\n    else:\\n        p1=1\\n        p2=1\\n    for p in range(p1,p2+1,2):\\n        for z in [-1,1]:\\n            nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)\\n            if nrep != 0:\\n                Hk = Ham_total_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\\n                eigenvalue, featurevector =np.linalg.eigh(Hk)\\n                Hk_spin = sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\\n                spn = transform(nrep,Hk_spin,featurevector)\\n                spin = []\\n                for spin_i in range(len(spn)):\\n                    spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\\n                E1 = eigenvalue.tolist()\\n                #print(len(E),len(eigenvalue))\\n                E.extend(eigenvalue.tolist())\\n                \\n            for i in range(len(E1)):\\n                idx = E1.index(np.min(E1))\\n                k_min.append(k)\\n                p_min.append(p)\\n                z_min.append(z)\\n                E_min.append(E1[idx])\\n                spi_min.append(spin[idx])\\n                #print(len(E1))\\n                #print(np.min(E1),E1.index(np.min(E1)))\\n                E1.pop(idx)\\n                spin.pop(idx)   \\n    return E,k_min,p_min,z_min,E_min,spi_min'"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["'''def fullspectrum(N,min_n,k):\n", "    E=[]\n", "    #k=0\n", "    k_min = []\n", "    p_min = []\n", "    z_min = []\n", "    E_min = []\n", "    spi_min = []\n", "    if k==0 or k==N//2:\n", "        p1=-1\n", "        p2=1\n", "    else:\n", "        p1=1\n", "        p2=1\n", "    for p in range(p1,p2+1,2):\n", "        for z in [-1,1]:\n", "            nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)\n", "            if nrep != 0:\n", "                Hk = Ham_total_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                eigenvalue, featurevector =np.linalg.eigh(Hk)\n", "                Hk_spin = sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                spn = transform(nrep,Hk_spin,featurevector)\n", "                spin = []\n", "                for spin_i in range(len(spn)):\n", "                    spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\n", "                E1 = eigenvalue.tolist()\n", "                #print(len(E),len(eigenvalue))\n", "                <PERSON>.extend(eigenvalue.tolist())\n", "                \n", "            for i in range(len(E1)):\n", "                idx = E1.index(np.min(E1))\n", "                k_min.append(k)\n", "                p_min.append(p)\n", "                z_min.append(z)\n", "                E_min.append(E1[idx])\n", "                spi_min.append(spin[idx])\n", "                #print(len(E1))\n", "                #print(np.min(E1),E1.index(np.min(E1)))\n", "                E1.pop(idx)\n", "                spin.pop(idx)   \n", "    return E,k_min,p_min,z_min,E_min,spi_min'''"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def fullspectrum(N, min_n, k):\n", "    # 存储所有能量本征值（全谱）\n", "    E = []\n", "    # 存储每个能量对应的动量（平移量子数）\n", "    k_all = []\n", "    # 存储每个能量态的自旋期望值（全谱）\n", "    spi_all = []\n", "    \n", "    # 仅基于平移对称性获取基组（无需p和z参数）\n", "    nrep, repr, typee, peri, mtrf, ntrf, capr = findbasis(N, k)\n", "    \n", "    if nrep != 0:  # 若平移对称基组有效\n", "        # 构建仅含平移对称的哈密顿量\n", "        Hk = Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, k)\n", "        eigenvalues, featurevectors = np.linalg.eigh(Hk)  # 得到所有能量本征值\n", "        \n", "        # 构建长程自旋矩阵（仅平移对称）\n", "        Hk_spin = sqinsquared_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, k)\n", "        spn = transform(nrep, Hk_spin, featurevectors)  # 计算所有态的自旋期望值\n", "        \n", "        # 计算每个本征态的自旋期望值（全谱）\n", "        spin = []\n", "        for spin_i in range(len(spn)):\n", "            spin.append(1/2 * abs(np.sqrt(1 + 4 * spn[spin_i]) - 1))\n", "        \n", "        # 收集当前动量扇区的所有能量（全谱）\n", "        <PERSON>.extend(eigenvalues.tolist())\n", "        \n", "        # 为每个能量态记录对应的动量（当前k值）\n", "        # 长度与能量本征值数量一致，确保一一对应\n", "        k_all.extend([k] * len(eigenvalues))\n", "        \n", "        # 记录所有态的自旋期望值\n", "        spi_all.extend(spin)\n", "    \n", "    # 输出全谱信息：所有能量、对应动量、对应自旋期望\n", "    return E, k_all, spi_all\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["#H_tot = Ham_total(N,J,h)\n", "#eigvals_all, eigvecs_all = np.linalg.eig(H)\n", "#eigvals_all= np.linalg.eigvalsh(H_tot)\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["#min_n =8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,0)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["k=0: 有效状态数=7（仅平移对称）\n", "k=1: 有效状态数=5（仅平移对称）\n", "k=2: 有效状态数=6（仅平移对称）\n", "k=3: 有效状态数=5（仅平移对称）\n", "k=4: 有效状态数=7（仅平移对称）\n", "k=5: 有效状态数=5（仅平移对称）\n", "k=6: 有效状态数=6（仅平移对称）\n", "k=7: 有效状态数=5（仅平移对称）\n", "k=8: 有效状态数=7（仅平移对称）\n", "53 53\n"]}], "source": ["min_n =8\n", "kkkk = 0\n", "eeee = 0\n", "for k in range(N+1):\n", "    eigvals,k_min,spi_min = fullspectrum(N,min_n,k)\n", "    #eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,k)\n", "    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))\n", "    #print(np.array(eigvals))\n", "    kkkk+= len(k_min)\n", "    eeee+= len(eigvals)\n", "print(kkkk,eeee)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-2.916162342731737,\n", " -1.6056336923831471,\n", " -0.5132271632620368,\n", " -0.3793440135596989,\n", " 0.24188732131995752,\n", " 0.8714263968510806,\n", " 1.301053493765582]"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["eigvals"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["#print (len(eigvals),len(eigvals_all))\n", "#for x in eigvals:\n", "#    index = np.abs(eigvals_all - x).argmin()\n", "#    if np.isclose(eigvals_all[index], x) != True:\n", "#        print(np.isclose(eigvals_all[index], x), eigvals_all[index], x)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["#min_n=8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["file = open('ED_TPZ_1D_N=16.txt', 'w') \n", "lines = [\"\"] \n", "with open('ED_TPZ_1D_N=16.txt', 'w') as file: \n", "    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\n", "    for i in range(len(k_min)):\n", "        if i%(min_n)==0:\n", "            file.write(\"----------------------------------------\"+\"\\n\")\n", "        file.write(str(k_min[i])+\"\\t\")\n", "        #file.write(str(p_min[i])+\"\\t\")\n", "        #file.write(str(z_min[i])+\"\\t\")\n", "        #file.write(str(E_min[i])+\"\\t\")\n", "        file.write(str(spi_min[i])+\"\\t\")\n", "        file.write(\"\\n\")\n", "file.close()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "系统总维数: 16\n", "有效状态总数: 10\n", "遗漏基矢数量: 6\n", "遗漏的基矢: [5, 7, 8, 10, 13, 15]...\n", "验证失败：有效状态数与总维数不一致\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA04AAAE6CAYAAAAhjAJ/AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAw1klEQVR4nO3de1hVdaLG8XcDctEEFRVEkDA1TTIFNMW8J6SNZtNJy8ZLdjphliGjeT1TOibZmN0MzVNqnbEOp1Izj5doNJS0VJRuOlN5wwQiHAWvKLDOHz3uZwhw7a3A2rC/n+fZzyNr/dbe717u3L2stX7LZhiGIQAAAABAlTysDgAAAAAAro7iBAAAAAAmKE4AAAAAYILiBAAAAAAmKE4AAAAAYILiBAAAAAAmKE4AAAAAYILiBAAAAAAmKE4AAAAAYILiBAAAAAAmKE4AUEsOHjyoJk2aXPXxww8/uN04Mzt27JCPj4+OHTtmX9a/f3/ZbDbdddddFcYfPXpUNptNixYtqta/P0l68803ZbPZdMMNN1zzc9x0001X3ScrV66skXHXY86cOfrd736n1q1by2azafz48VWONQxDK1euVI8ePdSoUSP5+/srKipKH330kX3MqVOn1KRJE61bt+66swFAbfGyOgAAuIvS0lJFRkYqIyOj0vV33HGHSktL3W7c1RiGocTERD366KMKDw+vsH7Lli3aunWrBg4ceNXnqQ4nTpzQ1KlTFRISosLCwmt+nsLCQhUUFMjLq+JX8Jw5c1RcXFwj467HSy+9pC5dumj48OFasWLFVcdOnDhRq1at0pQpU5ScnKySkhJ98803On/+vH1M06ZNNWXKFE2bNk1Dhw6Vt7f3dWcEgJpGcQIAuKzNmzdr3759evfddyus69Chg0pKSvT0009rz549stlsNZolISFBffv2VbNmzfTBBx/U6Gu5mjNnzsjD49eTVP77v/+7ynHr1q3TG2+8odTUVI0cOdK+PD4+vsLYhIQEzZ8/Xx988IFGjx5d/aEBoJpxqh4AwGUtXbpU3bt3180331xhXYMGDfTcc88pMzNTqampNZrjr3/9q9LT05WSklKjr+OqrpQmM6+88opuvPHGcqWpKkFBQRo8eLCWLVt2vfEAoFZQnAAALunSpUv69NNPNWDAgCrHjBo1StHR0ZozZ44uX7581ecrKSlx6GEYRrnt8vPzlZiYqOeff16hoaHV8t6scq37wNHn3rVrl7p166bFixcrPDxcnp6eatu2rRYtWlTpc/bv31+ff/65Tp8+XQ3vDgBqFsUJAOCSsrKydOHCBUVFRVU5xmazaeHChTp06JDeeOONKscdPXpUDRo0cOiRnp5ebtvHH39cN998syZOnFht780qju6Dt99+2+nnLigoUHFxsf72t7/ppZde0p///GelpaUpPj5e06ZN05w5cypsExUVpbKyMn3xxRfV8fYAoEZxjRMAwCXl5ORIklq2bHnVcYMGDVJcXJzmzZuncePGVTomJCREe/bsceh1//W0wA8//FAff/yx9u/fX+PXUNUGR/dBRESE089dVlYmSSoqKtKWLVvUs2dPSdLAgQOVl5enxYsXa+bMmeVmJLzyd3vixAmnXw8AahvFCQDgki5cuCBJ8vX1NR27cOFCRUVFadGiRXr44YcrrPf29lbXrl0del1PT09J0tmzZzVp0iQ9+eSTCgkJsZ9OdunSJUnS6dOn1aBBAzVq1Mih53UFzu4DZzRt2lQ2m02NGze2l6YrhgwZonXr1unAgQPq0aOHffmVv9srf9cA4Mo4VQ8A4JKaN28uSfrnP/9pOrZr16568MEHtXjxYv38888V1l/LqXoFBQX6+eef9eKLL6pp06b2x3vvvadz586padOmeuihh6r3TdewmjxVz8/PT+3bt6903ZXrm347ycSVv9srf9cA4Mo44gQAcEmdOnWSJB06dMih8Vemtp47d26Fdddyql5wcLC2bdtWYf3zzz+v9PR0bdq0qc79D39NnqonSffdd5+Sk5O1c+dOxcbG2pdv3LhRN9xwgzp37lxu/OHDhyVJt9xyyzW9HgDUJooTAMAlhYaGqm3btvriiy80efJk0/ERERGaOHGiXnnllQrrvL29FRMT49Tr+/r6qn///hWWr1q1Sp6enhXWrVq1Sg8//LBWrlyp8ePHO/VatcXZfXBFenq6fvnlF0m/3sj52LFj9ntZ9evXTy1atJAkTZ06VatXr9b999+vP//5zwoNDdUHH3yg9evXa9GiRfLz8yv3vF988YUCAwN16623Xse7AoDaQXECALishx56SEuWLFFxcbF8fHxMx8+ZM0crV65UUVFRLaQr7+zZs5KkVq1a1fpr17Rnnnmm3GyDn332mT777DNJ0rZt2+wlslmzZsrIyNDTTz+tqVOn6ty5c+rYsaNWrFhR4dozwzC0fv16jR49ul5MvAGg/qM4AQBcVkJCghYuXKh169Zp1KhR9uVX/qf9t5o3b67CwsIazbRq1SqtWrWqwvLt27crJiZG8fHxNfr6Vqhqf1cmLCxM7733num4rVu36vjx43r88cevIxkA1B4mhwAAuKyQkBAlJibqueees0937YoMw9Bnn32m5557zuoodcb8+fM1YcIEdezY0eooAOAQjjgBQC3x9PTUV199pSZNmlS6vrS0VB4eHm43zsycOXPUsGFDnThxQmFhYabjrWCz2ZSfn+/Q2BtuuKHKSSXKysr00ksv1cg4V3Lq1Cn169ePo00A6hSbcWWOUAAAAABApThVDwAAAABMUJwAAAAAwITbXeNUVlamnJwcNW7cmOlPAQAAADdmGIbOnDmjkJAQ0+tu3a445eTkuOzFxQAAAABq3/HjxxUaGnrVMW5XnBo3bizp153j7+9vcRoAAAAAVikqKlJYWJi9I1yN2xWnK6fn+fv7U5wAAAAAOHQJD5NDAAAAAIAJihMAAAAAmKA4AQAAAIAJihMAAAAAmKA4AQAAAIAJihMAAAAAmKA4AQAAAIAJihMAAAAAmKA4AQAAAIAJihMAAAAAmKA4AQAAAIAJihMAAAAAmLC0OG3fvl3Dhg1TSEiIbDab1q1bZ7pNenq6oqOj5evrq7Zt22rZsmU1HxQAAACAW7O0OJ07d0633XablixZ4tD4I0eOaOjQoerTp4/279+vWbNmafLkyfrwww9rOGn1y8nJ0e9+9zsFBwcrOjpan3/+udWRHHb8+HENGTJEQUFB6t69u7788kurIznsyJEjiouLU1BQkHr27Kl9+/ZZHclhP/74owYNGqSgoCDFxsbq66+/tjqSw/7+97+rf//+CgoKUt++fXXgwAGrI7mFrKws9erVS0FBQbrzzjt16NAhqyPBxe3YsUNRUVEKDg7W8OHDlZuba3Ukt/DJJ5+oS5cuCg4O1siRI3Xy5EmrIzlsw4YNioyMVKtWrTR69GidOnXK6khu4X//93/VsWNHhYSE6JFHHtHZs2etjuQeDBchyVi7du1Vxzz99NNGx44dyy177LHHjJ49ezr8OoWFhYYko7Cw8FpiVovi4mKjY8eOhqenpyHJ8PDwMHx9fY1//OMflmVy1IULF4ybbrrJ8PLyMiQZnp6eRsOGDY3Dhw9bHc3U2bNnjfDwcPt+9/T0NBo3bmxkZ2dbHc3U6dOnjZCQkHLZmzRpYuTm5lodzdTJkyeNFi1alMseGBho/PLLL1ZHq9dOnDhhBAQE2Pe7l5eX0bp1a6OoqMjqaHBRBw8eNHx8fAwPDw/7Z6Zz587GpUuXrI5Wr+3Zs8fw8vKy73dPT0+jZ8+eRllZmdXRTH3++eeGh4eHYbPZ7Nn79etXJ7LXZVu2bDEk2R+enp7GPffcY3WsOsuZblCnrnHatWuX4uLiyi2Lj4/X3r17dfny5Uq3KS4uVlFRUbmH1b7++mv9/e9/V2lpqSSprKxMly9f1tq1ay1OZm7v3r06dOiQSkpKJEmlpaW6ePGi1q9fb3Eyc7t27dKxY8fs+720tFRnz57Vxo0bLU5mLiMjQzk5OeWynz59Wlu2bLE4mblt27bpl19+KZf95MmT+vTTTy1OVr9t2rRJhYWF9v1eUlKiEydO1Kmj26hda9asUUlJicrKyiT9+pn57rvv9O2331qcrH57//33Jcm+30tLS/XFF1/o6NGjFqZyTGpqqjw8PGQYhqRfs6enp3OksoatXr1anp6e9p9LS0v10Ucf6dy5cxamcg91qjjl5eUpKCio3LKgoCCVlJSooKCg0m2Sk5MVEBBgf4SFhdVG1Kvy8Kh8t1e13JVUltEwjDqd3WazWZDGOVVlrAvZ6/LnvS6rav/Whc8MrMF/q9aoy/++V5WRz0zN4t9369S5T/ZvPxRXfstR1Ydl5syZKiwstD+OHz9e4xnNdOnSRd26dbP/tsDT01N+fn66//77LU5mLiYmRp07d5aXl5ekX7P7+/vr3nvvtTiZuV69eqlDhw7lsjdr1kzDhw+3OJm5vn37KiIiolz2Fi1aaOjQoRYnMzdw4ECFhobas3t5ealVq1YVjh6jet19991q3ry5/d8ZLy8v3XTTTerTp4/FyeCqRo4cKR8fn3LfTTExMYqMjLQ4Wf320EMPycPDw/4/w56enhowYIDCw8MtTmZuzJgxklQu+9ChQyv8khvV6+GHH1ZZWZn9/309PDz04IMPqmHDhhYnq//qVHEKDg5WXl5euWX5+fny8vJSYGBgpdv4+PjI39+/3MNqXl5e+uSTT/TAAw+offv26tu3r7Zv364bb7zR6mimvL29tXXrVt13331q3769BgwYoB07dig0NNTqaKb8/Pz02WefacSIEWrfvr0GDRqkjIwMBQcHWx3N1A033GCfhbJdu3aKj4/X559/rubNm1sdzVRAQIAyMjI0dOhQtWvXTnfddZcyMjLUpEkTq6PVay1btlRGRoYGDx6sdu3aafjw4UpPT+eLFVVq27attm/frj59+qh9+/Z68MEHtXnz5nKnBKH63Xrrrdq6dat69+6tDh06aMKECVq3bl2dOHoQHR2ttLQ0+y8mH330UaWmptaJ7HVZ3759tWHDBvXo0UM333yzJk+erBUrVlgdyy3YjCuHbCxms9m0du1ajRgxosox06dP18cff1xuRq6JEycqKytLu3btcuh1ioqKFBAQoMLCQpcoUQAAAACs4Uw3sPSI09mzZ5WVlaWsrCxJv04VnZWVpezsbEm/nmY3duxY+/iEhAQdO3ZMSUlJOnjwoFasWKG33npLU6dOtSI+AAAAADfhZeWL7927VwMGDLD/nJSUJEkaN26cVq1apdzcXHuJkqSIiAht3LhRU6ZM0euvv66QkBC9+uqruu+++2o9OwAAAAD34TKn6tUWTtUDAAAAINWhU/UAAAAAoC6gOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJigOAEAAACACYoTAAAAAJjwcnaDwsJCrV27Vjt27NDRo0d1/vx5tWjRQt26dVN8fLxiY2NrIicAAAAAWMbhI065ubl69NFH1apVK82bN0/nzp1T165dNWjQIIWGhmrbtm0aPHiwbrnlFqWmptZkZgAAAACoVQ4fcbrttts0duxY7d69W5GRkZWOuXDhgtatW6fFixfr+PHjmjp1arUFBQAAAACr2AzDMBwZ+Msvv6hFixYOP7Gz42tLUVGRAgICVFhYKH9/f6vjAAAAALCIM93A4VP1nC1BrliaAAAAAOBaODU5RFxcnM6ePevQWMMw1KxZM/3f//3fNQUDAAAAAFfhVHH65ZdftH//fofHd+/e3elAAAAAAOBqnLqPk81mc+rJnR0PAAAAAK6IG+ACAAAAgAmKEwAAAACYoDgBAAAAgAmnJocwDEMDBw6U2a2fbDabDMMwHSdJKSkp+stf/qLc3Fx17txZL7/8svr06VPl+NWrV+uFF17QDz/8oICAAN11111atGiRAgMDnXkrAAAAAOAwh2+AK0mXL19WWVmZw0/u4eGhBg0aVLk+NTVVY8aMUUpKinr37q033nhDb775pg4cOKA2bdpUGJ+RkaF+/frppZde0rBhw3TixAklJCSoffv2Wrt2rUOZuAEuAAAAAMm5buBUcVqyZIlycnIcDhIaGqrHH3+8yvW33367oqKitHTpUvuyTp06acSIEUpOTq4wftGiRVq6dKkOHTpkX/baa6/phRde0PHjxyt9jeLiYhUXF9t/LioqUlhYGMUJAAAAcHPOFCenTtVbsWKFXn75ZYdOwZOkadOmVVmcLl26pMzMTM2YMaPc8ri4OO3cubPSbWJjYzV79mxt3LhRQ4YMUX5+vj744APdfffdVWZITk7W3LlzHcoLAAAAAJVx+hqnvn37OjW+KgUFBSotLVVQUFC55UFBQcrLy6t0m9jYWK1evVqjRo3SxYsXVVJSouHDh+u1116r8nVmzpyppKQk+89XjjgBAAAAgKMsvwHub8cYhlHldgcOHNDkyZP1pz/9SZmZmdq8ebOOHDmihISEKp/fx8dH/v7+5R4AAAAA4AynjjhVp+bNm8vT07PC0aX8/PwKR6GuSE5OVu/evTVt2jRJUpcuXdSoUSP16dNH8+fPV6tWrWo8NwAAAAD3Y9l9nLy9vRUdHa20tLRyy9PS0hQbG1vpNufPn5eHR/nInp6ekq5+WiAAAAAAXA+nr3F65513HB5rVmaSkpI0ZswYxcTEqFevXlq+fLmys7Ptp97NnDlTJ06csL/msGHD9Oijj2rp0qWKj49Xbm6uEhMT1aNHD4WEhDjzVgAAAADAYU4Vpzlz5qigoMDh8bNmzbrq+lGjRunkyZOaN2+ecnNzFRkZqY0bNyo8PFySlJubq+zsbPv48ePH68yZM1qyZIn++Mc/qkmTJho4cKAWLlzozNsAAAAAAKc4dR+n7OxsXbx40eEn9/Pzc7kZ7LgBLgAAAACpBu/jNGLECHXt2tX0FDybzSbDMPTdd99p9+7dzrwEAAAAALgcp69xWrFihcPju3fv7nQgAAAAAHA1lt/HCQAAAABcnWXTkQMAAABAXUFxAgAAAAATThUnZ28yy01pAQAAANQHTk0O0aVLF8XGxjo1HgAAAADqOqeK09tvv11TOQAAAADAZXGNEwAAAACYuObi5O/vr8OHD1f4MwAAAADUN9dcnP514gcmgQAAAABQn3GqHgAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgIlrLk59+vSRn59fhT8DAAAAQH1jM9zsJkxFRUUKCAhQYWGh/P39rY4DAAAAwCLOdANO1QMAAAAAE17ObnDu3Dm9++672rlzp/Ly8mSz2RQUFKTevXvrwQcfVKNGjWoiJwAAAABYxqkjTgcOHFCHDh309NNP69SpU2rTpo1CQ0N16tQpTZs2TTfffLMOHDhQU1kBAAAAwBJOXeM0YMAABQcH6+2335a3t3e5dZcuXdL48eOVm5urbdu2VXvQ6sI1TgAAAAAk57qBU6fqffnll9q7d2+F0iRJ3t7emjVrlnr06OFcWgAAAABwcU6dqte0aVP98MMPVa7/8ccf1bRp0+sOBQAAAACuxKkjTo8++qjGjRunOXPmaPDgwQoKCpLNZlNeXp7S0tK0YMECJSYm1lBUAAAAALCG0/dxWrhwoV555RX7jHqSZBiGgoODlZiYqKeffrpGglYXrnECAAAAINXwfZymT5+unJwcHTp0SBkZGcrIyNChQ4eUk5NzTaUpJSVFERER8vX1VXR0tHbs2HHV8cXFxZo9e7bCw8Pl4+Ojm266SStWrHD6dQEAAADAUU7fx+mKiIgIRUREXNeLp6amKjExUSkpKerdu7feeOMNDRkyRAcOHFCbNm0q3WbkyJH6+eef9dZbb6ldu3bKz89XSUnJdeUAAAAAgKtx+lS96nT77bcrKipKS5cutS/r1KmTRowYoeTk5ArjN2/erAceeECHDx9Ws2bNruk1OVUPAAAAgFTDp+pVl0uXLikzM1NxcXHllsfFxWnnzp2VbrN+/XrFxMTohRdeUOvWrdWhQwdNnTpVFy5cqPJ1iouLVVRUVO4BAAAAAM645lP1rldBQYFKS0sVFBRUbnlQUJDy8vIq3ebw4cPKyMiQr6+v1q5dq4KCAj3++OP65z//WeV1TsnJyZo7d2615wcAAADgPiw74nTFlZn5rjAMo8KyK8rKymSz2bR69Wr16NFDQ4cO1eLFi7Vq1aoqjzrNnDlThYWF9sfx48er/T0AAAAAqN+qvThlZ2ertLTUdFzz5s3l6elZ4ehSfn5+haNQV7Rq1UqtW7dWQECAfVmnTp1kGIZ++umnSrfx8fGRv79/uQcAAAAAOKPai9ONN96oW265RWvWrLnqOG9vb0VHRystLa3c8rS0NMXGxla6Te/evZWTk6OzZ8/al33//ffy8PBQaGjo9YcHAAAAgEpUe3Hatm2bZs6cqffff990bFJSkt58802tWLFCBw8e1JQpU5Sdna2EhARJv55mN3bsWPv40aNHKzAwUA8//LAOHDig7du3a9q0aZowYYL8/Pyq+60AAAAAgKQamByiX79+6tevn8aPH286dtSoUTp58qTmzZun3NxcRUZGauPGjQoPD5ck5ebmKjs72z7+hhtuUFpamp588knFxMQoMDBQI0eO1Pz586v7bQAAAACAnVP3cVqzZo0KCgocfvKWLVtqxIgR15KrxnAfJwAAAABSDd7Haf78+fL19ZWPj49DjwULFlzXGwEAAAAAV+DUqXqGYZS75sjMkiVLnA4EAAAAAK7GqSNOVd1fqbrGAwAAAIArsvwGuAAAAADg6ihOAAAAAGDC6Wuctm/f7vBYJybsAwAAAACX5VRxmjBhgjZt2uTweEfu5QQAAAAArs6p4jRx4kSVlZU5PN7DgzMBAQAAANR9ThWnHj16qEmTJg6NNQxD58+f15dffnktuQAAAADAZTh9jdPWrVsdHt+9e3enAwEAAACAq+E+TgAAAABggouQAAAAAMAExQkAAAAATFCcAAAAAMCEU5NDNGvWTLGxsQ7f2DYwMPCaQgEAAACAK3GqOH366ac1lQMAAAAAXJZTxWnWrFk6evSow+PbtWunefPmOZsJAAAAAFyKU8Vp8+bNWrt2rUNjDcPQyJEjKU4AAAAA6jynb4AbHh7u1HgAAAAAqOu4AS4AAAAAmGA6cgAAAAAwQXECAAAAABNOX+Pk6GQPXN8EAAAAoL5wqjilpKSoqKjI4fHx8fFOBwIAAAAAV+NUcerVq1dN5QAAAAAAl3XN1zg9//zzOn36dIU/AwAAAEB9c83FacGCBfrnP/9Z4c8AAAAAUN9cc3H618kfmAgCAAAAQH1m+XTkKSkpioiIkK+vr6Kjo7Vjxw6Htvv888/l5eWlrl271mxAAAAAAG7P0uKUmpqqxMREzZ49W/v371efPn00ZMgQZWdnX3W7wsJCjR07VoMGDaqlpAAAAADcmaXFafHixXrkkUf07//+7+rUqZNefvllhYWFaenSpVfd7rHHHtPo0aMdmuWvuLhYRUVF5R4AAAAA4AzLitOlS5eUmZmpuLi4csvj4uK0c+fOKrdbuXKlDh06pGeeecah10lOTlZAQID9ERYWdl25AQAAALgfy4pTQUGBSktLFRQUVG55UFCQ8vLyKt3mhx9+0IwZM7R69Wp5eTl2C6qZM2eqsLDQ/jh+/Ph1ZwcAAADgXpy6AW5NsNls5X42DKPCMkkqLS3V6NGjNXfuXHXo0MHh5/fx8ZGPj8915wQAAADgvq65OG3atEmtW7eu8GdHNW/eXJ6enhWOLuXn51c4CiVJZ86c0d69e7V//3498cQTkqSysjIZhiEvLy998sknGjhw4DW+GwAAAACo2jUXpzvuuKPSPzvK29tb0dHRSktL07333mtfnpaWpnvuuafCeH9/f33zzTfllqWkpGjr1q364IMPFBER4XQGAAAAAHCEpafqJSUlacyYMYqJiVGvXr20fPlyZWdnKyEhQdKv1yedOHFC77zzjjw8PBQZGVlu+5YtW8rX17fCcgAAAACoTtVanL766itFRUWptLTUofGjRo3SyZMnNW/ePOXm5ioyMlIbN25UeHi4JCk3N9f0nk4AAAAAUNNshmEY1fVkX331lbp166aysrLqespqV1RUpICAABUWFsrf39/qOAAAAAAs4kw3cOqI0+9///urri8sLKx0RjwAAAAAqMucKk4ff/yxBg8eXOmsd5IcPkUPAAAAAOoSp4pTp06ddN999+mRRx6pdH1WVpY2bNhQLcEAAAAAwFV4ODM4Ojpa+/btq3K9j4+P2rRpc92hAAAAAMCVODU5RHFxsUpLS9WwYcOazFSjmBwCAAAAgFSDk0P4+PhcVzAAAAAAqIscPlXv3LlzTj2xs+MBAAAAwFU5XJzatWunBQsWKCcnp8oxhmEoLS1NQ4YM0auvvlotAQEAAADAag6fqvfZZ59pzpw5mjt3rrp27aqYmBiFhITI19dXp06d0oEDB7Rr1y41aNBAM2fO1H/8x3/UZG4AAAAAqDVOTQ4hST/99JPef/99bd++XUePHtWFCxfUvHlzdevWTfHx8Ro6dKg8PJyarK9WMTkEAAAAAMm5buB0carrKE4AAAAAJOe6geseGgIAAAAAF0FxAgAAAAATFCcAAAAAMEFxAgAAAAATFCcAAAAAMOHwfZwkafz48fr+++8dHn/LLbfozTffdDoUAAAAALgSp4rT119/rX379jk8vkePHk4HAgAAAABXw6l6AAAAAGCC4gQAAAAAJihOAAAAAGCC4gQAAAAAJpyaHMIwDE2YMMHhsYZhXFMoAAAAAHAlThWndevW6eLFiw6P9/PzczoQAAAAALgap4pTZmamCgoKHB7fsmVLtWnTxulQAAAAAOBKnLrGaf78+fL19ZWPj49DjwULFtRUbgAAAACoNU5f4zR27FiHxy9ZssTpQAAAAADgapw64mSz2Zx6ckfGp6SkKCIiQr6+voqOjtaOHTuqHLtmzRoNHjxYLVq0kL+/v3r16qUtW7Y4lQkAAAAAnGXpdOSpqalKTEzU7NmztX//fvXp00dDhgxRdnZ2peO3b9+uwYMHa+PGjcrMzNSAAQM0bNgw7d+/v5aTAwAAAHAnNsOJOcOjoqK0b98+h5+8R48e2r17d5Xrb7/9dkVFRWnp0qX2ZZ06ddKIESOUnJzs0Gt07txZo0aN0p/+9CeHxhcVFSkgIECFhYXy9/d3aBsAAAAA9Y8z3cDpa5y2b9/u8NirdbJLly4pMzNTM2bMKLc8Li5OO3fudOg1ysrKdObMGTVr1qzKMcXFxSouLrb/XFRU5NBzAwAAAMAVThWnCRMmaNOmTQ6PHz9+fJXrCgoKVFpaqqCgoHLLg4KClJeX59Dzv/jiizp37pxGjhxZ5Zjk5GTNnTvXoecDAAAAgMo4VZwmTpyosrIyh8d7eJhfQvXbCSQMw3BoUon33ntPzz77rD766CO1bNmyynEzZ85UUlKS/eeioiKFhYWZPj8AAAAAXOFUcerRo4eaNGni0FjDMHT+/Hl9+eWXla5v3ry5PD09Kxxdys/Pr3AU6rdSU1P1yCOP6P3339edd9551bFX7ikFAAAAANfK6Wuctm7d6vD47t27V7nO29tb0dHRSktL07333mtfnpaWpnvuuafK7d577z1NmDBB7733nu6++26HswAAAADAtXKqOFX3fZySkpI0ZswYxcTEqFevXlq+fLmys7OVkJAg6dfT7E6cOKF33nlH0q+laezYsXrllVfUs2dP+9EqPz8/BQQEOJUNAAAAABzlVHGqbqNGjdLJkyc1b9485ebmKjIyUhs3blR4eLgkKTc3t9w9nd544w2VlJRo0qRJmjRpkn35uHHjtGrVqtqODwAAAMBNWHofJytwHycAAAAAknPdwHzaOwAAAABwc06dqtesWTPFxsZe9ca2/yowMPCaQgEAAACAK3GqOH366ac1lQMAAAAAXJZTxWnWrFk6evSow+PbtWunefPmOZsJAAAAAFyKU8Vp8+bNWrt2rUNjDcPQyJEjKU4AAAAA6jynb4B7ZapwR8cDAAAAQF3n1Kx61X0DXAAAAACoC5iOHAAAAABMUJwAAAAAwITT1zg5OtkD1zcBAAAAqC+cKk4pKSkqKipyeHx8fLzTgQAAAADA1ThVnHr16lVTOQAAAADAZXGNEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAmKEwAAAACYoDgBAAAAgAnLi1NKSooiIiLk6+ur6Oho7dix46rj09PTFR0dLV9fX7Vt21bLli2rpaQAAAAA3JWlxSk1NVWJiYmaPXu29u/frz59+mjIkCHKzs6udPyRI0c0dOhQ9enTR/v379esWbM0efJkffjhh7WcHAAAAIA7sRmGYVj14rfffruioqK0dOlS+7JOnTppxIgRSk5OrjB++vTpWr9+vQ4ePGhflpCQoK+++kq7du2q9DWKi4tVXFxs/7moqEhhYWEqLCyUv79/Nb4bAAAAAHVJUVGRAgICHOoGlh1xunTpkjIzMxUXF1dueVxcnHbu3FnpNrt27aowPj4+Xnv37tXly5cr3SY5OVkBAQH2R1hYWPW8AQAAAABuw7LiVFBQoNLSUgUFBZVbHhQUpLy8vEq3ycvLq3R8SUmJCgoKKt1m5syZKiwstD+OHz9ePW8AAAAAgNvwsjqAzWYr97NhGBWWmY2vbPkVPj4+8vHxuc6UAAAAANyZZUecmjdvLk9PzwpHl/Lz8yscVboiODi40vFeXl4KDAyssawAAAAA3Jtlxcnb21vR0dFKS0srtzwtLU2xsbGVbtOrV68K4z/55BPFxMSoQYMGNZYVAAAAgHuzdDrypKQkvfnmm1qxYoUOHjyoKVOmKDs7WwkJCZJ+vT5p7Nix9vEJCQk6duyYkpKSdPDgQa1YsUJvvfWWpk6datVbAAAAAOAGLL3GadSoUTp58qTmzZun3NxcRUZGauPGjQoPD5ck5ebmlrunU0REhDZu3KgpU6bo9ddfV0hIiF599VXdd999Vr0FAAAAAG7A0vs4WcGZudoBAAAA1F914j5OAAAAAFBXUJwAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMeFkdoLYZhiFJKioqsjgJAAAAACtd6QRXOsLVuF1xOnPmjCQpLCzM4iQAAAAAXMGZM2cUEBBw1TE2w5F6VY+UlZUpJydHjRs3ls1mszqOioqKFBYWpuPHj8vf39/qOG6D/W4N9nvtY59bg/1uDfa7Ndjv1mC/Vw/DMHTmzBmFhITIw+PqVzG53REnDw8PhYaGWh2jAn9/fz70FmC/W4P9XvvY59Zgv1uD/W4N9rs12O/Xz+xI0xVMDgEAAAAAJihOAAAAAGCC4mQxHx8fPfPMM/Lx8bE6ilthv1uD/V772OfWYL9bg/1uDfa7Ndjvtc/tJocAAAAAAGdxxAkAAAAATFCcAAAAAMAExQkAAAAATFCcAAAAAMAExclCKSkpioiIkK+vr6Kjo7Vjxw6rI9VrycnJ6t69uxo3bqyWLVtqxIgR+sc//mF1LLeTnJwsm82mxMREq6PUeydOnNAf/vAHBQYGqmHDhuratasyMzOtjlWvlZSUaM6cOYqIiJCfn5/atm2refPmqayszOpo9cr27ds1bNgwhYSEyGazad26deXWG4ahZ599ViEhIfLz81P//v313XffWRO2Hrnafr98+bKmT5+uW2+9VY0aNVJISIjGjh2rnJwc6wLXE2af93/12GOPyWaz6eWXX661fO6E4mSR1NRUJSYmavbs2dq/f7/69OmjIUOGKDs72+po9VZ6eromTZqkL774QmlpaSopKVFcXJzOnTtndTS3sWfPHi1fvlxdunSxOkq9d+rUKfXu3VsNGjTQpk2bdODAAb344otq0qSJ1dHqtYULF2rZsmVasmSJDh48qBdeeEF/+ctf9Nprr1kdrV45d+6cbrvtNi1ZsqTS9S+88IIWL16sJUuWaM+ePQoODtbgwYN15syZWk5av1xtv58/f1779u3Tf/7nf2rfvn1as2aNvv/+ew0fPtyCpPWL2ef9inXr1unLL79USEhILSVzQwYs0aNHDyMhIaHcso4dOxozZsywKJH7yc/PNyQZ6enpVkdxC2fOnDHat29vpKWlGf369TOeeuopqyPVa9OnTzfuuOMOq2O4nbvvvtuYMGFCuWW///3vjT/84Q8WJar/JBlr1661/1xWVmYEBwcbzz//vH3ZxYsXjYCAAGPZsmUWJKyffrvfK7N7925DknHs2LHaCeUGqtrvP/30k9G6dWvj22+/NcLDw42XXnqp1rO5A444WeDSpUvKzMxUXFxcueVxcXHauXOnRancT2FhoSSpWbNmFidxD5MmTdLdd9+tO++80+oobmH9+vWKiYnR/fffr5YtW6pbt276r//6L6tj1Xt33HGH/va3v+n777+XJH311VfKyMjQ0KFDLU7mPo4cOaK8vLxy37E+Pj7q168f37G1rLCwUDabjSPdNaysrExjxozRtGnT1LlzZ6vj1GteVgdwRwUFBSotLVVQUFC55UFBQcrLy7MolXsxDENJSUm64447FBkZaXWceu9//ud/tG/fPu3Zs8fqKG7j8OHDWrp0qZKSkjRr1izt3r1bkydPlo+Pj8aOHWt1vHpr+vTpKiwsVMeOHeXp6anS0lI999xzevDBB62O5jaufI9W9h177NgxKyK5pYsXL2rGjBkaPXq0/P39rY5Try1cuFBeXl6aPHmy1VHqPYqThWw2W7mfDcOosAw144knntDXX3+tjIwMq6PUe8ePH9dTTz2lTz75RL6+vlbHcRtlZWWKiYnRggULJEndunXTd999p6VLl1KcalBqaqr++te/6t1331Xnzp2VlZWlxMREhYSEaNy4cVbHcyt8x1rn8uXLeuCBB1RWVqaUlBSr49RrmZmZeuWVV7Rv3z4+37WAU/Us0Lx5c3l6elY4upSfn1/hN2Sofk8++aTWr1+vbdu2KTQ01Oo49V5mZqby8/MVHR0tLy8veXl5KT09Xa+++qq8vLxUWlpqdcR6qVWrVrrlllvKLevUqRMT0NSwadOmacaMGXrggQd06623asyYMZoyZYqSk5OtjuY2goODJYnvWItcvnxZI0eO1JEjR5SWlsbRphq2Y8cO5efnq02bNvbv2GPHjumPf/yjbrzxRqvj1TsUJwt4e3srOjpaaWlp5ZanpaUpNjbWolT1n2EYeuKJJ7RmzRpt3bpVERERVkdyC4MGDdI333yjrKws+yMmJkYPPfSQsrKy5OnpaXXEeql3794Vptv//vvvFR4eblEi93D+/Hl5eJT/avX09GQ68loUERGh4ODgct+xly5dUnp6Ot+xNexKafrhhx/06aefKjAw0OpI9d6YMWP09ddfl/uODQkJ0bRp07Rlyxar49U7nKpnkaSkJI0ZM0YxMTHq1auXli9fruzsbCUkJFgdrd6aNGmS3n33XX300Udq3Lix/beRAQEB8vPzszhd/dW4ceMK15E1atRIgYGBXF9Wg6ZMmaLY2FgtWLBAI0eO1O7du7V8+XItX77c6mj12rBhw/Tcc8+pTZs26ty5s/bv36/FixdrwoQJVkerV86ePasff/zR/vORI0eUlZWlZs2aqU2bNkpMTNSCBQvUvn17tW/fXgsWLFDDhg01evRoC1PXfVfb7yEhIfq3f/s37du3Txs2bFBpaan9e7ZZs2by9va2KnadZ/Z5/21BbdCggYKDg3XzzTfXdtT6z9pJ/dzb66+/boSHhxve3t5GVFQU02LXMEmVPlauXGl1NLfDdOS14+OPPzYiIyMNHx8fo2PHjsby5cutjlTvFRUVGU899ZTRpk0bw9fX12jbtq0xe/Zso7i42Opo9cq2bdsq/fd83LhxhmH8OiX5M888YwQHBxs+Pj5G3759jW+++cba0PXA1fb7kSNHqvye3bZtm9XR6zSzz/tvMR15zbEZhmHUUkcDAAAAgDqJa5wAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMUJwAAAAAwATFCQAAAABMeFkdAACAa7Vz5049/vjjla676667tHfvXhUUFFS6fvfu3Vq2bJlWrFhR6fo5c+YoJiZGI0aMqHR9ly5d9M4771xTbgBA3UNxAgDUWUVFRRoxYoSeffbZcsuPHj2qGTNm6OzZs8rKyqqwXf/+/VVWVqacnBy9/PLL6t+/f7n1q1atUkFBgS5evKiuXbtq1apVFZ6jZ8+e1fdGAAAuj1P1AAAAAMAExQkAAAAATFCcAAAAAMAExQkAAAAATFCcAAAAAMAExQkAAAAATFCcAAAAAMAExQkAAAAATFCcAAAAAMAExQkAAAAATHhZHQAAgGsVEBCgDRs2aMOGDRXWxcfH6/Tp04qJial0Ww8PD4WGhmrq1KmVrp81a5b8/Pz07bffVvoct9566/WFBwDUKTbDMAyrQwAAAACAK+NUPQAAAAAwQXECAAAAABMUJwAAAAAwQXECAAAAABMUJwAAAAAwQXECAAAAABMUJwAAAAAwQXECAAAAABMUJwAAAAAw8f/NL11cxYHqzwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 全局变量：记录所有基矢的分配情况\n", "basis_allocation = {}  # {基矢索引s: 分块标识(k,p,z), ...}\n", "block_records = []     # 分块详情: (k,p,z, 基矢列表, 维度)\n", "\n", "\n", "def findbasis(N, k, p, z):\n", "    \"\"\"生成严格无重复、无遗漏的基矢列表\"\"\"\n", "    # 1. 按k扇区筛选基矢（核心：确保每个基矢只属于一个k）\n", "    # 示例逻辑：根据s的二进制表示分配到唯一k（需替换为你的物理对称规则）\n", "    k_basis = []\n", "    for s in range(2** N):\n", "        # 关键：通过哈希函数确保s仅属于一个k（示例：s mod N == k）\n", "        if s % N == k and s not in basis_allocation:\n", "            k_basis.append(s)\n", "    \n", "    # 2. 按p和z进一步筛选（在k扇区内细分，不改变总数量）\n", "    # 示例：p=1保留偶数索引，p=-1保留奇数索引（仅为演示）\n", "    if p == 1:\n", "        filtered_basis = [s for i, s in enumerate(k_basis) if i % 2 == 0]\n", "    else:\n", "        filtered_basis = [s for i, s in enumerate(k_basis) if i % 2 == 1]\n", "    \n", "    # 3. z的筛选（示例：z=1保留s为偶数的基矢）\n", "    if z == 1:\n", "        filtered_basis = [s for s in filtered_basis if s % 2 == 0]\n", "    else:\n", "        filtered_basis = [s for s in filtered_basis if s % 2 == 1]\n", "    \n", "    return len(filtered_basis), filtered_basis, [], [], [], [], []\n", "\n", "\n", "def collect_blocks(N):\n", "    \"\"\"收集所有分块，验证基矢总数与总维数一致\"\"\"\n", "    global basis_allocation, block_records\n", "    basis_allocation.clear()\n", "    block_records.clear()\n", "    total_valid = 0  # 有效状态总数\n", "    total_dim = 2 **N  # 系统总维数\n", "    \n", "    for k in range(N):\n", "        # p的取值范围（根据原逻辑）\n", "        p_values = (-1, 1) if (k == 0 or k == N//2) else (1,)\n", "        for p in p_values:\n", "            for z in (-1, 1):\n", "                nrep, basis_list, _, _, _, _, _ = findbasis(N, k, p, z)\n", "                if nrep == 0:\n", "                    continue\n", "                \n", "                # 记录基矢分配（确保唯一）\n", "                for s in basis_list:\n", "                    if s in basis_allocation:\n", "                        # 检测重复分配\n", "                        print(f\"错误：基矢{s}被重复分配到({k},{p},{z})和{basis_allocation[s]}\")\n", "                    basis_allocation[s] = (k, p, z)\n", "                \n", "                total_valid += nrep\n", "                block_records.append({\n", "                    'k': k, 'p': p, 'z': z,\n", "                    'dimension': nrep,\n", "                    'basis': basis_list\n", "                })\n", "    \n", "    # 检查基矢覆盖完整性\n", "    missing_basis = [s for s in range(total_dim) if s not in basis_allocation]\n", "    print(f\"\\n系统总维数: {total_dim}\")\n", "    print(f\"有效状态总数: {total_valid}\")\n", "    print(f\"遗漏基矢数量: {len(missing_basis)}\")\n", "    if missing_basis:\n", "        print(f\"遗漏的基矢: {missing_basis[:10]}...\")  # 最多显示10个\n", "    \n", "    # 验证结果\n", "    if total_valid == total_dim and len(missing_basis) == 0:\n", "        print(\"验证成功：有效状态数与总维数一致\")\n", "    else:\n", "        print(\"验证失败：有效状态数与总维数不一致\")\n", "    \n", "    return block_records\n", "\n", "\n", "def visualize_basis_coverage(N, block_records):\n", "    \"\"\"可视化基矢覆盖情况\"\"\"\n", "    total_dim = 2 **N\n", "    coverage = np.zeros(total_dim)  # 0=未覆盖, 1=已覆盖\n", "    for block in block_records:\n", "        for s in block['basis']:\n", "            coverage[s] = 1\n", "    \n", "    plt.figure(figsize=(10, 3))\n", "    plt.scatter(range(total_dim), coverage, s=10, c=coverage, cmap='binary')\n", "    plt.xlabel('基矢索引')\n", "    plt.ylabel('覆盖状态 (1=已覆盖, 0=未覆盖)')\n", "    plt.title(f'基矢覆盖可视化 (N={N}, 总维数={total_dim})')\n", "    plt.ylim(-0.1, 1.1)\n", "    plt.show()\n", "\n", "\n", "# 执行验证\n", "if __name__ == \"__main__\":\n", "    N = 4  # 示例：4个格点（总维数=16）\n", "    blocks = collect_blocks(N)\n", "    visualize_basis_coverage(N, blocks)\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["分块总维数之和: 16 / 16 -> 通过\n", "谱一致性: 通过  (len=16)\n", "块维度列表:\n", "(k= 0, n_up= 0) dim=  1\n", "(k= 0, n_up= 1) dim=  1\n", "(k= 1, n_up= 1) dim=  1\n", "(k= 2, n_up= 1) dim=  1\n", "(k= 3, n_up= 1) dim=  1\n", "(k= 0, n_up= 2) dim=  2\n", "(k= 1, n_up= 2) dim=  1\n", "(k= 2, n_up= 2) dim=  2\n", "(k= 3, n_up= 2) dim=  1\n", "(k= 0, n_up= 3) dim=  1\n", "(k= 1, n_up= 3) dim=  1\n", "(k= 2, n_up= 3) dim=  1\n", "(k= 3, n_up= 3) dim=  1\n", "(k= 0, n_up= 4) dim=  1\n"]}], "source": ["import numpy as np\n", "from numpy.linalg import eigh\n", "\n", "# ========= 仅用平移对称性的 (k, n_up) 分块基 =========\n", "\n", "def popcount(x: int) -> int:\n", "    return int(bin(x).count('1'))\n", "\n", "\n", "def orbit_states_and_period(N: int, s: int):\n", "    \"\"\"返回状态s的平移轨道列表和周期R（最小正整数R使T^R s = s）。\"\"\"\n", "    seen = {}\n", "    order = []\n", "    t = s\n", "    for r in range(N):\n", "        if t in seen:\n", "            R = r - seen[t]\n", "            return order[:R], R\n", "        seen[t] = r\n", "        order.append(t)\n", "        t = translate(N, t, 1)\n", "    # 全周期\n", "    return order, N\n", "\n", "\n", "def build_representatives_by_magnetization(N: int, n_up: int):\n", "    \"\"\"枚举所有态，选择各平移轨道的字典序最小代表，且满足给定 n_up。\"\"\"\n", "    reps = []\n", "    visited = set()\n", "    for s in range(1 << N):\n", "        if popcount(s) != n_up or s in visited:\n", "            continue\n", "        orbit, R = orbit_states_and_period(N, s)\n", "        # 轨道全部加入visited\n", "        for t in orbit:\n", "            visited.add(t)\n", "        # 代表：轨道中的最小整数\n", "        rep = min(orbit)\n", "        if popcount(rep) == n_up:\n", "            reps.append((rep, R))\n", "    # 去重（不同起点可能给相同代表）\n", "    reps = list({rep: R for rep, R in reps}.items())\n", "    reps.sort(key=lambda x: (x[1], x[0]))  # 先按周期，再按代表排序\n", "    return reps  # 列表元素: (rep, R)\n", "\n", "\n", "def momentum_compatible(R: int, k: int, N: int) -> bool:\n", "    \"\"\"动量k与周期R相容: e^{i 2π k R / N} = 1 => N | kR。\"\"\"\n", "    return (k * R) % N == 0\n", "\n", "\n", "def projector_vector_for_rep(N: int, rep: int, R: int, k: int) -> np.ndarray:\n", "    \"\"\"给定代表rep与周期R，构造动量k的规范化投影向量 v = (1/√R)∑_r e^{-i 2π k r/N} |T^r rep>。\n", "    仅使用最短周期R的R个平移（避免重复）。\"\"\"\n", "    vec = np.zeros(1 << N, dtype=complex)\n", "    phase_unit = np.exp(-1j * 2 * np.pi * k / N)\n", "    t = rep\n", "    phase = 1.0 + 0j\n", "    for r in range(R):\n", "        vec[t] += phase\n", "        # 下一步\n", "        t = translate(N, t, 1)\n", "        phase *= phase_unit\n", "    vec /= np.sqrt(R)\n", "    return vec\n", "\n", "\n", "def build_block_basis(N: int, k: int, n_up: int):\n", "    \"\"\"返回 (basis_vectors, reps), 其中basis_vectors是2^N×dim的列正交矩阵，reps为[(rep,R), ...]。\"\"\"\n", "    reps = build_representatives_by_magnetization(N, n_up)\n", "    vectors = []\n", "    kept_reps = []\n", "    for rep, R in reps:\n", "        if momentum_compatible(R, k, N):\n", "            v = projector_vector_for_rep(N, rep, R, k)\n", "            # 过滤零向量（数值安全）\n", "            nrm = np.vdot(v, v).real\n", "            if nrm > 1e-12:\n", "                v /= np.sqrt(nrm)\n", "                vectors.append(v)\n", "                kept_reps.append((rep, R))\n", "    if len(vectors) == 0:\n", "        return np.zeros((1 << N, 0), dtype=complex), []\n", "    V = np.stack(vectors, axis=1)\n", "    return V, kept_reps\n", "\n", "\n", "def build_full_H(N: int, J: float, h: float) -> np.ndarray:\n", "    return Ham_total(N, J, h).astype(complex)\n", "\n", "\n", "def build_block_H_from_full(H_full: np.ndarray, V: np.ndarray) -> np.ndarray:\n", "    # H_block = V^\\dagger H V\n", "    return V.conj().T @ (H_full @ V)\n", "\n", "\n", "def verify_block_diagonalization(N: int, J: float, h: float, atol: float = 1e-8):\n", "    \"\"\"遍历所有(k, n_up)分块：\n", "    - 构造仅基于平移对称的动量投影基\n", "    - 统计各块维数，总和应为 2^N\n", "    - 聚合所有块的本征值，与全空间本征值一致\n", "    返回(覆盖是否为2^N, 谱是否一致, 统计信息dict)\n", "    \"\"\"\n", "    H_full = build_full_H(N, J, h)\n", "    evals_full = np.linalg.eigvalsh(H_full)\n", "    evals_blocks = []\n", "    dim_sum = 0\n", "    stats = []  # 每块记录\n", "\n", "    for n_up in range(N + 1):\n", "        for k in range(N):\n", "            V, reps = build_block_basis(N, k, n_up)\n", "            dim = V.shape[1]\n", "            if dim == 0:\n", "                continue\n", "            Hk = build_block_H_from_full(H_full, V)\n", "            ev = np.linalg.eig<PERSON>(Hk)\n", "            evals_blocks.extend(ev.tolist())\n", "            dim_sum += dim\n", "            stats.append({\n", "                'k': k,\n", "                'n_up': n_up,\n", "                'dim': dim,\n", "                'num_reps': len(reps)\n", "            })\n", "\n", "    # 维度校验\n", "    cover_ok = (dim_sum == (1 << N))\n", "\n", "    # 谱一致性（多重集比较）\n", "    evals_full_sorted = np.sort(evals_full)\n", "    evals_blocks_sorted = np.sort(np.array(evals_blocks))\n", "    same_len = (len(evals_full_sorted) == len(evals_blocks_sorted))\n", "    spectrum_ok = same_len and np.allclose(evals_full_sorted, evals_blocks_sorted, atol=1e-8, rtol=1e-6)\n", "\n", "    return cover_ok, spectrum_ok, dim_sum, stats, evals_full_sorted, evals_blocks_sorted\n", "\n", "\n", "# ===== 示例运行与输出 =====\n", "if __name__ == \"__main__\":\n", "    cover_ok, spectrum_ok, dim_sum, stats, ev_full, ev_blocks = verify_block_diagonalization(N, J, h)\n", "    print(f\"分块总维数之和: {dim_sum} / {1<<N} -> {'通过' if cover_ok else '失败'}\")\n", "    print(f\"谱一致性: {'通过' if spectrum_ok else '失败'}  (len={len(ev_blocks)})\")\n", "    # 简要打印每个非零块的维度\n", "    stats_sorted = sorted(stats, key=lambda x: (x['n_up'], x['k']))\n", "    lines = [f\"(k={s['k']:2d}, n_up={s['n_up']:2d}) dim={s['dim']:3d}\" for s in stats_sorted]\n", "    print(\"块维度列表:\")\n", "    print(\"\\n\".join(lines[:min(40, len(lines))]))\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nif __name__ == \"__main__\":\\n    # 全空间哈密顿量（验证用）\\n    H_full = Ham_total(N, J, h)\\n    eval_full, _ = np.linalg.eigh(H_full)\\n    eval_full = np.sort(eval_full.real)\\n    # 平移+磁化分块\\n    E_blocks, labels = fullspectrum_by_blocks(N)\\n    E_blocks = np.sort(np.array(E_blocks))\\n    # 维度核对\\n    basis = build_translation_basis_by_Mk(N)\\n    total_dim = sum(len(v[\\'repr\\']) for v in basis.values())\\n    print(f\"N={N}，所有 (M,k) 块维度之和: {total_dim}，应为 {2**N}\")\\n    # 谱一致性\\n    if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):\\n        print(\"警告：块对角化谱与全空间谱不一致！\")\\n        print(f\"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}\")\\n        m = min(len(E_blocks), len(eval_full))\\n        if m > 0:\\n            print(f\"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}\")\\n    else:\\n        print(\"验证成功：块对角化本征值与全空间本征值一致。\")\\n'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from scipy.sparse.linalg import eigsh\n", "\n", "J=1\n", "h=1\n", "N=12\n", "\n", "# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)\n", "\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    g=0\n", "    if t == -1:\n", "        g = 1-np.cos(kk*l)\n", "    elif t==1:\n", "        g = 1+np.cos(kk*l) \n", "    return g\n", "\n", "def checkstate(s,k,N):\n", "    R=-1\n", "    tz=-1\n", "    tp = -1\n", "    tpz = -1\n", "    smax = 2**N-1\n", "    # 仅基于平移对称性判断周期 R（去除对总磁化的限制）\n", "    t=s\n", "    for i in range(1,N+1):\n", "        t = translate(N,t,1)\n", "        az = smax -t\n", "        if t<s or az<s:\n", "            break\n", "        if t==s:\n", "            R=i\n", "            break\n", "    return R,tp,tz,tpz\n", "\n", "import numpy as np\n", "#仅保留平移对称性\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p=None, z=None):  # 1. 非平移参数p/z设为默认None（无需传入）\n", "    repr_list = []\n", "    typee = []\n", "    peri = []\n", "    mtrf = []  # 反演相关参数，后续无赋值（可保留占位）\n", "    ntrf = []  # 自旋翻转相关参数，后续无赋值（可保留占位）\n", "    capr = []  # 联合对称类标签，后续简化（可保留占位）\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 注释/删除sigma循环（自旋宇称与平移无关，无需遍历）\n", "        # for sigma in (-1, 1):\n", "        sigma = 1  # 固定sigma=1，避免无意义循环（仅保留形式，不影响逻辑）\n", "        m, n = None, None\n", "        ca = 1  # 3. 简化对称类：仅保留1类（无反演/自旋翻转对称）\n", "        R, tp, tz, tpz = checkstate(s, k, N)  # 获取平移周期R（核心）\n", "        \n", "        # 4. 仅保留平移对称性的基础过滤：R>0表示满足平移对称\n", "        if R <= -1:\n", "            continue\n", "        \n", "        # 5. 注释/删除特殊k值的sigma约束（sigma已固定，该逻辑无效）\n", "        # if (k == 0 or k == N // 2) and (sigma == -1):\n", "        #     R = -1  # 标记为无效状态\n", "        \n", "        if R > 0:\n", "            # 6. 注释/删除所有反演/自旋翻转相关的分支逻辑（ca已固定为1）\n", "            # 原ca=2~5的分支全删除，仅保留ca=1的基础赋值\n", "            m, n = None, None  # 反演/自旋翻转参数无意义，设为None\n", "            \n", "            # 7. 注释/删除反演/自旋翻转的兼容性验证（ggun与平移无关）\n", "            # if np.isclose(ggun(...), 0, atol=1e-8):\n", "            #     R = -1\n", "            \n", "            # 最终筛选：仅需ca已定义（固定为1）且R>0（平移对称有效）\n", "            if ca is not None and R > 0:\n", "                repr_list.append(s)\n", "                # 8. 简化typee：仅体现ca=1和sigma=1（无需复杂计算）\n", "                typee.append(2 * ca + (sigma + 1) / 2)\n", "                capr.append(ca)  # 固定为1，标记仅平移对称\n", "                peri.append(R)   # 记录平移周期（核心输出，体现平移对称性）\n", "                mtrf.append(m)\n", "                ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 9. 简化打印：仅输出k和有效状态数（p/z已无关）\n", "    print(f\"k={k}: 有效状态数={nrep}（仅平移对称）\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr\n", "\n", "def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            if ai != aj:\n", "                b = flip_state(a, i)\n", "                b = flip_state(b, j)\n", "                H[a, b] += h\n", "    return H\n", "\n", "def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        #az=smax-at\n", "        #if az<a:\n", "        #    a=az\n", "        #   l=t\n", "        #    g=1    \n", "    '''at = reverseBits(a0,L)\n", "    for t in range(L):\n", "        if at<a:\n", "            a=at\n", "            l=t\n", "            q=1\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            q=1\n", "            g=1\n", "        at = translate(L, at, 1)'''\n", "    return a,l,q,g\n", "\n", "def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = 2*(typee[a]%2)-1\n", "    t = 2*(typee[b]%2)-1\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)\n", "    return matelement\n", "\n", "import numpy as np\n", "\n", "def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, k):\n", "    # 1. 删除与反演/自旋翻转相关的参数p、z（不再传入）\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        # 跳过重复基矢（平移对称下可能出现的简并）\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        # 确定当前基矢的简并度（na=1或2）\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        \n", "        # 2. 计算自旋-自旋相互作用对角项（仅与平移对称兼容）\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N  # 周期边界条件（平移对称性核心）\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J / 4\n", "            else:\n", "                Ez -= J / 4\n", "        # 填充对角元（覆盖简并基矢）\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        \n", "        # 3. 计算横场项（仅保留平移对称相关逻辑）\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai != aj:\n", "                # 生成自旋翻转后的状态sb（仅与平移相关的翻转）\n", "                if ai == 1:\n", "                    sb = flip_state(flip_state(sa, i), j)\n", "                else:\n", "                    sb = flip_state(flip_state(sa, j), i)\n", "                \n", "                # 4. 仅用平移对称找代表态（忽略反演/自旋翻转参数）\n", "                representative, l, _, _ = represent(N, sb)  # 忽略q、g（反演/翻转标记）\n", "                \n", "                if representative in repr:\n", "                    ib = repr.index(representative)\n", "                    # 确定目标基矢的简并度\n", "                    if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                        ib = ib - 1\n", "                        nb = 2\n", "                    elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                        nb = 2\n", "                    else:\n", "                        nb = 1\n", "                    \n", "                    # 5. 计算矩阵元时仅保留平移相关参数（删除反演/翻转参数）\n", "                    for ii in range(ia, ia + na):\n", "                        for jj in range(ib, ib + nb):\n", "                            try:\n", "                                # 简化helement调用：仅传入平移相关参数（l是平移步数）\n", "                                elem = h/2 * helement_translation(ii, jj, typee, peri, mtrf, ntrf, capr,l,k,N)\n", "                                if np.isfinite(elem):\n", "                                    Hk[ii, jj] += elem\n", "                            except Exception as e:\n", "                                print(f\"矩阵元计算错误 at ii={ii}, jj={jj}: {e}\")\n", "    return Hk\n", "\n", "\n", "# 6. 简化的矩阵元函数（仅考虑平移对称性）\n", "def helement_translation(a, b, typee, peri, mtrf, ntrf, capr, l,k, N):\n", "    # 1. 删除与反演/自旋翻转相关的参数：p, z, q, g\n", "    # 2. 对称类简化：仅关注平移对称性（忽略ca/cb的反演/翻转分类）\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    \n", "    # 3. 自旋宇称简化：仅保留平移相关的自旋标记（或直接固定为1）\n", "    s = 2 * (typee[a] % 2) - 1  # 可简化为 s = 1（若自旋宇称与平移无关）\n", "    t = 2 * (typee[b] % 2) - 1  # 可简化为 t = 1\n", "    \n", "    # 4. 初始化矩阵元：仅保留平移周期比（核心平移对称参数）\n", "    matelement = peri[a] / peri[b]\n", "    \n", "    # 5. 注释/删除所有反演/自旋翻转相关的振幅修正（基于ggun的部分）\n", "    # 原ca=2/3/4/5的修正逻辑全部删除\n", "    # if ca==2 or ca==5: ...\n", "    # if ca==3 or ca==5: ...\n", "    # if ca==4: ...\n", "    # if cb==2 or cb==5: ...\n", "    # if cb==3 or cb==5: ...\n", "    # if cb==4: ...\n", "    \n", "    # 6. 注释/删除反演+自旋翻转的相位修正\n", "    # 仅保留平移相关的振幅开方（若需要）\n", "    matelement = np.sqrt(matelement)  # 若平移对称无需开方，可直接删除此步\n", "    \n", "    # 7. 仅保留平移跃迁的相位修正（基于ffun），删除反演/翻转相关的叠加项\n", "    # 统一使用基础平移相位，忽略所有反演相关的term2\n", "    matelement = matelement * ffun(t, s, l, k, N)  # l为平移步数（需从参数传入）\n", "    \n", "    return matelement\n", "\n", "\n", "# 辅助函数：仅考虑平移对称性的ffun（示例实现）\n", "def ffun(t, s, l, k, N):\n", "    \"\"\"仅计算平移l步在动量k扇区的相位（布洛赫相位）\"\"\"\n", "    # 核心：平移l步的相位因子 e^(i * 2πk l / N)\n", "    return np.exp(1j * 2 * np.pi * k * l / N)\n", "\n", "# ========= 新增：仅基于平移对称与总磁化(M,k)的基与块哈密顿量 =========\n", "def get_magnetization(state: int, N: int) -> int:\n", "    m = 0\n", "    for i in range(N):\n", "        m += get_site_value(state, i)\n", "    return m\n", "\n", "def orbit_period(N: int, s: int) -> int:\n", "    t = translate(N, s, 1)\n", "    R = 1\n", "    while t != s:\n", "        t = translate(N, t, 1)\n", "        R += 1\n", "    return R\n", "\n", "def is_k_compatible(N: int, R: int, k: int) -> bool:\n", "    return (k * R) % N == 0\n", "\n", "def build_translation_basis_by_Mk(N: int):\n", "    basis = {}\n", "    seen = set()\n", "    for s in range(2 ** N):\n", "        rep, _, _, _ = represent(N, s)\n", "        if rep in seen:\n", "            continue\n", "        seen.add(rep)\n", "        R = orbit_period(N, rep)\n", "        M = get_magnetization(rep, N)\n", "        for k in range(N):\n", "            if is_k_compatible(N, R, k):\n", "                key = (M, k)\n", "                if key not in basis:\n", "                    basis[key] = {'repr': [], 'peri': []}\n", "                basis[key]['repr'].append(rep)\n", "                basis[key]['peri'].append(R)\n", "    return basis\n", "\n", "def generate_orbit_states(N: int, rep: int):\n", "    states = [rep]\n", "    t = translate(N, rep, 1)\n", "    while t != rep:\n", "        states.append(t)\n", "        t = translate(N, t, 1)\n", "    return states\n", "\n", "def build_block_projection_matrix(N: int, reps: list, peri: list, k: int):\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    for col, rep in enumerate(reps):\n", "        orbit = generate_orbit_states(N, rep)\n", "        R = len(orbit)\n", "        norm = 1.0 / np.sqrt(R)\n", "        for r, s in enumerate(orbit):\n", "            phase = np.exp(-1j * 2 * np.pi * k * r / N)\n", "            V[s, col] = norm * phase\n", "    return V\n", "\n", "def fullspectrum_by_blocks(N: int):\n", "    basis = build_translation_basis_by_Mk(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    assert total_dim == 2 ** N, f\"块维度求和 {total_dim} != 2^{N}\"\n", "    H_full = Ham_total(N, J, h)\n", "    energies = []\n", "    labels = []\n", "    for (M, k), data in sorted(basis.items()):\n", "        reps = data['repr']\n", "        peri = data['peri']\n", "        if len(reps) == 0:\n", "            continue\n", "        V = build_block_projection_matrix(N, reps, peri, k)\n", "        Hk = V.conj().T @ (H_full @ V)\n", "        w, _ = np.linalg.eigh(Hk)\n", "        energies.extend(w.real.tolist())\n", "        labels.extend([(M, k)] * len(w))\n", "    return energies, labels\n", "\n", "def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,k):\n", "    # 1. 删除与反演/自旋翻转相关的参数：p, z\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        # 跳过重复基矢（平移对称下的简并态）\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        # 确定当前基矢的简并度（na=1或2）\n", "        elif (ia < nrep - 1 and sa == repr[ia + 1]):\n", "            na = 2\n", "        else:\n", "            na = 1\n", "        \n", "        # 填充对角元（常数项，与平移对称兼容）\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += (1/2) * N    \n", "        \n", "        # 遍历所有长程格点对（i < j）\n", "        for i in range(N):\n", "            for j in range(i + 1, N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                \n", "                if ai != aj:  # 仅反向自旋有非零跃迁\n", "                    # 生成自旋交换后的状态sb\n", "                    if ai == 1:   \n", "                        sb = flip_state(flip_state(sa, i), j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa, j), i)\n", "                    \n", "                    # 2. 仅保留平移相关的代表态参数（忽略反演/自旋翻转标记q、g）\n", "                    representative, l, _, _ = represent(N, sb)  # 丢弃q、g\n", "                    \n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        # 确定目标基矢的简并度（nb=1或2）\n", "                        if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                            ib = ib - 1\n", "                            nb = 2\n", "                        elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                            nb = 2\n", "                        else:\n", "                            nb = 1\n", "                        \n", "                        # 计算非对角矩阵元（仅保留平移对称修正）\n", "                        for ii in range(ia, ia + na):\n", "                            for jj in range(ib, ib + nb):\n", "                                # 3. 调用简化的helement（仅传入平移相关参数）\n", "                                Hk[ii, jj] += J * helement_translation(ii, jj, typee, peri, mtrf, ntrf, capr,l,k,N)\n", "    return Hk\n", "\n", "def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    \n", "'''\n", "if __name__ == \"__main__\":\n", "    # 全空间哈密顿量（验证用）\n", "    H_full = Ham_total(N, J, h)\n", "    eval_full, _ = np.linalg.eigh(H_full)\n", "    eval_full = np.sort(eval_full.real)\n", "    # 平移+磁化分块\n", "    E_blocks, labels = fullspectrum_by_blocks(N)\n", "    E_blocks = np.sort(np.array(E_blocks))\n", "    # 维度核对\n", "    basis = build_translation_basis_by_Mk(N)\n", "    total_dim = sum(len(v['repr']) for v in basis.values())\n", "    print(f\"N={N}，所有 (M,k) 块维度之和: {total_dim}，应为 {2**N}\")\n", "    # 谱一致性\n", "    if len(E_blocks) != len(eval_full) or not np.allclose(E_blocks, eval_full, atol=1e-8):\n", "        print(\"警告：块对角化谱与全空间谱不一致！\")\n", "        print(f\"块谱长度={len(E_blocks)} 全谱长度={len(eval_full)}\")\n", "        m = min(len(E_blocks), len(eval_full))\n", "        if m > 0:\n", "            print(f\"最大绝对偏差: {np.max(np.abs(E_blocks[:m] - eval_full[:m]))}\")\n", "    else:\n", "        print(\"验证成功：块对角化本征值与全空间本征值一致。\")\n", "'''\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-14.92023475, -14.66739244, -13.29441441, ...,  10.        ,\n", "        12.        ,  12.        ])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["E_blocks, labels = fullspectrum_by_blocks(N)\n", "E_blocks = np.sort(np.array(E_blocks))\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E_blocks [-14.92023475 -14.66739244 -13.29441441 ...  10.          12.\n", "  12.        ]\n"]}], "source": ["print(\"E_blocks\", E_blocks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.00788574e+01, -9.56133022e+00, -7.95927427e+00, -7.95927427e+00,\n", "       -7.24894438e+00, -6.71380425e+00, -6.71380425e+00, -6.71380425e+00,\n", "       -6.71380425e+00, -6.58051392e+00, -6.58051392e+00, -6.19836960e+00,\n", "       -6.19836960e+00, -6.14845538e+00, -5.73909180e+00, -5.73909180e+00,\n", "       -5.73909180e+00, -5.73909180e+00, -5.48965216e+00, -5.48965216e+00,\n", "       -5.18756533e+00, -5.18756533e+00, -5.12167001e+00, -5.12167001e+00,\n", "       -5.12167001e+00, -5.12167001e+00, -4.82842712e+00, -4.82842712e+00,\n", "       -4.64335202e+00, -4.64335202e+00, -4.35377331e+00, -4.35377331e+00,\n", "       -4.21433738e+00, -4.21433738e+00, -4.21433738e+00, -4.21433738e+00,\n", "       -4.14626437e+00, -4.14626437e+00, -4.00000000e+00, -3.82601377e+00,\n", "       -3.82601377e+00, -3.82601377e+00, -3.82601377e+00, -3.68488567e+00,\n", "       -3.56330691e+00, -3.56330691e+00, -3.53351341e+00, -3.53351341e+00,\n", "       -2.72001275e+00, -2.72001275e+00, -2.72001275e+00, -2.72001275e+00,\n", "       -2.69052192e+00, -2.69052192e+00, -2.62406472e+00, -2.62406472e+00,\n", "       -2.50592393e+00, -2.50592393e+00, -2.50592393e+00, -2.50592393e+00,\n", "       -2.43953094e+00, -2.14626437e+00, -2.14626437e+00, -2.14626437e+00,\n", "       -2.14626437e+00, -2.14221266e+00, -2.00000000e+00, -2.00000000e+00,\n", "       -2.00000000e+00, -2.00000000e+00, -1.68423938e+00, -1.53532934e+00,\n", "       -1.53532934e+00, -1.53532934e+00, -1.53532934e+00, -1.46168393e+00,\n", "       -1.46168393e+00, -1.40785878e+00, -1.40785878e+00, -1.40785878e+00,\n", "       -1.40785878e+00, -1.31783725e+00, -1.31783725e+00, -1.25560126e+00,\n", "       -1.25560126e+00, -1.24551071e+00, -1.24551071e+00, -1.24551071e+00,\n", "       -1.24551071e+00, -1.14126944e+00, -9.69680755e-01, -9.69680755e-01,\n", "       -9.13814038e-01, -9.13814038e-01, -9.13814038e-01, -9.13814038e-01,\n", "       -9.05440734e-01, -9.05440734e-01, -8.34781484e-01, -8.34781484e-01,\n", "       -8.34781484e-01, -8.34781484e-01, -8.28427125e-01, -8.28427125e-01,\n", "       -8.28427125e-01, -8.28427125e-01, -6.82162755e-01, -6.82162755e-01,\n", "       -5.74671644e-01, -5.74671644e-01, -3.07095015e-01, -3.07095015e-01,\n", "       -3.07095015e-01, -3.07095015e-01, -2.87704139e-01, -2.87704139e-01,\n", "       -1.11099590e-01, -1.11099590e-01, -1.11099590e-01, -1.11099590e-01,\n", "       -1.08928719e-15, -6.50611653e-16, -3.90824588e-16, -3.20703204e-16,\n", "       -5.36319992e-17, -2.09358357e-17, -6.26056873e-32, -6.13803145e-32,\n", "       -1.85061981e-64, -1.77888490e-64,  6.13803145e-32,  6.26056873e-32,\n", "        2.65192053e-16,  4.28567048e-16,  5.15404255e-16,  8.07158222e-16,\n", "        1.33577657e-15,  1.61357567e-15,  6.20435434e-01,  6.20435434e-01,\n", "        6.20435434e-01,  6.20435434e-01,  6.82162755e-01,  6.82162755e-01,\n", "        6.82162755e-01,  6.82162755e-01,  6.90521924e-01,  6.90521924e-01,\n", "        7.07725349e-01,  7.07725349e-01,  7.35361250e-01,  8.28427125e-01,\n", "        8.28427125e-01,  8.97950367e-01,  8.97950367e-01,  8.97950367e-01,\n", "        8.97950367e-01,  1.20593970e+00,  1.20593970e+00,  1.20593970e+00,\n", "        1.20593970e+00,  1.21296233e+00,  1.31783725e+00,  1.31783725e+00,\n", "        1.31783725e+00,  1.31783725e+00,  1.44355373e+00,  1.44355373e+00,\n", "        1.73544124e+00,  1.73544124e+00,  1.85051412e+00,  1.85051412e+00,\n", "        1.85051412e+00,  1.85051412e+00,  2.00000000e+00,  2.00000000e+00,\n", "        2.00000000e+00,  2.00000000e+00,  2.05159254e+00,  2.05159254e+00,\n", "        2.05159254e+00,  2.05159254e+00,  2.08574865e+00,  2.08574865e+00,\n", "        2.14626437e+00,  2.14626437e+00,  2.17351515e+00,  2.17351515e+00,\n", "        2.17351515e+00,  2.17351515e+00,  2.30709501e+00,  2.30709501e+00,\n", "        2.30709501e+00,  2.30709501e+00,  2.38748532e+00,  2.58578644e+00,\n", "        2.58578644e+00,  2.58578644e+00,  2.58578644e+00,  2.69299254e+00,\n", "        2.79186372e+00,  2.79186372e+00,  3.10481891e+00,  3.10481891e+00,\n", "        4.00000000e+00,  4.00000000e+00,  4.00000000e+00,  4.00000000e+00,\n", "        4.00000000e+00,  4.00000000e+00,  4.14626437e+00,  4.14626437e+00,\n", "        4.14626437e+00,  4.14626437e+00,  4.39822841e+00,  4.39822841e+00,\n", "        4.43597782e+00,  4.43597782e+00,  4.43597782e+00,  4.43597782e+00,\n", "        4.47551299e+00,  4.48607374e+00,  4.48607374e+00,  4.50592393e+00,\n", "        4.50592393e+00,  4.50592393e+00,  4.50592393e+00,  4.52219385e+00,\n", "        4.52219385e+00,  4.52219385e+00,  4.52219385e+00,  4.52325235e+00,\n", "        4.52325235e+00,  4.58051392e+00,  4.58051392e+00,  4.62520493e+00,\n", "        4.62520493e+00,  4.62520493e+00,  4.62520493e+00,  4.62541105e+00,\n", "        4.69778844e+00,  4.69778844e+00,  4.82842712e+00,  4.82842712e+00,\n", "        4.82842712e+00,  4.82842712e+00,  5.03315963e+00,  5.03315963e+00,\n", "        5.41421356e+00,  5.41421356e+00,  5.41421356e+00,  5.41421356e+00,\n", "        6.00000000e+00,  6.00000000e+00,  8.00000000e+00,  8.00000000e+00])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "nikola": {"category": "Strongly Correlated Systems", "date": "2020-12-20 13:18:31 UTC+08:00", "description": "", "link": "", "slug": "exact-diagonalization", "tags": "Correlated Electronic Systems, Numerical Method, Exact Diagonalization", "title": "Exact Diagonalization", "type": "text"}, "toc-autonumbering": true}, "nbformat": 4, "nbformat_minor": 4}