{"cells": [{"cell_type": "code", "execution_count": 5361, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy \n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 5362, "metadata": {}, "outputs": [], "source": ["N = 4\n", "#T=t1+t2\n", "t_1 = 1\n", "t_2 = 1\n", "lam_h = 0.01\n", "lam_J = 0.1\n", "h_x=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J_z=np.pi/4 - lam_J#J*t2# 0.75"]}, {"cell_type": "code", "execution_count": 5363, "metadata": {}, "outputs": [], "source": ["def translate(N, state, steps):\n", "    \"\"\"平移操作（示例：循环左移steps位）\"\"\"\n", "    # 实际实现需与checkstate中的平移逻辑一致\n", "    bits = [(state >> i) & 1 for i in range(N)]\n", "    steps = steps % N\n", "    shifted_bits = bits[steps:] + bits[:steps]\n", "    return sum(bit << i for i, bit in enumerate(shifted_bits))\n", "\n", "def spin_flip_state(N, rep):\n", "    \"\"\"自旋翻转（逐位取反）\"\"\"\n", "    return rep ^ ((1 << N) - 1)\n", "\n", "def reverseBits(state: int, num_spins: int) -> int:\n", "    \"\"\"\n", "    自旋态的空间反演操作（优化版）：将N位自旋的位置完全翻转\n", "    例：N=4时，state=0b1010（第1、3位自旋为1）→ 反演后=0b0101（第0、2位自旋为1）\n", "    \n", "    参数:\n", "        state: 整数编码的自旋态（需确保二进制位数 ≤ num_spins）\n", "        num_spins: 系统自旋总数（即反演的目标位数N）\n", "    返回:\n", "        reversed_state: 反演后的自旋态（整数编码）\n", "    \"\"\"\n", "    # 1. 输入验证：避免无效输入（如state位数超过num_spins）\n", "    if state < 0:\n", "        raise ValueError(\"自旋态state必须为非负整数\")\n", "    if num_spins < 1:\n", "        raise ValueError(\"自旋总数num_spins必须≥1\")\n", "    if state >= (1 << num_spins):\n", "        raise ValueError(f\"state={bin(state)}的位数超过num_spins={num_spins}，请检查输入\")\n", "    \n", "    reversed_state = 0\n", "    remaining_state = state\n", "    \n", "    # 2. 逐位提取并反转位置（仅遍历num_spins位，避免多余计算）\n", "    for i in range(num_spins):\n", "        # 提取当前最低位（第i位，从0开始计数）\n", "        current_bit = remaining_state & 1\n", "        # 将当前位放到反演后的位置：原第i位 → 反演后第(num_spins-1 -i)位\n", "        reversed_state |= current_bit << (num_spins - 1 - i)\n", "        # 移除已处理的最低位\n", "        remaining_state >>= 1\n", "    \n", "    return reversed_state"]}, {"cell_type": "code", "execution_count": 5364, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def translate(L, state, n_translation_sites):\\n    new_state = 0\\n    for site in range(L):\\n        site_value = get_site_value(state, site)\\n        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\\n    return new_state\\ndef spin_flip_state(N: int, rep: int) -> int:\\n    \"\"\"\\n    对N维系统中的量子态rep执行自旋翻转操作\\n    \\n    参数:\\n        N: 系统维度（自旋数量）\\n        rep: 待翻转的量子态（整数表示，二进制编码）\\n        \\n    返回:\\n        自旋翻转后的量子态（整数表示）\\n        \\n    异常:\\n        ValueError: 当rep超出N维系统的可能状态范围时抛出\\n    \"\"\"\\n    # 验证输入态的有效性\\n    max_state = (1 << N) - 1  # N个自旋的最大可能状态\\n    if rep < 0 or rep > max_state:\\n        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\\n    \\n    # 自旋翻转逻辑：翻转每个比特位（0<->1）\\n    # 构造N位全1的掩码，用于异或操作实现翻转\\n    mask = (1 << N) - 1\\n    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\\n    \\n    return flipped_rep\\ndef reverseBits(s,N):\\n    bin_chars = \"\"\\n    temp = s\\n    for i in range(N):\\n        bin_char = bin(temp % 2)[-1]\\n        temp = temp // 2\\n        bin_chars = bin_char + bin_chars\\n    bits =  bin_chars.upper()\\n    return int(bits[::-1], 2)'"]}, "execution_count": 5364, "metadata": {}, "output_type": "execute_result"}], "source": ["# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "'''def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def spin_flip_state(N: int, rep: int) -> int:\n", "    \"\"\"\n", "    对N维系统中的量子态rep执行自旋翻转操作\n", "    \n", "    参数:\n", "        N: 系统维度（自旋数量）\n", "        rep: 待翻转的量子态（整数表示，二进制编码）\n", "        \n", "    返回:\n", "        自旋翻转后的量子态（整数表示）\n", "        \n", "    异常:\n", "        ValueError: 当rep超出N维系统的可能状态范围时抛出\n", "    \"\"\"\n", "    # 验证输入态的有效性\n", "    max_state = (1 << N) - 1  # N个自旋的最大可能状态\n", "    if rep < 0 or rep > max_state:\n", "        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\n", "    \n", "    # 自旋翻转逻辑：翻转每个比特位（0<->1）\n", "    # 构造N位全1的掩码，用于异或操作实现翻转\n", "    mask = (1 << N) - 1\n", "    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\n", "    \n", "    return flipped_rep\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)'''"]}, {"cell_type": "code", "execution_count": 5365, "metadata": {}, "outputs": [], "source": ["def ffun(t,s,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    if t == -1:\n", "        if s == -1:\n", "            f = np.cos(kk*l)\n", "        elif s==1:\n", "            f = -np.sin(kk*l)\n", "    elif t ==1:\n", "        if s == -1:\n", "            f = np.sin(kk*l)\n", "        elif s==1:\n", "            f = np.cos(kk*l)\n", "    return f\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    g=0\n", "    if t == -1:\n", "        g = 1-np.cos(kk*l)\n", "    elif t==1:\n", "        g = 1+np.cos(kk*l) \n", "    return g"]}, {"cell_type": "code", "execution_count": 5366, "metadata": {}, "outputs": [], "source": ["def block_direct_sum(blocks):\n", "    \"\"\"\n", "    沿对角线构造块直和矩阵（块对角矩阵）\n", "    \n", "    参数:\n", "        blocks: 子矩阵列表，每个元素为numpy数组（方阵）\n", "    返回:\n", "        块直和矩阵（对角线上是输入的子矩阵，其余为0）\n", "    \"\"\"\n", "    # 检查输入是否为空\n", "    if not blocks:\n", "        return np.array([], dtype=float)\n", "    \n", "    # 检查所有子矩阵是否为方阵\n", "    for i, b in enumerate(blocks):\n", "        if b.ndim != 2 or b.shape[0] != b.shape[1]:\n", "            raise ValueError(f\"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）\")\n", "    \n", "    # 初始化结果矩阵（从空矩阵开始）\n", "    result = np.array([], dtype=blocks[0].dtype)\n", "    \n", "    for block in blocks:\n", "        m = block.shape[0]  # 当前块的维度\n", "        if result.size == 0:\n", "            # 第一次拼接：直接用当前块作为初始矩阵\n", "            result = block.copy()\n", "        else:\n", "            # 非第一次拼接：构造新的块对角矩阵\n", "            n = result.shape[0]  # 现有矩阵的维度\n", "            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）\n", "            new_result = np.zeros((n + m, n + m), dtype=result.dtype)\n", "            # 填充左上角为现有矩阵\n", "            new_result[:n, :n] = result\n", "            # 填充右下角为当前块\n", "            new_result[n:, n:] = block\n", "            result = new_result\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 5367, "metadata": {}, "outputs": [], "source": ["def checkstate(s, k, N):\n", "    R = -1\n", "    tp = -1  # 反演+平移的周期\n", "    tz = -1  # 自旋翻转+平移的周期\n", "    tpz = -1  # 反演+自旋翻转+平移的周期\n", "    smax = 2 **N - 1\n", "\n", "    # 1. 首先检查平移对称性，确定最小平移周期R\n", "    t = s\n", "    for i in range(1, N + 1):\n", "        t = translate(N, t, 1)  # 平移一位\n", "        if t < s:  # 存在更小的代表元，当前态无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到平移周期\n", "            if k % (N // i) == 0:  # 满足动量守恒\n", "                R = i\n", "            else:\n", "                return -1, -1, -1, -1\n", "            break\n", "    if R == -1:  # 无平移对称性\n", "        return -1, -1, -1, -1\n", "\n", "    # 2. 检查反演+平移对称性 (P*T^i)\n", "    t = reverseBits(s, N)  # 先反演\n", "    for i in range(R):  # 只在平移周期内检查\n", "        if t < s:  # 存在更小的代表元，无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到反演+平移的周期\n", "            tp = i\n", "            break\n", "        t = translate(N, t, 1)  # 平移一位\n", "    if t < s:  # 循环结束后仍需检查\n", "        return -1, -1, -1, -1\n", "\n", "    # 3. 检查自旋翻转+平移对称性 (Z*T^i)\n", "    t = smax - s  # 先自旋翻转\n", "    for i in range(R):\n", "        if t < s:  # 存在更小的代表元，无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到自旋翻转+平移的周期\n", "            tz = i\n", "            break\n", "        t = translate(N, t, 1)  # 平移一位\n", "    if t < s:\n", "        return -1, -1, -1, -1\n", "\n", "    # 4. 检查反演+自旋翻转+平移对称性 (PZ*T^i)\n", "    t = reverseBits(smax - s, N)  # 先自旋翻转再反演\n", "    for i in range(R):\n", "        if t < s:  # 存在更小的代表元，无效\n", "            return -1, -1, -1, -1\n", "        if t == s:  # 找到复合操作的周期\n", "            tpz = i\n", "            break\n", "        t = translate(N, t, 1)  # 平移一位\n", "    if t < s:\n", "        return -1, -1, -1, -1\n", "\n", "    return R, tp, tz, tpz"]}, {"cell_type": "code", "execution_count": 5368, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2, 1, 1, 0)"]}, "execution_count": 5368, "metadata": {}, "output_type": "execute_result"}], "source": ["checkstate(5,2,4)"]}, {"cell_type": "code", "execution_count": 5369, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p, z):\n", "    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数\n", "    repr_list = []\n", "    typee = []# 分类标签\n", "    peri = []# 平移对称性\n", "    mtrf = []# 反演对称性\n", "    ntrf = []# 自旋翻转对称性\n", "    capr = []# 联合对称性\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 显式列出sigma值（替代range，更清晰）\n", "        for sigma in [-1, 1]:\n", "            # 3. 每次迭代初始化m、n，避免跨状态污染\n", "            m, n = None, None\n", "            ca = None  # 显式初始化分类标签\n", "            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\n", "            \n", "            # 基础过滤：仅处理有效平移对称性的状态\n", "            if R <= -1:\n", "                continue\n", "            \n", "            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\n", "            if (k == 0 or k == N // 2) and (sigma == -1):\n", "                R = -1  # 标记为无效状态\n", "            \n", "            # 5. 仅处理R仍有效的状态\n", "            if R > 0:\n", "                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\n", "                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\n", "                if tp == -1 and tz == -1 and tpz == -1:\n", "                    ca = 1\n", "                    m, n = None, None  # 明确赋值，避免未定义\n", "                \n", "                # 分支2：tp≠-1、tz=-1（反演-平移对称）\n", "                elif tp != -1 and tz == -1 and tpz == -1:\n", "                    ca = 2\n", "                    m = tp\n", "                    # 7. 浮点数比较改用np.isclose，增强稳健性\n", "                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\n", "                elif tp == -1 and tz != -1 and tpz == -1:\n", "                    ca = 3\n", "                    n = tz\n", "                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\n", "                elif tp == -1 and tz == -1 and tpz != -1:\n", "                    ca = 4\n", "                    m = tpz\n", "                    n = tz  # 明确n未定义\n", "                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\n", "                elif tp != -1 and tz != -1 and tpz != -1:\n", "                    ca = 5\n", "                    m, n = tp, tz\n", "                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 8. 捕获未覆盖的状态组合（调试用）\n", "                else:\n", "                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\n", "                    continue\n", "                \n", "                # 9. 最终检查：ca必须已定义且R仍有效\n", "                if ca is not None and R > 0:\n", "                    repr_list.append(s)\n", "                    typee.append(sigma)\n", "                    capr.append(ca)\n", "                    peri.append(R)\n", "                    mtrf.append(m)\n", "                    ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 10. 简化打印，避免输出过载\n", "    #print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr"]}, {"cell_type": "code", "execution_count": 5370, "metadata": {}, "outputs": [], "source": ["def represent(L, a0):\n", "    sa = a0  # 最小代表态\n", "    la = 0   # 平移步数\n", "    qa = 0   # 反演标志 (0:无反演, 1:有反演)\n", "    ga = 0   # 自旋翻转标志 (0:无翻转, 1:有翻转)\n", "    smax = 2 **L - 1\n", "\n", "    # 1. 检查平移操作 (T^i)\n", "    auxt = a0\n", "    for i in range(1, L):\n", "        auxt = translate(L, auxt, 1)  # 每次平移1步，累计i步\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 0\n", "            ga = 0\n", "\n", "    # 2. 检查反演+平移操作 (P*T^i)\n", "    auxt = reverseBits(a0, L)  # 先反演\n", "    for i in range(L):\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 1\n", "            ga = 0\n", "        auxt = translate(L, auxt, 1)  # 平移1步\n", "\n", "    # 3. 检查自旋翻转+平移操作 (Z*T^i)\n", "    auxt = smax ^ a0  # 自旋翻转 (Bits_Invert)\n", "    for i in range(L):\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 0\n", "            ga = 1\n", "        auxt = translate(L, auxt, 1)  # 平移1步\n", "\n", "    # 4. 检查反演+自旋翻转+平移操作 (PZ*T^i)\n", "    auxt = reverseBits(smax ^ a0, L)  # 先自旋翻转再反演\n", "    for i in range(L):\n", "        if auxt < sa:\n", "            sa = auxt\n", "            la = i\n", "            qa = 1\n", "            ga = 1\n", "        auxt = translate(L, auxt, 1)  # 平移1步\n", "\n", "    return sa, a0, la, qa, ga"]}, {"cell_type": "code", "execution_count": 5371, "metadata": {}, "outputs": [], "source": ["def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H"]}, {"cell_type": "code", "execution_count": 5372, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def represent(L, a0):\\n    \"\"\"\\n    找到状态a0的表示态和达到该表示态所需的对称操作\\n    \\n    参数:\\n        L: 系统大小\\n        a0: 初始状态\\n        \\n    返回:\\n        a: 表示态\\n        l: 平移步数\\n        q: 是否应用了反演 (0=否, 1=是)\\n        g: 是否应用了自旋翻转 (0=否, 1=是)\\n    \"\"\"\\n    smax = (1 << L) - 1  # 全1状态\\n    \\n    # 初始化最小状态为原始状态\\n    min_state = a0\\n    best_l = 0\\n    best_q = 0\\n    best_g = 0\\n    \\n    # 考虑所有可能的对称操作组合\\n    for q in [0, 1]:  # 是否应用反演\\n        for g in [0, 1]:  # 是否应用自旋翻转\\n            # 应用反演和自旋翻转\\n            if q == 1 and g == 1:\\n                transformed = smax - reverseBits(a0, L)  # 反演+自旋翻转\\n            elif q == 1:\\n                transformed = reverseBits(a0, L)  # 仅反演\\n            elif g == 1:\\n                transformed = smax - a0  # 仅自旋翻转\\n            else:\\n                transformed = a0  # 无操作\\n            \\n            # 考虑所有可能的平移\\n            for l in range(L):\\n                # 应用平移\\n                final_state = translate(L, transformed, l)\\n                \\n                # 检查是否找到更小的状态\\n                if final_state < min_state:\\n                    min_state = final_state\\n                    best_l = l\\n                    best_q = q\\n                    best_g = g\\n                \\n                # 如果状态已经是0，不需要继续检查（0是最小的）\\n                if min_state == 0:\\n                    break\\n            if min_state == 0:\\n                break\\n        if min_state == 0:\\n            break\\n    \\n    return min_state, best_l, best_q, best_g'"]}, "execution_count": 5372, "metadata": {}, "output_type": "execute_result"}], "source": ["'''def represent(L, a0):\n", "    \"\"\"\n", "    找到状态a0的表示态和达到该表示态所需的对称操作\n", "    \n", "    参数:\n", "        L: 系统大小\n", "        a0: 初始状态\n", "        \n", "    返回:\n", "        a: 表示态\n", "        l: 平移步数\n", "        q: 是否应用了反演 (0=否, 1=是)\n", "        g: 是否应用了自旋翻转 (0=否, 1=是)\n", "    \"\"\"\n", "    smax = (1 << L) - 1  # 全1状态\n", "    \n", "    # 初始化最小状态为原始状态\n", "    min_state = a0\n", "    best_l = 0\n", "    best_q = 0\n", "    best_g = 0\n", "    \n", "    # 考虑所有可能的对称操作组合\n", "    for q in [0, 1]:  # 是否应用反演\n", "        for g in [0, 1]:  # 是否应用自旋翻转\n", "            # 应用反演和自旋翻转\n", "            if q == 1 and g == 1:\n", "                transformed = smax - reverseBits(a0, L)  # 反演+自旋翻转\n", "            elif q == 1:\n", "                transformed = reverseBits(a0, L)  # 仅反演\n", "            elif g == 1:\n", "                transformed = smax - a0  # 仅自旋翻转\n", "            else:\n", "                transformed = a0  # 无操作\n", "            \n", "            # 考虑所有可能的平移\n", "            for l in range(L):\n", "                # 应用平移\n", "                final_state = translate(L, transformed, l)\n", "                \n", "                # 检查是否找到更小的状态\n", "                if final_state < min_state:\n", "                    min_state = final_state\n", "                    best_l = l\n", "                    best_q = q\n", "                    best_g = g\n", "                \n", "                # 如果状态已经是0，不需要继续检查（0是最小的）\n", "                if min_state == 0:\n", "                    break\n", "            if min_state == 0:\n", "                break\n", "        if min_state == 0:\n", "            break\n", "    \n", "    return min_state, best_l, best_q, best_g'''"]}, {"cell_type": "code", "execution_count": 5373, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 14, 0, 0, 1)"]}, "execution_count": 5373, "metadata": {}, "output_type": "execute_result"}], "source": ["represent(4,14)"]}, {"cell_type": "code", "execution_count": 5374, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def c_for_sigma(sigma, k,l,N):\n", "    kk = 2 * np.pi * k / N\n", "    if sigma == 1:\n", "        return np.cos(kk * l)\n", "    else:\n", "        return np.sin(kk * l)\n", "def build_projection_matrix(N, k, p, z, repr_list, typee, peri, mtrf, ntrf, capr):\n", "    \"\"\"\n", "    构建投影矩阵，将原始希尔伯特空间投影到对称性适应的子空间。\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        k: 动量量子数\n", "        p: 反演对称性量子数\n", "        z: 自旋翻转对称性量子数\n", "        repr_list: 代表性构型列表\n", "        typee, peri, mtrf, ntrf, capr: 对称性信息（来自findbasis）\n", "        \n", "    返回:\n", "        proj_matrix: 投影矩阵，形状为 (nrep, 2^N)\n", "    \"\"\"\n", "    nrep = len(repr_list)\n", "    dim_hilbert = 2 ** N\n", "    proj_matrix = np.zeros((dim_hilbert,nrep), dtype=complex)\n", "    print(repr_list)\n", "    for idx in range(nrep):\n", "        s = repr_list[idx]\n", "        ca = capr[idx]\n", "        R = peri[idx]\n", "        s_sigma = typee[idx]#sigma\n", "        # 从typee中提取sigma（typee = 2*ca + (sigma+1)/2）\n", "        #sigma = 2 * (typee[idx] - 2 * ca) - 1\n", "        #print(\"态，分类\")\n", "        #print(s,ca)\n", "        for state in range(2**N):\n", "            representative,state_orginal, l, q, g = represent(N, state)\n", "            #print(representative)\n", "            R,tp,tz,tpz = checkstate(representative,k,N)\n", "            kk = 2 * np.pi * k / N\n", "            if representative == s:\n", "                #假设变量类型为 my_int，使用 printf 格式化输出（更适合C++代码风格）\n", "                # 使用 f-string 格式化，添加字段说明\n", "                print(f\"s: {s}, state: {state}, R: {R}, tp: {tp}, tz: {tz}, tpz: {tpz}, k: {k}, p: {p}, z: {z}, sigma:{s_sigma}\")\n", "                matelement = 1/R/2\n", "                if k == 0 or k == N//2:\n", "                    matelement = matelement * 2#g_k项\n", "                #print(R,m,n)\n", "                # 根据分类ca处理不同的对称性\n", "                if ((-1 == tp) and (-1 == tz) and (-1 == tpz)):\n", "                    # 只有平移对称性\n", "                    #print(ca)\n", "                    proj_matrix[state,idx] += np.sqrt(matelement)#* c_for_sigma(s_sigma,k,l,N) #phase / norm\n", "                        \n", "                if (-1 != tp) and (-1 == tz) and (-1 == tpz):\n", "                    # 反演对称性\n", "                    \n", "                    '''if np.isclose(ggun(s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    elif s_sigma == -1 and not np.isclose(ggun(-s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    else:'''\n", "                    proj_matrix[state,idx] += np.sqrt(matelement/ (1+ s_sigma * p * np.cos(kk*tp)))# * c_for_sigma(s_sigma,k,l,N)  #phase2 / norm    \n", "                if (-1 != tz) and (-1 == tp) and (-1 == tpz):\n", "                    # 自旋翻转对称性\n", "                    #if np.isclose(ggun(z, tz, k, N), 0, atol=1e-8):\n", "                    #    continue\n", "                    #else:\n", "                    proj_matrix[state,idx] += np.sqrt(matelement / (1+ z * np.cos(kk*tz))) #* c_for_sigma(s_sigma,k,l,N)  #phase3 / norm\n", "                    \n", "                if (-1 != tpz) and (-1 == tz) and (-1 == tp):\n", "                    # 联合对称性（反演+自旋翻转）\n", "                    '''if np.isclose(ggun(s_sigma * p * z, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    elif s_sigma == -1 and not np.isclose(ggun(-s_sigma * p * z, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    else:'''\n", "                    proj_matrix[state,idx] += np.sqrt(matelement/(1+ s_sigma * p * z * np.cos(kk*tpz)))# * c_for_sigma(s_sigma,k,l,N) #phase4 / norm* p#phase4 / norm\n", "                        \n", "                if (-1 != tp) and (-1 != tz) and (-1 != tpz):\n", "                    #print(representative,tp,tz,tpz)\n", "                    # 多重对称性（反演和自旋翻转）\n", "                    '''if np.isclose(ggun(z, tz, k, N) * ggun(s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    elif s_sigma == -1 and not np.isclose(ggun(-s_sigma * p, tp, k, N), 0, atol=1e-8):\n", "                        continue\n", "                    else:'''\n", "                    proj_matrix[ state,idx] += np.sqrt(matelement / (1+ s_sigma * p * np.cos(kk*tp))/(1+ z * np.cos(kk*tz))) #* c_for_sigma(s_sigma,k,l,N)  \n", "                    #phase5 / norm4 / norm\n", "                    #print(proj_matrix[ state,idx],state,idx)\n", "    print(proj_matrix)                \n", "    return proj_matrix"]}, {"cell_type": "code", "execution_count": 5375, "metadata": {}, "outputs": [], "source": ["def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = typee[a]\n", "    t = typee[b]\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)\n", "    return matelement"]}, {"cell_type": "code", "execution_count": 5376, "metadata": {}, "outputs": [], "source": ["def Ham_total_TPZ(J,h,N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        state_orginal_rep = []\n", "        sa = repr[ia]\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J\n", "            else:\n", "                Ez -= J\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            #横场项\n", "            #sb = flip_state(sa, i)\n", "            #if ai == 1:\n", "            sb = flip_state(sa, i)\n", "            #else:\n", "                #sb = flip_state(flip_state(sa, j), i)\n", "            representative,state_orginal, l, q, g = represent(N, sb)\n", "            if representative == repr[ia]:\n", "                state_orginal_rep.append(state_orginal)\n", "            if representative in repr:\n", "                ib = repr.index(representative)\n", "                if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                    ib = ib - 1\n", "                    nb = 2\n", "                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                    nb = 2\n", "                else:\n", "                    nb = 1\n", "                for ii in range(ia, ia + na):\n", "                    for jj in range(ib, ib + nb):\n", "                        try:\n", "                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)\n", "                            if np.isfinite(elem):\n", "                                Hk[ii, jj] += elem\n", "                        except Exception as e:\n", "                            print(f\"helement error at ii={ii}, jj={jj}: {e}\")\n", "        #state_orginal_all.append(state_orginal_rep)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 5377, "metadata": {}, "outputs": [], "source": ["def sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia>1 and sa==repr[ia-1]):\n", "            continue\n", "        elif (ia<nrep-1 and sa==repr[ia+1]):\n", "            na=2\n", "        else:\n", "            na=1\n", "        for a in range(ia,ia+na):\n", "            Hk[a,a] += (1/2 )*N    \n", "        for i in range(N):\n", "            for j in range(i+1,N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                if ai != aj:\n", "                    if  ai == 1:   \n", "                        sb = flip_state(flip_state(sa,i),j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa,j),i)\n", "                    representative,state, l,q,g = represent(N,sb)\n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        if ib >1 and repr[ib]==repr[ib-1]:\n", "                            ib = ib-1\n", "                            nb=2\n", "                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\n", "                            nb=2\n", "                        else:\n", "                            nb=1\n", "                        for ii in range(ia,ia+na):\n", "                            for jj in range(ib,ib+nb):\n", "                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 5378, "metadata": {}, "outputs": [], "source": ["def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    #print(a)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    "]}, {"cell_type": "code", "execution_count": 5379, "metadata": {}, "outputs": [], "source": ["def fullspectrum(J,h,N):\n", "    E=[]\n", "    #k=0\n", "    k_min = []\n", "    p_min = []\n", "    z_min = []\n", "    E_min = []\n", "    spi_min = []\n", "\n", "    v_full = []\n", "    Hk_full = []\n", "    new_basis_matrix = []\n", "\n", "    quasi_energies = []\n", "    for k in range(N):\n", "        if k==0 or k==N//2:\n", "            p1=-1\n", "            p2=1\n", "        else:\n", "            p1=1\n", "            p2=1\n", "        for p in range(p1,p2+1,2):\n", "            for z in [-1,1]:\n", "                nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)#k,p,z是标记是否有对称性的参数\n", "                \n", "                #print(k,p,z,repr,capr)\n", "                if nrep != 0:\n", "                    Hk_1 = Ham_total_TPZ(0*J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    Hk_2 = Ham_total_TPZ(J,0*h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    H_f = expm(-1j*t_1*Hk_1) @ expm(-1j*t_2*Hk_2)\n", "                    #print(repr)\n", "                    # 拼接为完整矩阵\n", "                    if len(Hk_full) == 0:\n", "                        Hk_full = H_f\n", "                    else:\n", "                        Hk_full = block_direct_sum([Hk_full,H_f])#np.block(block_structure)\n", "\n", "                    eigenvalue, featurevector =np.linalg.eig(H_f)\n", "                    Hk_spin = sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    spn = transform(nrep,Hk_spin,featurevector)\n", "                    spin = []\n", "                    for spin_i in range(len(spn)):\n", "                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\n", "                    E1 = eigenvalue.tolist()\n", "                    #print(E1)\n", "                    #print(k,p,z,repr)\n", "                    <PERSON>.extend(eigenvalue.tolist())\n", "                    \n", "                    if len(v_full) == 0:\n", "                        v_full = featurevector\n", "                    else:\n", "                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)\n", "                    # 构造投影矩阵 V_k\n", "                    #V_k = build_projection_matrix(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    V_k = build_projection_matrix(N, k, p, z, repr, typee, peri, mtrf, ntrf, capr)\n", "                    #print(V_k)\n", "                    #V_k = build_projection_matrix_for_k_test(N, repr, peri, mtrf, ntrf, capr, p, z, k, sigma=1)\n", "                    #矩阵按列直接拼接\n", "                    if len(new_basis_matrix) == 0:\n", "                        new_basis_matrix = V_k\n", "                    else:\n", "                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）\n", "                    #print(new_basis_matrix.shape)\n", "                    if len(E1) != 0:\n", "                        for i in range(len(E1)):\n", "                            idx = E1.index(np.min(E1))\n", "                            k_min.append(k)\n", "                            p_min.append(p)\n", "                            z_min.append(z)\n", "                            E_min.append(E1[idx])\n", "                            spi_min.append(spin[idx])\n", "                            #print(len(E1))\n", "                            #print(np.min(E1),E1.index(np.min(E1)))\n", "                            E1.pop(idx)\n", "                            spin.pop(idx)   \n", "    full_eigenvectors = new_basis_matrix @ v_full\n", "    quasi_energies.extend(-np.angle(E))  # Floquet准能\n", "    # 使用您的方法计算全空间 IPR\n", "    #ipr1 = np.abs(full_eigenvectors) **4\n", "    ipr1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), \n", "                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))\n", "    ipr = np.sum(ipr1, axis=0)\n", "    return quasi_energies,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_full,ipr"]}, {"cell_type": "code", "execution_count": 5380, "metadata": {}, "outputs": [], "source": ["H1 = Ham_total(N,0*J_z,h_x)\n", "H2 = Ham_total(N,J_z,0*h_x)\n", "H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)\n", "\n", "eigvals_all, v_full = np.linalg.eig(H_F)\n", "#eigvals_all= np.linalg.eigvalsh(H_tot)\n", "quasienergy = [0 for index in range(2**N)]\n", "for i in range(0,2**N,1):\n", "    quasienergy[i] = (-cmath.phase(eigvals_all[i]))\n", "quasienergy1 = quasienergy.copy()\n"]}, {"cell_type": "code", "execution_count": 5381, "metadata": {}, "outputs": [], "source": ["#eigvals_all"]}, {"cell_type": "code", "execution_count": 5382, "metadata": {}, "outputs": [], "source": ["#min_n =8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,0)"]}, {"cell_type": "code", "execution_count": 5383, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1]\n", "s: 0, state: 0, R: 1, tp: 0, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 0, state: 15, R: 1, tp: 0, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: -1, sigma:1\n", "[[0.70710678+0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.70710678+0.j 0.        +0.j]]\n", "[0, 1, 3, 5]\n", "s: 0, state: 0, R: 1, tp: 0, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 0, state: 15, R: 1, tp: 0, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 0, p: 1, z: 1, sigma:1\n", "s: 3, state: 3, R: 4, tp: 2, tz: 2, tpz: 0, k: 0, p: 1, z: 1, sigma:1\n", "s: 3, state: 6, R: 4, tp: 2, tz: 2, tpz: 0, k: 0, p: 1, z: 1, sigma:1\n", "s: 3, state: 9, R: 4, tp: 2, tz: 2, tpz: 0, k: 0, p: 1, z: 1, sigma:1\n", "s: 3, state: 12, R: 4, tp: 2, tz: 2, tpz: 0, k: 0, p: 1, z: 1, sigma:1\n", "s: 5, state: 5, R: 2, tp: 1, tz: 1, tpz: 0, k: 0, p: 1, z: 1, sigma:1\n", "s: 5, state: 10, R: 2, tp: 1, tz: 1, tpz: 0, k: 0, p: 1, z: 1, sigma:1\n", "[[0.70710678+0.j 0.        +0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j 0.25      +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j 0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.        +0.j 0.25      +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j 0.25      +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j 0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j 0.25      +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j 0.        +0.j 0.        +0.j]\n", " [0.70710678+0.j 0.        +0.j 0.        +0.j 0.        +0.j]]\n", "[1, 3]\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: -1, sigma:1\n", "s: 3, state: 3, R: 4, tp: 2, tz: 2, tpz: 0, k: 1, p: 1, z: -1, sigma:-1\n", "s: 3, state: 6, R: 4, tp: 2, tz: 2, tpz: 0, k: 1, p: 1, z: -1, sigma:-1\n", "s: 3, state: 9, R: 4, tp: 2, tz: 2, tpz: 0, k: 1, p: 1, z: -1, sigma:-1\n", "s: 3, state: 12, R: 4, tp: 2, tz: 2, tpz: 0, k: 1, p: 1, z: -1, sigma:-1\n", "[[0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]]\n", "[1]\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 1, p: 1, z: 1, sigma:1\n", "[[0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]]\n", "[1, 5]\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: -1, sigma:1\n", "s: 5, state: 5, R: 2, tp: 1, tz: 1, tpz: 0, k: 2, p: -1, z: -1, sigma:1\n", "s: 5, state: 10, R: 2, tp: 1, tz: 1, tpz: 0, k: 2, p: -1, z: -1, sigma:1\n", "[[0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.35355339+0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]]\n", "[1]\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 2, p: -1, z: 1, sigma:1\n", "[[0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]]\n", "[3]\n", "s: 3, state: 3, R: 4, tp: 2, tz: 2, tpz: 0, k: 2, p: 1, z: 1, sigma:1\n", "s: 3, state: 6, R: 4, tp: 2, tz: 2, tpz: 0, k: 2, p: 1, z: 1, sigma:1\n", "s: 3, state: 9, R: 4, tp: 2, tz: 2, tpz: 0, k: 2, p: 1, z: 1, sigma:1\n", "s: 3, state: 12, R: 4, tp: 2, tz: 2, tpz: 0, k: 2, p: 1, z: 1, sigma:1\n", "[[0.  +0.j]\n", " [0.  +0.j]\n", " [0.  +0.j]\n", " [0.25+0.j]\n", " [0.  +0.j]\n", " [0.  +0.j]\n", " [0.25+0.j]\n", " [0.  +0.j]\n", " [0.  +0.j]\n", " [0.25+0.j]\n", " [0.  +0.j]\n", " [0.  +0.j]\n", " [0.25+0.j]\n", " [0.  +0.j]\n", " [0.  +0.j]\n", " [0.  +0.j]]\n", "[1, 3]\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: -1, sigma:1\n", "s: 3, state: 3, R: 4, tp: 2, tz: 2, tpz: 0, k: 3, p: 1, z: -1, sigma:-1\n", "s: 3, state: 6, R: 4, tp: 2, tz: 2, tpz: 0, k: 3, p: 1, z: -1, sigma:-1\n", "s: 3, state: 9, R: 4, tp: 2, tz: 2, tpz: 0, k: 3, p: 1, z: -1, sigma:-1\n", "s: 3, state: 12, R: 4, tp: 2, tz: 2, tpz: 0, k: 3, p: 1, z: -1, sigma:-1\n", "[[0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.        +0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.1767767 +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.35355339+0.j 0.        +0.j]\n", " [0.        +0.j 0.        +0.j]]\n", "[1]\n", "s: 1, state: 1, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 2, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 4, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 7, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 8, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 11, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 13, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "s: 1, state: 14, R: 4, tp: 3, tz: -1, tpz: -1, k: 3, p: 1, z: 1, sigma:1\n", "[[0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]\n", " [0.35355339+0.j]\n", " [0.35355339+0.j]\n", " [0.        +0.j]]\n"]}], "source": ["E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform ,Hk_block_full,ipr= fullspectrum(J_z,h_x,N)\n", "    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))\n", "    #print(np.array(eigvals))\n"]}, {"cell_type": "code", "execution_count": 5384, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16 16\n"]}], "source": ["print (len(E_full),len(quasienergy))\n", "for x in E_full:\n", "    index = np.abs(quasienergy - x).argmin()\n", "    if np.isclose(quasienergy[index], x) != True:\n", "        print(np.isclose(quasienergy[index], x), quasienergy[index], x)"]}, {"cell_type": "code", "execution_count": 5385, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiIAAAGdCAYAAAAvwBgXAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAs+UlEQVR4nO3df3RU9Z3/8dedyWQGNDOQUEiyhCRUVwzxJ8GKguJaOFWXypdTWZaCeixWVkWBXY9SCkZzMFat0eoCstlVjy5i3WJ07ZESrCIcpQaBVX6sVMkPLIEIYTMpdCaTmfv9IzI1ApHg3Pkkk+fjnDkyMzfzfk9ynM9rPvdz77Vs27YFAABggMt0AwAAoO8iiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwJs10A12JxWLat2+fMjIyZFmW6XYAAMApsG1bra2tys3NlcvV9ZxHjw4i+/btU15enuk2AADAadi7d6+GDh3a5TY9OohkZGRI6ngjfr/fcDcAAOBUBINB5eXlxcfxrvToIHJsd4zf7yeIAADQy5zKsgoWqwIAAGMIIgAAwBiCCAAAMKZHrxE5FbZtq729XdFo1HQrKcHtdistLY3DpQEASdGrg0hbW5saGxt19OhR062klP79+ysnJ0fp6emmWwEApLheG0RisZhqa2vldruVm5ur9PR0vsV/S7Ztq62tTV988YVqa2t19tlnf+OJaAAA+DZ6bRBpa2tTLBZTXl6e+vfvb7qdlNGvXz95PB7V19erra1NPp/PdEsAgBTW67/u8o098fidAkDfEIpE9UVrWKGIuXWWvXZGBAAAnJ6aumZVbtij6p0HFLMllyVNKBqiW8cNV0lBZlJ74asvAABfMjVDkMy6L2yq19Tl72vdribF7I7HYra0bleTblj+vl7cVO94D1/FjIgBtm3rtttu03/913/p8OHD2rp1qy688MIuf8ayLL366quaPHmy6urqVFhYeEo/BwD4ZqZmCJJdt6auWYurtsuW5I+1yKeIGpWlHB1SKObRYfm1qGq7RmRnJG1mhBmRLyUzja5Zs0bPPfec3njjDTU2Nqq4uNjxmgCAEzM1Q2CibuWGPXK5LA1UUCvTl2hVepkutnZrVXqZVqYv0UAF5XJZqtxYm/DaJ9PnZ0RMpODPPvtMOTk5uuyyyxx5fQDAqTE1Q2CibigSjY91PkXUX2Hlu5q02lsqSaqPDZZPER2O2Vq7Y79Ckah8HndCanelT8+ImEijN998s+bMmaOGhgZZlqWCggIVFBToiSee6LTdhRdeqNLS0oTXBwD8lakZAhN1W0Pt8bGuUVmaF7m90/PzIrerUVmSOsbC1lB7wmp3pc8Gka+m0eixv8yXojFbtqRFVdu1ua45oXWffPJJPfjggxo6dKgaGxtVU1OT0NcHAJyaYzME0Zh93AxBvqtJ/RWWTxFFvzJD0JvrZvjS5PryvJ85OqQKz9JOz1d4lipHhyR17B3I8CVnp0mfDSLH0mhXnEjBgUBAGRkZcrvdys7O1ne+852Evj4A4NSYmiEwVdfncWtC0RC5XZZC8uiovKqPDdaUcKnqY4N1VF6F5JHbZWniyOyk7JaR+mgQ+Woa7Uqi0ygAoOcwNUNgcmZi1rjhisVsHZZf09sWalrbIm2x/1bT2hZpettCHZZfsZitWWMLE1bzmzgaRJYtW6bzzz9ffr9ffr9fY8aM0ZtvvulkyVPy1TT6TZKxn8zlcsm2OzcUiUQcrQkAfZ2pGQKTMxOjCzJVNrlYlqSgKxCfeWlUloKugCxJZZOLk3pSM0d3AA0dOlQPP/ywzjrrLEnS888/r+uvv15bt27VyJEjnSzdpWNp9FTCSDL2k33nO99RY2Nj/H4wGFRtbfIOnQKAvmrWuOFau+NAfIbg2NEr09oWKaSOo1csB2YITNWVpBmX5mtEdoYqN9Zq7Y79nY4YnTW2MOlnVnV0hJ00aVKn+0uWLNGyZcu0adMmo0HkWBpdt6upy90zbpelCUVDHN9P9nd/93d67rnnNGnSJA0cOFCLFi2S252cfXMA0JcdmyFYVLVdQVdAh78cExqVJbfLkhWzHZkhMFX3mJKCTJUUZCoUiao11K4MX1rS1oR8XdLOIxKNRvXKK6/oyJEjGjNmzAm3CYfDCofD8fvBYNCxfo6l0a4kaz/ZggULtGfPHv393/+9AoGAysrKmBEBgCQxNUPQE2YmfB63sQByjGV/fXFCgn388ccaM2aMQqGQzjzzTK1cuVLXXnvtCbctLS3VAw88cNzjLS0t8vv9nR4LhUKqra1VYWHhaV+q/sVN9VpUtV0ul9VpZsTtshT7Mo3OuDT/tF67N0vE7xYAeiNTMwQ9YWYikYLBoAKBwAnH769zPIi0tbWpoaFB//d//6ff/OY3qqys1Pr161VUVHTctieaEcnLy3MsiEjS5rrm49LoxJHZRvaT9RQEEQDAt9GdIOL4rpn09PT4YtWSkhLV1NToySef1DPPPHPctl6vV16v1+mWOulJ+8kAAOhrkn6tGdu2O8169BQ9YT8ZAAB9jaNB5Gc/+5muueYa5eXlqbW1VatWrdI777yjNWvWOFkWAAD0Eo4GkQMHDmjmzJlqbGxUIBDQ+eefrzVr1mjChAlOlgUAAL2Eo0Hk3//93518eQAA0Mv1yWvNAACAnoEgAgDoUUKRqL5oDXPB0T6CIGLA+PHjNXfu3JM+X1BQoCeeeCJh9RL9egDghJq6Zt32wmYVLV6j0UvWqWjxGt32wmZtrms23RocRBABABj3wqZ6TV3+vtbtaopfkDRmS+t2NemG5e/rxU31ZhuEYwgiAACjauqatbhqu2xJ/liLcnRIkpSjQ/LHWmRLWlS1nZmRFEUQMaS9vV133nmnBgwYoKysLP385z/Xyc6239DQoOuvv15nnnmm/H6/pk6dqgMHOl+w7/XXX1dJSYl8Pp8GDRqkKVOmnLT2s88+q0AgoOrq6oS+JwA4HZUb9sjlsjRQQa1MX6JV6WW62NqtVellWpm+RAMVlMtlqXIjFwNNRQQRSTpySGr5vOPfLZ933HfY888/r7S0NP3hD3/Qr371K1VUVKiysvK47Wzb1uTJk9Xc3Kz169erurpan332mf7hH/4hvs1vf/tbTZkyRdddd522bt2qt956SyUlJSes+9hjj+lf/uVf9Lvf/Y7zuQAwLhSJqnrnAUVjtnyKqL/Cync1abW3VPmuJvVXWD5FFI3ZWrtjPwtYU1DST/He4xw5JD0/SYockf7fCunVn0qeM6Sb/ls6I8uxsnl5eaqoqJBlWTrnnHP08ccfq6KiQrfeemun7datW6ePPvpItbW1ysvLkyS98MILGjlypGpqajR69GgtWbJE06ZN63Tl4gsuuOC4mgsWLNDzzz+vd955R+edd55j7w0ATlVrqD2+JqRRWZoXuV2rvaXx5+dFblejOj6LY3bH9lyOI7UwI9L+l44QcrhO+o+JHf+NHOl43EGXXnqpLMuK3x8zZoz++Mc/KhrtnPZ37dqlvLy8eAiRpKKiIg0YMEC7du2SJG3btk1XX311l/V++ctf6plnntHGjRsJIQB6jAxfmlxffhTm6JAqPEs7PV/hWRpfM+KyOrZHaiGIBIZ2zIR81f9b0fF4D2DbdqfAcqLH+/Xr942vM27cOEWjUf36179OeI8AcLp8HrcmFA2R22UpJI+Oyqv62GBNCZeqPjZYR+VVSB65XZYmjsxmNiQFEURaPu/YHfNVr/70r2tGHLJp06bj7p999tlyuzv/T1ZUVKSGhgbt3bs3/tjOnTvV0tKic889V5J0/vnn66233uqy3iWXXKI1a9booYce0qOPPpqgdwEA396sccMVi9k6LL+mty3UtLZF2mL/raa1LdL0toU6LL9iMVuzxhaabhUOIIik9etYEzKwQLplbcd/PWd0PO6gvXv3av78+frkk0/00ksv6amnntLdd9993Hbf//73df755+vHP/6xtmzZog8++EA33nijrrzyyviC1Pvvv18vvfSS7r//fu3atUsff/yxHnnkkeNea8yYMXrzzTf14IMPqqKiwtH3BwCnanRBpsomF8uSFHQF4mtCGpWloCsgS1LZ5GKVFGQa7RPOYGfbGVkdC1Pb/9KxO+bm33aEEAcXqkrSjTfeqL/85S+65JJL5Ha7NWfOHP30pz89bjvLslRVVaU5c+boiiuukMvl0g9+8AM99dRT8W3Gjx+vV155RWVlZXr44Yfl9/t1xRVXnLDu5Zdfrt/+9re69tpr5Xa7dddddzn2HgHgVM24NF8jsjNUubFWa3fsV8zuWBMyoWiIZo0tJISkMMs+2ckreoBgMKhAIKCWlhb5/f5Oz4VCIdXW1qqwsFA+n89Qh6mJ3y0Ak0KRqFpD7crwpbEmpJfqavz+OmZEAAA9is/jJoD0IawRAQAAxhBEAACAMQQRAABgDEEEAAAY0+uDSA8+6KfX4ncKAEiWXhtEPB6PJOno0aOGO0k9x36nx37HAAA4pdcevut2uzVgwAA1NTVJkvr373/Ca7Lg1Nm2raNHj6qpqUkDBgw47nTzAAAkWq8NIpKUnZ0tSfEwgsQYMGBA/HcLAICTenUQsSxLOTk5Gjx4sCKRiOl2UoLH42EmBACQNL06iBzjdrsZPAEA6IV67WJVAADQ+xFEAACAMQQRAABgDEEEAAAYQxABAADGEEQAAIAxBBEAAGAMQQQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQAAxhBEAACAMQQRAABgDEEEAAAYQxABAADGOBpEysvLNXr0aGVkZGjw4MGaPHmyPvnkEydLAgCAXsTRILJ+/Xrdcccd2rRpk6qrq9Xe3q6JEyfqyJEjTpYFAAC9hGXbtp2sYl988YUGDx6s9evX64orrvjG7YPBoAKBgFpaWuT3+5PQIQAA+La6M34ndY1IS0uLJCkzMzOZZQEAQA+VlqxCtm1r/vz5Gjt2rIqLi0+4TTgcVjgcjt8PBoPJag8AABiQtBmRO++8Ux999JFeeumlk25TXl6uQCAQv+Xl5SWrPQAAYEBS1ojMmTNHVVVVevfdd1VYWHjS7U40I5KXl8caEQAAepHurBFxdNeMbduaM2eOXn31Vb3zzjtdhhBJ8nq98nq9TrYEAAB6EEeDyB133KGVK1fqtddeU0ZGhvbv3y9JCgQC6tevn5OlAQBAL+DorhnLsk74+LPPPqubb775G3+ew3cBAOh9etSuGQAAgJPhWjMAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjHE0iLz77ruaNGmScnNzZVmWqqqqnCwHAAB6GUeDyJEjR3TBBRfo6aefdrIMAADopdKcfPFrrrlG11xzjZMlAABAL+ZoEOmucDiscDgcvx8MBg12AwAAnNajFquWl5crEAjEb3l5eaZbAgAADupRQWTBggVqaWmJ3/bu3Wu6JQAA4KAetWvG6/XK6/WabgMAACRJj5oRAQAAfYujMyJ//vOf9emnn8bv19bWatu2bcrMzNSwYcOcLA0AAHoBR4PI5s2bddVVV8Xvz58/X5J000036bnnnnOyNAAA6AUcDSLjx4+XbdtOlgAAAL0Ya0QAAIAxBBEAAGAMQQQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQAAxhBEAACAMQQRAABgDEEEAAAYQxABAADGEEQAAIAxBBEAAGAMQQQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQCcUCgS1RetYYUiUdOtIIWlmW4AANCz1NQ1q3LDHlXvPKCYLbksaULREN06brhKCjJNt4cUw4wIACDuhU31mrr8fa3b1aSY3fFYzJbW7WrSDcvf14ub6s02iJRDEAEASOqYCVlctV22JH+sRTk6JEnK0SH5Yy2yJS2q2q7Ndc1G+0RqIYgAACRJlRv2yOWyNFBBrUxfolXpZbrY2q1V6WVamb5EAxWUy2WpcmOt6VaRQggiAACFIlFV7zygaMyWTxH1V1j5riat9pYq39Wk/grLp4iiMVtrd+xnASsShiACAFBrqD2+JqRRWZoXub3T8/Mit6tRWZI61oy0htqT3SJSFEEEAKAMX5pcVse/c3RIFZ6lnZ6v8CyNrxlxWR3bA4lAEAEAyOdxa0LRELldlkLy6Ki8qo8N1pRwqepjg3VUXoXkkdtlaeLIbPk8btMtI0UQaQEAkqRZ44Zr7Y4DOiy/prctlE8RNSpL09oWKSSPDssvK2Zr1thC060ihTAjAgCQJI0uyFTZ5GJZkoKuQHxNSKOyFHQFZEkqm1zMSc2QUMyIAADiZlyarxHZGarcWKu1O/Z3OrPqrLGFhBAkHEEEANBJSUGmSgoyFYpE1RpqV4YvjTUhcAxBBABwQj6PmwACx7FGBAAAGEMQAQAAxhBEAACAMQQRAABgDEEEAAAYQxABAADGEEQAoIcLRaL6ojWsUCRquhUg4TiPCJACTJ14irrOqqlrVuWGPareeaDTGU5vHTecM5wiZRBEgF7M1EBFXefrvrCpXourtsvlshSzOx6L2dK6XU1au+OAyiYXa8al+Y7UBpIpKbtmli5dqsLCQvl8Po0aNUobNmxIRlkg6ZI5hf7CpnpNXf6+1u1qOm6gumH5+3pxUz11e2ndmrpmLa7aLluSP9aiHB2SJOXokPyxFtmSFlVt1+a65oTXBpLN8SDy8ssva+7cuVq4cKG2bt2qcePG6ZprrlFDQ4PTpYGkqalr1m0vbFbR4jUavWSdihav0W0vbHZsoDA1UFE3OXUrN+yRy2VpoIJamb5Eq9LLdLG1W6vSy7QyfYkGKiiXy1LlxtqE1gVMcDyIPP744/rJT36iWbNm6dxzz9UTTzyhvLw8LVu2zOnSQFKY+MZsaqCirvN1Q5GoqnceUDRmy6eI+iusfFeTVntLle9qUn+F5VNE0ZittTv2s4AVvZ6jQaStrU0ffvihJk6c2OnxiRMn6r333jtu+3A4rGAw2OkG9GQmvjGbGqiom5y6raH2eKBtVJbmRW7v9Py8yO1qVJakjsDbGmpPSF3AFEeDyMGDBxWNRjVkyJBOjw8ZMkT79+8/bvvy8nIFAoH4LS8vz8n2gG/NxDdmUwMVdZNTN8OXJpfV8e8cHVKFZ2mn5ys8S+OB12V1bA/0ZklZrGpZVqf7tm0f95gkLViwQC0tLfHb3r17k9EecFpMfWM2NVBRNzl1fR63JhQNkdtlKSSPjsqr+thgTQmXqj42WEflVUgeuV2WJo7MTurhy4ATHA0igwYNktvtPm72o6mp6bhZEknyer3y+/2dbkBPZeobs6mBirrJCwSzxg1XLGbrsPya3rZQ09oWaYv9t5rWtkjT2xbqsPyKxWzNGluYsJqAKZZt27aTBb73ve9p1KhRWrr0r98mioqKdP3116u8vLzLnw0GgwoEAmppaSGUoMcJRaIqWrxGMbvjG/Oq9DLlu5riz9fHBmta2yI1KksuS9r54A8SNljV1DVr6vL3ZUsaqKB8iqhRWcrRIYXk0WH5ZUl6ZfaYhJ7ngrrJqStJL26q16IvzyMSjf31Y9rtshSL2ZxHBD1ad8Zvx3fNzJ8/X5WVlfqP//gP7dq1S/PmzVNDQ4Nmz57tdGnAUSa/MY8uyFTZ5GJZkoKuQHzmpVFZCroCsiSVTS5O+OBI3eTUlaQZl+brldljNKFoSHwX0bETqb0yewwhBCnD8RkRqeOEZo888ogaGxtVXFysiooKXXHFFd/4c8yIoKcz+Y1ZkjbXNatyY63W7tgfP+PnxJHZmjW20NEzjVI3OXWPMXVKe+B0dWf8TkoQOV0EEfQGPWEKva9ce6Wv1gV6m+6M3xz3BXxLMy7N14jsjOO+MU8oGpK0b8w+j9vIwEhdAN8WQQRIgJKCTJUUZPKNGQC6iSACJBDfmAGge5JyQjMAAIATIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIkhJoUhUX7SGFYpETbcCAOgC5xFBSqmpa1blhj2q3nmg0xlObx03PClnOAUAdA8zIkgZL2yq19Tl72vdriYdu+RLzJbW7WrSDcvf14ub6s02CAA4DkEEKaGmrlmLq7bLluSPtShHhyRJOTokf6xFtqRFVdu1ua7ZaJ8AgM4IIkgJlRv2yOWyNFBBrUxfolXpZbrY2q1V6WVamb5EAxWUy2WpcmOt6VYBAF9BEEGvF4pEVb3zgKIxWz5F1F9h5buatNpbqnxXk/orLJ8iisZsrd2xnwWsANCDEETQ67WG2uNrQhqVpXmR2zs9Py9yuxqVJaljzUhrqD3ZLQIAToIggl4vw5cml9Xx7xwdUoVnaafnKzxL42tGXFbH9gCAnoEggl7P53FrQtEQuV2WQvLoqLyqjw3WlHCp6mODdVReheSR22Vp4shs+Txu0y0DAL7EV0OkhFnjhmvtjgM6LL+mty2UTxE1KkvT2hYpJI8Oyy8rZmvW2ELTrQIAvoIZEaSE0QWZKptcLEtS0BWIrwlpVJaCroAsSWWTizmpGQD0MMyIIGXMuDRfI7IzVLmxVmt37O90ZtVZYwsJIQDQAxFEkFJKCjJVUpCpUCSq1lC7MnxprAkBgB6MIIKU5PO4CSAA0AuwRgQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQAAxhBEAACAMQQRAABgDEEEAAAYQxABAADGEEQAAIAxBBEAAGAMQQQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQAAxhBEAACAMQQRAABgjKNBZMmSJbrsssvUv39/DRgwwMlSAACgF3I0iLS1temGG27QP/3TPzlZBgAA9FJpTr74Aw88IEl67rnnnCwDAAB6KUeDSHeFw2GFw+H4/WAwaLAbAADgtB61WLW8vFyBQCB+y8vLM90SAABwULeDSGlpqSzL6vK2efPm02pmwYIFamlpid/27t17Wq8DAAB6h27vmrnzzjs1bdq0LrcpKCg4rWa8Xq+8Xu9p/SwAAOh9uh1EBg0apEGDBjnRCwAA6GMcXaza0NCg5uZmNTQ0KBqNatu2bZKks846S2eeeaaTpQEAQC/gaBBZvHixnn/++fj9iy66SJL09ttva/z48U6WBgAAvYBl27ZtuomTCQaDCgQCamlpkd/vN90OAAA4Bd0Zv3vU4bsAAKBvIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjCGIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMcCyJ1dXX6yU9+osLCQvXr10/f/e53df/996utrc2pkgAAoJdJc+qF//d//1exWEzPPPOMzjrrLG3fvl233nqrjhw5oscee8ypsgAAoBexbNu2k1Xs0Ucf1bJly7Rnz55T2j4YDCoQCKilpUV+v9/h7gAAQCJ0Z/x2bEbkRFpaWpSZmXnS58PhsMLhcPx+MBhMRlsAAMCQpC1W/eyzz/TUU09p9uzZJ92mvLxcgUAgfsvLy0tWewAAwIBuB5HS0lJZltXlbfPmzZ1+Zt++ffrBD36gG264QbNmzTrpay9YsEAtLS3x2969e7v/jgAAQK/R7TUiBw8e1MGDB7vcpqCgQD6fT1JHCLnqqqv0ve99T88995xcrlPPPqwRAQCg93F0jcigQYM0aNCgU9r2T3/6k6666iqNGjVKzz77bLdCCAAASH2OLVbdt2+fxo8fr2HDhumxxx7TF198EX8uOzvbqbIAAKAXcSyIrF27Vp9++qk+/fRTDR06tNNzSTxiGAAA9GCO7Su5+eabZdv2CW8AAAAS15oBAAAGEUQAAIAxBBEAAGAMQQQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQAAxhBEAACAMQQRAABgDEEEAAAYQxABAADGEEQAAIAxBBEAAGAMQQQAABhDEAEAAMYQRAAAgDEEEQAAYAxBBAAAGEMQAQAAxhBEAACAMQQRAABgDEEEAAAYQxABAADGEEQAAIAxBBEAAGAMQQSOCkWi+qI1rFAkaroVAEAPlGa6AaSmmrpmVW7Yo+qdBxSzJZclTSgaolvHDVdJQabp9gAAPQQzIki4FzbVa+ry97VuV5NidsdjMVtat6tJNyx/Xy9uqjfbIACgxyCIIKFq6pq1uGq7bEn+WItydEiSlKND8sdaZEtaVLVdm+uajfYJAOgZCCJIqMoNe+RyWRqooFamL9Gq9DJdbO3WqvQyrUxfooEKyuWyVLmx1nSrAIAegCCChAlFoqreeUDRmC2fIuqvsPJdTVrtLVW+q0n9FZZPEUVjttbu2M8CVgAAQQSJ0xpqj68JaVSW5kVu7/T8vMjtalSWpI41I62h9mS3CADoYQgiSJgMX5pcVse/c3RIFZ6lnZ6v8CyNrxlxWR3bAwD6NoIIEsbncWtC0RC5XZZC8uiovKqPDdaUcKnqY4N1VF6F5JHbZWniyGz5PG7TLQMADOMrKRJq1rjhWrvjgA7Lr+ltC+VTRI3K0rS2RQrJo8Pyy4rZmjW20HSrAIAegBkRJNTogkyVTS6WJSnoCsTXhDQqS0FXQJakssnFnNQMACCJGRE4YMal+RqRnaHKjbVau2N/pzOrzhpbSAgBAMQRROCIkoJMlRRkKhSJqjXUrgxfGmtCAADHIYjAUT6PmwACADgp1ogAAABjCCIAAMAYR4PID3/4Qw0bNkw+n085OTmaOXOm9u3b52RJAADQizgaRK666ir9+te/1ieffKLf/OY3+uyzz/SjH/3IyZIAAKAXsWzbtpNV7PXXX9fkyZMVDofl8Xi+cftgMKhAIKCWlhb5/f4kdAgAAL6t7ozfSTtqprm5Wf/5n/+pyy677KQhJBwOKxwOx+8Hg8FktQcAAAxwfLHqvffeqzPOOENZWVlqaGjQa6+9dtJty8vLFQgE4re8vDyn20u6UCSqL1rDCkWifaIuAABd6faumdLSUj3wwANdblNTU6OSkhJJ0sGDB9Xc3Kz6+no98MADCgQCeuONN2RZ1nE/d6IZkby8PEd2zST7RFs1dc2q3LBH1TsPdDrT6K3jhjt6plFTdQEAfVd3ds10O4gcPHhQBw8e7HKbgoIC+Xy+4x7//PPPlZeXp/fee09jxoz5xlpOrBExMTC/sKlei6u2y+WyFI399dftdlmKxWyVTS7WjEvzU6YuAKBvc3SNyKBBgzRo0KDTauxY5vnqrEcyfXVgPjYux2xp3a4mrd1xwJGBuaauWYurtsuW5I+1xK9Gm6NDCsU6rka7qGq7RmRnJDQImaoLAEB3OLZG5IMPPtDTTz+tbdu2qb6+Xm+//bamT5+u7373u6c0G5JoXx+Yc3RIkpSjQ/LHWmRLWlS1XZvrmhNat3LDHrlclgYqqJXpS7QqvUwXW7u1Kr1MK9OXaKCCcrksVW6sTYm6AAB0h2NBpF+/flq9erWuvvpqnXPOObrllltUXFys9evXy+v1OlX2pEwMzKFIVNU7Dygas+VTRP0VVr6rSau9pcp3Nam/wvIpomjM1tod+xO2kNRUXQAAusuxw3fPO+88/f73v3fq5bvl2MAcs3XcwCxJ9bHB8imiw18ZmBOxgLU11B7fBdSoLM2L3B6vKUnzIrerUVmSOnYRtYbae3VdAAC6q09ca+ZEA/NXnWhgToQMX5pcXx4clKNDqvAs7fR8hWdpfBeRy+rYvjfXBQCgu/pEEDE1MPs8bk0oGiK3y1JIHh2VV/WxwZoSLlV9bLCOyquQPHK7LE0cmZ2wWQlTdQEA6K4+8VX42MC8bleTQrG/DszzIrerwrO008A8oWhIQgfmWeOGa+2OAzosv6a3LYwfvTKtbZFC6jh6xYrZmjW2MGE1TdYFAKA7knqtme5K5HlEauqaNXX5+7IlDVSw8+GsxwZmSa/MHpPww1lf3FSvRQbO52GqLgCgb3P0hGbJlOgTmpkcmDfXNatyY63W7tgfP5HaxJHZmjW20NHzeJiqCwDouwgiXTA9MCf71PKm6wIA+p4eefXdnqKkIFMlBZnGBmafx20kCJiqCwBAV/pcEDmGgRkAAPP6xOG7AACgZyKIAAAAYwgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCmR5/Q7NjZ54PBoOFOAADAqTo2bp/KVWR6dBBpbW2VJOXl5RnuBAAAdFdra6sCgUCX2/Toi97FYjHt27dPGRkZsiwroa8dDAaVl5envXv3JuyCej0Z7ze18X5TG+839aXae7ZtW62trcrNzZXL1fUqkB49I+JyuTR06FBHa/j9/pT4o58q3m9q4/2mNt5v6kul9/xNMyHHsFgVAAAYQxABAADG9Nkg4vV6df/998vr9ZpuJSl4v6mN95vaeL+pry++52N69GJVAACQ2vrsjAgAADCPIAIAAIwhiAAAAGMIIgAAwJg+GUSWLl2qwsJC+Xw+jRo1Shs2bDDdkiPKy8s1evRoZWRkaPDgwZo8ebI++eQT020lTXl5uSzL0ty5c0234qg//elPmjFjhrKystS/f39deOGF+vDDD0235Yj29nb9/Oc/V2Fhofr166fhw4frwQcfVCwWM91aQrz77ruaNGmScnNzZVmWqqqqOj1v27ZKS0uVm5urfv36afz48dqxY4eZZhOgq/cbiUR077336rzzztMZZ5yh3Nxc3Xjjjdq3b5+5hr+lb/r7ftVtt90my7L0xBNPJK0/U/pcEHn55Zc1d+5cLVy4UFu3btW4ceN0zTXXqKGhwXRrCbd+/Xrdcccd2rRpk6qrq9Xe3q6JEyfqyJEjpltzXE1NjVasWKHzzz/fdCuOOnz4sC6//HJ5PB69+eab2rlzp375y19qwIABpltzxC9+8QstX75cTz/9tHbt2qVHHnlEjz76qJ566inTrSXEkSNHdMEFF+jpp58+4fOPPPKIHn/8cT399NOqqalRdna2JkyYEL8uV2/T1fs9evSotmzZokWLFmnLli1avXq1du/erR/+8IcGOk2Mb/r7HlNVVaU//OEPys3NTVJnhtl9zCWXXGLPnj2702MjRoyw77vvPkMdJU9TU5MtyV6/fr3pVhzV2tpqn3322XZ1dbV95ZVX2nfffbfplhxz77332mPHjjXdRtJcd9119i233NLpsSlTptgzZsww1JFzJNmvvvpq/H4sFrOzs7Pthx9+OP5YKBSyA4GAvXz5cgMdJtbX3++JfPDBB7Yku76+PjlNOehk7/fzzz+3/+Zv/sbevn27nZ+fb1dUVCS9t2TrUzMibW1t+vDDDzVx4sROj0+cOFHvvfeeoa6Sp6WlRZKUmZlpuBNn3XHHHbruuuv0/e9/33Qrjnv99ddVUlKiG264QYMHD9ZFF12kf/u3fzPdlmPGjh2rt956S7t375Yk/c///I82btyoa6+91nBnzqutrdX+/fs7fX55vV5deeWVfeLzS+r4DLMsK2Vn/GKxmGbOnKl77rlHI0eONN1O0vToi94l2sGDBxWNRjVkyJBOjw8ZMkT79+831FVy2Lat+fPna+zYsSouLjbdjmNWrVqlLVu2qKamxnQrSbFnzx4tW7ZM8+fP189+9jN98MEHuuuuu+T1enXjjTeabi/h7r33XrW0tGjEiBFyu92KRqNasmSJ/vEf/9F0a4479hl1os+v+vp6Ey0lVSgU0n333afp06enzEXhvu4Xv/iF0tLSdNddd5luJan6VBA5xrKsTvdt2z7usVRz55136qOPPtLGjRtNt+KYvXv36u6779batWvl8/lMt5MUsVhMJSUleuihhyRJF110kXbs2KFly5alZBB5+eWX9eKLL2rlypUaOXKktm3bprlz5yo3N1c33XST6faSoi9+fkUiEU2bNk2xWExLly413Y4jPvzwQz355JPasmVLyv89v65P7ZoZNGiQ3G73cbMfTU1Nx33LSCVz5szR66+/rrfffltDhw413Y5jPvzwQzU1NWnUqFFKS0tTWlqa1q9fr1/96ldKS0tTNBo13WLC5eTkqKioqNNj5557bkouvpake+65R/fdd5+mTZum8847TzNnztS8efNUXl5uujXHZWdnS1Kf+/yKRCKaOnWqamtrVV1dnbKzIRs2bFBTU5OGDRsW//yqr6/XP//zP6ugoMB0e47qU0EkPT1do0aNUnV1dafHq6urddlllxnqyjm2bevOO+/U6tWr9fvf/16FhYWmW3LU1VdfrY8//ljbtm2L30pKSvTjH/9Y27Ztk9vtNt1iwl1++eXHHZK9e/du5efnG+rIWUePHpXL1fljy+12p8zhu10pLCxUdnZ2p8+vtrY2rV+/PiU/v6S/hpA//vGPWrdunbKysky35JiZM2fqo48+6vT5lZubq3vuuUe/+93vTLfnqD63a2b+/PmaOXOmSkpKNGbMGK1YsUINDQ2aPXu26dYS7o477tDKlSv12muvKSMjI/5NKhAIqF+/foa7S7yMjIzj1r+cccYZysrKStl1MfPmzdNll12mhx56SFOnTtUHH3ygFStWaMWKFaZbc8SkSZO0ZMkSDRs2TCNHjtTWrVv1+OOP65ZbbjHdWkL8+c9/1qeffhq/X1tbq23btikzM1PDhg3T3Llz9dBDD+nss8/W2WefrYceekj9+/fX9OnTDXZ9+rp6v7m5ufrRj36kLVu26I033lA0Go1/hmVmZio9Pd1U26ftm/6+Xw9aHo9H2dnZOuecc5LdanKZPWjHjH/913+18/Pz7fT0dPviiy9O2cNZJZ3w9uyzz5puLWlS/fBd27bt//7v/7aLi4ttr9drjxgxwl6xYoXplhwTDAbtu+++2x42bJjt8/ns4cOH2wsXLrTD4bDp1hLi7bffPuH/szfddJNt2x2H8N5///12dna27fV67SuuuML++OOPzTb9LXT1fmtra0/6Gfb222+bbv20fNPf9+v6yuG7lm3bdpIyDwAAQCd9ao0IAADoWQgiAADAGIIIAAAwhiACAACMIYgAAABjCCIAAMAYgggAADCGIAIAAIwhiAAAAGMIIgAAwBiCCAAAMIYgAgAAjPn/POkDVAkHC/cAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(quasienergy1)), np.sort(quasienergy1), label='full',s = 50)\n", "plt.scatter(range(len(E_full)), np.sort(E_full), label='block',s=10,marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5386, "metadata": {}, "outputs": [{"data": {"text/plain": ["'file = open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') \\nlines = [\"\"] \\nwith open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') as file: \\n    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\\n    for i in range(len(k_min)):\\n        if i%(min_n)==0:\\n            file.write(\"----------------------------------------\"+\"\\n\")\\n        file.write(str(k_min[i])+\"\\t\")\\n        file.write(str(p_min[i])+\"\\t\")\\n        file.write(str(z_min[i])+\"\\t\")\\n        file.write(str(E_min[i])+\"\\t\")\\n        file.write(str(spi_min[i])+\"\\t\")\\n        file.write(\"\\n\")\\nfile.close()'"]}, "execution_count": 5386, "metadata": {}, "output_type": "execute_result"}], "source": ["'''file = open('ED_TPZ_1D_N=16.txt', 'w') \n", "lines = [\"\"] \n", "with open('ED_TPZ_1D_N=16.txt', 'w') as file: \n", "    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\n", "    for i in range(len(k_min)):\n", "        if i%(min_n)==0:\n", "            file.write(\"----------------------------------------\"+\"\\n\")\n", "        file.write(str(k_min[i])+\"\\t\")\n", "        file.write(str(p_min[i])+\"\\t\")\n", "        file.write(str(z_min[i])+\"\\t\")\n", "        file.write(str(E_min[i])+\"\\t\")\n", "        file.write(str(spi_min[i])+\"\\t\")\n", "        file.write(\"\\n\")\n", "file.close()'''"]}, {"cell_type": "code", "execution_count": 5387, "metadata": {}, "outputs": [], "source": ["\n", "full_evecs_norm = U_transform @ v_block_full \n", "direct_evecs_norm = v_full "]}, {"cell_type": "code", "execution_count": 5388, "metadata": {}, "outputs": [], "source": ["ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), \n", "                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))\n", "IPR_block_full = np.sum(ipr1, axis=0)\n", "ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), \n", "                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))\n", "IPR_full = np.sum(ipr2, axis=0)"]}, {"cell_type": "code", "execution_count": 5389, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 0.4998958862783824\n", "1 0.4998959023911921\n", "2 0.4998958862783825\n", "6 0.499895902391192\n"]}], "source": ["for i in range(len(IPR_block_full)):\n", "    if IPR_full[i] > 0.3:\n", "        print(i,IPR_full[i])"]}, {"cell_type": "code", "execution_count": 5390, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.49989589, 0.4998959 , 0.49989589, 0.09373699, 0.09373699,\n", "       0.12497398, 0.4998959 , 0.12842548, 0.12497398, 0.12001805,\n", "       0.10870073, 0.12048403, 0.1563007 , 0.1038746 , 0.20182491,\n", "       0.17512726])"]}, "execution_count": 5390, "metadata": {}, "output_type": "execute_result"}], "source": ["IPR_full"]}, {"cell_type": "code", "execution_count": 5391, "metadata": {}, "outputs": [], "source": ["# 代码位置：[floquet_ED_TPZ copy_forTFIM copy 2.ipynb](./floquet_ED_TPZ%20copy_forTFIM%20copy%202.ipynb#L100-L120)\n", "# 修正前：\n", "# U_transform = full_vecs @ full_vecs_eig\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5392, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1129100461.py:41: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def plot_matrix(matrix, title=\"矩阵热图\", cmap=\"viridis\", annot=True, figsize=(8, 6)):\n", "    \"\"\"\n", "    可视化矩阵，用颜色深浅表示元素值差异\n", "    \n", "    参数:\n", "        matrix: 待可视化的NumPy矩阵\n", "        title: 图表标题\n", "        cmap: 颜色映射（如\"viridis\", \"coolwarm\", \"RdBu\"）\n", "        annot: 是否在单元格中显示数值\n", "        figsize: 图表尺寸\n", "    \"\"\"\n", "    # 设置绘图风格\n", "    plt.style.use(\"default\")\n", "    \n", "    # 创建画布\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # 绘制热图\n", "    sns.heatmap(\n", "        matrix,\n", "        annot=annot,          # 显示数值\n", "        fmt=\".2f\",            # 数值格式（保留2位小数）\n", "        cmap=cmap,            # 颜色映射\n", "        cbar=True,            # 显示颜色条\n", "        square=True,          # 单元格为正方形\n", "        ax=ax,                # 子图对象\n", "        linewidths=0.5,       # 单元格边框宽度\n", "        linecolor=\"gray\"      # 单元格边框颜色\n", "    )\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=14, pad=10)\n", "    ax.set_xlabel(\"列索引\", fontsize=12, labelpad=10)\n", "    ax.set_ylabel(\"行索引\", fontsize=12, labelpad=10)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig\n", "\n", "fig = plot_matrix(\n", "        #np.abs(U_transform @ v_block_full ),\n", "        #np.abs((U_transform @ v_block_full ) - v_full),\n", "        #np.abs(v_full),\n", "        #np.abs(U_transform @ U_transform.T.conj()),\n", "        #np.abs(full_vecs_eig),\n", "        np.abs(U_transform),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(V @ np.linalg.inv(V)),\n", "        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),\n", "        #np.abs(H_F),\n", "        #np.abs(V ** 4),\n", "        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(H_F),\n", "        #np.abs(np.linalg.inv(v_block_full) @ np.linalg.inv(U_transform) @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  - np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ H_F @ U_transform @ v_block_full  ),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  ),\n", "        #np.abs(np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(U_transform)),\n", "        #np.abs(Hk_full),\n", "        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),\n", "        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data - E )),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data) - np.diag(E)),\n", "        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),\n", "        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),\n", "        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),\n", "        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),\n", "        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),\n", "        title=\"示例矩阵热图（颜色表示元素值大小）\",\n", "        cmap=\"coolwarm\"  # 冷暖色映射（正值暖色，负值冷色）\n", "    )\n", "    \n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5393, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 25490 (\\N{CJK UNIFIED IDEOGRAPH-6392}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 21518 (\\N{CJK UNIFIED IDEOGRAPH-540E}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 30340 (\\N{CJK UNIFIED IDEOGRAPH-7684}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 26412 (\\N{CJK UNIFIED IDEOGRAPH-672C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 24449 (\\N{CJK UNIFIED IDEOGRAPH-5F81}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_82610/1605157511.py:23: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25490 (\\N{CJK UNIFIED IDEOGRAPH-6392}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21518 (\\N{CJK UNIFIED IDEOGRAPH-540E}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30340 (\\N{CJK UNIFIED IDEOGRAPH-7684}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 26412 (\\N{CJK UNIFIED IDEOGRAPH-672C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24449 (\\N{CJK UNIFIED IDEOGRAPH-5F81}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ipr_full_sorted = np.sort(IPR_full)\n", "ipr_block_sorted = np.sort(IPR_block_full)\n", "# 步骤6：可视化对比\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# 子图1：排序后的IPR对比（散点图）\n", "ax1.scatter(range(len(ipr_full_sorted)), ipr_full_sorted, label=\"full space\", alpha=0.8, s=50)\n", "ax1.scatter(range(len(ipr_block_sorted)), ipr_block_sorted, label=\"block space\", alpha=0.8, s=30, marker=\"x\")\n", "ax1.set_xlabel(\"排序后的本征态索引\")\n", "ax1.set_ylabel(\"IPR值\")\n", "#ax1.set_title(f\"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）\")\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "ax2.scatter(range(len(ipr_full_sorted)), np.sort(quasienergy), label=\"full space\", alpha=0.8, s=50)\n", "ax2.scatter(range(len(ipr_block_sorted)), np.sort(E_full), label=\"block space\", alpha=0.8, s=30, marker=\"x\")\n", "ax2.set_xlabel(\"排序后的本征态索引\")\n", "ax2.set_ylabel(\"E值\")\n", "#ax1.set_title(f\"IPR一致性对比（N={N}，最大误差={ipr_max_diff:.2e}）\")\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5394, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.+0.j, 1.+0.j, 1.+0.j, 0.+0.j, 1.+0.j, 0.+0.j, 0.+0.j, 1.+0.j,\n", "       1.+0.j, 0.+0.j, 0.+0.j, 1.+0.j, 0.+0.j, 1.+0.j, 1.+0.j, 0.+0.j])"]}, "execution_count": 5394, "metadata": {}, "output_type": "execute_result"}], "source": ["U_transform @ U_transform.T.conj()[:,1]"]}, {"cell_type": "code", "execution_count": 5395, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.    +0.j, 1.    +0.j, 1.    +0.j, 0.1875+0.j, 1.    +0.j,\n", "       0.25  +0.j, 0.1875+0.j, 1.    +0.j, 1.    +0.j, 0.1875+0.j,\n", "       0.25  +0.j, 1.    +0.j, 0.1875+0.j, 1.    +0.j, 1.    +0.j,\n", "       1.    +0.j])"]}, "execution_count": 5395, "metadata": {}, "output_type": "execute_result"}], "source": ["np.diag(U_transform @ U_transform.T.conj())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "nikola": {"category": "Strongly Correlated Systems", "date": "2020-12-20 13:18:31 UTC+08:00", "description": "", "link": "", "slug": "exact-diagonalization", "tags": "Correlated Electronic Systems, Numerical Method, Exact Diagonalization", "title": "Exact Diagonalization", "type": "text"}, "toc-autonumbering": true}, "nbformat": 4, "nbformat_minor": 4}