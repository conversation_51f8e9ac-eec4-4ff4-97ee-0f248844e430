import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import expm
from scipy.sparse.linalg import eigsh
from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定
from math import * 
import pandas as pd
import cmath
import os #导入os库
import random
import time
import seaborn as sns
import cmath
import scipy 
import functools
import matplotlib
import matplotlib.pyplot as plt

N = 4
#T=t1+t2
t_1 = 1
t_2 = 1
lam_h = 0.01
lam_J = 0.1
h_x=np.pi/2 -lam_h#hx*t1#标准选的是 1.5
J_z=np.pi/4 - lam_J#J*t2# 0.75

def translate(N, state, steps):
    """平移操作（示例：循环左移steps位）"""
    # 实际实现需与checkstate中的平移逻辑一致
    bits = [(state >> i) & 1 for i in range(N)]
    steps = steps % N
    shifted_bits = bits[steps:] + bits[:steps]
    return sum(bit << i for i, bit in enumerate(shifted_bits))

def spin_flip_state(N, rep):
    """自旋翻转（逐位取反）"""
    return rep ^ ((1 << N) - 1)

def reverseBits(state: int, num_spins: int) -> int:
    """
    自旋态的空间反演操作（优化版）：将N位自旋的位置完全翻转
    例：N=4时，state=0b1010（第1、3位自旋为1）→ 反演后=0b0101（第0、2位自旋为1）
    
    参数:
        state: 整数编码的自旋态（需确保二进制位数 ≤ num_spins）
        num_spins: 系统自旋总数（即反演的目标位数N）
    返回:
        reversed_state: 反演后的自旋态（整数编码）
    """
    # 1. 输入验证：避免无效输入（如state位数超过num_spins）
    if state < 0:
        raise ValueError("自旋态state必须为非负整数")
    if num_spins < 1:
        raise ValueError("自旋总数num_spins必须≥1")
    if state >= (1 << num_spins):
        raise ValueError(f"state={bin(state)}的位数超过num_spins={num_spins}，请检查输入")
    
    reversed_state = 0
    remaining_state = state
    
    # 2. 逐位提取并反转位置（仅遍历num_spins位，避免多余计算）
    for i in range(num_spins):
        # 提取当前最低位（第i位，从0开始计数）
        current_bit = remaining_state & 1
        # 将当前位放到反演后的位置：原第i位 → 反演后第(num_spins-1 -i)位
        reversed_state |= current_bit << (num_spins - 1 - i)
        # 移除已处理的最低位
        remaining_state >>= 1
    
    return reversed_state

# Functions to manipulate states
def get_site_value(state, site):
    return (state >> site) & 1
def flip_state(state: int, index: int) -> int:
    mask = 1 << index
    return state ^ mask
def set_site_value(state, site, value):
    site_val = (value << site)
    return (state ^ site_val) | site_val
'''def translate(L, state, n_translation_sites):
    new_state = 0
    for site in range(L):
        site_value = get_site_value(state, site)
        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)
    return new_state
def spin_flip_state(N: int, rep: int) -> int:
    """
    对N维系统中的量子态rep执行自旋翻转操作
    
    参数:
        N: 系统维度（自旋数量）
        rep: 待翻转的量子态（整数表示，二进制编码）
        
    返回:
        自旋翻转后的量子态（整数表示）
        
    异常:
        ValueError: 当rep超出N维系统的可能状态范围时抛出
    """
    # 验证输入态的有效性
    max_state = (1 << N) - 1  # N个自旋的最大可能状态
    if rep < 0 or rep > max_state:
        raise ValueError(f"态{rep}超出N={N}维系统的范围[0, {max_state}]")
    
    # 自旋翻转逻辑：翻转每个比特位（0<->1）
    # 构造N位全1的掩码，用于异或操作实现翻转
    mask = (1 << N) - 1
    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转
    
    return flipped_rep
def reverseBits(s,N):
    bin_chars = ""
    temp = s
    for i in range(N):
        bin_char = bin(temp % 2)[-1]
        temp = temp // 2
        bin_chars = bin_char + bin_chars
    bits =  bin_chars.upper()
    return int(bits[::-1], 2)'''

def ffun(t,s,l,k,N):
    kk = 2*np.pi*k/N
    if t == -1:
        if s == -1:
            f = np.cos(kk*l)
        elif s==1:
            f = -np.sin(kk*l)
    elif t ==1:
        if s == -1:
            f = np.sin(kk*l)
        elif s==1:
            f = np.cos(kk*l)
    return f
def ggun(t,l,k,N):
    kk = 2*np.pi*k/N
    g=0
    if t == -1:
        g = 1-np.cos(kk*l)
    elif t==1:
        g = 1+np.cos(kk*l) 
    return g

def block_direct_sum(blocks):
    """
    沿对角线构造块直和矩阵（块对角矩阵）
    
    参数:
        blocks: 子矩阵列表，每个元素为numpy数组（方阵）
    返回:
        块直和矩阵（对角线上是输入的子矩阵，其余为0）
    """
    # 检查输入是否为空
    if not blocks:
        return np.array([], dtype=float)
    
    # 检查所有子矩阵是否为方阵
    for i, b in enumerate(blocks):
        if b.ndim != 2 or b.shape[0] != b.shape[1]:
            raise ValueError(f"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）")
    
    # 初始化结果矩阵（从空矩阵开始）
    result = np.array([], dtype=blocks[0].dtype)
    
    for block in blocks:
        m = block.shape[0]  # 当前块的维度
        if result.size == 0:
            # 第一次拼接：直接用当前块作为初始矩阵
            result = block.copy()
        else:
            # 非第一次拼接：构造新的块对角矩阵
            n = result.shape[0]  # 现有矩阵的维度
            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）
            new_result = np.zeros((n + m, n + m), dtype=result.dtype)
            # 填充左上角为现有矩阵
            new_result[:n, :n] = result
            # 填充右下角为当前块
            new_result[n:, n:] = block
            result = new_result
    
    return result

def checkstate(s, k, N):
    R = -1
    tz = -1
    tp = -1
    tpz = -1
    smax = 2**N - 1
    
    # 检查平移对称性和自旋翻转对称性
    t = s
    for i in range(1, N + 1):
        t = translate(N, t, 1)  # 平移一位
        az = smax - t  # 自旋翻转
        
        # 如果发现更小的状态，说明s不是最小代表态
        if t < s or az < s:
            R = -1
            break
            
        # 找到平移周期
        if t == s:
            # 检查动量k是否与平移对称性兼容
            if k % (N // i) != 0:
                R = -1
                break
            R = i
            break
            
        # 找到自旋翻转对称性
        if az == s:
            tz = i
            
    # 如果已经确定没有平移对称性，直接返回
    if R <= 0:
        return R, tp, tz, tpz
        
    # 检查反演对称性
    t = reverseBits(s, N)  # 反演操作
    az = smax - t  # 自旋翻转
    
    # 检查反演后的状态是否小于s（即s不是最小代表态）
    if t < s or az < s:
        R = -1
        return R, tp, tz, tpz
        
    # 检查反演对称性
    for i in range(R):
        if t == s:
            tp = i
        if az == s:
            tpz = i
            
        # 平移一位继续检查
        t = translate(N, t, 1)
        az = smax - t
        
    return R, tp, tz, tpz

import numpy as np

reprcount = []  # 全局记录列表（按需保留）

def findbasis(N, k, p, z):
    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数
    repr_list = []
    typee = []# 分类标签
    peri = []# 平移对称性
    mtrf = []# 反演对称性
    ntrf = []# 自旋翻转对称性
    capr = []# 联合对称性
    
    for s in range(2 **N):
        # 2. 显式列出sigma值（替代range，更清晰）
        for sigma in (-1, 1):
            # 3. 每次迭代初始化m、n，避免跨状态污染
            m, n = None, None
            ca = None  # 显式初始化分类标签
            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数
            
            # 基础过滤：仅处理有效平移对称性的状态
            if R <= -1:
                continue
            
            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）
            if (k == 0 or k == N // 2) and (sigma == -1):
                R = -1  # 标记为无效状态
            
            # 5. 仅处理R仍有效的状态
            if R > 0:
                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类
                # 分支1：tp、tz、tpz均为-1（无反演相关对称）
                if tp == -1 and tz == -1 and tpz == -1:
                    ca = 1
                    m, n = None, None  # 明确赋值，避免未定义
                
                # 分支2：tp≠-1、tz=-1（反演-平移对称）
                elif tp != -1 and tz == -1:
                    ca = 2
                    m = tp
                    # 7. 浮点数比较改用np.isclose，增强稳健性
                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                
                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）
                elif tp == -1 and tz != -1:
                    ca = 3
                    n = tz
                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):
                        R = -1
                
                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）
                elif tp == -1 and tz == -1 and tpz != -1:
                    ca = 4
                    m = tpz
                    n = None  # 明确n未定义
                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):
                        R = -1
                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):
                        R = -1
                
                # 分支5：tp≠-1、tz≠-1（多重对称叠加）
                elif tp != -1 and tz != -1:
                    ca = 5
                    m, n = tp, tz
                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):
                        R = -1
                
                # 8. 捕获未覆盖的状态组合（调试用）
                else:
                    print(f"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})")
                    continue
                
                # 9. 最终检查：ca必须已定义且R仍有效
                if ca is not None and R > 0:
                    repr_list.append(s)
                    typee.append(2 * ca + (sigma + 1) / 2)
                    capr.append(ca)
                    peri.append(R)
                    mtrf.append(m)
                    ntrf.append(n)
    
    nrep = len(repr_list)
    reprcount.append(repr_list)
    # 10. 简化打印，避免输出过载
    #print(f"k={k}, p={p}, z={z}: 有效状态数={nrep}")
    return nrep, repr_list, typee, peri, mtrf, ntrf, capr


def represent(L,a0):
    at = a0
    a = a0
    l=0
    q=0
    g=0
    smax = 2**L-1
    for t in range(1,L+1):
        at = translate(L,a0,t)
        if at < a:
            a = at
            l=t
            g=0
        az=smax-at
        if az<a:
            a=az
            l=t
            g=1    
    at = reverseBits(a0,L)
    for t in range(L):
        if at<a:
            a=at
            l=t
            q=1
            g=0
        az=smax-at
        if az<a:
            a=az
            l=t
            q=1
            g=1
        at = translate(L, at, 1)
    return a,l,q,g


def Ham_total(N,J,h):
    H = np.zeros((2 ** N, 2 ** N))

    # a is a basis
    for a in range(2 ** N):
        # i is position of this basis
        for i in range(N):
            # j is the nearest neighbor, mod N for periodic boundary conditions
            j = (i + 1) % N
            ai = get_site_value(a,i)
            aj = get_site_value(a,j)

            # Sz
            b = a
            if ai == aj:
                H[a, b] += J
            else:
                H[a, b] += -J

            # SxSx + SySy
            #if ai != aj:
            b = flip_state(a, i)
            #b = flip_state(b, j)
            H[a, b] += h
    return H

import numpy as np
from collections import deque

def generate_orbit_states_T(N: int, rep: int):
    """生成代表元 rep 的完整平移轨道"""
    """修正后的轨道生成"""
    states = [rep]
    t = translate(N, rep, 1)
    count = 0
    max_iter = N  # 防止无限循环
    
    while t != rep and count < max_iter:
        states.append(t)
        t = translate(N, t, 1)
        count += 1
    
    if count >= max_iter:
        print(f"警告：轨道生成可能有问题，rep={rep}")
    
    return states

def generate_symmetry_orbit(N, rep, ca, m, n):
    """
    生成包含所有对称操作的状态轨道（修正：保证轨道闭合性，避免重复入队）

    参数:
        N: 系统大小
        rep: 代表元
        ca: 对称性分类
        m: 反演参数
        n: 自旋翻转参数
    返回:
        orbit: 包含所有对称操作生成的状态列表（去重后）
    """
    orbit = []
    visited = set()
    queue = deque([rep])

    while queue:
        state = queue.popleft()
        if state in visited:
            continue
        visited.add(state)
        orbit.append(state)

        # 平移操作（所有分类都包含）
        translated = translate_k(N, state, 1)
        if translated not in visited:
            queue.append(translated)

        # 反演操作（ca=2时生效）
        if m == 1 and n is not None:
            inverted = reverseBits(state, N)
            orbit_T = generate_orbit_states_T(N, inverted)
            for inverted_stats in orbit_T:
                if inverted_stats not in visited:
                    queue.append(inverted_stats)

        # 自旋翻转操作（ca=3,4,5时生效）
        if m is not None and n == 1:
            flipped = flip_all_spins_k(N, state)
            orbit_T = generate_orbit_states_T(N, flipped)
            for flipped_stats in orbit_T:
                if flipped_stats not in visited:
                    queue.append(flipped_stats)

        # 反演+自旋翻转操作（ca=4时生效）
        if m == 1 and n is None:
            flipped = flip_all_spins_k(N, state)
            inverted_flipped_state = reverseBits(flipped,N)
            orbit_T = generate_orbit_states_T(N, inverted_flipped_state)
            for invertedflipped_stats in orbit_T:
                if invertedflipped_stats not in visited:
                    queue.append(invertedflipped_stats)

    return orbit

def translate_k(N, state, n_translation_sites):
    """
    平移操作（逐位平移，保证周期边界）
    """
    new_state = 0
    for site in range(N):
        bit = (state >> site) & 1
        new_site = (site + n_translation_sites) % N
        new_state |= bit << new_site
    return new_state


def flip_all_spins_k(N, state):
    """
    自旋翻转操作：将所有自旋翻转（按位异或全1数）
    """
    return state ^ ((1 << N) - 1)

import numpy as np

def build_projection_matrix(N, k, p, z, repr_list, typee, peri, mtrf, ntrf, capr):
    """
    构建投影矩阵，将原始希尔伯特空间投影到对称性适应的子空间。
    
    参数:
        N: 系统大小
        k: 动量量子数
        p: 反演对称性量子数
        z: 自旋翻转对称性量子数
        repr_list: 代表性构型列表
        typee, peri, mtrf, ntrf, capr: 对称性信息（来自findbasis）
        
    返回:
        proj_matrix: 投影矩阵，形状为 (nrep, 2^N)
    """
    nrep = len(repr_list)
    dim_hilbert = 2 ** N
    proj_matrix = np.zeros((dim_hilbert,nrep), dtype=complex)
    
    for idx in range(nrep):
        s = repr_list[idx]
        ca = capr[idx]
        R = peri[idx]
        m = mtrf[idx]
        n = ntrf[idx]
        s_sigma = 2*(typee[idx]%2)-1#sigma
        # 从typee中提取sigma（typee = 2*ca + (sigma+1)/2）
        #sigma = 2 * (typee[idx] - 2 * ca) - 1
        #print("态，分类")
        #print(s,ca)
        matelement = R/N/N/2
        if k != 0 or k != N // 2:
            matelement = matelement * 2#g_k项


        if ca==2 or ca==5:
            matelement = matelement/ggun(s_sigma*p,m,k,N)
        if ca==3 or ca==5:
            matelement = matelement/ggun(z,n,k,N)
        if ca==4:
            matelement = matelement/ggun(s_sigma*p*z,m,k,N)
        matel  =  np.sqrt(matelement) 
        #print(R,m,n)
        # 根据分类ca处理不同的对称性
        if ca == 1:
            # 只有平移对称性
            #norm = np.sqrt(R)
            for j in range(R):
                s_translated = translate(N, s, j)
                #phase = phase_factor(-k, j, N)  # 注意负号
                if s_translated != s:    
                    proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase / norm
                
        elif ca == 2 or( m>0 and n is None) or (p > 0 and z < 0):
            # 反演对称性
            #norm = np.sqrt(2 * R)
            for j in range(R):
                # 平移操作
                s_translated = translate(N, s, j)
                #phase1 = phase_factor(-k, j, N)
                if s_translated != s or j==0: 
                    proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase1 / norm
                
                # 反演操作（结合平移）
                s_inverted = reverseBits(s, N)
                s_inv_translated = translate(N,s_inverted, j)
                #phase2 = sigma * p * phase_factor(-k, 2*m - j, N)
                if s_inv_translated != s_inverted or j==0:
                    proj_matrix[s_inv_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * p #phase2 / norm
                
        elif ca == 3 or( n>0 and m is None) or (p < 0 and z > 0):
            # 自旋翻转对称性
            #norm = np.sqrt(2 * R)
            for j in range(R):
                s_translated = translate(N, s, j)
                #phase1 = phase_factor(-k, j, N)
                if s_translated != s or j==0: 
                    proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase1 / norm
                
                s_flipped = flip_all_spins_k( N,s)
                s_flip_translated = translate(N,s_flipped, j)
                #phase3 = z * phase_factor(-k, j, N)
                if s_flip_translated != s_flipped or j==0:
                    proj_matrix[s_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z#phase3 / norm
                
        elif ca == 4 or (m>0 and n>0) or (p > 0 and z > 0):
            # 联合对称性（反演+自旋翻转）
            norm = np.sqrt(2 * R)
            for j in range(R):
                s_translated = translate(N, s, j)
                #phase1 = phase_factor(-k, j, N)
                if s_translated != s or j==0: 
                    proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase1 / norm
                
                s_inv_flip = reverseBits(flip_all_spins_k( N,s), N)
                s_inv_flip_translated = translate(N,s_inv_flip, j)
                #phase4 = sigma * p * z * phase_factor(-k, 2*m - j, N)
                if s_inv_flip_translated != s_inv_flip or j==0: 
                    proj_matrix[ s_inv_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z * p#phase4 / norm
                
        elif ca == 5 or (m>0 and n is None) or (p > 0 and z > 0):
            # 多重对称性（反演和自旋翻转）
            norm = np.sqrt(4 * R)
            for j in range(R):
                s_translated = translate(N, s, j)
                #phase1 = phase_factor(-k, j, N)
                if s_translated != s or j==0: 
                    proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase1 / norm
                
                s_inverted = reverseBits(s, N)
                s_inv_translated = translate(N,s_inverted, j)
                #phase2 = sigma * p * phase_factor(-k, 2*m - j, N)
                if s_inv_translated != s_inverted or j==0:
                    proj_matrix[s_inv_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * p #phase2 / norm
                
                s_flipped = flip_all_spins_k( N,s)
                s_flip_translated = translate(N,s_flipped, j)
                #phase3 = z * phase_factor(-k, j, N)
                if s_flip_translated != s_flipped or j==0:
                    proj_matrix[s_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z#phase3 / norm
                
                s_inv_flip = reverseBits(flip_all_spins_k( N,s), N)
                s_inv_flip_translated = translate(N,s_inv_flip, j)
                #phase4 = sigma * p * z * phase_factor(-k, 2*m - j, N)
                if s_inv_flip_translated != s_inv_flip or j==0: 
                    proj_matrix[ s_inv_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z * p#phase4 / norm
            
    return proj_matrix

def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):
    ca = capr[a]
    cb = capr[b]
    s = 2*(typee[a]%2)-1
    t = 2*(typee[b]%2)-1
    matelement = peri[a]/peri[b]
    if ca==2 or ca==5:
        matelement = matelement/ggun(s*p,mtrf[a],k,N)
    if ca==3 or ca==5:
        matelement = matelement/ggun(z,ntrf[a],k,N)
    if ca==4:
        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)
    if cb==2 or cb==5:
        matelement = matelement*ggun(t*p,mtrf[b],k,N)
    if cb==3 or cb==5:
        matelement = matelement*ggun(z,ntrf[b],k,N)
    if cb==4:
        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)
    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)
    if cb==1 or cb==3:
        matelement = matelement*ffun(t,s,l,k,N)
    elif cb==2 or cb==5:
        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)
    elif cb==4:
        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)
    return matelement

def Ham_total_TPZ(J,h,N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):
    Hk = np.zeros((nrep,) * 2).astype(complex)
    for ia in range(nrep):
        sa = repr[ia]
        if (ia > 1 and sa == repr[ia - 1]):
            continue
        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1
        Ez = 0
        for i in range(N):
            j = (i + 1) % N
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            if ai == aj:
                Ez += J
            else:
                Ez -= J
        for a in range(ia, ia + na):
            Hk[a, a] += Ez
        for i in range(N):
            #j = (i + 1) % N
            #ai = get_site_value(sa, i)
            #aj = get_site_value(sa, j)
            #if ai != aj:
            #横场项
            #sb = flip_state(sa, i)
            #if ai == 1:
            sb = flip_state(sa, i)
            #else:
                #sb = flip_state(flip_state(sa, j), i)
            representative, l, q, g = represent(N, sb)
            if representative in repr:
                ib = repr.index(representative)
                if ib > 1 and repr[ib] == repr[ib - 1]:
                    ib = ib - 1
                    nb = 2
                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:
                    nb = 2
                else:
                    nb = 1
                for ii in range(ia, ia + na):
                    for jj in range(ib, ib + nb):
                        try:
                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)
                            if np.isfinite(elem):
                                Hk[ii, jj] += elem
                        except Exception as e:
                            print(f"helement error at ii={ii}, jj={jj}: {e}")
    return Hk

def sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):
    Hk = np.zeros((nrep,) * 2).astype(complex)
    for ia in range(nrep):
        sa = repr[ia]
        if (ia>1 and sa==repr[ia-1]):
            continue
        elif (ia<nrep-1 and sa==repr[ia+1]):
            na=2
        else:
            na=1
        for a in range(ia,ia+na):
            Hk[a,a] += (1/2 )*N    
        for i in range(N):
            for j in range(i+1,N):
                ai = get_site_value(sa, i)
                aj = get_site_value(sa, j)
                if ai != aj:
                    if  ai == 1:   
                        sb = flip_state(flip_state(sa,i),j)
                    else:
                        sb = flip_state(flip_state(sa,j),i)
                    representative, l,q,g = represent(N,sb)
                    if representative in repr:
                        ib = repr.index(representative)
                        if ib >1 and repr[ib]==repr[ib-1]:
                            ib = ib-1
                            nb=2
                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:
                            nb=2
                        else:
                            nb=1
                        for ii in range(ia,ia+na):
                            for jj in range(ib,ib+nb):
                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)
    return Hk

def transform(nrep,mat,vec):
    Hk = []
    a = np.transpose(vec) @ (mat @ vec)
    for i in range(nrep):
        Hk.append(a[i,i])
    return Hk    

def fullspectrum(J,h,N):
    E=[]
    #k=0
    k_min = []
    p_min = []
    z_min = []
    E_min = []
    spi_min = []

    v_full = []
    Hk_full = []
    new_basis_matrix = []
    for k in range(N):
        if k==0 or k==N//2:
            p1=-1
            p2=1
        else:
            p1=1
            p2=1
        for p in range(p1,p2+1,2):
            for z in [-1,1]:
                nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)#k,p,z是标记是否有对称性的参数
                
                print(k,p,z,repr,capr)
                if nrep != 0:
                    Hk_1 = Ham_total_TPZ(0*J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)
                    Hk_2 = Ham_total_TPZ(J,0*h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)
                    H_f = expm(-1j*t_1*Hk_1) @ expm(-1j*t_2*Hk_2)
                    # 拼接为完整矩阵
                    if len(Hk_full) == 0:
                        Hk_full = H_f
                    else:
                        Hk_full = block_direct_sum([Hk_full,H_f])#np.block(block_structure)

                    eigenvalue, featurevector =np.linalg.eig(H_f)
                    Hk_spin = sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)
                    spn = transform(nrep,Hk_spin,featurevector)
                    spin = []
                    for spin_i in range(len(spn)):
                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))
                    E1 = eigenvalue.tolist()
                    #print(E1)
                    #print(k,p,z,repr)
                    E.extend(eigenvalue.tolist())
                    
                    if len(v_full) == 0:
                        v_full = featurevector
                    else:
                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)
                    # 构造投影矩阵 V_k
                    #V_k = build_projection_matrix(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)
                    V_k = build_projection_matrix(N, k, p, z, repr, typee, peri, mtrf, ntrf, capr)
                    #print(V_k)
                    #V_k = build_projection_matrix_for_k_test(N, repr, peri, mtrf, ntrf, capr, p, z, k, sigma=1)
                    #矩阵按列直接拼接
                    if len(new_basis_matrix) == 0:
                        new_basis_matrix = V_k
                    else:
                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）
                    #print(new_basis_matrix.shape)
                    if len(E1) != 0:
                        for i in range(len(E1)):
                            idx = E1.index(np.min(E1))
                            k_min.append(k)
                            p_min.append(p)
                            z_min.append(z)
                            E_min.append(E1[idx])
                            spi_min.append(spin[idx])
                            #print(len(E1))
                            #print(np.min(E1),E1.index(np.min(E1)))
                            E1.pop(idx)
                            spin.pop(idx)   
    return E,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_full

H1 = Ham_total(N,0*J_z,h_x)
H2 = Ham_total(N,J_z,0*h_x)
H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)

eigvals_all, v_full = np.linalg.eig(H_F)
#eigvals_all= np.linalg.eigvalsh(H_tot)

E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform ,Hk_block_full= fullspectrum(J_z,h_x,N)
    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))
    #print(np.array(eigvals))



# 绘制匹配后的IPR对比
plt.scatter(range(len(eigvals_all)), np.sort(eigvals_all), label='full',s = 50)
plt.scatter(range(len(E_full)), np.sort(E_full), label='block',s=10,marker="x")
plt.legend()
plt.show()

