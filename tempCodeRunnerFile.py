
# ========== 仅基于平移与磁化(M,k)直接构建块矩阵 ==========
def build_translation_basis_by_k(col: int, row: int):
    """
    构建二维平移对称性下的基，按动量量子数(k_row, k_col)分块
    参考1D代码的正确做法：每个动量块包含所有兼容该动量的代表元
    """
    basis = {}
    seen = set()
    N = col * row

    for s in range(2 ** N):
        rep, _, _, _ = represent(N, s, row, col)
        if rep in seen:
            continue
        seen.add(rep)

        # 计算轨道周期（行列方向分别计算）
        R_row, R_col = orbit_period_2d(rep, row, col)

        # 遍历所有可能的动量量子数组合
        for k_row in range(row):
            for k_col in range(col):
                # 检查动量兼容性（两个方向都要满足）
                if is_k_compatible(row, R_row, k_row) and is_k_compatible(col, R_col, k_col):
                    k = (k_row, k_col)  # 二维动量作为键
                    if k not in basis:
                        basis[k] = {
                            'repr': [],
                            'peri_row': [],
                            'peri_col': []
                        }
                    basis[k]['repr'].append(rep)
                    basis[k]['peri_row'].append(R_row)
                    basis[k]['peri_col'].append(R_col)

    return basis
def helement_translation_2d_simple(Ra_row: int, Ra_col: int, Rb_row: int, Rb_col: int,
                                  l_row: int, l_col: int, k_row: int, k_col: int,
                                  row: int, col: int) -> complex:
    """
    二维平移对称性下的矩阵元相位因子（参考1D版本）
    注意：k_row, k_col是块的固定动量，不是每个态的动量
    """
    return (np.sqrt(Ra_row / Rb_row) * np.sqrt(Ra_col / Rb_col) *
            np.exp(1j * 2 * np.pi * k_row * l_row / row) *
            np.exp(1j * 2 * np.pi * k_col * l_col / col))

def build_block_Hamiltonian_translation_2d_corrected(row: int, col: int,
                                                    reps: list,
                                                    peri_row: list, peri_col: list,
                                                    k_row: int, k_col: int,  # 块的固定动量
                                                    J: float, h: float) -> np.ndarray:
    """
    构建二维平移对称性下的块对角哈密顿量（修正版本，参考1D代码）
    关键修正：k_row, k_col是块的固定动量，不是每个态的动量
    """
    nrep = len(reps)
    Hk = np.zeros((nrep, nrep), dtype=complex)
    N = row * col

    for ia in range(nrep):
        sa = reps[ia]
        Ra_row = peri_row[ia]
        Ra_col = peri_col[ia]

        # 对角项：计算二维格点上的相邻自旋相互作用
        Ez = 0.0
        for i in range(N):
            row_i, col_i = divmod(i, col)

            # 行方向相邻（周期边界条件）
            j_row = (row_i + 1) % row
            j = j_row * col + col_i
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)

            # 列方向相邻（周期边界条件）
            j_col = (col_i + 1) % col
            j = row_i * col + j_col
            ai = get_site_value(sa, i)
            aj = get_site_value(sa, j)
            Ez += (J if ai == aj else -J)

        Hk[ia, ia] += Ez

        # 非对角项：横向场引起的自旋翻转
        for i in range(N):
            sb = flip_state(sa, i)
            rep_b, l_row, l_col, _ = represent(N, sb, row, col)
            if rep_b in reps:
                ib = reps.index(rep_b)
                Rb_row = peri_row[ib]
                Rb_col = peri_col[ib]
                elem = h * helement_translation_2d_simple(
                    Ra_row, Ra_col, Rb_row, Rb_col,
                    l_row, l_col, k_row, k_col,  # 使用块的固定动量
                    row, col
                )
                Hk[ia, ib] += elem

    return Hk
def fullspectrum_blocks_direct(col: int, row: int, J: float, h: float):
    """
    计算二维TFIM的完整能谱（修正版本，参考1D代码）
    """
    basis = build_translation_basis_by_k(col, row)
    total_dim = sum(len(v['repr']) for v in basis.values())
    N = col * row
    assert total_dim == 2 ** N, f"块维度求和 {total_dim} != 2^{N}"

    energies = []
    labels = []

    for k, data in sorted(basis.items()):
        k_row, k_col = k  # 解包二维动量
        reps = data['repr']
        peri_row = data['peri_row']
        peri_col = data['peri_col']

        if len(reps) == 0:
            continue

        print(f"处理动量块 k=({k_row},{k_col}), 包含 {len(reps)} 个代表元")

        # 使用修正的哈密顿量构建函数
        Hk = build_block_Hamiltonian_translation_2d_corrected(
            row, col, reps, peri_row, peri_col,
            k_row, k_col,  # 块的固定动量
            J, h
        )

        w, _ = np.linalg.eig(Hk)
        print(f"块 k=({k_row},{k_col}) 的本征值数量: {len(w)}")

        energies.extend(w.real.tolist())
        labels.extend([k_row * col + k_col] * len(w))  # 将二维动量编码为标签

    return energies, labels