{"cells": [{"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy\n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [], "source": ["N = 4\n", "#T=t1+t2\n", "t_1 = 1\n", "t_2 = 1\n", "lam_h = 0.01\n", "lam_J = 0.1\n", "h_x=np.pi/2 -lam_h#hx*t1#标准选的是 1.5\n", "J_z=np.pi/4 - lam_J#J*t2# 0.75"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [], "source": ["def translate(N, state, steps):\n", "    \"\"\"平移操作（示例：循环左移steps位）\"\"\"\n", "    # 实际实现需与checkstate中的平移逻辑一致\n", "    bits = [(state >> i) & 1 for i in range(N)]\n", "    steps = steps % N\n", "    shifted_bits = bits[steps:] + bits[:steps]\n", "    return sum(bit << i for i, bit in enumerate(shifted_bits))\n", "\n", "def spin_flip_state(N, rep):\n", "    \"\"\"自旋翻转（逐位取反）\"\"\"\n", "    return rep ^ ((1 << N) - 1)\n", "\n", "def reverseBits(state: int, num_spins: int) -> int:\n", "    \"\"\"\n", "    自旋态的空间反演操作（优化版）：将N位自旋的位置完全翻转\n", "    例：N=4时，state=0b1010（第1、3位自旋为1）→ 反演后=0b0101（第0、2位自旋为1）\n", "    \n", "    参数:\n", "        state: 整数编码的自旋态（需确保二进制位数 ≤ num_spins）\n", "        num_spins: 系统自旋总数（即反演的目标位数N）\n", "    返回:\n", "        reversed_state: 反演后的自旋态（整数编码）\n", "    \"\"\"\n", "    # 1. 输入验证：避免无效输入（如state位数超过num_spins）\n", "    if state < 0:\n", "        raise ValueError(\"自旋态state必须为非负整数\")\n", "    if num_spins < 1:\n", "        raise ValueError(\"自旋总数num_spins必须≥1\")\n", "    if state >= (1 << num_spins):\n", "        raise ValueError(f\"state={bin(state)}的位数超过num_spins={num_spins}，请检查输入\")\n", "    \n", "    reversed_state = 0\n", "    remaining_state = state\n", "    \n", "    # 2. 逐位提取并反转位置（仅遍历num_spins位，避免多余计算）\n", "    for i in range(num_spins):\n", "        # 提取当前最低位（第i位，从0开始计数）\n", "        current_bit = remaining_state & 1\n", "        # 将当前位放到反演后的位置：原第i位 → 反演后第(num_spins-1 -i)位\n", "        reversed_state |= current_bit << (num_spins - 1 - i)\n", "        # 移除已处理的最低位\n", "        remaining_state >>= 1\n", "    \n", "    return reversed_state"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/plain": ["'def translate(L, state, n_translation_sites):\\n    new_state = 0\\n    for site in range(L):\\n        site_value = get_site_value(state, site)\\n        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\\n    return new_state\\ndef spin_flip_state(N: int, rep: int) -> int:\\n    \"\"\"\\n    对N维系统中的量子态rep执行自旋翻转操作\\n    \\n    参数:\\n        N: 系统维度（自旋数量）\\n        rep: 待翻转的量子态（整数表示，二进制编码）\\n        \\n    返回:\\n        自旋翻转后的量子态（整数表示）\\n        \\n    异常:\\n        ValueError: 当rep超出N维系统的可能状态范围时抛出\\n    \"\"\"\\n    # 验证输入态的有效性\\n    max_state = (1 << N) - 1  # N个自旋的最大可能状态\\n    if rep < 0 or rep > max_state:\\n        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\\n    \\n    # 自旋翻转逻辑：翻转每个比特位（0<->1）\\n    # 构造N位全1的掩码，用于异或操作实现翻转\\n    mask = (1 << N) - 1\\n    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\\n    \\n    return flipped_rep\\ndef reverseBits(s,N):\\n    bin_chars = \"\"\\n    temp = s\\n    for i in range(N):\\n        bin_char = bin(temp % 2)[-1]\\n        temp = temp // 2\\n        bin_chars = bin_char + bin_chars\\n    bits =  bin_chars.upper()\\n    return int(bits[::-1], 2)'"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "'''def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def spin_flip_state(N: int, rep: int) -> int:\n", "    \"\"\"\n", "    对N维系统中的量子态rep执行自旋翻转操作\n", "    \n", "    参数:\n", "        N: 系统维度（自旋数量）\n", "        rep: 待翻转的量子态（整数表示，二进制编码）\n", "        \n", "    返回:\n", "        自旋翻转后的量子态（整数表示）\n", "        \n", "    异常:\n", "        ValueError: 当rep超出N维系统的可能状态范围时抛出\n", "    \"\"\"\n", "    # 验证输入态的有效性\n", "    max_state = (1 << N) - 1  # N个自旋的最大可能状态\n", "    if rep < 0 or rep > max_state:\n", "        raise ValueError(f\"态{rep}超出N={N}维系统的范围[0, {max_state}]\")\n", "    \n", "    # 自旋翻转逻辑：翻转每个比特位（0<->1）\n", "    # 构造N位全1的掩码，用于异或操作实现翻转\n", "    mask = (1 << N) - 1\n", "    flipped_rep = rep ^ mask  # 异或操作实现逐位翻转\n", "    \n", "    return flipped_rep\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)'''"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [], "source": ["def ffun(t,s,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    if t == -1:\n", "        if s == -1:\n", "            f = np.cos(kk*l)\n", "        elif s==1:\n", "            f = -np.sin(kk*l)\n", "    elif t ==1:\n", "        if s == -1:\n", "            f = np.sin(kk*l)\n", "        elif s==1:\n", "            f = np.cos(kk*l)\n", "    return f\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    g=0\n", "    if t == -1:\n", "        g = 1-np.cos(kk*l)\n", "    elif t==1:\n", "        g = 1+np.cos(kk*l) \n", "    return g"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [], "source": ["def block_direct_sum(blocks):\n", "    \"\"\"\n", "    沿对角线构造块直和矩阵（块对角矩阵）\n", "    \n", "    参数:\n", "        blocks: 子矩阵列表，每个元素为numpy数组（方阵）\n", "    返回:\n", "        块直和矩阵（对角线上是输入的子矩阵，其余为0）\n", "    \"\"\"\n", "    # 检查输入是否为空\n", "    if not blocks:\n", "        return np.array([], dtype=float)\n", "    \n", "    # 检查所有子矩阵是否为方阵\n", "    for i, b in enumerate(blocks):\n", "        if b.ndim != 2 or b.shape[0] != b.shape[1]:\n", "            raise ValueError(f\"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）\")\n", "    \n", "    # 初始化结果矩阵（从空矩阵开始）\n", "    result = np.array([], dtype=blocks[0].dtype)\n", "    \n", "    for block in blocks:\n", "        m = block.shape[0]  # 当前块的维度\n", "        if result.size == 0:\n", "            # 第一次拼接：直接用当前块作为初始矩阵\n", "            result = block.copy()\n", "        else:\n", "            # 非第一次拼接：构造新的块对角矩阵\n", "            n = result.shape[0]  # 现有矩阵的维度\n", "            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）\n", "            new_result = np.zeros((n + m, n + m), dtype=result.dtype)\n", "            # 填充左上角为现有矩阵\n", "            new_result[:n, :n] = result\n", "            # 填充右下角为当前块\n", "            new_result[n:, n:] = block\n", "            result = new_result\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [], "source": ["def checkstate(s,k,N):\n", "    R=-1\n", "    tz=-1\n", "    tp = -1\n", "    tpz = -1\n", "    smax = 2**N-1\n", "    #Sum_m = 0\n", "    #for i in range(N):\n", "    #    Sum_m += get_site_value(s,i)\n", "    #if Sum_m != N//2:\n", "    #    return R,tp,tz,tpz\n", "    t=s\n", "    for i in range(1,N+1):\n", "        t = translate(N,t,1)\n", "        az = smax -t\n", "        #print(t,s,az)\n", "        if t<s or az<s:\n", "            break\n", "        if t==s:\n", "            if k%(N/i)!=0:\n", "                break\n", "            R=i\n", "            break\n", "        if az==s:\n", "            tz=i\n", "    t = reverseBits(s,N)\n", "    az = smax-t\n", "    for i in range(R):\n", "        if t<s or az<s:\n", "            R=-1\n", "            break\n", "        if t==s:\n", "            tp=i\n", "        if az==s:\n", "            tpz=i\n", "        t = translate(N,t,1)\n", "        az = smax-t\n", "    return R,tp,tz,tpz\n", "#R 是直接平移得到的自身移动系数，对应 1\n", "#tz 是自旋翻转得到的自身然后再平移得到的系数，对应 2\n", "#tp 是反演得到的自身然后再平移得到的系数，对应 3\n", "#tpz 是联合对称得到的自身然后再平移得到的系数，对应 4"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p, z):\n", "    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数\n", "    repr_list = []\n", "    typee = []# 分类标签\n", "    peri = []# 平移对称性\n", "    mtrf = []# 反演对称性\n", "    ntrf = []# 自旋翻转对称性\n", "    capr = []# 联合对称性\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 显式列出sigma值（替代range，更清晰）\n", "        for sigma in (-1, 1):\n", "            # 3. 每次迭代初始化m、n，避免跨状态污染\n", "            m, n = None, None\n", "            ca = None  # 显式初始化分类标签\n", "            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\n", "            \n", "            # 基础过滤：仅处理有效平移对称性的状态\n", "            if R <= -1:\n", "                continue\n", "            \n", "            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\n", "            if (k == 0 or k == N // 2) and (sigma == -1):\n", "                R = -1  # 标记为无效状态\n", "            \n", "            # 5. 仅处理R仍有效的状态\n", "            if R > 0:\n", "                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\n", "                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\n", "                if tp == -1 and tz == -1 and tpz == -1:\n", "                    ca = 1\n", "                    m, n = None, None  # 明确赋值，避免未定义\n", "                \n", "                # 分支2：tp≠-1、tz=-1（反演-平移对称）\n", "                elif tp != -1 and tz == -1:\n", "                    ca = 2\n", "                    m = tp\n", "                    # 7. 浮点数比较改用np.isclose，增强稳健性\n", "                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\n", "                elif tp == -1 and tz != -1:\n", "                    ca = 3\n", "                    n = tz\n", "                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\n", "                elif tp == -1 and tz == -1 and tpz != -1:\n", "                    ca = 4\n", "                    m = tpz\n", "                    n = None  # 明确n未定义\n", "                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\n", "                elif tp != -1 and tz != -1:\n", "                    ca = 5\n", "                    m, n = tp, tz\n", "                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 8. 捕获未覆盖的状态组合（调试用）\n", "                else:\n", "                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\n", "                    continue\n", "                \n", "                # 9. 最终检查：ca必须已定义且R仍有效\n", "                if ca is not None and R > 0:\n", "                    repr_list.append(s)\n", "                    typee.append(2 * ca + (sigma + 1) / 2)\n", "                    capr.append(ca)\n", "                    peri.append(R)\n", "                    mtrf.append(m)\n", "                    ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 10. 简化打印，避免输出过载\n", "    #print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [], "source": ["def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            g=1    \n", "    at = reverseBits(a0,L)\n", "    for t in range(L):\n", "        if at<a:\n", "            a=at\n", "            l=t\n", "            q=1\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            q=1\n", "            g=1\n", "        at = translate(L, at, 1)\n", "    return a,l,q,g"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from collections import deque\n", "\n", "def generate_orbit_states_T(N: int, rep: int):\n", "    \"\"\"生成代表元 rep 的完整平移轨道\"\"\"\n", "    \"\"\"修正后的轨道生成\"\"\"\n", "    states = [rep]\n", "    t = translate(N, rep, 1)\n", "    count = 0\n", "    max_iter = N  # 防止无限循环\n", "    \n", "    while t != rep and count < max_iter:\n", "        states.append(t)\n", "        t = translate(N, t, 1)\n", "        count += 1\n", "    \n", "    if count >= max_iter:\n", "        print(f\"警告：轨道生成可能有问题，rep={rep}\")\n", "    \n", "    return states\n", "\n", "def generate_symmetry_orbit(N, rep, ca, m, n):\n", "    \"\"\"\n", "    生成包含所有对称操作的状态轨道（修正：保证轨道闭合性，避免重复入队）\n", "\n", "    参数:\n", "        N: 系统大小\n", "        rep: 代表元\n", "        ca: 对称性分类\n", "        m: 反演参数\n", "        n: 自旋翻转参数\n", "    返回:\n", "        orbit: 包含所有对称操作生成的状态列表（去重后）\n", "    \"\"\"\n", "    orbit = []\n", "    visited = set()\n", "    queue = deque([rep])\n", "\n", "    while queue:\n", "        state = queue.popleft()\n", "        if state in visited:\n", "            continue\n", "        visited.add(state)\n", "        orbit.append(state)\n", "\n", "        # 平移操作（所有分类都包含）\n", "        translated = translate_k(N, state, 1)\n", "        if translated not in visited:\n", "            queue.append(translated)\n", "\n", "        # 反演操作（ca=2时生效）\n", "        if m == 1 and n is not None:\n", "            inverted = reverseBits(state, N)\n", "            orbit_T = generate_orbit_states_T(N, inverted)\n", "            for inverted_stats in orbit_T:\n", "                if inverted_stats not in visited:\n", "                    queue.append(inverted_stats)\n", "\n", "        # 自旋翻转操作（ca=3,4,5时生效）\n", "        if m is not None and n == 1:\n", "            flipped = flip_all_spins_k(N, state)\n", "            orbit_T = generate_orbit_states_T(N, flipped)\n", "            for flipped_stats in orbit_T:\n", "                if flipped_stats not in visited:\n", "                    queue.append(flipped_stats)\n", "\n", "        # 反演+自旋翻转操作（ca=4时生效）\n", "        if m == 1 and n is None:\n", "            flipped = flip_all_spins_k(N, state)\n", "            inverted_flipped_state = reverseBits(flipped,N)\n", "            orbit_T = generate_orbit_states_T(N, inverted_flipped_state)\n", "            for invertedflipped_stats in orbit_T:\n", "                if invertedflipped_stats not in visited:\n", "                    queue.append(invertedflipped_stats)\n", "\n", "    return orbit\n", "\n", "def translate_k(N, state, n_translation_sites):\n", "    \"\"\"\n", "    平移操作（逐位平移，保证周期边界）\n", "    \"\"\"\n", "    new_state = 0\n", "    for site in range(N):\n", "        bit = (state >> site) & 1\n", "        new_site = (site + n_translation_sites) % N\n", "        new_state |= bit << new_site\n", "    return new_state\n", "\n", "\n", "def flip_all_spins_k(N, state):\n", "    \"\"\"\n", "    自旋翻转操作：将所有自旋翻转（按位异或全1数）\n", "    \"\"\"\n", "    return state ^ ((1 << N) - 1)"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [], "source": ["def generate_orbit_for_rep(N, k,rep, ca):\n", "    orbit = []\n", "    for s in range(2**N):\n", "        t,tp,tz,tpz = checkstate(s,k,N)\n", "        representative, l, q, g = represent(N, s)\n", "        print(rep,representative,l, q, g)\n", "        if representative == rep:   \n", "            '''if t > 0 :\n", "                orbit.append(s)\n", "            elif t<= 0 and  ca== 2 and tp > 0:\n", "                orbit.append(s)\n", "            elif t<= 0 and  ca== 3 and tz > 0:\n", "                orbit.append(s)\n", "            elif t<= 0 and  ca== 4 and tpz > 0 :\n", "                orbit.append(s)\n", "            elif t<= 0 and  ca== 5 and tp > 0 and tz > 0:\n", "                orbit.append(s)'''\n", "            orbit.append(s)\n", "    return orbit\n", "        "]}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ndef generate_orbit_for_rep(N, rep, ca, R, m, n):\\n    \"\"\"\\n    根据对称性分类生成代表元的轨道\\n    \\n    参数:\\n        N: 系统大小\\n        rep: 代表元状态\\n        ca: 对称性分类 (1-5)\\n        R: 平移周期\\n        m: 反演参数 (None 或 1)\\n        n: 自旋翻转参数 (None 或 1)\\n    \\n    返回:\\n        orbit: 轨道中的所有唯一状态\\n    \"\"\"\\n    orbit = []\\n    visited = set()\\n    queue = deque([(rep, 0, 0, 0, [])])  # (状态, 平移次数, 反演次数, 翻转次数, 操作链)\\n    \\n    while queue:\\n        state, t, p, f, op_chain = queue.popleft()\\n        p_parity = p % 2\\n        f_parity = f % 2\\n        t_mod = t % R\\n        visit_key = (state, p_parity, f_parity, t_mod)\\n        \\n        if visit_key in visited:\\n            continue\\n        visited.add(visit_key)\\n        orbit.append((state, t, p_parity, f_parity, op_chain.copy()))\\n        \\n        # 平移操作 (所有分类都允许)\\n        translated = translate_k(N, state, 1)\\n        queue.append((translated, t + 1, p, f, op_chain + [\\'T\\']))\\n        \\n        # 根据对称性分类添加不同的操作\\n        if ca == 1:\\n            # 只有平移对称性\\n            pass  # 不需要额外操作\\n        \\n        elif ca == 2:\\n            # 平移 + 空间反射对称性\\n            if m is not None:\\n                inverted = reverseBits(state, N)\\n                queue.append((inverted, t, p + 1, f, op_chain + [\\'P\\']))\\n        \\n        elif ca == 3:\\n            # 平移 + 自旋翻转对称性\\n            if n is not None:\\n                flipped = flip_all_spins_k(N, state)\\n                queue.append((flipped, t, p, f + 1, op_chain + [\\'Z\\']))\\n        \\n        elif ca == 4:\\n            # 平移 + 空间反射 + 自旋翻转对称性 (非交换)\\n            if m is not None:\\n                inverted = reverseBits(state, N)\\n                queue.append((inverted, t, p + 1, f, op_chain + [\\'P\\']))\\n            \\n            if n is not None:\\n                flipped = flip_all_spins_k(N, state)\\n                queue.append((flipped, t, p, f + 1, op_chain + [\\'Z\\']))\\n            \\n            # 显式添加联合操作 PZ\\n            if m is not None and n is not None:\\n                # 注意: 对于ca=4，P和Z操作不交换，需要显式处理\\n                pz_state = reverseBits(flip_all_spins_k(N, state), N)\\n                queue.append((pz_state, t, p + 1, f + 1, op_chain + [\\'PZ\\']))\\n        \\n        elif ca == 5:\\n            # 平移 + 空间反射 + 自旋翻转对称性 (交换)\\n            if m is not None:\\n                inverted = reverseBits(state, N)\\n                queue.append((inverted, t, p + 1, f, op_chain + [\\'P\\']))\\n            \\n            if n is not None:\\n                flipped = flip_all_spins_k(N, state)\\n                queue.append((flipped, t, p, f + 1, op_chain + [\\'Z\\']))\\n            \\n            # 对于交换情况，不需要显式添加 PZ，因为 P 和 Z 操作可交换\\n    \\n    # 提取所有唯一状态\\n    unique_states = set([s for s, _, _, _, _ in orbit])\\n    \\n    return unique_states\\n    '"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "def generate_orbit_for_rep(N, rep, ca, R, m, n):\n", "    \"\"\"\n", "    根据对称性分类生成代表元的轨道\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        rep: 代表元状态\n", "        ca: 对称性分类 (1-5)\n", "        R: 平移周期\n", "        m: 反演参数 (None 或 1)\n", "        n: 自旋翻转参数 (None 或 1)\n", "    \n", "    返回:\n", "        orbit: 轨道中的所有唯一状态\n", "    \"\"\"\n", "    orbit = []\n", "    visited = set()\n", "    queue = deque([(rep, 0, 0, 0, [])])  # (状态, 平移次数, 反演次数, 翻转次数, 操作链)\n", "    \n", "    while queue:\n", "        state, t, p, f, op_chain = queue.popleft()\n", "        p_parity = p % 2\n", "        f_parity = f % 2\n", "        t_mod = t % R\n", "        visit_key = (state, p_parity, f_parity, t_mod)\n", "        \n", "        if visit_key in visited:\n", "            continue\n", "        visited.add(visit_key)\n", "        orbit.append((state, t, p_parity, f_parity, op_chain.copy()))\n", "        \n", "        # 平移操作 (所有分类都允许)\n", "        translated = translate_k(N, state, 1)\n", "        queue.append((translated, t + 1, p, f, op_chain + ['T']))\n", "        \n", "        # 根据对称性分类添加不同的操作\n", "        if ca == 1:\n", "            # 只有平移对称性\n", "            pass  # 不需要额外操作\n", "        \n", "        elif ca == 2:\n", "            # 平移 + 空间反射对称性\n", "            if m is not None:\n", "                inverted = reverseBits(state, N)\n", "                queue.append((inverted, t, p + 1, f, op_chain + ['P']))\n", "        \n", "        elif ca == 3:\n", "            # 平移 + 自旋翻转对称性\n", "            if n is not None:\n", "                flipped = flip_all_spins_k(N, state)\n", "                queue.append((flipped, t, p, f + 1, op_chain + ['Z']))\n", "        \n", "        elif ca == 4:\n", "            # 平移 + 空间反射 + 自旋翻转对称性 (非交换)\n", "            if m is not None:\n", "                inverted = reverseBits(state, N)\n", "                queue.append((inverted, t, p + 1, f, op_chain + ['P']))\n", "            \n", "            if n is not None:\n", "                flipped = flip_all_spins_k(N, state)\n", "                queue.append((flipped, t, p, f + 1, op_chain + ['Z']))\n", "            \n", "            # 显式添加联合操作 PZ\n", "            if m is not None and n is not None:\n", "                # 注意: 对于ca=4，P和Z操作不交换，需要显式处理\n", "                pz_state = reverseBits(flip_all_spins_k(N, state), N)\n", "                queue.append((pz_state, t, p + 1, f + 1, op_chain + ['PZ']))\n", "        \n", "        elif ca == 5:\n", "            # 平移 + 空间反射 + 自旋翻转对称性 (交换)\n", "            if m is not None:\n", "                inverted = reverseBits(state, N)\n", "                queue.append((inverted, t, p + 1, f, op_chain + ['P']))\n", "            \n", "            if n is not None:\n", "                flipped = flip_all_spins_k(N, state)\n", "                queue.append((flipped, t, p, f + 1, op_chain + ['Z']))\n", "            \n", "            # 对于交换情况，不需要显式添加 PZ，因为 P 和 Z 操作可交换\n", "    \n", "    # 提取所有唯一状态\n", "    unique_states = set([s for s, _, _, _, _ in orbit])\n", "    \n", "    return unique_states\n", "    '''"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nfrom collections import deque\\n\\ndef generate_symmetry_orbit(N, rep):\\n    \"\"\"\\n    根据对称性分类ca值（1-5）生成对应轨道，明确区分不同对称类型\\n    \\n    参数:\\n        N: 系统大小\\n        rep: 代表元（自旋态）\\n    返回:\\n        orbit: 轨道列表，元素为(state, t, p, f, op_chain)\\n               t:平移次数, p:反演奇偶性, f:自旋翻转奇偶性, op_chain:操作链\\n        sym_info: 对称信息，含\\'ca\\'分类及对应操作类型说明\\n    \"\"\"\\n    # 步骤1：自动检测基础对称参数\\n    R = 1\\n    current = translate_k(N, rep, 1)\\n    while current != rep and R <= N:\\n        R += 1\\n        current = translate_k(N, current, 1)\\n    \\n    trans_orbit = [translate_k(N, rep, t) for t in range(R)]\\n    m = None\\n    for candidate_m in range(N):\\n        if invert_state_k(N, rep, candidate_m) in trans_orbit:\\n            m = candidate_m\\n            break\\n    \\n    n = None\\n    flipped_rep = flip_all_spins_k(N, rep)\\n    if flipped_rep in trans_orbit:\\n        n = 0\\n    elif m is not None and invert_state_k(N, flipped_rep, m) in trans_orbit:\\n        n = 0\\n    \\n    # 步骤2：判定ca分类并定义允许的操作\\n    ca = None\\n    allowed_ops = []  # 允许的操作：\\'T\\', \\'P\\', \\'Z\\'的组合\\n    op_description = \"\"  # 操作类型说明\\n    \\n    if m is None and n is None:\\n        ca = 1\\n        allowed_ops = [\\'T\\']  # 仅平移\\n        op_description = \"仅平移操作(T)\"\\n    elif m is not None and n is None:\\n        ca = 2\\n        allowed_ops = [\\'T\\', \\'P\\']  # 平移+反演\\n        op_description = \"平移(T)+反演(P)操作\"\\n    elif m is None and n is not None:\\n        ca = 3\\n        allowed_ops = [\\'T\\', \\'Z\\']  # 平移+自旋翻转\\n        op_description = \"平移(T)+自旋翻转(Z)操作\"\\n    elif m is not None and n is not None:\\n        pz_rep = invert_state_k(N, flip_all_spins_k(N, rep), m)\\n        zp_rep = flip_all_spins_k(N, invert_state_k(N, rep, m))\\n        if pz_rep == zp_rep:\\n            ca = 4\\n            allowed_ops = [\\'T\\', \\'P\\', \\'Z\\', \\'PZ\\']  # 含联合操作\\n            op_description = \"平移(T)+反演-自旋翻转联合操作(PZ)\"\\n        else:\\n            ca = 5\\n            allowed_ops = [\\'T\\', \\'P\\', \\'Z\\']  # 独立操作\\n            op_description = \"平移(T)+反演(P)+自旋翻转(Z)独立操作\"\\n    \\n    # 步骤3：根据ca分类生成对应轨道\\n    orbit = []\\n    visited = set()\\n    # 队列元素：(state, t, p, f, 操作链)\\n    queue = deque([(rep, 0, 0, 0, [])])\\n    \\n    while queue:\\n        state, t, p, f, op_chain = queue.popleft()\\n        p_parity = p % 2\\n        f_parity = f % 2\\n        t_mod = t % R\\n        visit_key = (state, p_parity, f_parity, t_mod)\\n        \\n        if visit_key in visited:\\n            continue\\n        visited.add(visit_key)\\n        orbit.append((state, t, p_parity, f_parity, op_chain.copy()))\\n        \\n        # 根据ca分类执行允许的操作\\n        # 1. 平移操作（所有分类都允许）\\n        if \\'T\\' in allowed_ops:\\n            translated = translate_k(N, state, 1)\\n            new_chain = op_chain + [\\'T\\']\\n            queue.append((translated, t + 1, p, f, new_chain))\\n        \\n        # 2. 反演操作（ca=2,4,5允许）\\n        if \\'P\\' in allowed_ops:\\n            inverted = invert_state_k(N, state, m)\\n            new_chain = op_chain + [\\'P\\']\\n            queue.append((inverted, t, p + 1, f, new_chain))\\n        \\n        # 3. 自旋翻转操作（ca=3,4,5允许）\\n        if \\'Z\\' in allowed_ops:\\n            flipped = flip_all_spins_k(N, state)\\n            new_chain = op_chain + [\\'Z\\']\\n            queue.append((flipped, t, p, f + 1, new_chain))\\n        \\n        # 4. 联合操作PZ（仅ca=4允许，优化生成效率）\\n        if \\'PZ\\' in allowed_ops:\\n            pz_state = invert_state_k(N, flip_all_spins_k(N, state), m)\\n            new_chain = op_chain + [\\'PZ\\']\\n            queue.append((pz_state, t, p + 1, f + 1, new_chain))\\n    \\n    # 整理对称信息\\n    sym_info = {\\n        \\'ca\\': ca,\\n        \\'R\\': R,\\n        \\'m\\': m,\\n        \\'n\\': n,\\n        \\'allowed_operations\\': allowed_ops,\\n        \\'description\\': f\"ca={ca}：{op_description}\"\\n    }\\n    \\n    return orbit, sym_info\\n\\n\\n# 辅助函数：算符实现\\ndef translate_k(N, state, n):\\n    new_state = 0\\n    for site in range(N):\\n        bit = (state >> site) & 1\\n        new_site = (site + n) % N\\n        new_state |= bit << new_site\\n    return new_state\\n\\ndef invert_state_k(N, state, m):\\n    result = 0\\n    for i in range(N):\\n        j = (2 * m - i) % N\\n        result |= ((state >> i) & 1) << j\\n    return result\\n\\ndef flip_all_spins_k(N, state):\\n    return state ^ ((1 << N) - 1)\\n\\n\\n# 示例验证\\nif __name__ == \"__main__\":\\n    # 测试ca=2（平移+反演）\\n    N = 4\\n    rep = 0b0011  # 具有平移和反演对称性\\n    orbit, info = generate_symmetry_orbit(N, rep)\\n    \\n    print(f\"对称信息：{info[\\'description\\']}\")\\n    print(f\"允许的操作：{info[\\'allowed_operations\\']}\")\\n    print(\"轨道前5个元素（状态, t, p, f, 操作链）：\")\\n    for item in orbit[:5]:\\n        state, t, p, f, chain = item\\n        print(f\"状态={bin(state)}, t={t}, p={p}, f={f}, 操作链={chain}\")\\n        '"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "from collections import deque\n", "\n", "def generate_symmetry_orbit(N, rep):\n", "    \"\"\"\n", "    根据对称性分类ca值（1-5）生成对应轨道，明确区分不同对称类型\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        rep: 代表元（自旋态）\n", "    返回:\n", "        orbit: 轨道列表，元素为(state, t, p, f, op_chain)\n", "               t:平移次数, p:反演奇偶性, f:自旋翻转奇偶性, op_chain:操作链\n", "        sym_info: 对称信息，含'ca'分类及对应操作类型说明\n", "    \"\"\"\n", "    # 步骤1：自动检测基础对称参数\n", "    R = 1\n", "    current = translate_k(N, rep, 1)\n", "    while current != rep and R <= N:\n", "        R += 1\n", "        current = translate_k(N, current, 1)\n", "    \n", "    trans_orbit = [translate_k(N, rep, t) for t in range(R)]\n", "    m = None\n", "    for candidate_m in range(N):\n", "        if invert_state_k(N, rep, candidate_m) in trans_orbit:\n", "            m = candidate_m\n", "            break\n", "    \n", "    n = None\n", "    flipped_rep = flip_all_spins_k(N, rep)\n", "    if flipped_rep in trans_orbit:\n", "        n = 0\n", "    elif m is not None and invert_state_k(N, flipped_rep, m) in trans_orbit:\n", "        n = 0\n", "    \n", "    # 步骤2：判定ca分类并定义允许的操作\n", "    ca = None\n", "    allowed_ops = []  # 允许的操作：'T', 'P', 'Z'的组合\n", "    op_description = \"\"  # 操作类型说明\n", "    \n", "    if m is None and n is None:\n", "        ca = 1\n", "        allowed_ops = ['T']  # 仅平移\n", "        op_description = \"仅平移操作(T)\"\n", "    elif m is not None and n is None:\n", "        ca = 2\n", "        allowed_ops = ['T', 'P']  # 平移+反演\n", "        op_description = \"平移(T)+反演(P)操作\"\n", "    elif m is None and n is not None:\n", "        ca = 3\n", "        allowed_ops = ['T', 'Z']  # 平移+自旋翻转\n", "        op_description = \"平移(T)+自旋翻转(Z)操作\"\n", "    elif m is not None and n is not None:\n", "        pz_rep = invert_state_k(N, flip_all_spins_k(N, rep), m)\n", "        zp_rep = flip_all_spins_k(N, invert_state_k(N, rep, m))\n", "        if pz_rep == zp_rep:\n", "            ca = 4\n", "            allowed_ops = ['T', 'P', 'Z', 'PZ']  # 含联合操作\n", "            op_description = \"平移(T)+反演-自旋翻转联合操作(PZ)\"\n", "        else:\n", "            ca = 5\n", "            allowed_ops = ['T', 'P', 'Z']  # 独立操作\n", "            op_description = \"平移(T)+反演(P)+自旋翻转(Z)独立操作\"\n", "    \n", "    # 步骤3：根据ca分类生成对应轨道\n", "    orbit = []\n", "    visited = set()\n", "    # 队列元素：(state, t, p, f, 操作链)\n", "    queue = deque([(rep, 0, 0, 0, [])])\n", "    \n", "    while queue:\n", "        state, t, p, f, op_chain = queue.popleft()\n", "        p_parity = p % 2\n", "        f_parity = f % 2\n", "        t_mod = t % R\n", "        visit_key = (state, p_parity, f_parity, t_mod)\n", "        \n", "        if visit_key in visited:\n", "            continue\n", "        visited.add(visit_key)\n", "        orbit.append((state, t, p_parity, f_parity, op_chain.copy()))\n", "        \n", "        # 根据ca分类执行允许的操作\n", "        # 1. 平移操作（所有分类都允许）\n", "        if 'T' in allowed_ops:\n", "            translated = translate_k(N, state, 1)\n", "            new_chain = op_chain + ['T']\n", "            queue.append((translated, t + 1, p, f, new_chain))\n", "        \n", "        # 2. 反演操作（ca=2,4,5允许）\n", "        if 'P' in allowed_ops:\n", "            inverted = invert_state_k(N, state, m)\n", "            new_chain = op_chain + ['P']\n", "            queue.append((inverted, t, p + 1, f, new_chain))\n", "        \n", "        # 3. 自旋翻转操作（ca=3,4,5允许）\n", "        if 'Z' in allowed_ops:\n", "            flipped = flip_all_spins_k(N, state)\n", "            new_chain = op_chain + ['Z']\n", "            queue.append((flipped, t, p, f + 1, new_chain))\n", "        \n", "        # 4. 联合操作PZ（仅ca=4允许，优化生成效率）\n", "        if 'PZ' in allowed_ops:\n", "            pz_state = invert_state_k(N, flip_all_spins_k(N, state), m)\n", "            new_chain = op_chain + ['PZ']\n", "            queue.append((pz_state, t, p + 1, f + 1, new_chain))\n", "    \n", "    # 整理对称信息\n", "    sym_info = {\n", "        'ca': ca,\n", "        'R': R,\n", "        'm': m,\n", "        'n': n,\n", "        'allowed_operations': allowed_ops,\n", "        'description': f\"ca={ca}：{op_description}\"\n", "    }\n", "    \n", "    return orbit, sym_info\n", "\n", "\n", "# 辅助函数：算符实现\n", "def translate_k(N, state, n):\n", "    new_state = 0\n", "    for site in range(N):\n", "        bit = (state >> site) & 1\n", "        new_site = (site + n) % N\n", "        new_state |= bit << new_site\n", "    return new_state\n", "\n", "def invert_state_k(N, state, m):\n", "    result = 0\n", "    for i in range(N):\n", "        j = (2 * m - i) % N\n", "        result |= ((state >> i) & 1) << j\n", "    return result\n", "\n", "def flip_all_spins_k(N, state):\n", "    return state ^ ((1 << N) - 1)\n", "\n", "\n", "# 示例验证\n", "if __name__ == \"__main__\":\n", "    # 测试ca=2（平移+反演）\n", "    N = 4\n", "    rep = 0b0011  # 具有平移和反演对称性\n", "    orbit, info = generate_symmetry_orbit(N, rep)\n", "    \n", "    print(f\"对称信息：{info['description']}\")\n", "    print(f\"允许的操作：{info['allowed_operations']}\")\n", "    print(\"轨道前5个元素（状态, t, p, f, 操作链）：\")\n", "    for item in orbit[:5]:\n", "        state, t, p, f, chain = item\n", "        print(f\"状态={bin(state)}, t={t}, p={p}, f={f}, 操作链={chain}\")\n", "        '''\n"]}, {"cell_type": "code", "execution_count": 126, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def build_projection_matrix(N,nrep,reps,typee,peri,mtrf,ntrf,capr,p,z,k):\n", "    \"\"\"\n", "    重构的投影算符构建函数（严格遵循对称性分类与正交性要求）\n", "    考虑平移、反演和自旋翻转三种对称性的共同作用，生成新基下的投影矩阵\n", "\n", "    参数:\n", "        N: 系统大小（自旋数）\n", "        reps: 代表元列表（findbasis输出）\n", "        peri: 平移周期列表（每个代表元的轨道长度R）\n", "        mtrf: 反演-平移参数tp\n", "        ntrf: 反演-自旋翻转参数tz\n", "        capr: 对称性分类（ca=1~5）\n", "        p, z: 对称性约束参数（±1）\n", "        k: 波矢（0 ≤ k < N）\n", "    返回:\n", "        V: 投影矩阵（列向量为对称化基矢，满足正交性）\n", "    \"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    orbit_all = []\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "\n", "    for col in range(nrep):\n", "        # 提取当前代表元的对称性参数\n", "        rep = reps[col]\n", "        R = peri[col]  # 理论轨道长度\n", "        ca = capr[col]\n", "        s_sigma = 2*(typee[col]%2)-1#sigma\n", "        m = mtrf[col] #if mtrf[col] != -1 else None\n", "        n = ntrf[col] #if ntrf[col] != -1 else None\n", "        matelement = peri[col]/N/N/2\n", "        \n", "        \n", "        if k != 0 or k != N // 2:\n", "            matelement = matelement * 2#g_k项\n", "\n", "\n", "        if ca==2 or ca==5:\n", "            matelement = matelement/ggun(s_sigma*p,m,k,N)\n", "        if ca==3 or ca==5:\n", "            matelement = matelement/ggun(z,n,k,N)\n", "        if ca==4:\n", "            matelement = matelement/ggun(s_sigma*p*z,m,k,N)\n", "        matel  =  np.sqrt(matelement)\n", "        \n", "        # 生成包含所有对称操作的状态轨道\n", "        #orbit = generate_symmetry_orbit(N, rep, ca, m, n)\n", "        #orbit = newbasis_to_original(N, reps, capr, peri, mtrf, ntrf)\n", "        orbit = generate_orbit_for_rep(N,k, rep, ca)\n", "        #print(ca,k,m,n,rep,orbit)\n", "        #print(k,p,z,orbit)\n", "        orbit_all.extend(orbit)\n", "        #删除重复元素\n", "        orbit_all = list(dict.fromkeys(orbit_all))\n", "        #print(rep,orbit)\n", "        # 填充投影矩阵：对轨道中每个态计算相位并累加\n", "        for r, s in enumerate(orbit):# index,value\n", "            #(1 + pP)(1 + zZ)T ^r|a〉\n", "            # 平移相位（基础相位）\n", "            #情况 1，只有平移对称性（ca=1）\n", "            matelement = matel * ffun(1,s_sigma,r,k,N)\n", "            V[s, col] += matelement\n", "            #情况 2，pP对称性\n", "            V[reverseBits(s,N),col] += matelement * (p) \n", "            #情况 3，zZ对称性\n", "            #print(flip_all_spins_k(N,s),s)\n", "            V[flip_all_spins_k(N,s),col] += matelement * (z)\n", "            #情况 4，pPzZ对称性\n", "            V[flip_all_spins_k(N,reverseBits(s,N)),col] += matelement * (p*z)\n", "\n", "    #print(k,p,z,(orbit_all))\n", "    return V\n"]}, {"cell_type": "code", "execution_count": 127, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def build_projection_matrix(N, k, p, z, repr_list, typee, peri, mtrf, ntrf, capr):\n", "    \"\"\"\n", "    构建投影矩阵，将原始希尔伯特空间投影到对称性适应的子空间。\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        k: 动量量子数\n", "        p: 反演对称性量子数\n", "        z: 自旋翻转对称性量子数\n", "        repr_list: 代表性构型列表\n", "        typee, peri, mtrf, ntrf, capr: 对称性信息（来自findbasis）\n", "        \n", "    返回:\n", "        proj_matrix: 投影矩阵，形状为 (nrep, 2^N)\n", "    \"\"\"\n", "    nrep = len(repr_list)\n", "    dim_hilbert = 2 ** N\n", "    proj_matrix = np.zeros((dim_hilbert,nrep), dtype=complex)\n", "    \n", "    # 预先计算相位因子函数\n", "    def phase_factor(q, n, N):\n", "        return np.exp(1j * 2 * np.pi * q * n / N)\n", "    \n", "    for idx in range(nrep):\n", "        s = repr_list[idx]\n", "        ca = capr[idx]\n", "        R = peri[idx]\n", "        m = mtrf[idx]\n", "        n = ntrf[idx]\n", "        s_sigma = 2*(typee[idx]%2)-1#sigma\n", "        # 从typee中提取sigma（typee = 2*ca + (sigma+1)/2）\n", "        #sigma = 2 * (typee[idx] - 2 * ca) - 1\n", "        \n", "        matelement = R/N/N/2\n", "        if k != 0 or k != N // 2:\n", "            matelement = matelement * 2#g_k项\n", "\n", "\n", "        if ca==2 or ca==5:\n", "            matelement = matelement/ggun(s_sigma*p,m,k,N)\n", "        if ca==3 or ca==5:\n", "            matelement = matelement/ggun(z,n,k,N)\n", "        if ca==4:\n", "            matelement = matelement/ggun(s_sigma*p*z,m,k,N)\n", "        matel  =  np.sqrt(matelement) \n", "\n", "        # 根据分类ca处理不同的对称性\n", "        if ca == 1:\n", "            # 只有平移对称性\n", "            #norm = np.sqrt(R)\n", "            for j in range(R):\n", "                s_translated = translate(N, s, j)\n", "                #phase = phase_factor(-k, j, N)  # 注意负号\n", "                proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase / norm\n", "                \n", "        elif ca == 2:\n", "            # 反演对称性\n", "            #norm = np.sqrt(2 * R)\n", "            for j in range(R):\n", "                # 平移操作\n", "                s_translated = translate(N, s, j)\n", "                #phase1 = phase_factor(-k, j, N)\n", "                proj_matrix[ s_translated,idx] += matel *ffun(1,s_sigma,j,k,N)#phase1 / norm\n", "                \n", "                # 反演操作（结合平移）\n", "                s_inverted = reverseBits(s, N)\n", "                s_inv_translated = translate(N,s_inverted, j)\n", "                #phase2 = s_sigma * p * phase_factor(-k, 2*m - j, N)  # 注意反演相位\n", "                proj_matrix[ s_inv_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * p#phase2 / norm\n", "                \n", "        elif ca == 3:\n", "            # 自旋翻转对称性\n", "            #norm = np.sqrt(2 * R)\n", "            for j in range(R):\n", "                s_translated = translate(N, s, j)\n", "                #phase1 = phase_factor(-k, j, N)\n", "                proj_matrix[s_translated,idx] += matel * ffun(1,s_sigma,j,k,N)#phase1 / norm\n", "                \n", "                s_flipped = flip_all_spins_k( N,s)\n", "                s_flip_translated = translate(N,s_flipped, j)\n", "                #phase2 = z * phase_factor(-k, j, N)  # 自旋翻转相位\n", "                proj_matrix[s_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z#phase2 / norm\n", "                \n", "        elif ca == 4:\n", "            # 联合对称性（反演+自旋翻转）\n", "            norm = np.sqrt(2 * R)\n", "            for j in range(R):\n", "                s_translated = translate(N, s, j)\n", "                #phase1 = phase_factor(-k, j, N)\n", "                proj_matrix[s_translated,idx] += matel * ffun(1,s_sigma,j,k,N)#phase1 / norm\n", "                \n", "                s_inv_flip = reverseBits(flip_all_spins_k( N,s), N)  # 反演+自旋翻转\n", "                s_inv_flip_translated = translate(N,s_inv_flip, j)\n", "                #phase2 = sigma * p * z * phase_factor(-k, 2*m - j, N)\n", "                proj_matrix[s_inv_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z * p#phase2 / norm\n", "                \n", "        elif ca == 5:\n", "            # 多重对称性（反演和自旋翻转）\n", "            norm = np.sqrt(4 * R)\n", "            for j in range(R):\n", "                s_translated = translate(N, s, j)\n", "                #phase1 = phase_factor(-k, j, N)\n", "                proj_matrix[ s_translated,idx] += matel* ffun(1,s_sigma,j,k,N) #phase1 / norm\n", "                \n", "                s_inverted = reverseBits(s, N)\n", "                s_inv_translated = translate(N,s_inverted, j)\n", "                #phase2 = sigma * p * phase_factor(-k, 2*m - j, N)\n", "                proj_matrix[s_inv_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * p #phase2 / norm\n", "                \n", "                s_flipped = flip_all_spins_k( N,s)\n", "                s_flip_translated = translate(N,s_flipped, j)\n", "                #phase3 = z * phase_factor(-k, j, N)\n", "                proj_matrix[s_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z#phase3 / norm\n", "                \n", "                s_inv_flip = reverseBits(flip_all_spins_k( N,s), N)\n", "                s_inv_flip_translated = translate(N,s_inv_flip, j)\n", "                #phase4 = sigma * p * z * phase_factor(-k, 2*m - j, N)\n", "                proj_matrix[ s_inv_flip_translated,idx] += matel * ffun(1,s_sigma,j,k,N) * z * p#phase4 / norm\n", "                \n", "    return proj_matrix"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ndef build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, p, z, k):\\n    \"\"\"\\n    严格按照公式构建投影算符：\\n    |a^σ(k,p,z)> = 1/√(N_a^σ) Σ_{r=0}^{R-1} c_k^σ(r) (1 + pP)(1 + zZ) T^r |a>\\n    \\n    参数:\\n        N: 系统大小（自旋数）\\n        reps: 代表元列表\\n        peri: 平移周期列表\\n        mtrf: 反演-平移参数tp\\n        ntrf: 反演-自旋翻转参数tz\\n        capr: 对称性分类（ca=1~5）\\n        p, z: 对称性约束参数（±1）\\n        k: 波矢（0 ≤ k < N）\\n    返回:\\n        V: 投影矩阵（列向量为对称化基矢）\\n    \"\"\"\\n    dim_full = 2 ** N\\n    nrep = len(reps)\\n    \\n    # 输入验证\\n    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\\n        raise ValueError(\"参数长度必须与代表元数量一致\")\\n    if not (0 <= k < N):\\n        raise ValueError(f\"波矢k={k}超出范围[0, {N-1}]\")\\n    \\n    # 初始化投影矩阵\\n    V = np.zeros((dim_full, nrep), dtype=complex)\\n    \\n    for col in range(nrep):\\n        # 提取当前代表元的对称性参数\\n        rep = reps[col]\\n        R = peri[col]  # 理论轨道长度\\n        ca = capr[col]\\n        m = mtrf[col] if mtrf[col] != -1 else None\\n        n = ntrf[col] if ntrf[col] != -1 else None\\n        \\n        # 生成平移轨道\\n        orbit, symmetry_info, ca, m, n, R = generate_symmetry_orbit_for_state(N, rep, k, p, z)\\n        \\n        # 对于情况2)、4)和5)，选择使归一化因子为正的sigma\\n        if ca in [2, 4, 5]:\\n            # 计算两种sigma的归一化因子\\n            if ca == 2:\\n                norm1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\\n                norm_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\\n            elif ca == 4:\\n                norm1 = 1 + 1 * p * z * np.cos(2 * np.pi * k * m / N)\\n                norm_minus1 = 1 + (-1) * p * z * np.cos(2 * np.pi * k * m / N)\\n            else:  # ca == 5\\n                term1_1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\\n                term1_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\\n                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                norm1 = term1_1 * term2\\n                norm_minus1 = term1_minus1 * term2\\n            \\n            # 选择使归一化因子为正的sigma\\n            if norm1 > 0:\\n                sigma = 1\\n            elif norm_minus1 > 0:\\n                sigma = -1\\n            else:\\n                raise ValueError(f\"代表元{col}的两种sigma归一化因子都不为正\")\\n        else:\\n            # 对于情况1)和3)，我们处理两种sigma\\n            sigma = None\\n        \\n        # 处理不同的对称性分类\\n        if sigma is None:\\n            # 情况1)和3)：处理两种sigma\\n            for sigma_val in [-1, 1]:\\n                # 计算归一化因子\\n                if ca == 1:\\n                    norm_factor = np.sqrt(R) / N\\n                elif ca == 3:\\n                    norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                    norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n                \\n                # 计算相位因子\\n                for r, s in enumerate(orbit):\\n                    # 平移相位\\n                    trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\\n                    \\n                    # 对称性操作相位\\n                    sym_phase = 1.0\\n                    if ca == 3:\\n                        # 自旋翻转操作相位\\n                        flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\\n                        sym_phase *= flip_phase\\n                    \\n                    # 填充投影矩阵\\n                    V[s, col] += norm_factor * trans_phase * sym_phase\\n        else:\\n            # 计算归一化因子\\n            if ca == 1:\\n                norm_factor = np.sqrt(R) / N\\n            elif ca == 2:\\n                norm_expr = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n            elif ca == 3:\\n                norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n            elif ca == 4:\\n                norm_expr = 1 + sigma * p * z * np.cos(2 * np.pi * k * m / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\\n            elif ca == 5:\\n                term1 = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\\n                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\\n                norm_factor = np.sqrt(R / (2 * N**2 * term1 * term2))\\n            else:\\n                raise ValueError(f\"代表元{col}的对称性分类ca={ca}无效\")\\n            \\n            # 计算相位因子\\n            for r, s in enumerate(orbit):\\n                # 平移相位\\n                trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\\n                \\n                # 对称性操作相位\\n                sym_phase = 1.0\\n                if ca in [2, 4, 5]:\\n                    # 反演操作相位\\n                    inv_phase = calculate_symmetry_phase(N, s, orbit, m, k, R, p, \"inversion\")\\n                    sym_phase *= inv_phase\\n                \\n                if ca in [3, 4, 5]:\\n                    # 自旋翻转操作相位\\n                    flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\\n                    sym_phase *= flip_phase\\n                \\n                # 填充投影矩阵\\n                if k ==0 or k == N//2:#g_k因子\\n                    V[s, col] += norm_factor * trans_phase * sym_phase *np.sqrt(2)\\n                else:\\n                    V[s, col] += norm_factor * trans_phase * sym_phase \\n    \\n    return V\\n\\n\\ndef calculate_symmetry_phase(N, state, orbit, param, k, R, factor, sym_type):\\n    \"\"\"\\n    计算对称操作的相位因子\\n    \\n    参数:\\n        N: 系统大小\\n        state: 当前状态\\n        orbit: 轨道\\n        param: 对称操作参数（m或n）\\n        k: 波矢\\n        R: 轨道长度\\n        factor: 对称性因子（p, z等）\\n        sym_type: 对称操作类型（\\'inversion\\'或\\'flip\\'）\\n    返回:\\n        相位因子\\n    \"\"\"\\n    if sym_type == \"inversion\":\\n        transformed = invert_state_k(N, state, param)\\n    elif sym_type == \"flip\":\\n        transformed = flip_all_spins_k(N, state)\\n    else:\\n        raise ValueError(f\"未知的对称操作类型: {sym_type}\")\\n    \\n    # 找到变换后状态在轨道中的位置\\n    try:\\n        idx = orbit.index(transformed)\\n        # 如果状态在对称操作下不变，添加额外因子\\n        if transformed == state:\\n            return 1.0 + factor\\n        else:\\n            return np.exp(1j * 2 * np.pi * k * idx / R)\\n    except ValueError:\\n        # 如果变换后状态不在轨道中，返回0\\n        return 0.0\\n'"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from collections import deque\n", "'''\n", "def build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, p, z, k):\n", "    \"\"\"\n", "    严格按照公式构建投影算符：\n", "    |a^σ(k,p,z)> = 1/√(N_a^σ) Σ_{r=0}^{R-1} c_k^σ(r) (1 + pP)(1 + zZ) T^r |a>\n", "    \n", "    参数:\n", "        N: 系统大小（自旋数）\n", "        reps: 代表元列表\n", "        peri: 平移周期列表\n", "        mtrf: 反演-平移参数tp\n", "        ntrf: 反演-自旋翻转参数tz\n", "        capr: 对称性分类（ca=1~5）\n", "        p, z: 对称性约束参数（±1）\n", "        k: 波矢（0 ≤ k < N）\n", "    返回:\n", "        V: 投影矩阵（列向量为对称化基矢）\n", "    \"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "    \n", "    # 输入验证\n", "    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\n", "        raise ValueError(\"参数长度必须与代表元数量一致\")\n", "    if not (0 <= k < N):\n", "        raise ValueError(f\"波矢k={k}超出范围[0, {N-1}]\")\n", "    \n", "    # 初始化投影矩阵\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    \n", "    for col in range(nrep):\n", "        # 提取当前代表元的对称性参数\n", "        rep = reps[col]\n", "        R = peri[col]  # 理论轨道长度\n", "        ca = capr[col]\n", "        m = mtrf[col] if mtrf[col] != -1 else None\n", "        n = ntrf[col] if ntrf[col] != -1 else None\n", "        \n", "        # 生成平移轨道\n", "        orbit, symmetry_info, ca, m, n, R = generate_symmetry_orbit_for_state(N, rep, k, p, z)\n", "        \n", "        # 对于情况2)、4)和5)，选择使归一化因子为正的sigma\n", "        if ca in [2, 4, 5]:\n", "            # 计算两种sigma的归一化因子\n", "            if ca == 2:\n", "                norm1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\n", "                norm_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\n", "            elif ca == 4:\n", "                norm1 = 1 + 1 * p * z * np.cos(2 * np.pi * k * m / N)\n", "                norm_minus1 = 1 + (-1) * p * z * np.cos(2 * np.pi * k * m / N)\n", "            else:  # ca == 5\n", "                term1_1 = 1 + 1 * p * np.cos(2 * np.pi * k * m / N)\n", "                term1_minus1 = 1 + (-1) * p * np.cos(2 * np.pi * k * m / N)\n", "                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm1 = term1_1 * term2\n", "                norm_minus1 = term1_minus1 * term2\n", "            \n", "            # 选择使归一化因子为正的sigma\n", "            if norm1 > 0:\n", "                sigma = 1\n", "            elif norm_minus1 > 0:\n", "                sigma = -1\n", "            else:\n", "                raise ValueError(f\"代表元{col}的两种sigma归一化因子都不为正\")\n", "        else:\n", "            # 对于情况1)和3)，我们处理两种sigma\n", "            sigma = None\n", "        \n", "        # 处理不同的对称性分类\n", "        if sigma is None:\n", "            # 情况1)和3)：处理两种sigma\n", "            for sigma_val in [-1, 1]:\n", "                # 计算归一化因子\n", "                if ca == 1:\n", "                    norm_factor = np.sqrt(R) / N\n", "                elif ca == 3:\n", "                    norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                    norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "                \n", "                # 计算相位因子\n", "                for r, s in enumerate(orbit):\n", "                    # 平移相位\n", "                    trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "                    \n", "                    # 对称性操作相位\n", "                    sym_phase = 1.0\n", "                    if ca == 3:\n", "                        # 自旋翻转操作相位\n", "                        flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\n", "                        sym_phase *= flip_phase\n", "                    \n", "                    # 填充投影矩阵\n", "                    V[s, col] += norm_factor * trans_phase * sym_phase\n", "        else:\n", "            # 计算归一化因子\n", "            if ca == 1:\n", "                norm_factor = np.sqrt(R) / N\n", "            elif ca == 2:\n", "                norm_expr = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "            elif ca == 3:\n", "                norm_expr = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "            elif ca == 4:\n", "                norm_expr = 1 + sigma * p * z * np.cos(2 * np.pi * k * m / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * norm_expr))\n", "            elif ca == 5:\n", "                term1 = 1 + sigma * p * np.cos(2 * np.pi * k * m / N)\n", "                term2 = 1 + z * np.cos(2 * np.pi * k * n / N)\n", "                norm_factor = np.sqrt(R / (2 * N**2 * term1 * term2))\n", "            else:\n", "                raise ValueError(f\"代表元{col}的对称性分类ca={ca}无效\")\n", "            \n", "            # 计算相位因子\n", "            for r, s in enumerate(orbit):\n", "                # 平移相位\n", "                trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "                \n", "                # 对称性操作相位\n", "                sym_phase = 1.0\n", "                if ca in [2, 4, 5]:\n", "                    # 反演操作相位\n", "                    inv_phase = calculate_symmetry_phase(N, s, orbit, m, k, R, p, \"inversion\")\n", "                    sym_phase *= inv_phase\n", "                \n", "                if ca in [3, 4, 5]:\n", "                    # 自旋翻转操作相位\n", "                    flip_phase = calculate_symmetry_phase(N, s, orbit, n, k, R, z, \"flip\")\n", "                    sym_phase *= flip_phase\n", "                \n", "                # 填充投影矩阵\n", "                if k ==0 or k == N//2:#g_k因子\n", "                    V[s, col] += norm_factor * trans_phase * sym_phase *np.sqrt(2)\n", "                else:\n", "                    V[s, col] += norm_factor * trans_phase * sym_phase \n", "    \n", "    return V\n", "\n", "\n", "def calculate_symmetry_phase(N, state, orbit, param, k, R, factor, sym_type):\n", "    \"\"\"\n", "    计算对称操作的相位因子\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        state: 当前状态\n", "        orbit: 轨道\n", "        param: 对称操作参数（m或n）\n", "        k: 波矢\n", "        R: 轨道长度\n", "        factor: 对称性因子（p, z等）\n", "        sym_type: 对称操作类型（'inversion'或'flip'）\n", "    返回:\n", "        相位因子\n", "    \"\"\"\n", "    if sym_type == \"inversion\":\n", "        transformed = invert_state_k(N, state, param)\n", "    elif sym_type == \"flip\":\n", "        transformed = flip_all_spins_k(N, state)\n", "    else:\n", "        raise ValueError(f\"未知的对称操作类型: {sym_type}\")\n", "    \n", "    # 找到变换后状态在轨道中的位置\n", "    try:\n", "        idx = orbit.index(transformed)\n", "        # 如果状态在对称操作下不变，添加额外因子\n", "        if transformed == state:\n", "            return 1.0 + factor\n", "        else:\n", "            return np.exp(1j * 2 * np.pi * k * idx / R)\n", "    except ValueError:\n", "        # 如果变换后状态不在轨道中，返回0\n", "        return 0.0\n", "'''\n"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [], "source": ["def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = 2*(typee[a]%2)-1\n", "    t = 2*(typee[b]%2)-1\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)\n", "    return matelement"]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [], "source": ["def Ham_total_TPZ(J,h,N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J\n", "            else:\n", "                Ez -= J\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            #横场项\n", "            #sb = flip_state(sa, i)\n", "            #if ai == 1:\n", "            sb = flip_state(sa, i)\n", "            #else:\n", "                #sb = flip_state(flip_state(sa, j), i)\n", "            representative, l, q, g = represent(N, sb)\n", "            if representative in repr:\n", "                ib = repr.index(representative)\n", "                if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                    ib = ib - 1\n", "                    nb = 2\n", "                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                    nb = 2\n", "                else:\n", "                    nb = 1\n", "                for ii in range(ia, ia + na):\n", "                    for jj in range(ib, ib + nb):\n", "                        try:\n", "                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)\n", "                            if np.isfinite(elem):\n", "                                Hk[ii, jj] += elem\n", "                        except Exception as e:\n", "                            print(f\"helement error at ii={ii}, jj={jj}: {e}\")\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [], "source": ["def sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia>1 and sa==repr[ia-1]):\n", "            continue\n", "        elif (ia<nrep-1 and sa==repr[ia+1]):\n", "            na=2\n", "        else:\n", "            na=1\n", "        for a in range(ia,ia+na):\n", "            Hk[a,a] += (1/2 )*N    \n", "        for i in range(N):\n", "            for j in range(i+1,N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                if ai != aj:\n", "                    if  ai == 1:   \n", "                        sb = flip_state(flip_state(sa,i),j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa,j),i)\n", "                    representative, l,q,g = represent(N,sb)\n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        if ib >1 and repr[ib]==repr[ib-1]:\n", "                            ib = ib-1\n", "                            nb=2\n", "                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\n", "                            nb=2\n", "                        else:\n", "                            nb=1\n", "                        for ii in range(ia,ia+na):\n", "                            for jj in range(ib,ib+nb):\n", "                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [], "source": ["def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    "]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [], "source": ["def fullspectrum(J,h,N):\n", "    E=[]\n", "    #k=0\n", "    k_min = []\n", "    p_min = []\n", "    z_min = []\n", "    E_min = []\n", "    spi_min = []\n", "\n", "    v_full = []\n", "    Hk_full = []\n", "    new_basis_matrix = []\n", "    quasi_energies = []\n", "    for k in range(N):\n", "        if k==0 or k==N//2:\n", "            p1=-1\n", "            p2=1\n", "        else:\n", "            p1=1\n", "            p2=1\n", "        for p in range(p1,p2+1,2):\n", "            for z in [-1,1]:\n", "                nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)#k,p,z是标记是否有对称性的参数\n", "                \n", "                print(k,p,z,repr,capr)\n", "                if nrep != 0:\n", "                    Hk_1 = Ham_total_TPZ(0*J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    Hk_2 = Ham_total_TPZ(J,0*h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    H_f = expm(-1j*t_1*Hk_1) @ expm(-1j*t_2*Hk_2)\n", "                    # 拼接为完整矩阵\n", "                    if len(Hk_full) == 0:\n", "                        Hk_full = H_f\n", "                    else:\n", "                        Hk_full = block_direct_sum([Hk_full,H_f])#np.block(block_structure)\n", "\n", "                    eigenvalue, featurevector =np.linalg.eig(H_f)\n", "                    Hk_spin = sqinsquared_TPZ(J,h,N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    spn = transform(nrep,Hk_spin,featurevector)\n", "                    spin = []\n", "                    for spin_i in range(len(spn)):\n", "                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\n", "                    E1 = eigenvalue.tolist()\n", "                    #print(E1)\n", "                    #print(k,p,z,nrep,repr)\n", "                    <PERSON>.extend(eigenvalue.tolist())\n", "                    \n", "                    if len(v_full) == 0:\n", "                        v_full = featurevector\n", "                    else:\n", "                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)\n", "                    # 构造投影矩阵 V_k\n", "                    #V_k = build_projection_matrix(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    V_k = build_projection_matrix(N, k, p, z, repr, typee, peri, mtrf, ntrf, capr)\n", "                    #V_k = build_projection_matrix_for_k_test(N, repr, peri, mtrf, ntrf, capr, p, z, k, sigma=1)\n", "                    #矩阵按列直接拼接\n", "                    if len(new_basis_matrix) == 0:\n", "                        new_basis_matrix = V_k\n", "                    else:\n", "                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）\n", "                    #print(new_basis_matrix.shape)\n", "                    if len(E1) != 0:\n", "                        for i in range(len(E1)):\n", "                            idx = E1.index(np.min(E1))\n", "                            k_min.append(k)\n", "                            p_min.append(p)\n", "                            z_min.append(z)\n", "                            E_min.append(E1[idx])\n", "                            spi_min.append(spin[idx])\n", "                            #print(len(E1))\n", "                            #print(np.min(E1),E1.index(np.min(E1)))\n", "                            E1.pop(idx)\n", "                            spin.pop(idx)   \n", "\n", "    full_eigenvectors = new_basis_matrix @ v_full\n", "    quasi_energies.extend(-np.angle(E))  # Floquet准能\n", "    # 使用您的方法计算全空间 IPR\n", "    #ipr1 = np.abs(full_eigenvectors) **4\n", "    ipr1 = np.multiply(np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)), \n", "                       np.multiply(np.abs(full_eigenvectors), np.abs(full_eigenvectors)))\n", "    ipr = np.sum(ipr1, axis=0)\n", "    return quasi_energies,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_full"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["H1 = Ham_total(N,0*J_z,h_x)\n", "H2 = Ham_total(N,J_z,0*h_x)\n", "H_F = expm(-1j*t_1*H1) @ expm(-1j*t_2*H2)\n", "\n", "eigvals_all, v_full = np.linalg.eig(H_F)\n", "#eigvals_all= np.linalg.eigvalsh(H_tot)\n", "quasienergy = [0 for index in range(2**N)]\n", "for i in range(0,2**N,1):\n", "    quasienergy[i] = (-cmath.phase(eigvals_all[i]))\n", "quasienergy1 = quasienergy.copy()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["#eigvals_all"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["#min_n =8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,0)"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 -1 -1 [] []\n", "0 -1 1 [] []\n", "0 1 -1 [0, 1] [2, 2]\n", "0 1 1 [0, 1, 3, 5] [2, 2, 5, 5]\n", "1 1 -1 [1, 3] [2, 5]\n", "1 1 1 [1] [2]\n", "2 -1 -1 [1, 5] [2, 5]\n", "2 -1 1 [1] [2]\n", "2 1 -1 [] []\n", "2 1 1 [3] [5]\n", "3 1 -1 [1, 3] [2, 5]\n", "3 1 1 [1] [2]\n"]}], "source": ["E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform ,Hk_block_full= fullspectrum(J_z,h_x,N)\n", "    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))\n", "    #print(np.array(eigvals))\n"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16 16\n"]}], "source": ["print (len(E_full),len(quasienergy))\n", "for x in E_full:\n", "    index = np.abs(quasienergy - x).argmin()\n", "    if np.isclose(quasienergy[index], x) != True:\n", "        print(np.isclose(quasienergy[index], x), quasienergy[index], x)"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(eigvals_all)), np.sort(quasienergy), label='full',s = 50)\n", "plt.scatter(range(len(E_full)), np.sort(E_full), label='block',s=10,marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"data": {"text/plain": ["'file = open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') \\nlines = [\"\"] \\nwith open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') as file: \\n    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\\n    for i in range(len(k_min)):\\n        if i%(min_n)==0:\\n            file.write(\"----------------------------------------\"+\"\\n\")\\n        file.write(str(k_min[i])+\"\\t\")\\n        file.write(str(p_min[i])+\"\\t\")\\n        file.write(str(z_min[i])+\"\\t\")\\n        file.write(str(E_min[i])+\"\\t\")\\n        file.write(str(spi_min[i])+\"\\t\")\\n        file.write(\"\\n\")\\nfile.close()'"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["'''file = open('ED_TPZ_1D_N=16.txt', 'w') \n", "lines = [\"\"] \n", "with open('ED_TPZ_1D_N=16.txt', 'w') as file: \n", "    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\n", "    for i in range(len(k_min)):\n", "        if i%(min_n)==0:\n", "            file.write(\"----------------------------------------\"+\"\\n\")\n", "        file.write(str(k_min[i])+\"\\t\")\n", "        file.write(str(p_min[i])+\"\\t\")\n", "        file.write(str(z_min[i])+\"\\t\")\n", "        file.write(str(E_min[i])+\"\\t\")\n", "        file.write(str(spi_min[i])+\"\\t\")\n", "        file.write(\"\\n\")\n", "file.close()'''"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 12, 12, 14, 5, 8, 8, 12, 3, 8, 14, 3, 3, 8, 12, 3]\n", "归一化后绝对值误差：1.35e+00\n", "最小内积绝对值：5.18e-17\n"]}], "source": ["# 1. 直接对角化全空间Floquet算子 F\n", "#evals_direct, evecs_direct = np.linalg.eig(F)\n", "# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列\n", "matched_indices = []\n", "for eval_block in E_full:  # energies是分块对角化得到的本征值（Hk的本征值）\n", "    # 找到直接对角化中最接近的本征值索引\n", "    idx = np.argmin(np.abs(eigvals_all - eval_block))\n", "    matched_indices.append(idx)\n", "# 3. 按匹配顺序重新排列直接对角化的本征矢\n", "print(matched_indices)\n", "evecs_direct_matched = v_full[:, matched_indices]\n", "# 4. 验证一致性（忽略相位，对比绝对值或内积）\n", "# 方法1：归一化后对比绝对值\n", "full_evecs_norm = U_transform @ v_block_full \n", "direct_evecs_norm = evecs_direct_matched \n", "abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))\n", "print(f\"归一化后绝对值误差：{abs_error:.2e}\")  # 正常应<1e-6\n", "\n", "# 方法2：计算内积绝对值（应为1，说明是同一本征矢）\n", "inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) \n", "                  for i in range(2**N)]\n", "print(f\"最小内积绝对值：{min(inner_products):.2e}\")  # 正常应>0.999"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [], "source": ["ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), \n", "                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))\n", "IPR_block_full = np.sum(ipr1, axis=0)\n", "ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), \n", "                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))\n", "IPR_full = np.sum(ipr2, axis=0)"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 假设分块计算的本征值和IPR为 evals_block, ipr_block\n", "# 整体计算的为 evals_full, ipr_full\n", "matched_ipr = []\n", "for e_block, ipr_b in zip(E_full, IPR_block_full):\n", "    # 找到整体计算中与e_block接近的本征值\n", "    idx = np.argmin(np.abs(eigvals_all - e_block))\n", "    matched_ipr.append((ipr_b, IPR_full[idx]))\n", "# 查看匹配后的IPR是否一致\n", "print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')\n", "plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [], "source": ["# 代码位置：[floquet_ED_TPZ copy_forTFIM copy 2.ipynb](./floquet_ED_TPZ%20copy_forTFIM%20copy%202.ipynb#L100-L120)\n", "# 修正前：\n", "# U_transform = full_vecs @ full_vecs_eig\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_52590/1129100461.py:41: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def plot_matrix(matrix, title=\"矩阵热图\", cmap=\"viridis\", annot=True, figsize=(8, 6)):\n", "    \"\"\"\n", "    可视化矩阵，用颜色深浅表示元素值差异\n", "    \n", "    参数:\n", "        matrix: 待可视化的NumPy矩阵\n", "        title: 图表标题\n", "        cmap: 颜色映射（如\"viridis\", \"coolwarm\", \"RdBu\"）\n", "        annot: 是否在单元格中显示数值\n", "        figsize: 图表尺寸\n", "    \"\"\"\n", "    # 设置绘图风格\n", "    plt.style.use(\"default\")\n", "    \n", "    # 创建画布\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # 绘制热图\n", "    sns.heatmap(\n", "        matrix,\n", "        annot=annot,          # 显示数值\n", "        fmt=\".2f\",            # 数值格式（保留2位小数）\n", "        cmap=cmap,            # 颜色映射\n", "        cbar=True,            # 显示颜色条\n", "        square=True,          # 单元格为正方形\n", "        ax=ax,                # 子图对象\n", "        linewidths=0.5,       # 单元格边框宽度\n", "        linecolor=\"gray\"      # 单元格边框颜色\n", "    )\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=14, pad=10)\n", "    ax.set_xlabel(\"列索引\", fontsize=12, labelpad=10)\n", "    ax.set_ylabel(\"行索引\", fontsize=12, labelpad=10)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig\n", "\n", "fig = plot_matrix(\n", "        #np.abs(U_transform @ v_block_full ),\n", "        #np.abs((U_transform @ v_block_full ) - v_full),\n", "        #np.abs(v_full),\n", "        #np.abs(U_transform @ U_transform.T.conj()),\n", "        #np.abs(full_vecs_eig),\n", "        np.abs(U_transform),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(V @ np.linalg.inv(V)),\n", "        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),\n", "        #np.abs(H_F),\n", "        #np.abs(V ** 4),\n", "        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(H_F),\n", "        #np.abs(np.linalg.inv(v_block_full) @ np.linalg.inv(U_transform) @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  - np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ H_F @ U_transform @ v_block_full  ),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ (v_full @ np.diag(eigvals_all)) @ np.linalg.inv(v_full) @ U_transform @ v_block_full  ),\n", "        #np.abs(np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(U_transform)),\n", "        #np.abs(Hk_full),\n", "        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),\n", "        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data - E )),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data) - np.diag(E)),\n", "        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),\n", "        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),\n", "        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),\n", "        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),\n", "        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),\n", "        title=\"示例矩阵热图（颜色表示元素值大小）\",\n", "        cmap=\"coolwarm\"  # 冷暖色映射（正值暖色，负值冷色）\n", "    )\n", "    \n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.+0.j,  3.+0.j, -1.+0.j,  0.+0.j,  1.+0.j,  0.+0.j,  0.+0.j,\n", "        0.+0.j,  1.+0.j,  0.+0.j,  0.+0.j,  0.+0.j,  0.+0.j,  0.+0.j,\n", "        0.+0.j,  0.+0.j])"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["U_transform @ U_transform.T.conj()[:,1]"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.25+0.j, 3.  +0.j, 3.  +0.j, 2.  +0.j, 3.  +0.j, 1.  +0.j,\n", "       2.  +0.j, 0.  +0.j, 3.  +0.j, 2.  +0.j, 1.  +0.j, 0.  +0.j,\n", "       2.  +0.j, 0.  +0.j, 0.  +0.j, 0.  +0.j])"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["np.diag(U_transform @ U_transform.T.conj())"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试 N=6 的完整块对角化\n", "==================================================\n", "找到 8 个代表元\n", "\n", "处理代表元 1/8: 000000\n", "量子数: k=6, p=1, z=-1\n", "轨道大小: 2\n", "{0, 63}\n", "0\n", "\n", "处理代表元 2/8: 000001\n", "量子数: k=1, p=-1, z=-1\n", "轨道大小: 12\n", "{32, 1, 2, 4, 8, 47, 16, 55, 59, 61, 62, 31}\n", "1\n", "\n", "处理代表元 3/8: 000011\n", "量子数: k=1, p=-1, z=-1\n", "轨道大小: 12\n", "{33, 3, 6, 39, 12, 15, 48, 51, 24, 57, 60, 30}\n", "3\n", "\n", "处理代表元 4/8: 000101\n", "量子数: k=1, p=-1, z=-1\n", "轨道大小: 12\n", "{34, 5, 40, 10, 43, 46, 17, 20, 53, 23, 58, 29}\n", "5\n", "\n", "处理代表元 5/8: 000111\n", "量子数: k=1, p=-1, z=-1\n", "轨道大小: 6\n", "{35, 7, 14, 49, 56, 28}\n", "7\n", "\n", "处理代表元 6/8: 001001\n", "量子数: k=2, p=-1, z=-1\n", "轨道大小: 6\n", "{36, 9, 45, 18, 54, 27}\n", "9\n", "\n", "处理代表元 7/8: 001011\n", "量子数: k=1, p=-1, z=-1\n", "轨道大小: 12\n", "{37, 38, 41, 11, 44, 13, 50, 19, 52, 22, 25, 26}\n", "11\n", "\n", "处理代表元 8/8: 010101\n", "量子数: k=3, p=-1, z=-1\n", "轨道大小: 2\n", "{42, 21}\n", "21\n", "\n", "完整基大小: 64/64\n", "✓ 完整基包含所有状态\n", "\n", "块分解结果:\n", "块 (k=6, p=1, z=-1): 2 个状态\n", "块 (k=1, p=-1, z=-1): 54 个状态\n", "块 (k=2, p=-1, z=-1): 6 个状态\n", "块 (k=3, p=-1, z=-1): 2 个状态\n", "\n", "验证结果:\n", "总状态数: 64\n", "块中状态数: 64\n", "缺失状态数: 0\n", "多余状态数: 0\n", "✓ 所有状态都被正确分配到块中\n"]}], "source": ["import numpy as np\n", "from collections import defaultdict\n", "\n", "def generate_all_states(N):\n", "    \"\"\"生成所有可能的N位二进制状态\"\"\"\n", "    return [i for i in range(2**N)]\n", "\n", "def apply_translation(state, N, shift):\n", "    \"\"\"应用平移操作\"\"\"\n", "    # 将整数转换为二进制字符串\n", "    bin_str = bin(state)[2:].zfill(N)\n", "    # 循环平移\n", "    translated = bin_str[-shift:] + bin_str[:-shift]\n", "    # 转换回整数\n", "    return int(translated, 2)\n", "\n", "def apply_reflection(state, N):\n", "    \"\"\"应用反射操作\"\"\"\n", "    # 将整数转换为二进制字符串\n", "    bin_str = bin(state)[2:].zfill(N)\n", "    # 反转字符串\n", "    reflected = bin_str[::-1]\n", "    # 转换回整数\n", "    return int(reflected, 2)\n", "\n", "def apply_spin_flip(state, N):\n", "    \"\"\"应用自旋翻转操作\"\"\"\n", "    # 翻转所有自旋\n", "    return state ^ ((1 << N) - 1)\n", "\n", "def get_momentum(state, N):\n", "    \"\"\"计算动量量子数k\"\"\"\n", "    # 找到最小的平移周期\n", "    for shift in range(1, N+1):\n", "        if apply_translation(state, N, shift) == state:\n", "            return N // shift  # 周期数\n", "    return N  # 没有周期性，周期为N\n", "\n", "def get_parity(state, N):\n", "    \"\"\"计算宇称量子数p\"\"\"\n", "    reflected = apply_reflection(state, N)\n", "    if reflected == state:\n", "        return 1  # 偶宇称\n", "    else:\n", "        return -1  # 奇宇称\n", "\n", "def get_spin_flip_parity(state, N):\n", "    \"\"\"计算自旋翻转量子数z\"\"\"\n", "    flipped = apply_spin_flip(state, N)\n", "    if flipped == state:\n", "        return 1  # 偶自旋翻转\n", "    else:\n", "        return -1  # 奇自旋翻转\n", "\n", "def find_representatives(N, k_filter=None, p_filter=None, z_filter=None):\n", "    \"\"\"找到所有满足量子数条件的代表元\"\"\"\n", "    all_states = generate_all_states(N)\n", "    representatives = []\n", "    used_states = set()\n", "    \n", "    for state in all_states:\n", "        if state in used_states:\n", "            continue\n", "            \n", "        # 计算量子数\n", "        k = get_momentum(state, N)\n", "        p = get_parity(state, N)\n", "        z = get_spin_flip_parity(state, N)\n", "        \n", "        # 检查是否满足筛选条件\n", "        if (k_filter is not None and k != k_filter) or \\\n", "           (p_filter is not None and p != p_filter) or \\\n", "           (z_filter is not None and z != z_filter):\n", "            continue\n", "        \n", "        # 找到轨道中的所有状态\n", "        orbit = generate_orbit(state, N)\n", "        used_states.update(orbit)\n", "        \n", "        # 添加代表元和轨道信息\n", "        representatives.append({\n", "            'state': state,\n", "            'orbit': orbit,\n", "            'k': k,\n", "            'p': p,\n", "            'z': z,\n", "            'size': len(orbit)\n", "        })\n", "    \n", "    return representatives\n", "\n", "def generate_orbit(state, N):\n", "    \"\"\"生成一个状态的所有对称性轨道\"\"\"\n", "    orbit = set()\n", "    \n", "    # 添加原始状态\n", "    orbit.add(state)\n", "    \n", "    # 应用所有可能的对称操作\n", "    for shift in range(1, N):  # 平移\n", "        translated = apply_translation(state, N, shift)\n", "        orbit.add(translated)\n", "        \n", "        # 平移后再反射\n", "        reflected = apply_reflection(translated, N)\n", "        orbit.add(reflected)\n", "        \n", "        # 平移后再自旋翻转\n", "        flipped = apply_spin_flip(translated, N)\n", "        orbit.add(flipped)\n", "        \n", "        # 平移后再反射再自旋翻转\n", "        reflected_flipped = apply_spin_flip(reflected, N)\n", "        orbit.add(reflected_flipped)\n", "    \n", "    # 直接应用反射\n", "    reflected = apply_reflection(state, N)\n", "    orbit.add(reflected)\n", "    \n", "    # 直接应用自旋翻转\n", "    flipped = apply_spin_flip(state, N)\n", "    orbit.add(flipped)\n", "    \n", "    # 反射后再自旋翻转\n", "    reflected_flipped = apply_spin_flip(reflected, N)\n", "    orbit.add(reflected_flipped)\n", "    \n", "    return orbit\n", "\n", "def generate_complete_basis(N, k=None, p=None, z=None):\n", "    \"\"\"\n", "    使用对称性生成完整基\n", "    \n", "    参数:\n", "        N: 系统大小\n", "        k: 动量量子数 (可选)\n", "        p: 宇称量子数 (可选)\n", "        z: 自旋翻转量子数 (可选)\n", "    \n", "    返回:\n", "        complete_basis: 完整基中的所有状态\n", "        blocks: 按量子数分块的基\n", "    \"\"\"\n", "    # 获取所有满足条件的代表元\n", "    reps = find_representatives(N, k, p, z)\n", "    \n", "    complete_basis = set()\n", "    blocks = defaultdict(list)\n", "    \n", "    print(f\"找到 {len(reps)} 个代表元\")\n", "    \n", "    # 为每个代表元生成轨道\n", "    for i, rep in enumerate(reps):\n", "        state = rep['state']\n", "        orbit = rep['orbit']\n", "        k_val = rep['k']\n", "        p_val = rep['p']\n", "        z_val = rep['z']\n", "        \n", "        print(f\"\\n处理代表元 {i+1}/{len(reps)}: {bin(state)[2:].zfill(N)}\")\n", "        print(f\"量子数: k={k_val}, p={p_val}, z={z_val}\")\n", "        print(f\"轨道大小: {len(orbit)}\")\n", "        print(orbit)\n", "        print(state)\n", "        \n", "        # 添加到完整基\n", "        complete_basis.update(orbit)\n", "        \n", "        # 添加到相应的块\n", "        block_key = (k_val, p_val, z_val)\n", "        blocks[block_key].extend(orbit)\n", "    \n", "    # 验证完整性\n", "    expected_size = 2 ** N\n", "    actual_size = len(complete_basis)\n", "    \n", "    print(f\"\\n完整基大小: {actual_size}/{expected_size}\")\n", "    \n", "    if actual_size == expected_size:\n", "        print(\"✓ 完整基包含所有状态\")\n", "    else:\n", "        print(\"✗ 完整基不完整\")\n", "        missing = set(range(expected_size)) - complete_basis\n", "        print(f\"缺失 {len(missing)} 个状态\")\n", "        if missing:\n", "            print(\"示例缺失状态:\")\n", "            for i, state in enumerate(sorted(missing)[:10]):\n", "                print(f\"  {bin(state)[2:].zfill(N)}\")\n", "    \n", "    return complete_basis, blocks\n", "\n", "def test_complete_block_diagonalization():\n", "    \"\"\"测试完整的块对角化\"\"\"\n", "    N = 6\n", "    \n", "    print(f\"测试 N={N} 的完整块对角化\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 生成所有块\n", "    complete_basis, blocks = generate_complete_basis(N)\n", "    \n", "    # 打印每个块的信息\n", "    print(\"\\n块分解结果:\")\n", "    for (k, p, z), states in blocks.items():\n", "        print(f\"块 (k={k}, p={p}, z={z}): {len(states)} 个状态\")\n", "    \n", "    # 验证所有状态是否都被分配\n", "    all_states = set(range(2**N))\n", "    states_in_blocks = set()\n", "    for states in blocks.values():\n", "        states_in_blocks.update(states)\n", "    \n", "    missing = all_states - states_in_blocks\n", "    extra = states_in_blocks - all_states\n", "    \n", "    print(f\"\\n验证结果:\")\n", "    print(f\"总状态数: {len(all_states)}\")\n", "    print(f\"块中状态数: {len(states_in_blocks)}\")\n", "    print(f\"缺失状态数: {len(missing)}\")\n", "    print(f\"多余状态数: {len(extra)}\")\n", "    \n", "    if not missing and not extra:\n", "        print(\"✓ 所有状态都被正确分配到块中\")\n", "    else:\n", "        if missing:\n", "            print(\"缺失状态示例:\")\n", "            for i, state in enumerate(sorted(missing)[:5]):\n", "                print(f\"  {bin(state)[2:].zfill(N)}\")\n", "        if extra:\n", "            print(\"多余状态示例:\")\n", "            for i, state in enumerate(sorted(extra)[:5]):\n", "                print(f\"  {bin(state)[2:].zfill(N)}\")\n", "    \n", "    return blocks\n", "\n", "# 运行测试\n", "blocks = test_complete_block_diagonalization()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "nikola": {"category": "Strongly Correlated Systems", "date": "2020-12-20 13:18:31 UTC+08:00", "description": "", "link": "", "slug": "exact-diagonalization", "tags": "Correlated Electronic Systems, Numerical Method, Exact Diagonalization", "title": "Exact Diagonalization", "type": "text"}, "toc-autonumbering": true}, "nbformat": 4, "nbformat_minor": 4}