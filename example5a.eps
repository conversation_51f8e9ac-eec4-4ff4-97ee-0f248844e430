%!PS-Adobe-3.0 EPSF-3.0
%%LanguageLevel: 3
%%Title: example5a.eps
%%Creator: Mat<PERSON>lotlib v3.9.2, https://matplotlib.org/
%%CreationDate: Fri Sep  5 17:54:54 2025
%%Orientation: portrait
%%BoundingBox: 0 0 453 336
%%HiResBoundingBox: 0.000000 0.000000 452.613125 335.846875
%%EndComments
%%BeginProlog
/mpldict 9 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Mat<PERSON><PERSON><PERSON>b.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/minus /period /zero /one /two /three /four /five /six /seven /I /P /R /a /e /g /i /n /q /r /s /u /y] def
/CharStrings 24 dict dup begin
/.notdef 0 def
/minus{1716 0 217 557 1499 727 sc
217 727 m
1499 727 l
1499 557 l
217 557 l
217 727 l

ce} _d
/period{651 0 219 0 430 254 sc
219 254 m
430 254 l
430 0 l
219 0 l
219 254 l

ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/six{1303 0 143 -29 1174 1520 sc
676 827 m
585 827 513 796 460 734 c
407 672 381 587 381 479 c
381 372 407 287 460 224 c
513 162 585 131 676 131 c
767 131 838 162 891 224 c
944 287 971 372 971 479 c
971 587 944 672 891 734 c
838 796 767 827 676 827 c

1077 1460 m
1077 1276 l
1026 1300 975 1318 923 1331 c
872 1344 821 1350 770 1350 c
637 1350 535 1305 464 1215 c
394 1125 354 989 344 807 c
383 865 433 909 492 940 c
551 971 617 987 688 987 c
838 987 956 941 1043 850 c
1130 759 1174 636 1174 479 c
1174 326 1129 203 1038 110 c
947 17 827 -29 676 -29 c
503 -29 371 37 280 169 c
189 302 143 494 143 745 c
143 981 199 1169 311 1309 c
423 1450 573 1520 762 1520 c
813 1520 864 1515 915 1505 c
967 1495 1021 1480 1077 1460 c

ce} _d
/seven{1303 0 168 0 1128 1493 sc
168 1493 m
1128 1493 l
1128 1407 l
586 0 l
375 0 l
885 1323 l
168 1323 l
168 1493 l

ce} _d
/I{604 0 201 0 403 1493 sc
201 1493 m
403 1493 l
403 0 l
201 0 l
201 1493 l

ce} _d
/P{1235 0 201 0 1165 1493 sc
403 1327 m
403 766 l
657 766 l
751 766 824 790 875 839 c
926 888 952 957 952 1047 c
952 1136 926 1205 875 1254 c
824 1303 751 1327 657 1327 c
403 1327 l

201 1493 m
657 1493 l
824 1493 951 1455 1036 1379 c
1122 1304 1165 1193 1165 1047 c
1165 900 1122 788 1036 713 c
951 638 824 600 657 600 c
403 600 l
403 0 l
201 0 l
201 1493 l

ce} _d
/R{1423 0 201 0 1364 1493 sc
909 700 m
952 685 994 654 1035 606 c
1076 558 1118 492 1159 408 c
1364 0 l
1147 0 l
956 383 l
907 483 859 549 812 582 c
766 615 703 631 623 631 c
403 631 l
403 0 l
201 0 l
201 1493 l
657 1493 l
828 1493 955 1457 1039 1386 c
1123 1315 1165 1207 1165 1063 c
1165 969 1143 891 1099 829 c
1056 767 992 724 909 700 c

403 1327 m
403 797 l
657 797 l
754 797 828 819 877 864 c
927 909 952 976 952 1063 c
952 1150 927 1216 877 1260 c
828 1305 754 1327 657 1327 c
403 1327 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/g{1300 0 113 -426 1114 1147 sc
930 573 m
930 706 902 810 847 883 c
792 956 715 993 616 993 c
517 993 440 956 385 883 c
330 810 303 706 303 573 c
303 440 330 337 385 264 c
440 191 517 154 616 154 c
715 154 792 191 847 264 c
902 337 930 440 930 573 c

1114 139 m
1114 -52 1072 -193 987 -286 c
902 -379 773 -426 598 -426 c
533 -426 472 -421 415 -411 c
358 -402 302 -387 248 -367 c
248 -188 l
302 -217 355 -239 408 -253 c
461 -267 514 -274 569 -274 c
690 -274 780 -242 840 -179 c
900 -116 930 -21 930 106 c
930 197 l
892 131 843 82 784 49 c
725 16 654 0 571 0 c
434 0 323 52 239 157 c
155 262 113 400 113 573 c
113 746 155 885 239 990 c
323 1095 434 1147 571 1147 c
654 1147 725 1131 784 1098 c
843 1065 892 1016 930 950 c
930 1120 l
1114 1120 l
1114 139 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/n{1298 0 186 0 1124 1147 sc
1124 676 m
1124 0 l
940 0 l
940 670 l
940 776 919 855 878 908 c
837 961 775 987 692 987 c
593 987 514 955 457 892 c
400 829 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
415 1013 467 1064 526 1097 c
586 1130 655 1147 733 1147 c
862 1147 959 1107 1025 1027 c
1091 948 1124 831 1124 676 c

ce} _d
/q{1300 0 113 -426 1114 1147 sc
303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

930 168 m
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c
930 1120 l
1114 1120 l
1114 -426 l
930 -426 l
930 168 l

ce} _d
/r{842 0 186 0 842 1147 sc
842 948 m
821 960 799 969 774 974 c
750 980 723 983 694 983 c
590 983 510 949 454 881 c
399 814 371 717 371 590 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
410 1014 460 1064 522 1097 c
584 1130 659 1147 748 1147 c
761 1147 775 1146 790 1144 c
805 1143 822 1140 841 1137 c
842 948 l

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/u{1298 0 174 -29 1112 1147 sc
174 442 m
174 1120 l
358 1120 l
358 449 l
358 343 379 263 420 210 c
461 157 523 131 606 131 c
705 131 784 163 841 226 c
899 289 928 376 928 485 c
928 1120 l
1112 1120 l
1112 0 l
928 0 l
928 172 l
883 104 831 53 772 20 c
713 -13 645 -29 567 -29 c
438 -29 341 11 274 91 c
207 171 174 288 174 442 c

637 1147 m
637 1147 l

ce} _d
/y{1212 0 61 -426 1151 1120 sc
659 -104 m
607 -237 556 -324 507 -365 c
458 -406 392 -426 309 -426 c
162 -426 l
162 -272 l
270 -272 l
321 -272 360 -260 388 -236 c
416 -212 447 -155 481 -66 c
514 18 l
61 1120 l
256 1120 l
606 244 l
956 1120 l
1151 1120 l
659 -104 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
0 0 translate
0 0 452.613 335.847 rectclip
gsave
0 0 m
452.613125 0 l
452.613125 335.846875 l
0 335.846875 l
cl
1 setgray
fill
grestore
gsave
59.153125 49.16875 m
445.413125 49.16875 l
445.413125 322.56875 l
59.153125 322.56875 l
cl
1 setgray
fill
grestore
1 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.122 0.467 0.706 setrgbcolor
gsave
59.153 49.169 386.26 273.4 rectclip
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -0.866025 m
0.229673 -0.866025 0.449969 -0.774776 0.612372 -0.612372 c
0.774776 -0.449969 0.866025 -0.229673 0.866025 0 c
0.866025 0.229673 0.774776 0.449969 0.612372 0.612372 c
0.449969 0.774776 0.229673 0.866025 0 0.866025 c
-0.229673 0.866025 -0.449969 0.774776 -0.612372 0.612372 c
-0.774776 0.449969 -0.866025 0.229673 -0.866025 0 c
-0.866025 -0.229673 -0.774776 -0.449969 -0.612372 -0.612372 c
-0.449969 -0.774776 -0.229673 -0.866025 0 -0.866025 c
cl

gsave
0.122 0.467 0.706 setrgbcolor
fill
grestore
stroke
grestore
} bind def
396.222 244.352 o
220.645 244.352 o
331.917 114.221 o
156.33 114.253 o
123.887 73.4689 o
125.467 73.6738 o
426.1 76.3005 o
156.338 118.907 o
156.338 122.277 o
78.4805 76.0599 o
204.295 81.6814 o
301.039 73.5595 o
299.458 73.5656 o
331.91 145.99 o
331.91 136.369 o
124.677 81.7085 o
379.877 81.705 o
123.888 83.0145 o
123.888 84.2272 o
125.469 83.5657 o
125.469 85.5215 o
124.688 83.9066 o
124.688 95.1275 o
204.315 81.7085 o
379.886 81.7164 o
379.875 96.7655 o
379.888 94.495 o
379.888 97.4449 o
379.875 96.2922 o
300.254 81.7085 o
301.048 77.5313 o
301.048 78.3942 o
299.468 85.425 o
299.468 85.8363 o
300.265 92.813 o
300.265 90.9261 o
76.7104 92.1211 o
427.856 81.7085 o
76.7124 81.7085 o
426.893 72.3139 o
426.893 71.142 o
427.306 71.4396 o
427.306 72.7316 o
77.6753 67.0092 o
77.6753 66.2339 o
77.2699 71.3133 o
77.2699 72.8601 o
204.306 93.5029 o
204.304 97.8985 o
204.304 96.6469 o
204.306 85.7738 o
250.522 76.2405 o
254.056 76.1197 o
252.289 81.7085 o
251.319 66.8452 o
251.319 66.3895 o
251.726 68.9106 o
253.255 72.2123 o
253.255 71.9683 o
252.844 73.3782 o
252.844 73.4047 o
251.726 70.1388 o
252.284 92.1462 o
252.284 84.2718 o
grestore
0.8 setlinewidth
0 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
84.6206 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

72.8238 30.0125 translate
0 rotate
0 0 m /minus glyphshow
13.4062 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
140.508 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

128.712 30.0125 translate
0 rotate
0 0 m /minus glyphshow
13.4062 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
196.396 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

184.599 30.0125 translate
0 rotate
0 0 m /minus glyphshow
13.4062 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
252.284 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

247.19 30.0125 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
308.172 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

303.078 30.0125 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
364.06 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

358.966 30.0125 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
419.948 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

414.854 30.0125 translate
0 rotate
0 0 m /three glyphshow
grestore
/DejaVuSans 16.000 selectfont
gsave

202.939 10.5281 translate
0 rotate
0 0 m /q glyphshow
10.1562 0 m /u glyphshow
20.2969 0 m /a glyphshow
30.1016 0 m /s glyphshow
38.4375 0 m /i glyphshow
42.8828 0 m /e glyphshow
52.7266 0 m /n glyphshow
62.8672 0 m /e glyphshow
72.7109 0 m /r glyphshow
79.0391 0 m /g glyphshow
89.1953 0 m /y glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 49.1688 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 43.0906 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 88.2259 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 82.1478 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 127.283 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 121.205 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 166.34 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 160.262 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 205.397 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 199.319 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 244.454 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 238.376 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 283.512 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 277.433 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0 setgray
fill
grestore
stroke
grestore
} bind def
59.1531 322.569 o
grestore
/DejaVuSans 16.000 selectfont
gsave

26.6844 316.491 translate
0 rotate
0 0 m /zero glyphshow
10.1797 0 m /period glyphshow
15.2656 0 m /seven glyphshow
grestore
/DejaVuSans 16.000 selectfont
gsave

19.3563 173.119 translate
90 rotate
0 0 m /I glyphshow
4.71875 0 m /P glyphshow
14.3672 0 m /R glyphshow
grestore
0 setlinejoin
2 setlinecap
gsave
59.153125 49.16875 m
59.153125 322.56875 l
stroke
grestore
gsave
445.413125 49.16875 m
445.413125 322.56875 l
stroke
grestore
gsave
59.153125 49.16875 m
445.413125 49.16875 l
stroke
grestore
gsave
59.153125 322.56875 m
445.413125 322.56875 l
stroke
grestore
1 setlinewidth
0 setlinecap
0.8 setgray
gsave
427.813125 301.76875 m
434.213125 301.76875 l
436.346458 301.76875 437.413125 302.835417 437.413125 304.96875 c
437.413125 311.36875 l
437.413125 313.502083 436.346458 314.56875 434.213125 314.56875 c
427.813125 314.56875 l
425.679792 314.56875 424.613125 313.502083 424.613125 311.36875 c
424.613125 304.96875 l
424.613125 302.835417 425.679792 301.76875 427.813125 301.76875 c
cl
gsave
1 setgray
fill
grestore
stroke
grestore

end
showpage
