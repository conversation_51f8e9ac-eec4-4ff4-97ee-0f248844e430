{"cells": [{"cell_type": "code", "execution_count": 209, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.sparse.linalg import expm\n", "from scipy.sparse.linalg import eigsh\n", "from scipy.linalg import expm as scipy_expm  # 可能比numpy更稳定\n", "from math import * \n", "import pandas as pd\n", "import cmath\n", "import os #导入os库\n", "import random\n", "import time\n", "import seaborn as sns\n", "import cmath\n", "import scipy\n", "import functools\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 210, "metadata": {}, "outputs": [], "source": ["J=1\n", "h=1\n", "N=8"]}, {"cell_type": "code", "execution_count": 211, "metadata": {}, "outputs": [], "source": ["# Functions to manipulate states\n", "def get_site_value(state, site):\n", "    return (state >> site) & 1\n", "def flip_state(state: int, index: int) -> int:\n", "    mask = 1 << index\n", "    return state ^ mask\n", "def set_site_value(state, site, value):\n", "    site_val = (value << site)\n", "    return (state ^ site_val) | site_val\n", "def translate(L, state, n_translation_sites):\n", "    new_state = 0\n", "    for site in range(L):\n", "        site_value = get_site_value(state, site)\n", "        new_state = set_site_value(new_state, (site + n_translation_sites)%L, site_value)\n", "    return new_state\n", "def reverseBits(s,N):\n", "    bin_chars = \"\"\n", "    temp = s\n", "    for i in range(N):\n", "        bin_char = bin(temp % 2)[-1]\n", "        temp = temp // 2\n", "        bin_chars = bin_char + bin_chars\n", "    bits =  bin_chars.upper()\n", "    return int(bits[::-1], 2)"]}, {"cell_type": "code", "execution_count": 212, "metadata": {}, "outputs": [], "source": ["def ffun(t,s,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    if t == -1:\n", "        if s == -1:\n", "            f = np.cos(kk*l)\n", "        elif s==1:\n", "            f = -np.sin(kk*l)\n", "    elif t ==1:\n", "        if s == -1:\n", "            f = np.sin(kk*l)\n", "        elif s==1:\n", "            f = np.cos(kk*l)\n", "    return f\n", "def ggun(t,l,k,N):\n", "    kk = 2*np.pi*k/N\n", "    g=0\n", "    if t == -1:\n", "        g = 1-np.cos(kk*l)\n", "    elif t==1:\n", "        g = 1+np.cos(kk*l) \n", "    return g"]}, {"cell_type": "code", "execution_count": 213, "metadata": {}, "outputs": [], "source": ["def block_direct_sum(blocks):\n", "    \"\"\"\n", "    沿对角线构造块直和矩阵（块对角矩阵）\n", "    \n", "    参数:\n", "        blocks: 子矩阵列表，每个元素为numpy数组（方阵）\n", "    返回:\n", "        块直和矩阵（对角线上是输入的子矩阵，其余为0）\n", "    \"\"\"\n", "    # 检查输入是否为空\n", "    if not blocks:\n", "        return np.array([], dtype=float)\n", "    \n", "    # 检查所有子矩阵是否为方阵\n", "    for i, b in enumerate(blocks):\n", "        if b.ndim != 2 or b.shape[0] != b.shape[1]:\n", "            raise ValueError(f\"子矩阵 {i} 必须是方阵（当前形状: {b.shape}）\")\n", "    \n", "    # 初始化结果矩阵（从空矩阵开始）\n", "    result = np.array([], dtype=blocks[0].dtype)\n", "    \n", "    for block in blocks:\n", "        m = block.shape[0]  # 当前块的维度\n", "        if result.size == 0:\n", "            # 第一次拼接：直接用当前块作为初始矩阵\n", "            result = block.copy()\n", "        else:\n", "            # 非第一次拼接：构造新的块对角矩阵\n", "            n = result.shape[0]  # 现有矩阵的维度\n", "            # 创建新的全零矩阵（维度为 (n+m)×(n+m)）\n", "            new_result = np.zeros((n + m, n + m), dtype=result.dtype)\n", "            # 填充左上角为现有矩阵\n", "            new_result[:n, :n] = result\n", "            # 填充右下角为当前块\n", "            new_result[n:, n:] = block\n", "            result = new_result\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 214, "metadata": {}, "outputs": [], "source": ["def checkstate(s,k,N):\n", "    R=-1\n", "    tz=-1\n", "    tp = -1\n", "    tpz = -1\n", "    smax = 2**N-1\n", "    #Sum_m = 0\n", "    #for i in range(N):\n", "    #    Sum_m += get_site_value(s,i)\n", "    #if Sum_m != N//2:\n", "    #    return R,tp,tz,tpz\n", "    t=s\n", "    for i in range(1,N+1):\n", "        t = translate(N,t,1)\n", "        az = smax -t\n", "        #print(t,s,az)\n", "        if t<s or az<s:\n", "            break\n", "        if t==s:\n", "            if k%(N/i)!=0:\n", "                break\n", "            R=i\n", "            break\n", "        if az==s:\n", "            tz=i\n", "    t = reverseBits(s,N)\n", "    az = smax-t\n", "    for i in range(R):\n", "        if t<s or az<s:\n", "            R=-1\n", "            break\n", "        if t==s:\n", "            tp=i\n", "        if az==s:\n", "            tpz=i\n", "        t = translate(N,t,1)\n", "        az = smax-t\n", "    return R,tp,tz,tpz"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "reprcount = []  # 全局记录列表（按需保留）\n", "\n", "def findbasis(N, k, p, z):\n", "    # 1. 重命名'repr'为'repr_list'，避免覆盖内置函数\n", "    repr_list = []\n", "    typee = []# 分类标签\n", "    peri = []# 平移对称性\n", "    mtrf = []# 反演对称性\n", "    ntrf = []# 自旋翻转对称性\n", "    capr = []# 联合对称性\n", "    \n", "    for s in range(2 **N):\n", "        # 2. 显式列出sigma值（替代range，更清晰）\n", "        for sigma in (-1, 1):\n", "            # 3. 每次迭代初始化m、n，避免跨状态污染\n", "            m, n = None, None\n", "            ca = None  # 显式初始化分类标签\n", "            R, tp, tz, tpz = checkstate(s, k, N)  # 获取状态对称性参数\n", "            \n", "            # 基础过滤：仅处理有效平移对称性的状态\n", "            if R <= -1:\n", "                continue\n", "            \n", "            # 4. 特殊k值的sigma约束（保留物理逻辑，明确标记）\n", "            if (k == 0 or k == N // 2) and (sigma == -1):\n", "                R = -1  # 标记为无效状态\n", "            \n", "            # 5. 仅处理R仍有效的状态\n", "            if R > 0:\n", "                # 6. 分支逻辑严格化：避免重叠，确保每个状态唯一分类\n", "                # 分支1：tp、tz、tpz均为-1（无反演相关对称）\n", "                if tp == -1 and tz == -1 and tpz == -1:\n", "                    ca = 1\n", "                    m, n = None, None  # 明确赋值，避免未定义\n", "                \n", "                # 分支2：tp≠-1、tz=-1（反演-平移对称）\n", "                elif tp != -1 and tz == -1:\n", "                    ca = 2\n", "                    m = tp\n", "                    # 7. 浮点数比较改用np.isclose，增强稳健性\n", "                    if np.isclose(ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支3：tp=-1、tz≠-1（反演-自旋翻转对称）\n", "                elif tp == -1 and tz != -1:\n", "                    ca = 3\n", "                    n = tz\n", "                    if np.isclose(ggun(z, n, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支4：tp=-1、tz=-1但tpz≠-1（联合对称）\n", "                elif tp == -1 and tz == -1 and tpz != -1:\n", "                    ca = 4\n", "                    m = tpz\n", "                    n = None  # 明确n未定义\n", "                    if np.isclose(ggun(sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p * z, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 分支5：tp≠-1、tz≠-1（多重对称叠加）\n", "                elif tp != -1 and tz != -1:\n", "                    ca = 5\n", "                    m, n = tp, tz\n", "                    if np.isclose(ggun(z, n, k, N) * ggun(sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                    if sigma == -1 and not np.isclose(ggun(-sigma * p, m, k, N), 0, atol=1e-8):\n", "                        R = -1\n", "                \n", "                # 8. 捕获未覆盖的状态组合（调试用）\n", "                else:\n", "                    print(f\"Warning: 未分类的状态组合 (tp={tp}, tz={tz}, tpz={tpz})\")\n", "                    continue\n", "                \n", "                # 9. 最终检查：ca必须已定义且R仍有效\n", "                if ca is not None and R > 0:\n", "                    repr_list.append(s)\n", "                    typee.append(2 * ca + (sigma + 1) / 2)\n", "                    capr.append(ca)\n", "                    peri.append(R)\n", "                    mtrf.append(m)\n", "                    ntrf.append(n)\n", "    \n", "    nrep = len(repr_list)\n", "    reprcount.append(repr_list)\n", "    # 10. 简化打印，避免输出过载\n", "    #print(f\"k={k}, p={p}, z={z}: 有效状态数={nrep}\")\n", "    return nrep, repr_list, typee, peri, mtrf, ntrf, capr"]}, {"cell_type": "code", "execution_count": 216, "metadata": {}, "outputs": [], "source": ["def Ham_total(N,J,h):\n", "    H = np.zeros((2 ** N, 2 ** N))\n", "\n", "    # a is a basis\n", "    for a in range(2 ** N):\n", "        # i is position of this basis\n", "        for i in range(N):\n", "            # j is the nearest neighbor, mod N for periodic boundary conditions\n", "            j = (i + 1) % N\n", "            ai = get_site_value(a,i)\n", "            aj = get_site_value(a,j)\n", "\n", "            # Sz\n", "            b = a\n", "            if ai == aj:\n", "                H[a, b] += J\n", "            else:\n", "                H[a, b] += -J\n", "\n", "            # SxSx + SySy\n", "            #if ai != aj:\n", "            b = flip_state(a, i)\n", "            #b = flip_state(b, j)\n", "            H[a, b] += h\n", "    return H"]}, {"cell_type": "code", "execution_count": 217, "metadata": {}, "outputs": [], "source": ["def represent(L,a0):\n", "    at = a0\n", "    a = a0\n", "    l=0\n", "    q=0\n", "    g=0\n", "    smax = 2**L-1\n", "    for t in range(1,L+1):\n", "        at = translate(L,a0,t)\n", "        if at < a:\n", "            a = at\n", "            l=t\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            g=1    \n", "    at = reverseBits(a0,L)\n", "    for t in range(L):\n", "        if at<a:\n", "            a=at\n", "            l=t\n", "            q=1\n", "            g=0\n", "        az=smax-at\n", "        if az<a:\n", "            a=az\n", "            l=t\n", "            q=1\n", "            g=1\n", "        at = translate(L, at, 1)\n", "    return a,l,q,g"]}, {"cell_type": "code", "execution_count": 218, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ndef build_projection_matrix_for_k(N, reps, peri, mtrf, ntrf, capr, p, z, k):\\n    \"\"\"为特定k构造投影矩阵V_k（严格匹配findbasis的基构建逻辑，考虑多重对称性）\\n    \\n    参数:\\n        reps: 代表元列表（来自findbasis的repr_list）\\n        peri: 平移周期列表（每个代表元的轨道长度）\\n        mtrf: 反演-平移对称参数（tp）\\n        ntrf: 反演-自旋翻转对称参数（tz）\\n        capr: 对称性分类标签（ca=1~5，来自findbasis的capr）\\n        p, z: 对称性约束参数（与findbasis中的p、z一致）\\n        k: 波矢参数\\n    \"\"\"\\n    dim_full = 2 **N\\n    nrep = len(reps)\\n    # 验证输入参数长度一致性\\n    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\\n        raise ValueError(\"reps、peri、mtrf、ntrf、capr长度必须一致\")\\n    \\n    V = np.zeros((dim_full, nrep), dtype=complex)\\n    \\n    for col in range(nrep):\\n        rep = reps[col]\\n        R = peri[col]  # 平移周期（轨道长度）\\n        ca = capr[col]  # 对称性分类\\n        m = mtrf[col]   # 反演-平移参数\\n        n = ntrf[col]   # 反演-自旋翻转参数\\n        \\n        # 生成代表元的完整轨道（与findbasis中的checkstate逻辑匹配）\\n        orbit = generate_orbit_states(N, rep)\\n        if len(orbit) != R:\\n            raise ValueError(f\"轨道长度{len(orbit)}与周期{R}不匹配（代表元{col}）\")\\n        \\n        # 1. 归一化因子：基于平移周期，保证右矢归一化\\n        norm = 1.0 / np.sqrt(R)\\n        \\n        # 2. 计算对称性相位因子（结合capr分类与p/z参数）\\n        for r, s in enumerate(orbit):\\n            # 基础平移相位（基于轨道周期R，与findbasis中的R一致）\\n            trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\\n            \\n            # 对称性附加相位（根据capr分类，匹配findbasis中的ggun约束）\\n            sym_phase = 1.0  # 默认为1（无额外相位）\\n            \\n            # 分类1：无反演相关对称（ca=1）\\n            if ca == 1:\\n                sym_phase = 1.0  # 仅平移相位\\n            \\n            # 分类2：反演-平移对称（ca=2，m=tp≠-1）\\n            elif ca == 2 and m is not None:\\n                # 对应findbasis中ggun(sigma*p, m, k, N)的相位贡献\\n                sym_phase = np.exp(1j * np.pi * p * m * r / R)\\n            \\n            # 分类3：反演-自旋翻转对称（ca=3，n=tz≠-1）\\n            elif ca == 3 and n is not None:\\n                # 对应findbasis中ggun(z, n, k, N)的相位贡献\\n                sym_phase = np.exp(1j * np.pi * z * n * r / R)\\n            \\n            # 分类4：联合对称（ca=4，m=tpz≠-1）\\n            elif ca == 4 and m is not None:\\n                # 对应findbasis中ggun(sigma*p*z, m, k, N)的相位贡献\\n                sym_phase = np.exp(1j * np.pi * p * z * m * r / R)\\n            \\n            # 分类5：多重对称叠加（ca=5，m=tp≠-1且n=tz≠-1）\\n            elif ca == 5 and m is not None and n is not None:\\n                # 对应findbasis中ggun(z, n, ...)*ggun(sigma*p, m, ...)的乘积相位\\n                sym_phase = np.exp(1j * np.pi * (z * n + p * m) * r / R)\\n            \\n            # 总相位 = 平移相位 × 对称性相位\\n            total_phase = trans_phase * sym_phase\\n            \\n            # 填充投影矩阵（右矢|k>的系数）\\n            V[s, col] = norm * total_phase\\n    \\n    # 验证双正交性（确保与findbasis的基正交性一致）\\n    V_dag = V.conj().T\\n    ortho_error = np.linalg.norm(V_dag @ V - np.eye(nrep), ord=np.inf)\\n    if not np.isclose(ortho_error, 0, atol=1e-8):\\n        raise RuntimeError(f\"双正交性验证失败，误差={ortho_error:.2e}（对称性相位可能错误）\")\\n    \\n    return V\\n\\ndef generate_orbit_states(N: int, rep: int):\\n    \"\"\"生成代表元 rep 的完整平移轨道\"\"\"\\n    \"\"\"修正后的轨道生成\"\"\"\\n    states = [rep]\\n    t = translate(N, rep, 1)\\n    count = 0\\n    max_iter = N  # 防止无限循环\\n    \\n    while t != rep and count < max_iter:\\n        states.append(t)\\n        t = translate(N, t, 1)\\n        count += 1\\n    \\n    if count >= max_iter:\\n        print(f\"警告：轨道生成可能有问题，rep={rep}\")\\n    \\n    return states'"]}, "execution_count": 218, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "'''\n", "def build_projection_matrix_for_k(N, reps, peri, mtrf, ntrf, capr, p, z, k):\n", "    \"\"\"为特定k构造投影矩阵V_k（严格匹配findbasis的基构建逻辑，考虑多重对称性）\n", "    \n", "    参数:\n", "        reps: 代表元列表（来自findbasis的repr_list）\n", "        peri: 平移周期列表（每个代表元的轨道长度）\n", "        mtrf: 反演-平移对称参数（tp）\n", "        ntrf: 反演-自旋翻转对称参数（tz）\n", "        capr: 对称性分类标签（ca=1~5，来自findbasis的capr）\n", "        p, z: 对称性约束参数（与findbasis中的p、z一致）\n", "        k: 波矢参数\n", "    \"\"\"\n", "    dim_full = 2 **N\n", "    nrep = len(reps)\n", "    # 验证输入参数长度一致性\n", "    if not all(len(arr) == nrep for arr in [peri, mtrf, ntrf, capr]):\n", "        raise ValueError(\"reps、peri、mtrf、ntrf、capr长度必须一致\")\n", "    \n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "    \n", "    for col in range(nrep):\n", "        rep = reps[col]\n", "        R = peri[col]  # 平移周期（轨道长度）\n", "        ca = capr[col]  # 对称性分类\n", "        m = mtrf[col]   # 反演-平移参数\n", "        n = ntrf[col]   # 反演-自旋翻转参数\n", "        \n", "        # 生成代表元的完整轨道（与findbasis中的checkstate逻辑匹配）\n", "        orbit = generate_orbit_states(N, rep)\n", "        if len(orbit) != R:\n", "            raise ValueError(f\"轨道长度{len(orbit)}与周期{R}不匹配（代表元{col}）\")\n", "        \n", "        # 1. 归一化因子：基于平移周期，保证右矢归一化\n", "        norm = 1.0 / np.sqrt(R)\n", "        \n", "        # 2. 计算对称性相位因子（结合capr分类与p/z参数）\n", "        for r, s in enumerate(orbit):\n", "            # 基础平移相位（基于轨道周期R，与findbasis中的R一致）\n", "            trans_phase = np.exp(-1j * 2 * np.pi * k * r / R)\n", "            \n", "            # 对称性附加相位（根据capr分类，匹配findbasis中的ggun约束）\n", "            sym_phase = 1.0  # 默认为1（无额外相位）\n", "            \n", "            # 分类1：无反演相关对称（ca=1）\n", "            if ca == 1:\n", "                sym_phase = 1.0  # 仅平移相位\n", "            \n", "            # 分类2：反演-平移对称（ca=2，m=tp≠-1）\n", "            elif ca == 2 and m is not None:\n", "                # 对应findbasis中ggun(sigma*p, m, k, N)的相位贡献\n", "                sym_phase = np.exp(1j * np.pi * p * m * r / R)\n", "            \n", "            # 分类3：反演-自旋翻转对称（ca=3，n=tz≠-1）\n", "            elif ca == 3 and n is not None:\n", "                # 对应findbasis中ggun(z, n, k, N)的相位贡献\n", "                sym_phase = np.exp(1j * np.pi * z * n * r / R)\n", "            \n", "            # 分类4：联合对称（ca=4，m=tpz≠-1）\n", "            elif ca == 4 and m is not None:\n", "                # 对应findbasis中ggun(sigma*p*z, m, k, N)的相位贡献\n", "                sym_phase = np.exp(1j * np.pi * p * z * m * r / R)\n", "            \n", "            # 分类5：多重对称叠加（ca=5，m=tp≠-1且n=tz≠-1）\n", "            elif ca == 5 and m is not None and n is not None:\n", "                # 对应findbasis中ggun(z, n, ...)*ggun(sigma*p, m, ...)的乘积相位\n", "                sym_phase = np.exp(1j * np.pi * (z * n + p * m) * r / R)\n", "            \n", "            # 总相位 = 平移相位 × 对称性相位\n", "            total_phase = trans_phase * sym_phase\n", "            \n", "            # 填充投影矩阵（右矢|k>的系数）\n", "            V[s, col] = norm * total_phase\n", "    \n", "    # 验证双正交性（确保与findbasis的基正交性一致）\n", "    V_dag = V.conj().T\n", "    ortho_error = np.linalg.norm(V_dag @ V - np.eye(nrep), ord=np.inf)\n", "    if not np.isclose(ortho_error, 0, atol=1e-8):\n", "        raise RuntimeError(f\"双正交性验证失败，误差={ortho_error:.2e}（对称性相位可能错误）\")\n", "    \n", "    return V\n", "\n", "def generate_orbit_states(N: int, rep: int):\n", "    \"\"\"生成代表元 rep 的完整平移轨道\"\"\"\n", "    \"\"\"修正后的轨道生成\"\"\"\n", "    states = [rep]\n", "    t = translate(N, rep, 1)\n", "    count = 0\n", "    max_iter = N  # 防止无限循环\n", "    \n", "    while t != rep and count < max_iter:\n", "        states.append(t)\n", "        t = translate(N, t, 1)\n", "        count += 1\n", "    \n", "    if count >= max_iter:\n", "        print(f\"警告：轨道生成可能有问题，rep={rep}\")\n", "    \n", "    return states'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from collections import deque\n", "def generate_symmetry_orbit(N, rep, ca, m, n):\n", "    \"\"\"\n", "    生成包含所有对称操作的状态轨道（修正：保证轨道闭合性，避免重复入队）\n", "\n", "    参数:\n", "        N: 系统大小\n", "        rep: 代表元\n", "        ca: 对称性分类\n", "        m: 反演参数\n", "        n: 自旋翻转参数\n", "    返回:\n", "        orbit: 包含所有对称操作生成的状态列表（去重后）\n", "    \"\"\"\n", "    orbit = []\n", "    visited = set()\n", "    queue = deque([rep])\n", "\n", "    while queue:\n", "        state = queue.popleft()\n", "        if state in visited:\n", "            continue\n", "        visited.add(state)\n", "        orbit.append(state)\n", "\n", "        # 平移操作（所有分类都包含）\n", "        translated = translate_k(N, state, 1)\n", "        if translated not in visited:\n", "            queue.append(translated)\n", "\n", "        # 反演操作（ca=2,4,5时生效）\n", "        if ca in [2, 4, 5] and m is not None:\n", "            inverted = invert_state_k(N, state, m)\n", "            if inverted not in visited:\n", "                queue.append(inverted)\n", "\n", "        # 自旋翻转操作（ca=3,4,5时生效）\n", "        if ca in [3, 4, 5] and n is not None:\n", "            flipped = flip_all_spins_k(N, state)\n", "            if flipped not in visited:\n", "                queue.append(flipped)\n", "\n", "    return orbit\n", "\n", "def translate_k(N, state, n_translation_sites):\n", "    \"\"\"\n", "    平移操作（逐位平移，保证周期边界）\n", "    \"\"\"\n", "    new_state = 0\n", "    for site in range(N):\n", "        bit = (state >> site) & 1\n", "        new_site = (site + n_translation_sites) % N\n", "        new_state |= bit << new_site\n", "    return new_state\n", "\n", "def invert_state_k(N, state, m):\n", "    \"\"\"\n", "    空间反演操作：以位置m为中心进行反演（逐位反演，保证位置合法性）\n", "    \"\"\"\n", "    result = 0\n", "    for i in range(N):\n", "        j = (2 * m - i) % N\n", "        j = j if j >= 0 else j + N  # 确保位置非负\n", "        bit = (state >> i) & 1\n", "        result |= bit << j\n", "    return result\n", "\n", "def flip_all_spins_k(N, state):\n", "    \"\"\"\n", "    自旋翻转操作：将所有自旋翻转（按位异或全1数）\n", "    \"\"\"\n", "    return state ^ ((1 << N) - 1)"]}, {"cell_type": "code", "execution_count": 220, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def build_projection_matrix(N, reps, peri, mtrf, ntrf, capr, typee,p, z, k):\n", "    \"\"\"\n", "    重构的投影算符构建函数（严格遵循对称性分类与正交性要求）\n", "    考虑平移、反演和自旋翻转三种对称性的共同作用，生成新基下的投影矩阵\n", "\n", "    参数:\n", "        N: 系统大小（自旋数）\n", "        reps: 代表元列表（findbasis输出）\n", "        peri: 平移周期列表（每个代表元的轨道长度R）\n", "        mtrf: 反演-平移参数tp\n", "        ntrf: 反演-自旋翻转参数tz\n", "        capr: 对称性分类（ca=1~5）\n", "        p, z: 对称性约束参数（±1）\n", "        k: 波矢（0 ≤ k < N）\n", "    返回:\n", "        V: 投影矩阵（列向量为对称化基矢，满足正交性）\n", "    \"\"\"\n", "    dim_full = 2 ** N\n", "    nrep = len(reps)\n", "\n", "    V = np.zeros((dim_full, nrep), dtype=complex)\n", "\n", "    for col in range(nrep):\n", "        # 提取当前代表元的对称性参数\n", "        rep = reps[col]\n", "        R = peri[col]  # 理论轨道长度\n", "        ca = capr[col]\n", "        s_sigma = 2*(typee[col]%2)-1#sigma\n", "        m = mtrf[col] if mtrf[col] != -1 else None\n", "        n = ntrf[col] if ntrf[col] != -1 else None\n", "        matelement = 1/peri[col]\n", "\n", "        if ca==2 or ca==5:\n", "            matelement = matelement/ggun(s_sigma*p,m,k,N)\n", "        if ca==3 or ca==5:\n", "            matelement = matelement/ggun(z,n,k,N)\n", "        if ca==4:\n", "            matelement = matelement/ggun(s_sigma*p*z,m,k,N)\n", "        matel  =  np.sqrt(matelement)\n", "        \n", "        # 生成包含所有对称操作的状态轨道\n", "        orbit = generate_symmetry_orbit(N, rep, ca, m, n)\n", "\n", "        # 填充投影矩阵：对轨道中每个态计算相位并累加\n", "        for r, s in enumerate(orbit):# index,value\n", "            #(1 + pP)(1 + zZ)T ^r|a〉\n", "            # 平移相位（基础相位）\n", "            #情况 1，只有平移对称性（ca=1）\n", "            matelement = matel * ffun(1,s_sigma,r,k,N)\n", "            V[s, col] = matelement\n", "            #情况 2，pP对称性\n", "            V[reverseBits(s,N),col] = matelement * (p) \n", "            #情况 3，zZ对称性\n", "            print(flip_all_spins_k(N,s),s)\n", "            V[flip_all_spins_k(N,s),col] = matelement * (z)\n", "            #情况 4，pPzZ对称性\n", "            V[reverseBits(flip_all_spins_k(N,s),N),col] = matelement * (p*z)\n", "\n", "    return V\n"]}, {"cell_type": "code", "execution_count": 221, "metadata": {}, "outputs": [], "source": ["def helement(a,b,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k):\n", "    ca = capr[a]\n", "    cb = capr[b]\n", "    s = 2*(typee[a]%2)-1\n", "    t = 2*(typee[b]%2)-1\n", "    matelement = peri[a]/peri[b]\n", "    if ca==2 or ca==5:\n", "        matelement = matelement/ggun(s*p,mtrf[a],k,N)\n", "    if ca==3 or ca==5:\n", "        matelement = matelement/ggun(z,ntrf[a],k,N)\n", "    if ca==4:\n", "        matelement = matelement/ggun(s*p*z,mtrf[a],k,N)\n", "    if cb==2 or cb==5:\n", "        matelement = matelement*ggun(t*p,mtrf[b],k,N)\n", "    if cb==3 or cb==5:\n", "        matelement = matelement*ggun(z,ntrf[b],k,N)\n", "    if cb==4:\n", "        matelement = matelement*ggun(t*p*z,mtrf[b],k,N)\n", "    matelement  =  ((p*s)**q)*(z**g)*np.sqrt(matelement)\n", "    if cb==1 or cb==3:\n", "        matelement = matelement*ffun(t,s,l,k,N)\n", "    elif cb==2 or cb==5:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p,mtrf[b],k,N)\n", "    elif cb==4:\n", "        matelement = matelement*(ffun(t,s,l,k,N)+t*p*z*ffun(t,s,l-mtrf[b],k,N))/ggun(t*p*z,mtrf[b],k,N)\n", "    return matelement"]}, {"cell_type": "code", "execution_count": 222, "metadata": {}, "outputs": [], "source": ["def Ham_total_TPZ(N, nrep, repr, typee, peri, mtrf, ntrf, capr, p, z, k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia > 1 and sa == repr[ia - 1]):\n", "            continue\n", "        na = 2 if (ia < nrep - 1 and sa == repr[ia + 1]) else 1\n", "        Ez = 0\n", "        for i in range(N):\n", "            j = (i + 1) % N\n", "            ai = get_site_value(sa, i)\n", "            aj = get_site_value(sa, j)\n", "            if ai == aj:\n", "                Ez += J\n", "            else:\n", "                Ez -= J\n", "        for a in range(ia, ia + na):\n", "            Hk[a, a] += Ez\n", "        for i in range(N):\n", "            #j = (i + 1) % N\n", "            #ai = get_site_value(sa, i)\n", "            #aj = get_site_value(sa, j)\n", "            #if ai != aj:\n", "            #横场项\n", "            #sb = flip_state(sa, i)\n", "            #if ai == 1:\n", "            sb = flip_state(sa, i)\n", "            #else:\n", "                #sb = flip_state(flip_state(sa, j), i)\n", "            representative, l, q, g = represent(N, sb)\n", "            if representative in repr:\n", "                ib = repr.index(representative)\n", "                if ib > 1 and repr[ib] == repr[ib - 1]:\n", "                    ib = ib - 1\n", "                    nb = 2\n", "                elif ib < nrep - 1 and repr[ib] == repr[ib + 1]:\n", "                    nb = 2\n", "                else:\n", "                    nb = 1\n", "                for ii in range(ia, ia + na):\n", "                    for jj in range(ib, ib + nb):\n", "                        try:\n", "                            elem = h * helement(ii, jj, typee, peri, mtrf, ntrf, capr, p, z, l, q, g, N, k)\n", "                            if np.isfinite(elem):\n", "                                Hk[ii, jj] += elem\n", "                        except Exception as e:\n", "                            print(f\"helement error at ii={ii}, jj={jj}: {e}\")\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 223, "metadata": {}, "outputs": [], "source": ["def sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k):\n", "    Hk = np.zeros((nrep,) * 2).astype(complex)\n", "    for ia in range(nrep):\n", "        sa = repr[ia]\n", "        if (ia>1 and sa==repr[ia-1]):\n", "            continue\n", "        elif (ia<nrep-1 and sa==repr[ia+1]):\n", "            na=2\n", "        else:\n", "            na=1\n", "        for a in range(ia,ia+na):\n", "            Hk[a,a] += (1/2 )*N    \n", "        for i in range(N):\n", "            for j in range(i+1,N):\n", "                ai = get_site_value(sa, i)\n", "                aj = get_site_value(sa, j)\n", "                if ai != aj:\n", "                    if  ai == 1:   \n", "                        sb = flip_state(flip_state(sa,i),j)\n", "                    else:\n", "                        sb = flip_state(flip_state(sa,j),i)\n", "                    representative, l,q,g = represent(N,sb)\n", "                    if representative in repr:\n", "                        ib = repr.index(representative)\n", "                        if ib >1 and repr[ib]==repr[ib-1]:\n", "                            ib = ib-1\n", "                            nb=2\n", "                        elif ib<nrep-1 and repr[ib]==repr[ib+1]:\n", "                            nb=2\n", "                        else:\n", "                            nb=1\n", "                        for ii in range(ia,ia+na):\n", "                            for jj in range(ib,ib+nb):\n", "                                Hk[ii,jj] += J*helement(ii,jj,typee,peri,mtrf,ntrf,capr,p,z,l,q,g,N,k)\n", "    return Hk"]}, {"cell_type": "code", "execution_count": 224, "metadata": {}, "outputs": [], "source": ["def transform(nrep,mat,vec):\n", "    Hk = []\n", "    a = np.transpose(vec) @ (mat @ vec)\n", "    for i in range(nrep):\n", "        Hk.append(a[i,i])\n", "    return Hk    "]}, {"cell_type": "code", "execution_count": 225, "metadata": {}, "outputs": [], "source": ["def fullspectrum(N):\n", "    E=[]\n", "    #k=0\n", "    k_min = []\n", "    p_min = []\n", "    z_min = []\n", "    E_min = []\n", "    spi_min = []\n", "\n", "    v_full = []\n", "    Hk_block_full = []\n", "    new_basis_matrix = []\n", "    for k in range(N):\n", "        if k==0 or k==N//2:\n", "            p1=-1\n", "            p2=1\n", "        else:\n", "            p1=1\n", "            p2=1\n", "        for p in range(p1,p2+1,2):\n", "            for z in [-1,1]:\n", "                nrep,repr,typee,peri,mtrf,ntrf,capr = findbasis(N,k,p,z)\n", "                if nrep != 0:\n", "                    Hk = Ham_total_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    eigenvalue, featurevector =np.linalg.eig(Hk)\n", "                    # 拼接为完整矩阵\n", "                    if len(Hk_block_full) == 0:\n", "                        Hk_block_full = Hk\n", "                    else:\n", "                        Hk_block_full = block_direct_sum([Hk_block_full,Hk])#np.block(block_structure)\n", "                    Hk_spin = sqinsquared_TPZ(N,nrep,repr,typee,peri,mtrf,ntrf,capr,p,z,k)\n", "                    spn = transform(nrep,Hk_spin,featurevector)\n", "                    spin = []\n", "                    for spin_i in range(len(spn)):\n", "                        spin.append(1/2*abs(np.sqrt(1+4*spn[spin_i])-1))\n", "                    E1 = eigenvalue.tolist()\n", "                    #print(E1)\n", "                    print(k,p,z,nrep,repr)\n", "                    <PERSON>.extend(eigenvalue.tolist())\n", "                    \n", "                    if len(v_full) == 0:\n", "                        v_full = featurevector\n", "                    else:\n", "                        v_full = block_direct_sum([v_full,featurevector])#np.block(block_structure)\n", "                    # 构造投影矩阵 V_k\n", "                    #V_k = build_projection_matrix_for_k(N, repr, peri,mtrf,ntrf,capr,p,z,k )\n", "                    V_k = build_projection_matrix(N, repr, peri, mtrf, ntrf, capr,typee, p, z, k)\n", "                    #矩阵按列直接拼接\n", "                    if len(new_basis_matrix) == 0:\n", "                        new_basis_matrix = V_k\n", "                    else:\n", "                        new_basis_matrix = np.hstack((new_basis_matrix, V_k))  # 收集新基（线性组合形式）\n", "                    print(new_basis_matrix.shape)\n", "                for i in range(len(E1)):\n", "                    idx = E1.index(np.min(E1))\n", "                    k_min.append(k)\n", "                    p_min.append(p)\n", "                    z_min.append(z)\n", "                    E_min.append(E1[idx])\n", "                    spi_min.append(spin[idx])\n", "                    #print(len(E1))\n", "                    #print(np.min(E1),E1.index(np.min(E1)))\n", "                    E1.pop(idx)\n", "                    spin.pop(idx)   \n", "    return E,k_min,p_min,z_min,E_min,spi_min,v_full,new_basis_matrix,Hk_block_full"]}, {"cell_type": "code", "execution_count": 226, "metadata": {}, "outputs": [], "source": ["H_tot = Ham_total(N,J,h)\n", "eigvals_all, v_full = np.linalg.eig(H_tot)\n", "#eigvals_all= np.linalg.eigvalsh(H_tot)\n"]}, {"cell_type": "code", "execution_count": 227, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[8., 1., 1., ..., 0., 0., 0.],\n", "       [1., 4., 0., ..., 0., 0., 0.],\n", "       [1., 0., 4., ..., 0., 0., 0.],\n", "       ...,\n", "       [0., 0., 0., ..., 4., 0., 1.],\n", "       [0., 0., 0., ..., 0., 4., 1.],\n", "       [0., 0., 0., ..., 1., 1., 8.]])"]}, "execution_count": 227, "metadata": {}, "output_type": "execute_result"}], "source": ["H_tot"]}, {"cell_type": "code", "execution_count": 228, "metadata": {}, "outputs": [], "source": ["#eigvals_all"]}, {"cell_type": "code", "execution_count": 229, "metadata": {}, "outputs": [], "source": ["#min_n =8\n", "#eigvals,k_min,p_min,z_min,E_min,spi_min = fullspectrum(N,min_n,0)"]}, {"cell_type": "code", "execution_count": 230, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 -1 -1 4 [11, 19, 23, 43]\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "(256, 4)\n", "0 -1 1 2 [11, 19]\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "(256, 6)\n", "0 1 -1 12 [0, 1, 3, 5, 7, 9, 11, 17, 19, 21, 27, 37]\n", "255 0\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "(256, 18)\n", "0 1 1 18 [0, 1, 3, 5, 7, 9, 11, 15, 17, 19, 21, 23, 27, 37, 43, 45, 51, 85]\n", "255 0\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "204 51\n", "153 102\n", "102 153\n", "51 204\n", "170 85\n", "85 170\n", "(256, 36)\n", "1 1 -1 16 [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "(256, 52)\n", "1 1 1 14 [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "(256, 66)\n", "2 1 -1 16 [1, 3, 5, 7, 9, 11, 11, 17, 19, 19, 21, 23, 27, 37, 43, 51]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "204 51\n", "153 102\n", "102 153\n", "51 204\n", "(256, 82)\n", "2 1 1 17 [1, 3, 5, 7, 9, 11, 11, 15, 17, 19, 19, 21, 23, 27, 37, 43, 45]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "(256, 99)\n", "3 1 -1 16 [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "(256, 115)\n", "3 1 1 14 [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "(256, 129)\n", "4 -1 -1 12 [1, 5, 7, 11, 17, 19, 21, 23, 27, 37, 43, 85]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "170 85\n", "85 170\n", "(256, 141)\n", "4 -1 1 9 [1, 5, 7, 11, 17, 19, 21, 27, 37]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "(256, 150)\n", "4 1 -1 4 [3, 9, 11, 19]\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "(256, 154)\n", "4 1 1 9 [3, 9, 11, 15, 19, 23, 43, 45, 51]\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "204 51\n", "153 102\n", "102 153\n", "51 204\n", "(256, 163)\n", "5 1 -1 16 [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "(256, 179)\n", "5 1 1 14 [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "(256, 193)\n", "6 1 -1 16 [1, 3, 5, 7, 9, 11, 11, 17, 19, 19, 21, 23, 27, 37, 43, 51]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "204 51\n", "153 102\n", "102 153\n", "51 204\n", "(256, 209)\n", "6 1 1 17 [1, 3, 5, 7, 9, 11, 11, 15, 17, 19, 19, 21, 23, 27, 37, 43, 45]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "238 17\n", "221 34\n", "187 68\n", "119 136\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "(256, 226)\n", "7 1 -1 16 [1, 3, 5, 7, 9, 11, 11, 15, 19, 19, 21, 23, 27, 37, 43, 45]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "240 15\n", "225 30\n", "30 225\n", "15 240\n", "195 60\n", "60 195\n", "135 120\n", "120 135\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "210 45\n", "165 90\n", "105 150\n", "45 210\n", "75 180\n", "180 75\n", "90 165\n", "150 105\n", "(256, 242)\n", "7 1 1 14 [1, 3, 5, 7, 9, 11, 11, 19, 19, 21, 23, 27, 37, 43]\n", "254 1\n", "253 2\n", "251 4\n", "247 8\n", "239 16\n", "127 128\n", "223 32\n", "191 64\n", "252 3\n", "249 6\n", "231 24\n", "243 12\n", "207 48\n", "159 96\n", "126 129\n", "63 192\n", "250 5\n", "245 10\n", "175 80\n", "235 20\n", "215 40\n", "95 160\n", "190 65\n", "125 130\n", "248 7\n", "241 14\n", "143 112\n", "227 28\n", "199 56\n", "31 224\n", "62 193\n", "124 131\n", "246 9\n", "237 18\n", "222 33\n", "219 36\n", "111 144\n", "189 66\n", "183 72\n", "123 132\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "244 11\n", "233 22\n", "211 44\n", "167 88\n", "79 176\n", "158 97\n", "61 194\n", "122 133\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "236 19\n", "217 38\n", "179 76\n", "103 152\n", "206 49\n", "157 98\n", "59 196\n", "118 137\n", "234 21\n", "213 42\n", "186 69\n", "171 84\n", "93 162\n", "117 138\n", "87 168\n", "174 81\n", "232 23\n", "209 46\n", "46 209\n", "163 92\n", "23 232\n", "92 163\n", "71 184\n", "139 116\n", "184 71\n", "116 139\n", "142 113\n", "197 58\n", "113 142\n", "58 197\n", "29 226\n", "226 29\n", "228 27\n", "201 54\n", "57 198\n", "147 108\n", "156 99\n", "114 141\n", "39 216\n", "78 177\n", "218 37\n", "181 74\n", "173 82\n", "107 148\n", "214 41\n", "91 164\n", "182 73\n", "109 146\n", "212 43\n", "169 86\n", "86 169\n", "83 172\n", "43 212\n", "172 83\n", "166 89\n", "149 106\n", "89 166\n", "106 149\n", "77 178\n", "202 53\n", "178 77\n", "53 202\n", "154 101\n", "101 154\n", "(256, 256)\n"]}], "source": ["E_full,k_all,p_min,z_min,E_min,spi_min,v_block_full,U_transform,Hk_blockfull = fullspectrum(N)\n", "    #print(len(k_min),len(eigvals),len(eigvals)*len(k_min))\n", "    #print(np.array(eigvals))\n"]}, {"cell_type": "code", "execution_count": 231, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.+0.j, 0.+0.j, 0.+0.j, ..., 0.+0.j, 0.+0.j, 0.+0.j],\n", "       [0.+0.j, 0.+0.j, 0.+0.j, ..., 0.+0.j, 0.+0.j, 0.+0.j],\n", "       [0.+0.j, 0.+0.j, 0.+0.j, ..., 0.+0.j, 0.+0.j, 0.+0.j],\n", "       ...,\n", "       [0.+0.j, 0.+0.j, 0.+0.j, ..., 0.+0.j, 0.+0.j, 0.+0.j],\n", "       [0.+0.j, 0.+0.j, 0.+0.j, ..., 0.+0.j, 0.+0.j, 0.+0.j],\n", "       [0.+0.j, 0.+0.j, 0.+0.j, ..., 0.+0.j, 0.+0.j, 0.+0.j]])"]}, "execution_count": 231, "metadata": {}, "output_type": "execute_result"}], "source": ["U_transform\n", "\n", "#print(k_all,E_full)"]}, {"cell_type": "code", "execution_count": 232, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["256 256\n"]}], "source": ["print (len(E_full),len(eigvals_all))\n", "for x in E_full:\n", "    index = np.abs(eigvals_all - x).argmin()\n", "    if np.isclose(eigvals_all[index], x) != True:\n", "        print(np.isclose(eigvals_all[index], x), eigvals_all[index], x)"]}, {"cell_type": "code", "execution_count": 233, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(-4.828427124746193+0j),\n", " (-2.0000000000000004+0j),\n", " (2.000000000000002+0j),\n", " (0.8284271247461903+0j),\n", " 0j,\n", " 0j,\n", " (10.054678984251687+0j),\n", " (-6.054678984251689+0j),\n", " (6.993211525330966+0j),\n", " (-2.993211525330974+0j),\n", " (4.397824734759306+0j),\n", " (-0.39782473475931707+0j),\n", " (1.3363572758385975+0j),\n", " (2.6636427241614014+0j),\n", " (-4.828427124746198+0j),\n", " (-2+0j),\n", " (0.82842712474619+0j),\n", " (2.0000000000000004+0j),\n", " (10.251661790966036+0j),\n", " (8.690939214837+0j),\n", " (-10.251661790966056+0j),\n", " (-8.690939214836979+0j),\n", " (5.807099926809219+0j),\n", " (-5.807099926809211+0j),\n", " (4.246377350680174+0j),\n", " (3.599904892545663+0j),\n", " (2.405379547740179+0j),\n", " (2.039182316416632+0j),\n", " (0.8446569716111556+0j),\n", " (-4.24637735068018+0j),\n", " (-3.599904892545659+0j),\n", " (-0.8446569716111554+0j),\n", " (-2.405379547740181+0j),\n", " (-2.039182316416637+0j),\n", " (1.6522404300128315e-16+0j),\n", " (-3.846575371726247e-16+0j),\n", " (8.523945254791348+0j),\n", " (-6.359160854206552+0j),\n", " (5.695518130045151+0j),\n", " (-4.5239452547913395+0j),\n", " (-3.297693395285823+0j),\n", " (3.5307337294603576+0j),\n", " (-1.695518130045145+0j),\n", " (2.8670910052989598+0j),\n", " (2.359160854206544+0j),\n", " (0.4692662705396423+0j),\n", " (1.1329089947010433+0j),\n", " (-0.7023066047141683+0j),\n", " (-5.695518130045156+0j),\n", " (-3.5307337294603562+0j),\n", " (1.695518130045146+0j),\n", " (-0.46926627053964093+0j),\n", " (7.249019570823096-4.699331307822131e-34j),\n", " (-7.249019570823106-7.67276131865175e-34j),\n", " (4.703502409677451-6.048457152893432e-33j),\n", " (-4.703502409677435+3.0363969749970474e-33j),\n", " (3.1427798335484076-2.9981318015220197e-33j),\n", " (3.002642220142924+2.2083681130492453e-33j),\n", " (-3.0026422201429215+1.985023563650047e-33j),\n", " (-3.1427798335484067+1.2117490457798255e-33j),\n", " (1.4419196440138968+7.493152640204773e-33j),\n", " (-1.4419196440138962+2.3733231857531586e-33j),\n", " (0.5972626724027398-1.0039641989893632e-32j),\n", " (-0.5972626724027404+6.637658550051805e-33j),\n", " (7.75117187867706e-18+4.420295814384148e-17j),\n", " (7.751171878677048e-18-4.4202958143841474e-17j),\n", " (7.226251859505526+0j),\n", " (-7.226251859505512+0j),\n", " (4.828427124746191+0j),\n", " (4.1647844005847965+0j),\n", " (3.2262518595055023+0j),\n", " (-4.164784400584787+0j),\n", " (-3.226251859505504+0j),\n", " (-0.82842712474619+0j),\n", " (0.16478440058478816+0j),\n", " (-0.1647844005847882+0j),\n", " (-4.828427124746188+0j),\n", " (-2.0000000000000004+0j),\n", " (0.8284271247461898+0j),\n", " (2+0j),\n", " (2.0000000000000018+0j),\n", " (-2.0000000000000004+0j),\n", " (7.249019570823091+0j),\n", " (6.145422053691323+0j),\n", " (-7.249019570823105+0j),\n", " (-6.1454220536913375+0j),\n", " (4.1062397372747+0j),\n", " (-4.106239737274698+0j),\n", " (3.0026422201429264+0j),\n", " (2.5455171611456713+0j),\n", " (-3.0026422201429166+0j),\n", " (-2.5455171611456686+0j),\n", " (1.7008601895345135+0j),\n", " (1.4419196440138966+0j),\n", " (0.5972626724027406+0j),\n", " (-1.700860189534513+0j),\n", " (-1.4419196440138966+0j),\n", " (-0.5972626724027412+0j),\n", " (-1.0824927746614993e-16+0j),\n", " (-8.523945254791332+0j),\n", " (6.359160854206558+0j),\n", " (5.695518130045155+0j),\n", " (4.523945254791335+0j),\n", " (3.5307337294603607+0j),\n", " (3.2976933952858265+0j),\n", " (-2.8670910052989527+0j),\n", " (-2.359160854206552+0j),\n", " (-1.6955181300451474+0j),\n", " (-1.132908994701043+0j),\n", " (0.7023066047141692+0j),\n", " (0.4692662705396406+0j),\n", " (-5.6955181300451585+0j),\n", " (-3.53073372946036+0j),\n", " (1.695518130045147+0j),\n", " (-0.469266270539641+0j),\n", " (6.145422053691323-5.135374035474804e-33j),\n", " (5.548159381288585-8.611399211373712e-33j),\n", " (-6.145422053691318+5.962998680071722e-33j),\n", " (-5.548159381288587-1.032997260821395e-32j),\n", " (4.106239737274694+2.1841329268833967e-33j),\n", " (-4.106239737274693-3.643466380409299e-33j),\n", " (2.5455171611456695+1.8859036149221023e-32j),\n", " (1.7008601895345157-9.662282773466978e-33j),\n", " (1.103597517131773-2.0342054348566272e-32j),\n", " (-2.5455171611456695-2.91741631168028e-33j),\n", " (-1.1035975171317707-2.5609602143797533e-32j),\n", " (-1.700860189534514+6.860105569473842e-33j),\n", " (3.127939866977113e-16+7.505238978593931e-17j),\n", " (3.127939866977116e-16-7.505238978593926e-17j),\n", " (-10.05467898425171+0j),\n", " (-6.993211525330991+0j),\n", " (6.054678984251701+0j),\n", " (4.828427124746195+0j),\n", " (2.9932115253309783+0j),\n", " (-4.397824734759311+0j),\n", " (-2.6636427241614005+0j),\n", " (0.39782473475931507+0j),\n", " (-0.8284271247461904+0j),\n", " (-1.3363572758385978+0j),\n", " (1.9999999999999987+0j),\n", " (-1.9999999999999991+0j),\n", " (5.548159381288606+0j),\n", " (4.7035024096774345+0j),\n", " (3.142779833548407+0j),\n", " (1.1035975171317731+0j),\n", " (-5.548159381288589+0j),\n", " (-1.1035975171317727+0j),\n", " (-4.703502409677433+0j),\n", " (-3.142779833548408+0j),\n", " (-4.0947035202716856e-17+0j),\n", " (4.82842712474619+0j),\n", " (-0.8284271247461904+0j),\n", " (2+0j),\n", " (-2.0000000000000004+0j),\n", " (5.548159381288594+0j),\n", " (4.703502409677444+0j),\n", " (3.142779833548408+0j),\n", " (1.1035975171317731+0j),\n", " (-1.1035975171317727+0j),\n", " (-5.5481593812885865+0j),\n", " (-3.142779833548412+0j),\n", " (-4.703502409677434+0j),\n", " (1.6056895134860989e-16+0j),\n", " (-8.52394525479136+0j),\n", " (6.359160854206542+0j),\n", " (5.69551813004514+0j),\n", " (4.523945254791333+0j),\n", " (3.530733729460355+0j),\n", " (3.2976933952858287+0j),\n", " (-2.867091005298953+0j),\n", " (-2.3591608542065456+0j),\n", " (-1.6955181300451467+0j),\n", " (-1.132908994701043+0j),\n", " (0.7023066047141698+0j),\n", " (0.4692662705396392+0j),\n", " (-5.695518130045146+0j),\n", " (-3.53073372946036+0j),\n", " (1.6955181300451487+0j),\n", " (-0.4692662705396413+0j),\n", " (6.14542205369132+0j),\n", " (5.5481593812885786+0j),\n", " (-6.145422053691337+0j),\n", " (-5.548159381288593+0j),\n", " (4.106239737274689+0j),\n", " (-4.106239737274697+0j),\n", " (2.5455171611456673+0j),\n", " (1.7008601895345137+0j),\n", " (1.1035975171317725+0j),\n", " (-2.545517161145671+0j),\n", " (-1.1035975171317725+0j),\n", " (-1.700860189534513+0j),\n", " (-9.069113185441565e-16+0j),\n", " (3.530591941969529e-16+0j),\n", " (7.226251859505515+0j),\n", " (-7.226251859505507+0j),\n", " (4.8284271247462+0j),\n", " (4.1647844005847965+0j),\n", " (3.2262518595055028+0j),\n", " (-4.164784400584784+0j),\n", " (-3.2262518595055076+0j),\n", " (-0.8284271247461875+0j),\n", " (0.1647844005847891+0j),\n", " (-0.1647844005847877+0j),\n", " (-4.828427124746197+0j),\n", " (0.8284271247461908+0j),\n", " (-1.9999999999999982+0j),\n", " (-2+0j),\n", " (1.9999999999999996+0j),\n", " (2.0000000000000013+0j),\n", " (7.249019570823087+0j),\n", " (6.145422053691319+0j),\n", " (-7.249019570823109+0j),\n", " (-6.145422053691328+0j),\n", " (4.106239737274687+0j),\n", " (-4.106239737274689+0j),\n", " (3.0026422201429206+0j),\n", " (2.545517161145664+0j),\n", " (-3.0026422201429206+0j),\n", " (-2.5455171611456713+0j),\n", " (1.7008601895345115+0j),\n", " (1.4419196440138928+0j),\n", " (0.5972626724027397+0j),\n", " (-1.700860189534515+0j),\n", " (-1.441919644013896+0j),\n", " (-0.5972626724027399+0j),\n", " (-2.37537992867442e-16+0j),\n", " (8.523945254791373+0j),\n", " (5.695518130045143+0j),\n", " (-6.359160854206539+0j),\n", " (-4.523945254791323+0j),\n", " (-3.29769339528583+0j),\n", " (3.5307337294603514+0j),\n", " (2.8670910052989544+0j),\n", " (2.3591608542065536+0j),\n", " (-1.6955181300451447+0j),\n", " (1.132908994701041+0j),\n", " (0.4692662705396388+0j),\n", " (-0.7023066047141717+0j),\n", " (-5.695518130045163+0j),\n", " (-3.5307337294603562+0j),\n", " (1.695518130045146+0j),\n", " (-0.46926627053964004+0j),\n", " (7.249019570823098+0j),\n", " (-7.249019570823124+0j),\n", " (4.70350240967744+0j),\n", " (-4.703502409677453+0j),\n", " (3.142779833548406+0j),\n", " (3.0026422201429175+0j),\n", " (-3.0026422201429197+0j),\n", " (-3.142779833548408+0j),\n", " (1.441919644013893+0j),\n", " (-1.441919644013898+0j),\n", " (0.5972626724027413+0j),\n", " (-0.5972626724027399+0j),\n", " (3.4414971352120937e-16+0j),\n", " (-3.5089981038800905e-16+0j)]"]}, "execution_count": 233, "metadata": {}, "output_type": "execute_result"}], "source": ["E_full"]}, {"cell_type": "code", "execution_count": 234, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.02516618e+01+0.00000000e+00j,  1.00546790e+01+0.00000000e+00j,\n", "       -1.02516618e+01+0.00000000e+00j,  8.69093921e+00+0.00000000e+00j,\n", "       -8.69093921e+00+0.00000000e+00j, -1.00546790e+01+0.00000000e+00j,\n", "       -8.52394525e+00+0.00000000e+00j,  6.99321153e+00+0.00000000e+00j,\n", "        5.80709993e+00+0.00000000e+00j,  7.24901957e+00+0.00000000e+00j,\n", "        7.22625186e+00+0.00000000e+00j,  8.52394525e+00+0.00000000e+00j,\n", "        8.52394525e+00+0.00000000e+00j, -6.05467898e+00+0.00000000e+00j,\n", "       -5.80709993e+00+0.00000000e+00j, -7.24901957e+00+0.00000000e+00j,\n", "       -7.22625186e+00+0.00000000e+00j, -6.99321153e+00+0.00000000e+00j,\n", "       -8.52394525e+00+0.00000000e+00j, -7.22625186e+00+0.00000000e+00j,\n", "       -6.14542205e+00+0.00000000e+00j, -6.35916085e+00+0.00000000e+00j,\n", "       -4.24637735e+00+0.00000000e+00j, -6.35916085e+00+0.00000000e+00j,\n", "       -7.24901957e+00+0.00000000e+00j, -7.24901957e+00+0.00000000e+00j,\n", "       -5.69551813e+00+0.00000000e+00j,  7.22625186e+00+0.00000000e+00j,\n", "        6.05467898e+00+0.00000000e+00j,  4.24637735e+00+0.00000000e+00j,\n", "        4.39782473e+00+0.00000000e+00j,  7.24901957e+00+0.00000000e+00j,\n", "        7.24901957e+00+0.00000000e+00j,  6.35916085e+00+0.00000000e+00j,\n", "        6.35916085e+00+0.00000000e+00j,  6.14542205e+00+0.00000000e+00j,\n", "        6.14542205e+00+0.00000000e+00j,  5.69551813e+00+0.00000000e+00j,\n", "        5.54815938e+00+0.00000000e+00j,  3.59990489e+00+0.00000000e+00j,\n", "        4.52394525e+00+0.00000000e+00j,  4.82842712e+00+0.00000000e+00j,\n", "        7.24901957e+00+0.00000000e+00j,  4.16478440e+00+0.00000000e+00j,\n", "        2.66364272e+00+0.00000000e+00j,  6.14542205e+00+0.00000000e+00j,\n", "        5.69551813e+00+0.00000000e+00j,  5.54815938e+00+0.00000000e+00j,\n", "        4.52394525e+00+0.00000000e+00j,  4.82842712e+00+0.00000000e+00j,\n", "       -3.59990489e+00+0.00000000e+00j, -7.24901957e+00+0.00000000e+00j,\n", "       -4.39782473e+00+0.00000000e+00j, -4.16478440e+00+0.00000000e+00j,\n", "       -4.82842712e+00+0.00000000e+00j, -6.14542205e+00+0.00000000e+00j,\n", "       -6.14542205e+00+0.00000000e+00j, -5.69551813e+00+0.00000000e+00j,\n", "       -5.54815938e+00+0.00000000e+00j, -5.54815938e+00+0.00000000e+00j,\n", "       -4.52394525e+00+0.00000000e+00j, -4.52394525e+00+0.00000000e+00j,\n", "       -2.40537955e+00+0.00000000e+00j, -2.99321153e+00+0.00000000e+00j,\n", "       -4.82842712e+00+0.00000000e+00j,  2.40537955e+00+0.00000000e+00j,\n", "        2.03918232e+00+0.00000000e+00j,  4.70350241e+00+0.00000000e+00j,\n", "        4.16478440e+00+0.00000000e+00j,  2.99321153e+00+0.00000000e+00j,\n", "        6.14542205e+00+0.00000000e+00j,  5.69551813e+00+0.00000000e+00j,\n", "        5.54815938e+00+0.00000000e+00j, -5.54815938e+00+0.00000000e+00j,\n", "       -2.03918232e+00+0.00000000e+00j, -4.16478440e+00+0.00000000e+00j,\n", "       -4.70350241e+00+0.00000000e+00j, -5.69551813e+00+0.00000000e+00j,\n", "        3.29769340e+00+0.00000000e+00j,  4.10623974e+00+0.00000000e+00j,\n", "        4.70350241e+00+0.00000000e+00j,  4.82842712e+00+0.00000000e+00j,\n", "       -6.14542205e+00+0.00000000e+00j,  1.33635728e+00+0.00000000e+00j,\n", "        3.22625186e+00+0.00000000e+00j,  2.86709101e+00+0.00000000e+00j,\n", "       -2.86709101e+00+0.00000000e+00j, -3.29769340e+00+0.00000000e+00j,\n", "       -3.22625186e+00+0.00000000e+00j, -4.82842712e+00+0.00000000e+00j,\n", "       -4.70350241e+00+0.00000000e+00j,  5.69551813e+00+0.00000000e+00j,\n", "        5.54815938e+00+0.00000000e+00j, -2.66364272e+00+0.00000000e+00j,\n", "       -2.35916085e+00+0.00000000e+00j,  3.53073373e+00+0.00000000e+00j,\n", "        2.86709101e+00+0.00000000e+00j,  3.29769340e+00+0.00000000e+00j,\n", "        3.14277983e+00+0.00000000e+00j,  3.22625186e+00+0.00000000e+00j,\n", "       -3.53073373e+00+0.00000000e+00j, -3.29769340e+00+0.00000000e+00j,\n", "       -3.22625186e+00+0.00000000e+00j, -5.69551813e+00+0.00000000e+00j,\n", "       -5.54815938e+00+0.00000000e+00j,  4.70350241e+00+0.00000000e+00j,\n", "        4.10623974e+00+0.00000000e+00j, -4.82842712e+00+0.00000000e+00j,\n", "       -4.70350241e+00+0.00000000e+00j,  3.53073373e+00+0.00000000e+00j,\n", "       -4.10623974e+00+0.00000000e+00j,  2.35916085e+00+0.00000000e+00j,\n", "        2.35916085e+00+0.00000000e+00j, -4.10623974e+00+0.00000000e+00j,\n", "       -4.10623974e+00+0.00000000e+00j,  8.44656972e-01+0.00000000e+00j,\n", "       -3.97824735e-01+0.00000000e+00j, -8.44656972e-01+0.00000000e+00j,\n", "        4.82842712e+00+0.00000000e+00j,  3.14277983e+00+0.00000000e+00j,\n", "       -1.33635728e+00+0.00000000e+00j, -2.86709101e+00+0.00000000e+00j,\n", "       -2.35916085e+00+0.00000000e+00j, -3.53073373e+00+0.00000000e+00j,\n", "       -4.70350241e+00+0.00000000e+00j, -3.14277983e+00+0.00000000e+00j,\n", "        4.70350241e+00+0.00000000e+00j, -2.54551716e+00+0.00000000e+00j,\n", "        1.13290899e+00+0.00000000e+00j,  3.53073373e+00+0.00000000e+00j,\n", "        3.97824735e-01+0.00000000e+00j,  7.02306605e-01+0.00000000e+00j,\n", "        4.10623974e+00+0.00000000e+00j,  4.10623974e+00+0.00000000e+00j,\n", "        3.00264222e+00+0.00000000e+00j,  3.00264222e+00+0.00000000e+00j,\n", "       -4.10623974e+00+0.00000000e+00j,  3.53073373e+00+0.00000000e+00j,\n", "        3.14277983e+00+0.00000000e+00j,  2.54551716e+00+0.00000000e+00j,\n", "        1.13290899e+00+0.00000000e+00j, -3.14277983e+00+0.00000000e+00j,\n", "       -3.53073373e+00+0.00000000e+00j, -3.53073373e+00+0.00000000e+00j,\n", "       -2.54551716e+00+0.00000000e+00j, -1.13290899e+00+0.00000000e+00j,\n", "       -1.13290899e+00+0.00000000e+00j,  1.64784401e-01+0.00000000e+00j,\n", "        1.64784401e-01+0.00000000e+00j, -1.64784401e-01+2.66951063e-16j,\n", "       -1.64784401e-01-2.66951063e-16j,  7.02306605e-01+0.00000000e+00j,\n", "       -7.02306605e-01+0.00000000e+00j, -7.02306605e-01+0.00000000e+00j,\n", "       -3.00264222e+00+0.00000000e+00j, -3.00264222e+00+0.00000000e+00j,\n", "       -3.14277983e+00+0.00000000e+00j,  2.54551716e+00+0.00000000e+00j,\n", "        3.00264222e+00+0.00000000e+00j,  3.14277983e+00+0.00000000e+00j,\n", "        1.44191964e+00+0.00000000e+00j,  1.69551813e+00+0.00000000e+00j,\n", "        1.70086019e+00+0.00000000e+00j,  1.10359752e+00+0.00000000e+00j,\n", "        2.54551716e+00+0.00000000e+00j, -2.54551716e+00+0.00000000e+00j,\n", "       -3.00264222e+00+0.00000000e+00j, -1.69551813e+00+0.00000000e+00j,\n", "       -1.70086019e+00+0.00000000e+00j, -1.44191964e+00+0.00000000e+00j,\n", "       -1.44191964e+00+0.00000000e+00j, -2.00000000e+00+0.00000000e+00j,\n", "       -2.00000000e+00+0.00000000e+00j, -2.00000000e+00+0.00000000e+00j,\n", "       -3.00264222e+00+0.00000000e+00j,  2.54551716e+00+0.00000000e+00j,\n", "        1.44191964e+00+0.00000000e+00j,  1.69551813e+00+0.00000000e+00j,\n", "        1.70086019e+00+0.00000000e+00j,  1.10359752e+00+0.00000000e+00j,\n", "       -5.97262672e-01+0.00000000e+00j, -4.69266271e-01+0.00000000e+00j,\n", "       -1.70086019e+00+0.00000000e+00j, -1.69551813e+00+0.00000000e+00j,\n", "        8.28427125e-01+0.00000000e+00j,  8.28427125e-01+0.00000000e+00j,\n", "       -1.10359752e+00+0.00000000e+00j, -1.10359752e+00+0.00000000e+00j,\n", "       -8.28427125e-01+0.00000000e+00j, -8.28427125e-01+0.00000000e+00j,\n", "        2.00000000e+00+0.00000000e+00j,  2.00000000e+00+0.00000000e+00j,\n", "        2.00000000e+00+0.00000000e+00j,  5.97262672e-01+0.00000000e+00j,\n", "        5.97262672e-01+0.00000000e+00j,  5.97262672e-01+0.00000000e+00j,\n", "        3.00264222e+00+0.00000000e+00j,  1.44191964e+00+0.00000000e+00j,\n", "       -5.97262672e-01+0.00000000e+00j, -3.14277983e+00+0.00000000e+00j,\n", "       -2.54551716e+00+0.00000000e+00j, -1.44191964e+00+0.00000000e+00j,\n", "        1.69551813e+00+0.00000000e+00j,  1.70086019e+00+0.00000000e+00j,\n", "        4.69266271e-01+0.00000000e+00j,  1.10359752e+00+0.00000000e+00j,\n", "       -8.28427125e-01+0.00000000e+00j, -1.69551813e+00+0.00000000e+00j,\n", "       -1.70086019e+00+0.00000000e+00j,  4.69266271e-01+0.00000000e+00j,\n", "       -4.69266271e-01+0.00000000e+00j, -4.69266271e-01+0.00000000e+00j,\n", "       -1.10359752e+00+0.00000000e+00j,  8.28427125e-01+0.00000000e+00j,\n", "       -5.97262672e-01+0.00000000e+00j,  4.69266271e-01+0.00000000e+00j,\n", "        4.69266271e-01+0.00000000e+00j, -2.00000000e+00+0.00000000e+00j,\n", "       -1.69551813e+00+0.00000000e+00j,  2.00000000e+00+0.00000000e+00j,\n", "        1.44191964e+00+0.00000000e+00j,  1.70086019e+00+0.00000000e+00j,\n", "        1.69551813e+00+0.00000000e+00j,  1.10359752e+00+0.00000000e+00j,\n", "       -2.00000000e+00+0.00000000e+00j, -1.44191964e+00+0.00000000e+00j,\n", "       -1.70086019e+00+0.00000000e+00j,  8.28427125e-01+0.00000000e+00j,\n", "        5.97262672e-01+0.00000000e+00j, -1.10359752e+00+0.00000000e+00j,\n", "       -8.28427125e-01+0.00000000e+00j, -4.69266271e-01+0.00000000e+00j,\n", "       -5.97262672e-01+0.00000000e+00j,  2.00000000e+00+0.00000000e+00j,\n", "       -1.20010002e-15+0.00000000e+00j,  1.18450325e-15+0.00000000e+00j,\n", "        5.77198509e-16+6.74113494e-16j,  5.77198509e-16-6.74113494e-16j,\n", "       -4.09585959e-16+6.73379680e-16j, -4.09585959e-16-6.73379680e-16j,\n", "        1.99914975e-16+6.15261445e-16j,  1.99914975e-16-6.15261445e-16j,\n", "       -4.72127515e-16+2.50752486e-16j, -4.72127515e-16-2.50752486e-16j,\n", "       -2.96504137e-16+0.00000000e+00j, -1.33468385e-16+0.00000000e+00j,\n", "       -5.70427626e-17+0.00000000e+00j,  1.42097474e-16+0.00000000e+00j,\n", "        9.18810436e-16+0.00000000e+00j,  6.78020024e-16+0.00000000e+00j,\n", "        2.00000000e+00+0.00000000e+00j,  2.00000000e+00+0.00000000e+00j,\n", "        2.00000000e+00+0.00000000e+00j, -2.00000000e+00+0.00000000e+00j,\n", "       -2.00000000e+00+0.00000000e+00j, -2.00000000e+00+0.00000000e+00j])"]}, "execution_count": 234, "metadata": {}, "output_type": "execute_result"}], "source": ["eigvals_all"]}, {"cell_type": "code", "execution_count": 235, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.12/site-packages/matplotlib/cbook.py:1762: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return math.isfinite(val)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/matplotlib/collections.py:197: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  offsets = np.asanyarray(offsets, float)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(eigvals_all)), np.sort(eigvals_all), label='full',s = 50)\n", "plt.scatter(range(len(E_full)), np.sort(E_full), label='block',s=10,marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 236, "metadata": {}, "outputs": [{"data": {"text/plain": ["'file = open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') \\nlines = [\"\"] \\nwith open(\\'ED_TPZ_1D_N=16.txt\\', \\'w\\') as file: \\n    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\\n    for i in range(len(k_min)):\\n        if i%(min_n)==0:\\n            file.write(\"----------------------------------------\"+\"\\n\")\\n        file.write(str(k_min[i])+\"\\t\")\\n        file.write(str(p_min[i])+\"\\t\")\\n        file.write(str(z_min[i])+\"\\t\")\\n        file.write(str(E_min[i])+\"\\t\")\\n        file.write(str(spi_min[i])+\"\\t\")\\n        file.write(\"\\n\")\\nfile.close()'"]}, "execution_count": 236, "metadata": {}, "output_type": "execute_result"}], "source": ["'''file = open('ED_TPZ_1D_N=16.txt', 'w') \n", "lines = [\"\"] \n", "with open('ED_TPZ_1D_N=16.txt', 'w') as file: \n", "    file.write(\"k\\t\"+\"p\\t\"+\"z\\t\"+\"E\\t\"+\"S\\n\")\n", "    for i in range(len(k_min)):\n", "        if i%(min_n)==0:\n", "            file.write(\"----------------------------------------\"+\"\\n\")\n", "        file.write(str(k_min[i])+\"\\t\")\n", "        file.write(str(p_min[i])+\"\\t\")\n", "        file.write(str(z_min[i])+\"\\t\")\n", "        file.write(str(E_min[i])+\"\\t\")\n", "        file.write(str(spi_min[i])+\"\\t\")\n", "        file.write(\"\\n\")\n", "file.close()'''"]}, {"cell_type": "code", "execution_count": 237, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[54, 253, 190, 184, 246, 246, 1, 13, 7, 63, 30, 116, 83, 44, 54, 255, 184, 250, 0, 3, 2, 4, 8, 14, 29, 39, 65, 66, 115, 22, 50, 117, 62, 74, 247, 244, 11, 23, 37, 60, 87, 129, 218, 85, 112, 204, 140, 152, 57, 123, 222, 181, 42, 24, 80, 76, 98, 158, 154, 156, 160, 169, 228, 232, 246, 246, 10, 16, 81, 68, 84, 53, 88, 188, 147, 149, 54, 253, 184, 250, 251, 253, 42, 45, 24, 82, 79, 113, 158, 157, 154, 165, 203, 160, 228, 226, 169, 214, 245, 18, 33, 37, 48, 129, 78, 86, 94, 218, 145, 131, 216, 57, 123, 222, 181, 45, 47, 55, 59, 79, 113, 157, 203, 223, 165, 187, 226, 247, 247, 5, 17, 28, 81, 69, 52, 93, 130, 188, 120, 233, 255, 72, 67, 98, 223, 59, 212, 76, 156, 246, 81, 188, 250, 253, 38, 80, 159, 223, 212, 59, 156, 76, 247, 6, 33, 37, 48, 129, 78, 86, 122, 218, 145, 151, 216, 77, 123, 222, 181, 45, 47, 82, 59, 79, 113, 139, 203, 223, 127, 187, 226, 234, 247, 10, 16, 41, 68, 84, 53, 88, 188, 148, 149, 54, 227, 255, 255, 191, 251, 42, 35, 24, 55, 79, 113, 135, 139, 154, 127, 221, 160, 193, 226, 169, 232, 244, 12, 37, 23, 60, 101, 129, 85, 112, 218, 140, 216, 153, 57, 123, 222, 181, 42, 15, 67, 108, 98, 134, 154, 156, 160, 170, 228, 232, 247, 244]\n", "归一化后绝对值误差：1.07e+00\n", "最小内积绝对值：5.88e-05\n"]}], "source": ["# 1. 直接对角化全空间Floquet算子 F\n", "#evals_direct, evecs_direct = np.linalg.eig(F)\n", "# 2. 按本征值匹配 full_eigenvectors 与 evecs_direct 的列\n", "matched_indices = []\n", "for eval_block in E_full:  # energies是分块对角化得到的本征值（Hk的本征值）\n", "    # 找到直接对角化中最接近的本征值索引\n", "    idx = np.argmin(np.abs(eigvals_all - eval_block))\n", "    matched_indices.append(idx)\n", "# 3. 按匹配顺序重新排列直接对角化的本征矢\n", "print(matched_indices)\n", "evecs_direct_matched = v_full[:, matched_indices]\n", "# 4. 验证一致性（忽略相位，对比绝对值或内积）\n", "# 方法1：归一化后对比绝对值\n", "full_evecs_norm = U_transform @ v_block_full \n", "direct_evecs_norm = evecs_direct_matched \n", "abs_error = np.max(np.abs(full_evecs_norm - direct_evecs_norm))\n", "print(f\"归一化后绝对值误差：{abs_error:.2e}\")  # 正常应<1e-6\n", "\n", "# 方法2：计算内积绝对值（应为1，说明是同一本征矢）\n", "inner_products = [np.abs(np.vdot(full_evecs_norm[:, i], direct_evecs_norm[:, i])) \n", "                  for i in range(2**N)]\n", "print(f\"最小内积绝对值：{min(inner_products):.2e}\")  # 正常应>0.999"]}, {"cell_type": "code", "execution_count": 238, "metadata": {}, "outputs": [], "source": ["ipr1 = np.multiply(np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)), \n", "                       np.multiply(np.abs(full_evecs_norm), np.abs(full_evecs_norm)))\n", "IPR_block_full = np.sum(ipr1, axis=0)\n", "ipr2 = np.multiply(np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)), \n", "                       np.multiply(np.abs(direct_evecs_norm), np.abs(direct_evecs_norm)))\n", "IPR_full = np.sum(ipr2, axis=0)"]}, {"cell_type": "code", "execution_count": 239, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}, {"data": {"image/png": "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**************************+L2E1oSRi+ZmQwYg2om2rAIs2PXskPa2NOlECDXPU1nREW+0Xbh4JALj4uGKc+fD6sPdVohfQnGzRXyz7gMWirOAx7FraKkeByMrSRC0/EzYdvBLRFtsCX/DFB5mePZqDbsSJooxouOT4Hhh0aJ6uaaatMfSBplgP9ym1yJaCiWf0wZJPQ0f0vhK9gG4rrXkipjGPdR+w9i4reAy7nmDlKBBZWZrI5WdC1owoYpnrIJqD3plOlGjbtcN9Sn1oxLG4860fwk4OFA8zGneEeJ9sMRY6og9Ye5YVPIaJJZKyFJ7/b0tXLD8TOhgBYpfroK2e1lqd8USJtl073M/9d3tN2BE9C2gvtyjBlmrBdaf1xjubdmNfo0t9L5we+l1RR/QBA9q3rIhmdBoZL5ocHrHoI9QVy8+ED0ZiJdTTj1ZnPVGibdcO/6kz/G3NTkvGvBHHJHwBHagzcW56Mi47vgfO7VcUN8mN2ltH9AGLhXD6GlDnEW0Oj9j0Eep65WfC9hnpCMrTT1GQtmZAfhLqjOO/w5lyPVC7drjt4ZE8YVqTTDivX1HYy3dFwaYir21swdIvtsNxwJXQN7FY9wGLlVB9DajzCHb9KR1Q1262B/3swF45yE23BH1f20cokctP1ozEmO/TT2fIwBoO7dNmMBcfV+y37uG2h59ySF5YVesAUFnnVPumJOLMqV0hh0BHMHK+G+q6Dub6U2pTtM2pWoFqxhO1/GQw0gHiYQ6OQIb2L8bEM/rgmU/LAr6/5NMynNAzx+9pM9z28FkX9cOkEMGOVlV9c8LOnNoZkmTFy3wX8XqtdYR4OYadTbTXXzhDdAP1EUrU8pPBCAXlFiW8+7/g1Y9A8CeCYE+p2qyDQ/sXY9q5h2PBh7+1uS7bq5uw8MNfE3KWZaOTZMXzfBda4dyMu+oNu6scQyNEc/2FM0Q3Nz0Zn/z1LFiS/HtLJGL5yWCE/CgF8he/7z2oJ/Jwsg5OOftwrPxmJyrrAv+OAKAwy4qV35Qn7CzLRibJivf5LhTh3Iy76g27qxxDo0R6/blFqc2MqACwr7EFG3bUBq3JS7Tykx1YSWftZjtOf/AjjHn2azy5fltYn/ni973416Zd+GpbjS4JWjidvswmAbMv7gcB/v3Dlb/HnNQz6MWm5Ttev6swKklWtInvOptwzsOD6aDYmXWVY2ikSK4/pfy8b/XWsL7735vtfuWmItHKz4QNRtyihK+21QS8iSaqYCd/W55cvw23vboJY579Gqc/+BHWbrZHVAgGG3WkjDTqnZ8e0fp05pTH0VA6BQPBC5xYDFmNNFlTZxTOeTj73Z8w+93gy0gA7nrzR3zxe3XclRNd4RgaLdzrb92WyojLzxe/2qErNxWJWH4mZDONodWxzQ7A2QDYevi/59gFWDPk/29rmRRbu65WJJONhaJE7VPPPTzkRZmBJkiOGrWJR9tGWle1HTnZuRh4ZG+YTQK+2lYT0Tp0pky2IYVzLniOsxFJsqLqqxLBNnWEtm7GGWgC6mpQCf+q8iLUoBGpqEca9h9owVXP/Te6csLAfdIVjmFn0Nb1d16/Ipz+4EdRl5++TWaRdpoNlbMmXsrPhAtGDG0/bXYAK0YCjXuBcasBW4naP6NuTxnO/PI6WDPzIAgCcGCfuozKUQEsHw6kdwPGvtmuBUIkE/uFogx1W/bF9qDLZKIJL1jmIw91+HlPP8DTZmo2CRiUfwD4v/HyNvaRt7GjMmx2qADngirIce7oJFkR91WJYptiLdTNWHsejnbNhF0TkBSjBq9a7kMNsnCt6y7UIw1AFOWEwfukKxzDzqKtG/7BlJ++Q4SjCSKDjSSLl/IzoZppDG8/dTbIF3ntdmD5cHz8zfc4/cGPMP3Z1Thq7RikNJSj0r4TB/ZXqsvAUSF/VikIarfL3+FsaNdVa8+qOQnA/gMtQd9PxwHkoQ69TFU488vr2txGpZo0nFuutrki2qa4DmnC8zkXwj3OHZkkK+K+KlFuUyyFuhlrz8NXLfehGPITpBKI9DJVIQ91SMcB9TMRlxMG75OOOoaBrplOff1FKdj11x7lp7a2oz07rUdbfnY0QZKkznOkg6irq4PNZoPD4UBWVlbU3/PVthqMefbrNpdbOeGUkLkKDmr4n+ai3iEWYFrLZCxIXoRepirsEAswxjUTAPBh3kNIa9wJ5PQGLlsCvD1RLghyevs/sbSDcPfNlLMOAwA8uf73NpfNTk2G40BLwOCvO2rwWsr9KMGesLcxknHy0TbFdWgTnraA76DjHCmlJhEInIDJr4agk22TW5Rw+oMfBZ0jSnseBroefWtMtNoqJ1QG75NYH8NA10x2WjIAYH+T96Gk011/7Sjc8jMcj48+Hhce2z3keavUYnz+t7PDvvcYlWck3Pt3QgUj/9q0C7e9uqnN5R4ffTwuOT5Aeyna52Jx1+6E/fFz5Buxxw6xANe77kADUrEHeTg2qwHvpD8AoXa794PZPYHr/h2TgqutQlt78n9Tti+sC2/auUdg4Ye/AghcCC4bUYw/f3WdXMgp2iicw8kgGKwpLmjh6xHt5w6KtuBXdJJARBHxOR/rbYqwT0NbN+NlI4px0ifXyMG/R1uBCBC6nPBfL2OPc6yOYTiJvRSd8vprJ+GUnznpydjXGLzGWKEEuREHkWGuZ0dnYGUwEsDB1oy018Xy1bYaPPzci3jLOlt9bazzLkxPfkPXfv3eJck45v2/eD/Y7Sjg+g9i1lYb7snf1oUHAEVZVnxx1zlYt6UydCFY/l9g6RDvB8d/APQ8OeptUNYtWPtosCeKaD/XLtp5H8RCxLWBsdqmKPs0tHkz9lnfEc7Z+F46IuSqhF0zojD4OLf3MWzrmgmkva6/zpicrq3y86krT8B9q7eGLDdz05Px9Yxz1URo8VpTpMVgJIBInv4DZWZsr5vV+19+h6PWjkEvU5X6WoWYB0BAiakaO8QC3Oqagldyn0V6k/dpDaYkYPz7QMmJ4W1wFMI9+YNdeIrstGTM98wUGbTgiMHTYrQBZ7ife/n6k2EyCe1XCMZBzUjEYrlNjl3A8gv8mwx8mxbGrfGrOYnkPNyJQlzRfE/AmpGoAtN4O85hrO/BNE1Ee/3NHN4XxbZU3Le6c96g2yo/2yo3fZcH4j8rMIORIKKt+mq3m5WjAs3PDkNKQ7lfG7U2IGmRTEgWRDkAEVu9/+2AAizck3/tZjvueutHXbuwos3aohi1o0fbFBfu57JTk3Wdcw+qEOxk/SvaRUdsU4DfkN6eCKF2OxrTSvHz0JU4vn//8ArsEOsbqKkmqiryeDvOYa5vuNdMINFef8F0pqactsrPUH03gM61Le0h3Pt3Qo2mAbzjxYMliAl28MPtLX3zK99jzLNf+yUBA+B5qhuOlIZyVKAQY1wz8b10BEa7ZmKHWIASUw3MaEWrEogA3gBk/Fr5v2ov913R7YAwhDNiwy1KyLQmI1goG3LUgWc/6Aq3nifL/z3IbYy2F3q4n/MdJWR3NGPSiu+x5ofd4a2gIob7wDAx3CbdCIvqVLiv+T/vdy4dAsETPJy7706MfKXcL4lUJOv78aBlqEChOsqmCN48DW2VEx25T2IigvU9mHwU0V5/wXSmbLJtlZ9D+xfjk7+ehdx0S8DPd6Zt6UgJF4wA8snw+d/OxsoJp+Dx0cdj5YRT8Pnfzg5ZwER7s9Klk7ZmyO3ZOb3x+wWvwo48CADsyFMDkj3IQZlUpP/Sy5YAJX/yFgjp3bzJ0QygpDy+6vn/whFiCG/Q7I6a/aB7KrSVHPQ2Rps6va3PtWXKyo1Y80MEKcNjuA8ME6Nt0k5RoAb5T/+Kr497QLfctJbJai1GWGncA6zv2s12XPeWHX9pvgc7xALUIAuNSFU/MnN438ieVuPtOEewvtFcM7G6/oD4yia7YUct9jW6gr4fT9vSXhKumSZa0XTWUujamF116kgA3+q6ItSgT6Ybzyc/jLQmzZOSrm3c2AyIkfSeVwQcdRBoRITyGuC/jRFsd7RNceG057bl6baemrXb7bsPtNsYr5kufbZJW2VdYqrF8YeVwJyWHfbXBTvfuqMGKz25QBS+zSph9e3QrK/vNa7NwBr297XxG34643GO4BhGcs10xPUHtDHKqZMci/ve+wnPh0gMqYhoxFYn2TZfbKZpZ2aTgJnD+0b1WV2Um2JTTxbfGpqnrjwBr2QslAORnN5y73Vd1WiF/FmDCq5oU8YHrFXS7AcA3lESyy8AIPkEIhXy6ytGysu1IdqmOOVzNk+OhEhlogmL3v00cNWqYxewf6e8DcuGAhXf6feBso3LL5KXi+Q4NzuCV/M7doW1zw6GrgllVyvcmd0ByDeX8+e/h2nPrsZtr27CyFd24PTHN+ibLUOsm3K+ZaBJ11RSrAlEKsR8jHXehR1igV/ysrCeLjXHwDcLcSXy1EBE+30bftkOOHYFTs7ls01uUcJXu1rxrzIETuClHOeOOoZBfsctStjww2as/vZnv2OorZXyPYbBrrXstGQ114gi3Ovv0Cy37njrvgM1yERTyE0MWoutLWOUZG6KCMuYoN8fxjF0ixLe3hRes1zYzVex3rYOkHDp4A9GTrr1oD4fqN+JmsLXsQtYPta/U9u41d423OXDA44S6CiRpoyPKL2wb+bHQKMklOXCuEkrqZs3/LIdtfv3yXPdFCfDnK1N7uR5WnA2qE8N5/Urwux3twBoOx+AlppavLkOmzb3w8Bjj9H8jmcbrDbgQI3899Lz5X5AJX/Sb6MpCVh1FXDte+EFIwan7w42euDi44qx8tOfsNwyH3kW73B1pflEzTETYt2+KduHBsc+Xcp2CVCzo1aI+QAkTE9+A9e77sDzlkfUgGSUa6Y630xU87MEkYkmHPbBtWhy7cOYlpn4X523eeW4rAasTL4PaTlFwNg3sfb3poD7ZubwvshJt3o7OBabYX7l8tgfwyDnytrNdix+91M80XwPBE/6+wxbLi4+rhhLPi1rc+qMYGnSAfkY7tu3F0XWVrlGpaVR/2U+1+DQw9JwfrcnUG+24/zav4WVol8r5OzV7VzGhLNv5W3UH8NvdrWGlW8kL90Sfmr2WG5bB2EwEoGDTfmri3J9q9SUtloAGPk8YM2U/18bkBjcthzJ9mtnswyrStvWwz/wCtiLP0Qg5rNPza46nPTFBKBhD2BJB1yN3qRxaoCQBTTvBzIK1YIinOm2fWlTi+evHQP0Wht4yOkFjwCrxsodk5cOBS5fDqy7xxuIiK3e7Qin0PAthMa8Km8TJP9CSBN0tYdgTSh2RzOe+bQMRT7p1pWApDtqcNiaaYCS9C/ItlbVN/ulbL/edQdqkAWzKAKQUGKqgVs0owGpGO2aqd6stP082jO1djoOwHSgGmnNu/CEeA9GQ96mYtTgieb7kOaqQhOA//7wB256a3fAfTP5lY26147NasCryZVy0rVYHsMAN6y1O82Yu2Kdt8lLlLdROYaB+M6jYjYJ8kNVjyTACcDmDSAG9UgC1k8B6nYDX+QArgb/G6X2Grz4SQhNe5F1oAKvp9yPv3iGV2tT9CvrGCgYCVnetEcZE8G+DRYMVNWH95WXHN89/ObAWG5bB2GfkQhEO67er605WBTd7ACqfwPevN7/SagTtC1Hsv1RD3mNNh9DoH2qzUmhyO4JXL4UePMGfQDgyU3xrzJEPcRQV2CGGsJZ8a0ciIit3g8fzNBt7T4zW4G0XMBkll9Xvg9o11qScPtQafeJ71D25oyeSJkQPKOwcr75fsfdLeMxP/lZlJhq/PqJaPt5KNfdJ389Cxt21IYcqu4WJXy9rQY3v/J90HmVBACFWVYUSDX4p/OegNu0QyzABNNs1FmKwg5qBc9+UqeAiOUx1JwrUk5v3NgwEXc7F4aV/j4QNV9IsDLNsUuuBXRo8iXl9AZGPhf0GtQGYZGk6J927hG47dzDI9oHunU62CHWYQ6Jbq9pSdpcB4XBw8eZZySYg+jkE07mUUCuyk3HAVR6RssAmk5bjl2Asx5YOSrsxE2dJelNONufnZqMp64agFMOOYhJ3KLJVBksGVaoG79vAGDNxIZfd2LkKzv8vt63M2MgAhA4jX+gwmDLe8BrY/VfcDCFhtL0o20vtpXISfKANhOCRSqiwFQbpHnsEAvw89CVOP/U4An8tOdbUZDvmGieg18PyNdroM7KNwzugze/36UbuaAEykrTwrotlXhn0+6QoxuU75t67uFY8OFvQbcp0pu59vuPzWrAO6lzIcT6GAa4YUW77moHy1DJ6LTnpckMiO7A16BSI2TrETgRndgNV7juDbiOSsbnsMucWGXDDSMYCKccLT6YbM+dLKMzO7AGcpCdfJTZDwH4DUHL8nSyK0l14QXLfKzydKQrsqVg6YjuGHpYmvc33p0iX3jazqnl//Uf3+8ZceM3rPHBj/Dhxl/9O0spHagCdXhrh05wobZf8PybP/IYnHZYfvSBiKNCfprQenui//HypVRT+u7TN2/wFnoKJTDxCUSwYiQGrL8Kx2U16LavGDVYZbkPL1jmB+08pyx/08VnQLhsif7Ny5b4tyGvu8f/S4bcH3JOnpAzmdpKgJFLdS85XS349LtNaH52WLtX1UbSZGdHHqa1TNa9Nq1lMrIK+4T8nPZ8qwzwHdNbJmPayLOCdlY+t18Bnv2szC/IUHLDnHnfO5j27Gos/WK73zK+HSWVzpe989NDblMDUqPqfCkB+F9dBjafskD3ekyOoa1EPid91j2aIEpt2gp2/akd70sAW6kciAD+1+CYV4F3b/GWzQHWsQkWNGia3xQCgNkXHx1ZNtwIy5iwZxIOsN6+13845WjUs+dGW352AolVM3IQqaS1fDvtZaIJr6Q+hEPTDsA6ahlaXhuPlIZyNGf0RPIVz8P89gRvu+j+8oDVkSqfnAc3rfgeGZqaFkAOfJZb5uOoTCfSxiwH8g+HW5TQtPRSmBsrYRIEWHN6QLjaU5V7MJ3gfGuSmh1Y/8Mf+Pt/9kFy7FJrC4ptKXjgnFycdewh3u+PdPhuxXfAG9d595GmmlPK6Y3vz1qBCjE3dO1QsCeTIffLfTV8KU8NmnOjKb0U59bcCTvydE/kO8QCjHLNxInH9sd322t11fBqs1SpO/STkW9nVd8am7FvwJ17OL6pSVVrwmobXbhv9Ra//a1rBgu03RoVKMTvF7yKwSeeEFYtm7tpPzb9XoEKMcd/OccufLPbhSte2BLwt3wFqkXYiUIU3vIhLHk91e8MViup7WCp/Y6m9FKkTZT75vjWHtbUOzHl1Y1+36VQOxxr5oLyXd8aZOFW0z14cOxgtaZPqRE6DBVYbnkQJSZv4FEu5sOBDOSgAeNcd+J3lAT8zqCdL1GDD3Mf0k8BoaE9hkrH7KyC3gGyPMv70i1KgY+ho0KuLdQ0nYRbM6Kt9fWrBXXsApx1wMrRgc9/xy79E7ti/AfytaEtm7XNOB6iYMY1uB+fH+jl3WeRNgeH0ZTiTs7Q7Tfl+rN7augakYoMW27g3w23mcSxCx/+0YiZa3cGT2sfaS1+iG0Lu/yMATbTBNNOqZl9x94PWH+VXDXvcyGJQhJMUqv6X7/fCFCl5i45SW2/dh9w+BWaRZ4n9V6mKkimJNRlHo4bmybhEed9KDFVAwDsyMfPF7yBs47sFn31rm87sKf2AI174b7sWbS8dj2aknPw+5AXMLAoCeYXL/QGPIB/G7LyffWVACQgs9gbHFV8J1fniq362YkdFWhaMhRpjTvVYKASeaELId99esUKbydRX0EChab0Ukx13RS0Pb0oy4oxJ/VE7/x078Vdvyv0uTXyebk/kG87+ZD7gdfHAWIrJAB7kYtLmueEHEnQ4LmZLR47QBcANaWX4up91+Op5MdRZKpVPz/SORsbpCOQnZbc5rTuH278FYXvXoUs9351m32DLSmtG87beyu21ZlDNllq84E4pSTUIhOtkgklphr15vrnIwuCBstuUcKmzZtx5NoxyGjaiYa0UpQNfgz9v/mr93rzuWbdooQ//WNdyBEL2mtIe2x9+6eMcs3EggnD1bZ7tyjh8rlL8ap0J6xCKyrEPNzacovap6FVMiFJEOGUkjDc+QB+R0nA76z0uelrl2nrGJakuvCE+361TICth/cYes7h/VIGdjuake6u0x3Deefk4M9fXAM4dkIyW3GbcCdudz0Tsj+GALnmJlgAp36vMjrqzzOAFSO8XzD+A7nMCRYs+/aJ0V4fyn8FMyC5A99U63eH158ujIfRprQeKD9gRaq7IWSQOs4TUOqGKmvvLdk9geR0oOY3/75gmodD95Vv4Bu72//hINJRciG2LeLys52xmSYYbSZBTyrpaOaIUIbkXnhsd7jSi/HBn55Hc0ZP+bvevAHfH3k7WmGWAxAAJqkVFSjEx4OWaTp3+VepNa26HiPmv4arnv8v9h9o8RtNUIwaVCIPt7qmoEUyQRBbkbb/F2Q2bIe21VwUgSff/hhNS4ZGX73r20O8+jf1b/NyeX6dXDhwUmaNHIjUbpffV3r9az/rqJBfq6+Un8gcFUC9XX7NUSHXiCiFz+XefbRmhxnn1Nzplw0zaIbNQNWUb4zzFnCA3G4NyH/rcrh4z420xp1Y0vL3oIX0njonFnz4G7ZXN6KqvhmbNv8EaZlcEDWmlWLDWSvgLjlJf669cR2QnOrfV6XvRcD4tRBhggCgAPvwumWOmi9De6PKQx3ScUA90ove/RSSpkPimJaZ2C3loRVm3S54PPlJFKPGbx4hpclC2Y9rN9sxc9XXyHLv159zjmbMXbFOPZ+Epr24+9xSAP5VzYqxfZPwWsr96rxLe6VsFAm1AARUiHkowR4c9n+X48Az5+nPHY+1m+24bP5ryH9jBDKa5ML0qtaZ2JV5DIQQ6dS/KdvX5tDJSk3WY2U7Bwi/6oKG0Z6CW9skZa7fjeXWh2AVlBotAXZJ/q4KMQ9JnmkcrEIrllseCvqdWtraNym77WPYeqBeVybAsQuTVnyPj77eoN4MG/bZ/Y4hHLtw+P/9Ra0RETK64bIh52CMz37QNjOdc1Q35HjSlgcqiwBAcOxCn//z9IGrt8vNLVpvjpdrYmq3a669ANcgIAfs2hpD5eGk2xGAKQlC7XYMXD8Wl/SBJyVCBZqfHYp9z1yEr38qwxe/VwdvSmkju2xTeim2NSQj3V3vt42+12AaDgDQpGz3TaN/+TKg9YC3TFO2seI773KNe2FuaQycOj5Q+QnoAx7t9RJk2yIuPw2UeDUjinbo5OPbXFOMGryecj9KlCGLPpRpya8/rTcu6VaJY76eCkHTJNG06no1gtU9eYQYkdAKM5LgVn9DO9meQsrpLRfe0XaMVE5+Wwlw/nz55q5cZH9ZDqydIRdwvhd5oFqoN8d7LyxbqVz4KLUH2T3li9gzK/GaH3ZjysqNEKXAHUjVUUq3DfTkL9A0e5mtQIoNUlMNBMmtDkeUskrgdLUg2VULs+iCCDNMcKMutQSfn/YC7FIuDnNtwZmfXeV33EIJ9NTo13ST3g246AngnUnyk49Pp7YJ857G/c6HkCfUwSq0hjWSIBNN+LLkKWS6a7HhrBWY8spGXR6OJLiRI9Sr3xesKj47LRnf/P1cnPnwetgdzQHPuYXJT6KnqVp3PinXgLYJSbtuK6wPIgcOjHLOBICg6+Z7jmqbKLX7VbmRa2uEpLRu+Pb0Z2F3WlBsdaGmdh9uerfSbxsDnUPhdETVjWpodkB6aSTsu8ogilBn2Z7WMhlPJD+JElM1nJIZNZIN3U37gn5noHPnqEwntp6/MqxjqN2X6nliWYReQhUqUIi/NN/jt8zdLePxvOUReX/bSiCMf189hkozmFLz1iikQXsvz7CaIEoCbK6qoGXRHuSgICsVQt1uuSYjtw+wrwyQ5PJJFEwwSSIkswXOJBuSWxzyNeipNa5LLcHXJz+FE7+djtzGbepvfzvgQRz7y+OwNu6Wy5yCfsCIZ/HZ71U4/INrUYR92CEWYLzrDjQiVT1Hjs1qwK3DTsC5JxzhbdYAAjZ9uEUJl81/DWV1JmTgQNBtDHQcV044RR7C7DearwJYNkxudjYlAXmHAS1N3mbokc8D+YcHr9EJtxZfac5R8rV4tm3ND7tx/8oPUS+lIh0Hgpef0XaUDRObaUIJt10vRJvd+m824ra3fkedT9vvAOFXvGWdHfBnlRO5G2rxpnU2kgURTanFSLtqBdy5h2HCglcxy/moetJPcU3BdnRHOg6gEPvwlOUJXRv1DrEAj7Vcjseti9TXRjjl39auww9DXkNj4Yn6qkBNWnr//ePTHlm1FXjmTMDt9C7j29/BbAVu/AQo8MlSG2hf20oACP7D/TT7f/03G3HrW7+HHL0CaG7GLXu935nVHZAkoN4u1yBIgCAArZIJNchEoeDALjEH+YI84H+flIFqZONK1z3IwAG8bpmjC+ZC9eL3XRdt3x7dSKpS0btPA6TbXv5FGe5bvRVFqEEGDqgJvBTlYj6muG7FDzjM73efvyAD5xxViPe3NeKof4/WFZoSoPu+YM0EAHD5gBK88b23o1ugG3WLZMLWYa/j2FPOVV9z1+5E/TPn44+mVL/+EL77JNB3Voh52HP5v9REcW5Rwvnz30NqXRm2Q84E6vsd6VnZeP+ui/Dpd5vwjw934vc6s3pT72aqx3XNt0MC0IhUNCIVfbAb/7Q8iRpk4U6XXHO2B7lIxwEcJuzCCut8dX1GOGfjN6kEGTgA2Hr4F9ZVP+Oz3/fiznfLAmxLPsa57kSW0KS7BkMFtDlpyXj4ot4495B0vL+lMuxjKABtBlLFqPE7n333t7LPN23+CR/+0YjFX3uXDfcYOiUzzJDkmiGfJhb1QcBzHbZIApIFSX1wqkU6ctCAWmRimmsylloeQk/N+rokM/ZJGZBMyShGNZDVA82uFggHamAV3JAk4HrndEyxvOttuvLsmxpkoXHoAgz+9hZ9s4bPNfjF79W46rn/AkDQazDYw8AjlxyC8wcN9L9fVP0sN8vu/VlfVipN+W+MB1JygHEBEhyqyeDqA5ef49+Xm83r7PKAiMa9uuBm/Tcb8Y+3vsHzlkdC9lMCohxCHAEGI8FEEm0GabNz1+6E/fFzUCVm6g5yoIsUAPZKNjilJLmdXMyDAAk9TPvQIgnYJvVAzwwJYko2aqr34lbXFDxheRJ1SEU2mrAfachBA7KFBjQiFYXCfvV7b3VOxsOWJZoq48A1I7vQDZc3e2+mh2W58UbGI8iWvE/nSh+Yuj1lOPPL62C1FWo6wO4CnjtHroJVDF8ArJ7m/TuzCLjhI31w49gFSCJg/5+cVVQx/gM5AdmKy/SveWqmgu3fQIpQg/8onf7MViA9H1JaPhr27UaqswZJgohWSUCtlIksoRFWwQ2nlIRrnXfiEcsSz5NsEmqkLNzkmuoJ+OR9VyVmIVdoQJIg6gIS38JZ61BUQAB0nRfVYYcB2raDTSfuG9T+IpYgBa6A7dgf5T+M1pQ8vFw6GydtuL3NTpnB9mmaxYwml1v3WqDgujGtFOk3rpULw+rfIL15vTprrhLohBoK7fudlVIOfjp7Oc45/nDA1gPfbC1D6spLcZRQjp+lUkxz3Yw9yJU77mq24+tBz+LhT+xqk5W2H4hTMgMQUC1loR5pOFTYjWRBRIWYBzNE5Ar12CZ1RzYa1JooRbmYjzpkIBNNKLtwFf580gDvymva6z844SkseXMN3rB4t2WEczbsUl5Yw34FALedczhuOUfOjfFN2T58+3MZTv/vjWEfQ999OcE5DeukP+k+87blXl3fkxHO2bh21BXykFyfuXnOn/8eGur2q8dQgISnLE8gD3W4xTUFZeiOeqThdOEHXQBXJdlQIDggCmZIFy5Ey0cPIqWxQg1AJPWBQECSIOkCLQESllgeQx7qIMKEElO12uQlN+t5zhPRBsCk2xaFXcyBG2aUmKpRKWbL54Npv3y8TYIcxNhKgL+8CGQUyE2mnrJ9/S97MXPNdlQ0W3T7+LGWy/2CVG1AqQS/h6U346vBLyCrsI9PJ+HzgYa9+oc4QO7D9sHfvTUm499Xa4N9zzFcvsy//MwsBq5+G3j7RnkbBLP8EOapNXJf+gyqnr4IudJ+tTYt2AMIEOH8N1FgMBJIqA5MSnWaOtIF3mVtpcBVr8tP/Y4KND8r95fQVg0KAFZZ5qCnqRotkgnJnptgkiDv3moxE26YUWjaDwDYJebiHtd1uM/yAkpM1RAFM0ySGzvEAsxxXY05lhdRatqr+S6T2h6t0H5/q2RCrZSObqZ63XvK57UXfyYO4Dkl6s/pjY8HLcOM/9Qi3fG7OkKgAoX4deirSO3WC3V7yvDnz66E9YCm+cnToUyVWQzc8KG+iWbZMKCxGnC79E8GthKg1QU0Vulf8+RT0O7fUBeR4s0re2Lg+rFA7XbUWbrhgKsVhahV94vvfqsQ83C5azYEAK9bZqu1TdplK0UbJJhQbKrV7cPxrjuw0LII2Wj0qy05DBVYbf07AKidFxUzB2fh+t+n6J7OgmUwDVYjoT2OduThOPyORSn/RA/sVfdVI7xVstr/agME3/eDBXuh1sO3g16gTqCBAp9gAbtoSkZLajd8fNpL2Lu7HKN+vF497/dKNtiRhztdE3W1A6NdM9Hgsz2+xxTQ3ghNqJEyUWhyeI639/qpEPNxa8sU/+bPUKPuRj6PxpXjkN7oreHTPgy0VcW/6MoTcMGx3QOOzvM9dgrtMQz09N4imXC5czb+h8M8tSL6fQHIgVH15W9h4BE9dQ9c3+xuQdLKy5GHOs1DURqy0aiWRT9LpbjHNR6vWe/TBXB2MQciBPQw7UOrZMYOKR+9hL1+157y+76J6t7QrGeFmI+/uGahwFODrP0O7TED5HK1GVY1gEkRWpAv1AEA9opZcMKCElM1WoVkJGXky31VUvOA5lpgfzkOpHVHTYMTVcjBHNfVau1zoIc63xrSItRglfU+9BK8xxa2Ht7OvLXb0ZxSCIuzGiZtWRksz5HvOZbdU84cXf2bvvwEgPQCeWoJ0S1nrm2q9uRvMcNlyYGlWV7vXWIubnJNDVijqpxLSyaczZqRcLVbMKKt7dAm2Gl2AC9cBOz5SW2PRFaxXEWmDIEzW+Ee/Qpa3rkNKY0V2CEW4HrXHXjIsgQFqFVrO1olAb9LPZCBZpjRilyhDlafC1Lpad+IVLxumY1uggNWoVUt6H3/qzzNFZuUG6wAAQLMnu9VLlCl0FXYxRzc7RqvBjZ2MRsiTNiHLExzTcaylEdRij2wizm4t+VaPGl5Uh0h8BfXbLV6eLnlIbkGAUmQrFlIcWomHhNMcu0H4A0o6iu9Q3QVpiQ59fn7d+nHvGuDmswiwJQMOHaqzRJVyFE7XfneVJUhrp/fdCS+3PIHen9wA0p9+utUiTYUmLz5VfaI2Zjgmq5enIEK7GoxE1lCEyyCGxViHm523YYnPFX8L7vOwoPW5/1qS4pRg7cs96rHSCkEytBdbSLoZaqS+0aMXAp37qG4bOH7KKszqdumbU7oZapCuZiPFiSjl7DHU8MjB0s7xAL8o+UqPGVZiGRB8iskM9GEVyz3Iwf1qEUmstCk3riV71dq3vbChptdtyIf+7Ed3SFZM9HodOs6Ve4Uu+G2lpvVm6okmCFoClcRJtzQPA0fYSCOxe94yvJPlJr26poatbUa2rb4J5L/qdv3lVKOOtpGHxzmoNXz5KsU/Gk4gMcsTyMHdXAgQ91OALpjAcDv2hAhwARvIPIX1yw404sw+bD9GFsxBymNFfqbxWVLgLdu0LT3e0fMHUgtxqyWazDV9azaT0R52PgOff36IMy23o6rLjgb555wBD7c+CtmrvrarwlQOYbBgt6XLA8gV2hQn3zvb7kKiyyPqwHcZNdtuDf5Rd0NXhtsSdk9IQx7CFh7l3rj++rIv6H7V3N0o4J2iAV4wnWJes63SAIcyFCbOJVgsdhUC7uYjW5CXcAAREtbe6MEXhk4gNXWv+tGKCl9cAI9UChaJRNudE7DvZaX/AJchW/5KdlKIAx7BNKa2yHUyR2f94pZyBKaYBVaYRez4UaSJ8DJx0MtV+BRy2LdtdagC37nqOfl9JbJeNyz3nbkw+3pV6QG8QpTEnDFi8D7f/f2xxv6IPD+DO85NnSeZ+oIt1xODn8M+Ozh4OWnzwNipZiNvchGFppwvesONHj60yjNUEstD6HRlIkjbloJs6171LOkt4XBSDDNDn0725hXAVeT3LGydjuQ1UMutTKLgbNmAv+aJM+roKE8fexBDt7wnIguyYRkiBAEwO6pJiw27cceMQsFQp2uIBzrvAvbpB6aTmp5yE23IO2Af89m7YUR7GJ3SWYkw637DUC+EbshYD/ScahQqS7TIpkw0TkN+5CF161zYRH0VfO7PTfSpyyPq4GSsi5JgoRugiZ5Wkq23OQiekYvpObK+VQk0b/2JKNQLtybPDcfwSwX8u9M8n4egDO1AGWNFmSgGYCEamRBgIBsNKi1Rnthw7Wuu/DPEYfipE+uwc/1Vr9qVaXqWMspJWGvlI2/uGapBXyofj7KjUoC0A9leM76GEwCdIX13S3j8XDyMyg21cIpmbFPykSxaT9aJAG7pW7oIVQjyRNUzky+AwuSn0SzKR0tDbWoQQYscCMLjahHKg7zNCcoNWTPWBcgWRB1T/fa4y+3l9+OjzBQfe1Y/K72SVKWLxfz0YQUtblCabffJeagHuk4VNiF36XuuMs1Ealwqk1YLZIJ26TuuM51J7phP17NWRzwPG2RBDzsGo2/WlYh2ROoTXHdgicsT6IBKXjEdTlmW1aoQdYU160oQ3eciK34h2WZrrMnIJ+7EiQUmup0r2s7cAYK5HeIBbjVNQXPWBYErM7XkswWuFLy8fFpLyEnJw8D06thfvsGb06glGxvbiCFKQkY9ZLcaVuTfl8ymdEoZCDVsQ1mqQVuIQmCJKI+8xBsHfoGzK316Ld2NGBJR5rQDCElB+5LF2PLM+OQ5d6vawLxPYYVYh4mu27DDzjMU/s2A1bPNVspZmOqazK+Rn+cgw14zvqoXzmgPYf7wI7nc5bJx9CUBFzxErD2b8D+cohCEuY3X44ZllfVc+1u13jcb1nqOZeAJM13K+eWXcyBGaIu6A9FKT//QHe1c/KtrilYZFmAfKFO3TZAvl7/6pqIJzT94nx/v0USsKTlQtxseS/g7z3luhAjkj5HsadWeg9ykZpkQnqrA+YAE2Iq31sh5nseKOvVvimC4M0nk4Um3Oqa4ldOyuvt7cSsPDwsseoT2sFWAlzyFPDyFfqmnMxiYMg/gA9n6fvVmZKAs2cC/5mrL1eDqJJscEnJalP0Xikb97quxf2WZcgT9sMqiHKNfEaBvC7aNAtvXKfO13WwAQmDkVC0zTVmq9yG+JdlwGvXAp5IGam5gNMBiG5vjhANpdB7yvI4CoT96g3d92lOAnRPaMrr2qe8W1xT8MCwnui/fpzfqu6RslElZatPff2FP/Cs5qSuFdOQLTT5FUA1UibyPE8vLZIJSZ5AyduJzIQKKRe9hWrdZ70XuEl3E6wQ8/BX140+wxo90rvJgZ0v5ckyLc8bgCgEM1B4tJxj46XLdBeXlNMH7todSIK8H7UBmbo+Uh6q/nQ3TvjtMQiOioDVqr43bm1wYhdzMMI1FwD8Ovj5fr5CzMPDLaPwiGURkj37ao9og+hpxtGqEPMw0zUOSzxBhLpNktx3yAW575Cyf7UBprZpbZLmaU/b7r5LykWJz41bW+2trZnwrWHzbb93SknYL6UjT6hTa9ZaYFY7HGqD121Sd6TDiVZTEnrDrhvOqyyvaPE8rc7yrL/o+d5tUg9koAkOZMCGBjU42iYVIweNahNmoGOgmOCchs3SIWogrz03tDenZCGMYi2jEBj9itzpT6kZFVv1fQu2/Qd47zb955Tq82YH4G71NjcqN5c3b9BfD6NXAr1Pk/sQKFXughlNmT1Rs79B1wRypWsm+mC35hjK29IimXC7axJmJL+i3lRbJQEiTJAgYLpzEmZYVvo1ybgkMy5wztPVwu4SCtFbqJSbDrJ7AsMeBFZdrWsK8K1JapHkIczajqWB+qz5fk5b3mjPwRZJwF3OCbjV8g56marU9yvFHF0QKT9Q+V9nADzXjkPXbBOK/JAo6L7LN8DaK2Whm1CHVsmE/VI68k3eGiClXPxdKkYGXLprLFANbBWykYMG/NU1EQ9aluj2nQRAMCXLN/s3rtOXj0KSvJMEAO4W+V50QH/N+z3oBd3mHADee5D2+OiuW1uJ3G+vfrd3+ozsnsB1aw86YzPzjISipC62lcgRqWMn8OpVQIOmmu/APkB0Q4IJkqSviZAkoJepCm9Y5er9ZM3QWm3BWWSqVU8Cl2RGq2RSX1eqAG9zTcHTKU/g8PUTAq5qobAfuZ7OYwAwK/kl3fs5pib1BqOVhXr1/5MFfSAiSfJrvoEIAOyX0tGqqVKUb1om/NV1I+ZbnvNUY+bAac33fqhpX+DoWWyVAxWzxf+9CxcAI5YA/5rsd1EJ+8vVQMTzCv7Zcqlu/XPSrBjw3XQIjgq1kCkxVWO3mItqMQOShAA1SElq57ZiUy3esdyDt633qoGIW9LvjCRP4VliqsHjVjkQaZHkC7zQ5EA3QV9Atkom3Oy6DesxEA+5rtBvkwAUmByeQERQn3jdSFILOTkQkd972vqYWkgrhbkgwC8QaZFMak6EIcK3eNM6G6WmvdgpdsPlztnYIRbojqX2v0kQUWhy6Jr4LIIbVsENi+BWOxwmCyIOFXaj1LQXJdIeCJCzYcLzX99BgUtahmO25QXP+gswCYBVcMOGBsxyXYscNKCnqRpHChXqd5sRuGDdj3Td34stj+Mty71qEjU3kjDLda1n/8nbqQ1EpFBFXMMeOdlc2WfAns2a3BZuwJIGvPIX/0AEkGtK9v4qNylqbyKiG0hKBZJ8Zv99bSywaaW+7V9yI6W+HAtdl6o3tKOEnRgmfK07hjc6p6vvP2FdhGLTftjFbFR6ainl49WKJ61PosRU41cOCHDjSJTjLctMlJhqYBVaYRGduOHAVDSl9QD2l6P53b/ipyOn6D63ovUc3d8Pu0ajWNCfe3clv+qXnl2pUfH9e5JzGnaIBZryR8Ij1iVY0XK2JmgWkOzzoJMr1KPYVKsOM1b/CxOKTftRJ+l/X/vbrT77IkVo8TvPknxO3mzUo1rKQpIgqoFIpZiNkZprKQMu/LPlErWcFCWoD36KFiThAdeVMKMVy60Pqn0J73TegBZJkK8ZsQV47Rr/BzWpVX7P3SKXnxb9NQAg8GsKwbsPlPtPjSTPAu8NREw+160A/P6hNxBR8z3FrmOrr8SsGVEEmlzMR4vFhmSXf/Wj7xOAb8cqLW11me51n6jft1ZC+4RnF3MgCFB7l9eImcgz+VwAPu2SvlF/oPUOR6skYK+UjWJTLSrEfChTtyOzSH4CFENE6MrEWL4yiwAI3hE6mZ5MgPX6JgDfp27fv+X1M6l9KtyAXx8dbU1UsKYu7RN4qGO5V7JhhvMGPJPyGALlHq0Q8/HPlksx3/JcyP3sWzumdBgM1O69QyzAopaL8aD1uYDr7HvcWyQTRjpn4wccFrIJSivQeeE/EsLbNPWQcA0eFxboctwE+05trVWFmA8LWtBNcOi+O9R62MUcmAVR1+S2R7ShBcmB2+PDoe3rFEhqntxBMCwmz3muqfLP6Q2c/wDw2tUhrw/f2ivt68oxPE/4VlcbWiNlwiWZ1RoShe/x8n0d8NYI2pGHQdY/8JIwK+QxDLaewX5Lqa2xCG64JDNMnuG+Sg3wPy1PogUmHCpU+tW8aINIOZAwqQ8ESvO3AEGtOXEDPmnhZLVSOjLQqNZihqNKsiEX9QH7ptRImbjQ+QAA7yAFhe/2azvNardJe+3MdV2NZ1MWwATf3xIA3zIlWPkZ6DOCWa6FbgzcdyYo398wJQHj1wIlfwr+mQiwZiQcASYX86UEIi2+Txw+J3otgu9kl+R9etPqZnL4BSI7xALNE638pAzIEa4SiNjFbByAVfddkgS/p3HfQCTQeiuUyDmQJEFCrlCPSikHSiDSnNETuOE/cqeqULQnuWCWe4EDcidXJfCwlcojcf7ygs+HfaN3yOPy/dZPVGtCtIHIHtGGCjEPu5GHcS45C2GxaT/2Slmo9tlepRCqEPMw0jkHFWIeXAEuj26CA0tSFvgFIrWS/KRSYqrGg9bn1IL6KddFAXeLUjumNLH8D4f5TbymWNFyDu636M9TZXsD3Yhv9vQvKEYNFiT7t7UHEui80NakiJLc6bNCzMetrim4U3rR7ya2V8z0+3yLp7boL65ZqBDzUWKqRoHJoda6+P6ust98a6kedf1F93eeUI8nWi7V3RyDb1yAW5Yk6p4gAchPoUpQHHYgAuCiBUB6vv61y5YAxcfJVey+NJM2Kjf4R1v026c9hvckv6x7L0+oR7FpP2oC7G/lvy2a/ac+DWv2UTFqMF96wu8Yvtpyht/qKjWpLZIJM5zXq7V0yndLEtSyTamtsYs5uMA5DyM8ZVkNslCG7hjlmonLXPfjb64bdL/R5FPDcrtrMrZKPeVaBM95dLf1Lvx84VtyeQHvzUvZLuW/OYI3EHFKSVjgusxvm7RaJRMKBAf2BSkD84R6vGW5F+k4gBYk694TfUoom9CIh1qu8JyXEkRPTapSs9LLVIV7LS9hd79ANeEBTmLf8jPVt/yT5Bo6k6fZRtfcEyhU85FZ7B/sDH+s3QKRSLBmJMTkYr582zN97RFtyBO80fVeMQutMOuGhypDeBWiBFRJ3l7PvsMj65CKQuzXtUdWSjkoEmrV0RaHCPaAnRsB+WKc3zIasywrQm5boHb2vWImWpHk3y8ChSi+7T8wN+7xzicTjitWyO3w2sy3ghm4/gO5psT3WAhJcpWljgnQPFFIECD4XMTaTqoS4B15o9mneaj36zRpF3Pw3JFL0L/f0Thq/+c46tObdKNGQmmVTKiXrMgxHVBfe8A5GlclfxS0lz/gzV0QbMgr4P/EG+xpWuFb0xKopi1aci1fktokEKi/UaD1mdYyGYt85lqZ7RqL2UHOS6eUBDNEuCHoOg8G0mbtitki9w8Z9hCw5g5vvzDfxH22Uu8IhkjYSuQCXVur51vzp3XOvXInxBB8j+EOsQBPt16IeZbQD0+KGa7xQZfVjlhqa98FO/e0tomFmN8yBs9aF3p/Q8zBpa65AXPOhDrXFQ1ppfj1rCUwuZtw+Ce3QkrJRuqENTCnZXvmsRqiu4nuEAuwtGUo5lhfVF+rkTIxxXkLHrY849eXBtCfy4GujRoxHTahSa0lrZXSkY3GNmvyFC2SCb9KPXCb6xa/OYqicv58uVOrb96StHzgnJnA6tvl8zmru1yumi3Avm0+X6IvP2G1yX0jtSKcGqUtrBlpizYQySwKK4o0tVEVXCA41OYCu5iDbqY6AJLattgKMySfmgSTALTCjKmuyRilGXtvRx5GuWZimutmuHyi8SJBbi4Z5ZqFS1334Q7nBF3nvSrJ23+jVkrH9UlrA66v9oki2dNvQMuNJExyTUOlqI/GK/68QE7so8wno63xAILvy/fvkjMSaglmOemSNn/D+A/k7/MLRAD1QvI81foGIgBwvesOXO4ZLVOJPLUQtCMPt7imIBtN6G7a57ddOWnJmHlhP1zWzY6+n02WAxGTZ8RPZlHATfrl6GlyTgpB1AUiAPBXy2tqMAD49+sBgCeSn8Rx+F0tpJRltZ/RPoXqa8705+MM13j16esN62xdILJDLMAk53Rdm3oklCftAsHhGXbrrdWoFjPV8893G5X+VW9ZZ/sF8ncnvxLkt0ywCq3YK9nwD9eVumaiic5pqBL1/ZN8bwbzkiZByvbMBiyY5Tw3kOSaius/8NZ+KIFIRqEciDh2yn1IwjV8gTc3RL1d/l61uVFT82fSXw9SiEAk0DFUApNJSf8X8DPzXVfoakIAYG7yMr/lWj3LFAm1QY6hoAYeDzhHqzUg2uWU/ElaqWjBrGR9UJkj1MsZbAH1GhTgP7XFfJ/aXJxzL5DTGxlNOzHgy5twfN+jkD5pHTJu/LcciAByh+M8fTbbRZZxGJ+sL+daJBMetixBialG7l/kc+67PbV2FWIeJJ8ajr1SFvJMjdgr2dTtzRF8AhF4AzXt/lXc7LoNo12z1HxDlcjDP1qu0i2DlGz934JJX5ZqrbtHDkRsJcCoFVBv303Vct8mpdPp9evkwDvgQ7anRlCpFfQNRHzn6+pAiRmMaCc1spV4eyabzIGrVT0KBIdflaC28FVOzInOaRjhmosdYgH2IAe/SiVokUxoyuwD8+eP6r6zRTKhFhmoQq5fci8BwPOWR9Tq/LHOu+CUlCpe+YfTcQC3Wv6lVtn/KpXALuWhQsyHXcxGkcmhjkLxXW9dp1b4F+pFplo8bXnM73Z/ysY75Y5O+8vlKDyjQG6nzCz2Vhf6Ekzyye3YKRf84z+Q/yu6gJdH6hPRmcz6avLUXP8q9dRs/9/wmJ/8HAQA2anJmHbu4Vh05QAU21JQhBr80/KknHMF+UhL1Td1pTRXyZlmX7vG24lr/PvySAhTcsDfOnL32xDOvkf32iuuP+uaD5TOqns1QaJScJWYqvFmgMDhcudslIv5fsfk/par8D8chjmuq/1u/JOS/g+3uqagXMxXa0K2Sd3V9vo5lhf8ai5C1Yv6BqtaytPiHjELzbDq2vZbJBP+4RyNVk11vqJSysEE5zS/5hVtEKbU+hSbajHX+pIaiIx0zsaP0iF+wbnvNkxNWQ3h8qXy+VR4tFxAZxZ75iYJ8Bh7xUvyaDrtDLHBbghanz/q09ZuBi54xH850ZM87aLHdanRn3BeHMYxNGGO62q1hsQuZvvdVO9IfkMdcaPsD+V47ZVsatDt2w/K/xhKarPIVckfYaJzGnaK3QJ+rlzMxwTnNFSIeehu2ocSUzX2JRViQtIDqBDzYRVa8bzlEfRNc2DRlSfg6bEDcExWgy4QedQ0Dn+1vKnfAesfAM6fp58EEdB3kHfWAy2Nuo/NF+UO341ppVhz+FxUaso+u5iDaikLZs8w5D2aTuxPWxbAglZY/GqUk1Eh5qPYtB/7Jf/Ooq2StwlZEOQOtWKqvvyeY31JDcgA4KzMnXg65Qn9FzXv1z+8SaJclgZ6oNPOB9b3IuCGdXIgrTXiOTkQfu1qbzmcmqcPiCVRHr6ulWLzlMetPgHJLnSUxAxGlBkOlTlS6nbJBdYVK0L3Uob3Ce0G5+2oQKFfYbtD6oZv0Vet2bjadTduTfkH/nfqk8gyu7wJbcZ/ACmnN5IFEdlo0p20gH4mT6VfwefSsRjufECe8dQkv5+OA6hBFnaIBRjpnI3RrlkY6/o7bnLdCjfkwEU7nG6HVKDeJJSCRwIgqDPaWoCx3nbZItN+OX9Gene4r3pTXndHhTzyyFbqWbZE/mdK0hfOWT08icwsPp0FJc9QsrXyflcugJHPy8fjjevUbILILPIGOVpNPkPdAEhpcrt9iakaH+fNw4Zb++K2c4/ABccW4/O/nY0nrh2MzLxiNKd1R5EtBVnOSm9NjGd70bhPbpfN7ikHIpnFnotyp39A5LlozR/N0b18qe0XlJtKNO3dgpqHoULMR4WYh61ST+wWc+GUzGqNlhI4KH1I/m6ahlafS/Te5BU4Dr9jtuUF9Vyc4Bmp0MtUhScsT+IW160oF/Pxs9QL17nuxCjXTDQhBd08Uwm0+tS6SFLgoKRKylLPlWBBy+0tk1GLDN2on5+lUrwqnYsbndMC1JRImJX8kr4TpCkJptErsD+lR9Dmp7uT78C0EYPxn7yH1I6wVaJNd24/YL4JTemlSGvcKQ+vHfk8MO7/5OGJSr4EZ53/ufPmeOB1TS2fyewNroPV8pmS5OugaZ+8nK1EDprfn+G/bFYPeV0+X6Cbo2Wy5T3dvlVGRmmP4e9Sd8yxvKjmIxJhgtlT+1ojZgCAWhs7QxmpoSmTnFISJrqmqbmPgh3DKmR7guA5ah+P79AXU1y3+PV1a5FMuMV1K36UDoE2uMtNT8bTUy7BnsvfQWNaKXqZqrDG9iAu6CVhaP9ivD31fOQW9EBjWikcFyzG49mvyqNbTEnARY97g8G1f5OTyuX0lstpZYI7wFujrZSjo16WZ/P1lCHpVy7DBVfdht/OfwlOT9DaTXCghyffxwjXXFzqug8VYh6cktwEne/pGL1LzFGvpRJTDcwmoC45H5k+NZ4AYDKZfP42y1lPbaVymWW2ohjVWJ//MJ65uBBvXtkTz6c/JTfR+wa7Js/fuqDE7alp85SfCrFVPrcduzy/4zNS8c3x+oepa94BrlsDFPbX9FUyyUGQ7qA2Axc/Ke9T5bMpNv2+j7Go+owsWrQIDz/8MOx2O44++mgsXLgQgwcPDrr8J598gunTp+Onn35C9+7dceedd2LSpElh/15M+oz4Jj8b+bzchKBMcy16LpKcPsD+HYDbpT7VHLAWYMvwt3F8dhPMy4fp2p2bUovx0/mvwZXRHdUNTnliurwDML8wPGAa+qYlQ9WZerWpz31nga0U8tRCyzfdNgC/tNHK5wuxDw5kIANNaqKpfpZKPJO6GOa0bGS01kBorJFHAmjT3mvbZbXzJ/jO4jv+A/mEVfZlwx45oHM1Atf9W95jdbs9TziS3NEvq4f35qCkjVcmjALkLLkNe+ShZYA3m2tmsbyvfXOaKDVbvm33tlJ5vbXD06p+Bl6+3H+WYUeFN9tudk85W2xmkTcfjVJQZvUAzp0LrL/Pvxr03DnAd88D+8shZffEz8ffg6Iv7sVupwWtSEI2GtSU/NqU3kstD8GBDEx0Tce5fQtx4nHHoMS0DwPWj4VQux3N6SV4q+BmDN72mC63gXbyPN/qb23mU+2cLbvEHOyDTZfnI1kQ4ZLM2C9lwCbIT5w1UhYqkYvnXUPxuHWRmgdFGSmhqBDzcbPrFtxvWYYsNKnJu7RZR5X8JdWeJFDKXEB1SMMRZjtMhUfLQYOzHtLy4WgSLUit26brWyXZSiB4JkKUcnrjx5Mfw2Gf3Iy0A3a1H5ac3VZzHStTOyjH3/fcHbnUOy+VJ3kZAPn8CTSZIyDfHJNSvHOBKHlJhj0CrL3Te5M8735PUN0iv3/VG8C7t6BxXyXu3TcMD1qfU3PY3Osah1mWl9SsuNrstdXIxlOWJ1CAWigjkpTj3oQUrLb+HRa0qsm4mmFBL6EK+6RMuGFCFXJwp2uimkVZORa+x3Cc605dhs5GpOqOYYWYh0ak4hBNUr4bXHfgQcsS9M1oQmqySb4+tde1Mr+KNnFWswOo/t2bZFI7U7eSbEvNcrsUyD/M+9lA03lYM/U5YjTH3F25BS0vXAbB6UBVa4ZfKvfesOMl63wkCyJ2i7kY6ZqDemsBJhxrwY1ltyGloVw36uqANQ8mU5J+WgwtW6n8gAVBDnpXjvaea8q5lN0TuPifwEf3yzUYkOR9ldVdbrI5UOvphCrIE49as+TveuUKIDlNnvU3o1D+DvX7S+VgbvV077mcni9neFXmvGl2yEPLX78WqN8jn5dqVtdHvOWhklnYagNGvwxklwbe1gjELOnZqlWrcPXVV2PRokU47bTT8Mwzz+C5557Dli1b0LNnT7/ly8rK0L9/f0yYMAE33ngjvvjiC0yePBkrV67EyJEj23VjoqKdflm5CabmyK8rsyDW7ZZvYFabPKdBVg/9yZDdU65a1Kby1XYACjHpnhKQ/NaYgrHNf/Ob8TTDE2RMPKMPlnxappsULNS8Isrnfee4EOAzi2ydHXjnJjmviu86a1Pkj/s/TaEQoqAJMJU1ADkIAOTCwzfNsG/qYeV7AH3hM/J5uU1fl5XQLNdovT/De+GLbvmJtfBo+clA+1ttHAvddgH+54TymYrvvJ13BbN8nlz/gX6WTU+B+uHeTMz/98/qBGSAfhBfEWpgScvEXZeejAuO7R6w0P2qOhXTn12tm/9IGfqpKEYN/p09Hzud6bjywJ3queEb2CqprJuQiuNSq3C/eyFqkY47XDepAXeD52akzH2yW8yF2QQUYp86rb3SByTQzWyVJjC6zzQJ9Ul5KG8weZOVIR+/nbccZ/RK099wtDek7J5ytbM2YMgokAt8a6b3OCoFqHLclGOgPT9DzUulHK+s7vJnmh1yZuZ3b/EG1811cnVGZrH32lczteaoc53ovrtqqyfwrZBfH7MKGypbMe6VrX6p3n0DAIcpG1c1y8cwE00owD48bFniN4HeYajAISn1uFt8BjXIws2uW5GGZnUKBe0xtIs5MEFCoWl/wGOozUrsewwnmuegNSkD6fVl6nQFFSjEH+c9jzP6H+K5ttu4rqO5BsO5dpUb7ZvX+3/OM1nn+l+r8ff/7NNNSKlNuX+DaS4uOP1ETDn7cLk/XMV3wPNDvE0dSqBRb/dc957X0/LlYMHt8k6HoZ2fa/lwzwgYQV/GqmWcpJ8UTzDJ5y8AFBzlvy+dDfL7K0eFPpeVB0XfiUuXnq954Frm/4AZKAg8SDELRk4++WQMGDAAixcvVl/r27cvLr30UsybN89v+b/97W949913sXXrVvW1SZMm4X//+x+++uqrsH4zpsGIVqibaaQng+9Tme/00lqOXXAnp+PJL/di2Rdl2H/Am6+g2JaCWRf1w9D+xQFneDUJ3kRAAJCdJldN7m/yT3Os/a6A2+27bspTTKATsx3nLgjKt/DR3oAuWgi8MxlI6ybXqGhvQBc/KX8+qzjw+rVxLAIGRr7nhBKsHaiVL2rP1N3yd/gXqMqsyFX1zSjITMHAXjnYsKNW/Vud7TPQdntmVT79wY8gOHbhVcscOJCBK133qAGHAKDIM08PLBn4xu5GVX0ztlc3YeGHvyITTUjT1J4pletPXXkCFr/3GbbXm/wCWyWIKUCtOvPpDrEAYzy1O9p07NqaPW3wM8Y1E/eOPQ/n9SsKPiu0dt+3FTBoC1ntcfQ9boGOYzg3QOWGoP1+5dgD3u/UlgeSqJsFNtTN1W3JwukPfoRGxz7d8VAIAI7JasAbtw7Bhj1i2Mfw6f/7DGV1oY+hAAE9TDE+huGK5Bpsj88BumswP90KCMD+2moUWVtxfP+jvdef8jvLLgCqf5VrIpQaD7Wm1AzADBT1lzuLvjHOG3TqauN2eZs6olzvgPugPYO5tj53kGISjLhcLqSlpeH111/HZZddpr5+2223YdOmTfjkk0/8PnPGGWfghBNOwOOPP66+9vbbb+OKK65AU1MTkpP9OwY6nU44nd7hS3V1dSgtLY19MBKuGB5U35uW7iYV4P1ANzVAno680nEA+xpdyM2woijL/7vigm/hE8kNqKPXTetg1yXAdyuz/BahBg2aWjHliC4eO8A/0PR8zjeI9Q1yJ634PuBqKE/lb3Z/GdmSQ53h2e5oVifcWpbyKFpT8nCDewa21ZvVz/XJEjH54jP816m9n5jD1cHHK9h3K8cR0GeWCHUcwzmGgWaABqCrWTkq04lvznwxdsewK1Ga8q2Z3uBUrY17HrBkeB94YngzD7puHRzMRSsmwcju3bvRo0cPfPHFFzj11FPV1x944AG88MIL+OWXX/w+c8QRR2DcuHH4+9//rr725Zdf4rTTTsPu3btRXOxfeM6ePRtz5szxe73TBCOAIQeVqK2bUjBtBblrN9tx11s/+tWo5aQlY96IYzD0sDT1fPf7rrwDMKdkwm3JCvkbYUmQ6yqa4xjOMfT9Tu133ze0FOcekh77Y9iVJcj52Z7CDUaSgr4TguAzhESSJL/X2lo+0OuKGTNmYPr06erfSs1Ip5JiC37SdWA+f0osQ/sXq9XlkdwwzCYBgw7NC/q+8r1fb6vBV39UA5CXP+WQPO93e873YN9lBkL+RlgS5LqK5jiGewzDqRWN6THsyhLk/DRCRMFIfn4+zGYzKisrda9XVVWhsLAw4GeKiooCLp+UlIS8vMAnvdVqhdVqDfgeUaJr66Z0MN972uH5OO3w/Hb/bvIXi+MYq3ODKNYiyjNisVgwcOBArFu3Tvf6unXrdM02WoMGDfJb/oMPPsCJJ54YsL8IERERJZaIk55Nnz4dzz33HJYuXYqtW7di2rRpKC8vV/OGzJgxA9dcc426/KRJk7Bjxw5Mnz4dW7duxdKlS/H888/jjjvuaL+tICIiorgVcZ+RUaNGoaamBnPnzoXdbkf//v2xZs0a9OrVCwBgt9tRXl6uLt+nTx+sWbMG06ZNw1NPPYXu3bvjiSeeCDvHCBEREXVtiT1rLxEREcUMZ+0lIiKiuMBghIiIiAzFYISIiIgMxWCEiIiIDMVghIiIiAwVVTr4jqYM+KmrqzN4TYiIiChcyn27rYG7cRGM1NfXA0Dnm5+GiIiI2lRfXw+bLfgkgnGRZ0QURezevRuZmZkhJ+SLlDIB386dO5m/JEa4j2OL+zf2uI9ji/s3tozev5Ikob6+Ht27d4fJFLxnSFzUjJhMJpSUlMTs+7OysngRxBj3cWxx/8Ye93Fscf/GlpH7N1SNiIIdWImIiMhQDEaIiIjIUAkdjFitVsyaNQtWq9XoVemyuI9ji/s39riPY4v7N7biZf/GRQdWIiIi6roSumaEiIiIjMdghIiIiAzFYISIiIgMxWCEiIiIDJXQwciiRYvQp08fpKSkYODAgfjss8+MXqW4NHv2bAiCoPtXVFSkvi9JEmbPno3u3bsjNTUVf/7zn/HTTz8ZuMad26effoqLLroI3bt3hyAIeOedd3Tvh7M/nU4nbrnlFuTn5yM9PR0XX3wxKioqOnArOre29vG4ceP8zulTTjlFtwz3cXDz5s3Dn/70J2RmZqKgoACXXnopfvnlF90yPI+jF87+jbdzOGGDkVWrVmHq1Km4++67sXHjRgwePBjDhg1DeXm50asWl44++mjY7Xb1348//qi+99BDD+Gxxx7Dk08+iW+//RZFRUU477zz1DmHSK+xsRHHHXccnnzyyYDvh7M/p06dirfffhuvvvoqPv/8czQ0NODCCy+E2+3uqM3o1NraxwAwdOhQ3Tm9Zs0a3fvcx8F98sknuPnmm/H1119j3bp1aG1txZAhQ9DY2Kguw/M4euHsXyDOzmEpQZ100knSpEmTdK8dddRR0l133WXQGsWvWbNmSccdd1zA90RRlIqKiqT58+errzU3N0s2m016+umnO2gN4xcA6e2331b/Dmd/7t+/X0pOTpZeffVVdZldu3ZJJpNJWrt2bYete7zw3ceSJEnXXnutdMkllwT9DPdxZKqqqiQA0ieffCJJEs/j9ua7fyUp/s7hhKwZcblc2LBhA4YMGaJ7fciQIfjyyy8NWqv49ttvv6F79+7o06cPRo8ejT/++AMAUFZWhsrKSt2+tlqtOPPMM7mvoxDO/tywYQNaWlp0y3Tv3h39+/fnPo/Axx9/jIKCAhxxxBGYMGECqqqq1Pe4jyPjcDgAALm5uQB4Hrc33/2riKdzOCGDkerqarjdbhQWFupeLywsRGVlpUFrFb9OPvlkvPjii3j//ffx7LPPorKyEqeeeipqamrU/cl93T7C2Z+VlZWwWCzIyckJugyFNmzYMLz88sv46KOP8Oijj+Lbb7/F2WefDafTCYD7OBKSJGH69Ok4/fTT0b9/fwA8j9tToP0LxN85HBez9saKIAi6vyVJ8nuN2jZs2DD1/4855hgMGjQIhx56KF544QW1wxT3dfuKZn9yn4dv1KhR6v/3798fJ554Inr16oXVq1djxIgRQT/HfexvypQp+OGHH/D555/7vcfz+OAF27/xdg4nZM1Ifn4+zGazX/RXVVXlF6lT5NLT03HMMcfgt99+U0fVcF+3j3D2Z1FREVwuF2pra4MuQ5EpLi5Gr1698NtvvwHgPg7XLbfcgnfffRfr169HSUmJ+jrP4/YRbP8G0tnP4YQMRiwWCwYOHIh169bpXl+3bh1OPfVUg9aq63A6ndi6dSuKi4vRp08fFBUV6fa1y+XCJ598wn0dhXD258CBA5GcnKxbxm63Y/PmzdznUaqpqcHOnTtRXFwMgPu4LZIkYcqUKXjrrbfw0UcfoU+fPrr3eR4fnLb2byCd/hzu8C6zncSrr74qJScnS88//7y0ZcsWaerUqVJ6erq0fft2o1ct7tx+++3Sxx9/LP3xxx/S119/LV144YVSZmamui/nz58v2Ww26a233pJ+/PFHacyYMVJxcbFUV1dn8Jp3TvX19dLGjRuljRs3SgCkxx57TNq4caO0Y8cOSZLC25+TJk2SSkpKpA8//FD6/vvvpbPPPls67rjjpNbWVqM2q1MJtY/r6+ul22+/Xfryyy+lsrIyaf369dKgQYOkHj16cB+H6aabbpJsNpv08ccfS3a7Xf3X1NSkLsPzOHpt7d94PIcTNhiRJEl66qmnpF69ekkWi0UaMGCAblgUhW/UqFFScXGxlJycLHXv3l0aMWKE9NNPP6nvi6IozZo1SyoqKpKsVqt0xhlnSD/++KOBa9y5rV+/XgLg9+/aa6+VJCm8/XngwAFpypQpUm5urpSamipdeOGFUnl5uQFb0zmF2sdNTU3SkCFDpG7duknJyclSz549pWuvvdZv/3EfBxdo3wKQli1bpi7D8zh6be3feDyHBUmSpI6rhyEiIiLSS8g+I0RERNR5MBghIiIiQzEYISIiIkMxGCEiIiJDMRghIiIiQzEYISIiIkMxGCEiIiJDMRghIiIiQzEYISIiIkMxGCEiIiJDMRghIiIiQzEYISIiIkP9P+XJ2o7psVapAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 假设分块计算的本征值和IPR为 evals_block, ipr_block\n", "# 整体计算的为 evals_full, ipr_full\n", "matched_ipr = []\n", "for e_block, ipr_b in zip(E_full, IPR_block_full):\n", "    # 找到整体计算中与e_block接近的本征值\n", "    idx = np.argmin(np.abs(eigvals_all - e_block))\n", "    matched_ipr.append((ipr_b, IPR_full[idx]))\n", "# 查看匹配后的IPR是否一致\n", "print(np.allclose([p[0] for p in matched_ipr], [p[1] for p in matched_ipr], atol=1e-6))\n", "\n", "# 绘制匹配后的IPR对比\n", "plt.scatter(range(len(matched_ipr)), [p[0] for p in matched_ipr], label='block')\n", "plt.scatter(range(len(matched_ipr)), [p[1] for p in matched_ipr], label='full',marker=\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 240, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/f4/69rntmy5123001gcp2c5g7jr0000gn/T/ipykernel_74310/2970498983.py:41: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34892 (\\N{CJK UNIFIED IDEOGRAPH-884C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32034 (\\N{CJK UNIFIED IDEOGRAPH-7D22}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24341 (\\N{CJK UNIFIED IDEOGRAPH-5F15}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 30697 (\\N{CJK UNIFIED IDEOGRAPH-77E9}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 38453 (\\N{CJK UNIFIED IDEOGRAPH-9635}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 28909 (\\N{CJK UNIFIED IDEOGRAPH-70ED}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65288 (\\N{FULLWIDTH LEFT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 39068 (\\N{CJK UNIFIED IDEOGRAPH-989C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 33394 (\\N{CJK UNIFIED IDEOGRAPH-8272}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 34920 (\\N{CJK UNIFIED IDEOGRAPH-8868}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20803 (\\N{CJK UNIFIED IDEOGRAPH-5143}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 32032 (\\N{CJK UNIFIED IDEOGRAPH-7D20}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22823 (\\N{CJK UNIFIED IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 23567 (\\N{CJK UNIFIED IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 65289 (\\N{FULLWIDTH RIGHT PARENTHESIS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/anaconda3/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21015 (\\N{CJK UNIFIED IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAo0AAAJNCAYAAABURU/5AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOy9eXwV1f3//5psEJEGE5YQKARFEkEMKFTUSFgEjBSpSN36RdwFXAp8CkpdKghErLUuqFgJbiTV9qMs1aBgZdGPKDcSrKCgIiCiLAINCUnIXeb3B78zzp2cmTkzd+beM3PP8/G4j+TOPe+znzPv8z6bJMuyDIFAIBAIBAKBwICUREdAIBAIBAKBQMA/QmkUCAQCgUAgEJgilEaBQCAQCAQCgSlCaRQIBAKBQCAQmCKURoFAIBAIBAKBKUJpFAgEAoFAIBCYIpRGgUAgEAgEAoEpQmkUCAQCgUAgEJgilEaBQCAQCAQCgSlCaRQIBAKBQCAQmCKURoHAJSRJMv38+te/BgC89NJLTO7/93//FwAwZMgQU7ennnoqAGD37t1Mft95550AgIceeojJfXV1NQAgPz/f1O3ZZ58NAFi3bh2T34899hgA4IYbbmBy/9NPP4k890CeCwQCb5OW6AgIBH7mP//5Dzp06ED97fXXX8eaNWuU70OHDkVlZaWuX+PGjYv6vmjRIowdO5bqdu/evRg6dKjyvVWrVti9e7eu34899hiampqU79dffz0WLFig637gwIFR399++22ce+65VLcfffQRHnzwQeV7z5498cEHH+j6/fvf/z7q+3333acoVzQ6d+4c9V3kOd95LhAIvItQGgUCF+nQoQNyc3Opv2VlZUV9z8jI0HVLftfK67lXKyMEI79PPfXUKJnMzExD96mpqVHfs7Ozdd1nZ2e3kDXyOzMzs0XcjNxrEXnOd54LBALvIqanBQKBQCAQCASmCKVRIBAIBAKBQGCKUBoFAoFAIBAIBKYIpVEgEAgEAoFAYIpQGgUCgUAgEAgEpgilUSAQCAQCgUBgilAaBQKBQCAQCASmCKVRIBAIBAKBQGCKUBoFAoFAIBAIBKYIpVEgEAgEAoFAYIpQGgUCgUAgEAgEpgilUSAQCAQCgUBgilAaBQKBQCAQCASmCKVRIHCRzp07Q5Ik6ufGG2+Mcvvuu+/qupUkCevXr49yf+211+q67dGjR5TbEydOGPo9e/bsKPfPP/+8ofs9e/ZEub/gggt03Q4dOjTK7Y4dOwz9fvnll6Pcz5o1y9C9yHNv5blAIPAukizLcqIjIRD4kZ9++snUTUZGBn7xi1/gxIkTqKurM3X/i1/8AhkZGaitrUUwGDR0K0kScnJyEIlEcOTIEVO/MzMz0aZNGzQ0NKChocHUfbt27ZCWloajR48iHA4buk1LS0O7du0QDAZRW1tr6nebNm2QmZmJ+vp6NDU1mbrPycmBJEkiz1XwmOcCgcDbCKVRIBAIBAKBQGCKmJ4WCAQCgUAgEJgilEaBQCAQCAQCgSlCaRQIBAKBQCAQmCKURoFAIBAIBAKBKUmnND777LPo0aMHsrKy0Lt3b/zqV7/C7Nmz0djYaCobiUQQDAYRDocRiURA20Mky3KLj0AgEAgEAoHXSard06+//jomTJiAZ599Fq1bt0ZlZSXy8vLw7LPP4uDBg+jatauubHNzM9LT03H06FGcdtppUb+Rc8uMspJ2tplAIBAIBAKBV0gqpfH888/Hueeei+eee055VlVVhTfffBPbt2/HoUOHkJOTg8suuwy33norOnTogJSUk8ZYkk2yLCMQCKC8vBxffPEFRowYgTvuuAPZ2dmGiiGxOqakpKCyshLl5eUYNmxYVDi0oohEIspv5H8SjlBW6ZDy2bp1K0aMGIGZM2ciMzPTUCYSiSAcDiv5SzvEmJbPyZi/icRu2ZK2RxBlKxAIBNZJS3QE4kVzczM+/fRT3HvvvVHP27RpAwC47LLL8PLLL2P69Om48sorUVNTg+zsbIRCIWRkZCjujxw5gqamJpxxxhmYMmUKevfujYMHD0KSJMiyTH3RBINBpKenQ5IkVFVVYf78+Vi8eDEGDRoUFU56enoLWfKikyQJqampAKAbjpZkfek1NDSgoKAAkydPxllnncVsRa6rq4uyIqvzWU8xZy0LgTPYLVuakijKViAQCKyRNErjTz/9hHA4jE6dOkU9LykpwY4dO/Dyyy9DkiT86U9/wrZt2zBz5kykpqZGKYOyLKNdu3Zo1aoVdu7ciTvvvFOxNBq9YNLT0xVLY4cOHVBcXIwZM2YolpK0NP1iUIcdiUSUOLFg5aWnDscN/50Ih1XmlFNOwY4dO/DGG28oeWwEKZ+2bdsiEonoXpXmZByFjD2ZU045Bdu3b7dctlpLoxV4redCxp8yWlk7MjymiyajfRZLfsWDt9MLXPV/dHCHq/47QdJMT//www/o0qULPvroI1xwwQXK861bt6JLly5o166dMvW8ZMkSbNu2TXkptW7dWqnINMhLKRKJRCmAtErvZHY7OT3ttc7GzL0Ws6UDRhiVvfp3HvPCbzKJLFsr8Jp/PMsEAgEsXrw4qu91a+kBccdbHtBk7cjwmC6hNJrjBaUxaSyN7du3R2pqKvbv3x/1/KyzzgIArFq1CpdeeimamprQq1cv3HLLLSgqKsLBgwfRpUsXxb36JUP+J52Wev0ji0JHQ91QtOGwvODU4bsxHuC1MathzS+aHBBdZtq00spHED/slq1aVv1d+ztBlG38aWhoQGFhIaZMmeL60gOBwA5SOt/vvniQNJZG4ORGmPPOOw/PPvssgJOj1IaGBrRq1UqxEKpfLBUVFViyZInuhhW1W/VGClmWLU0jE/Qsk2bP42lpNBolOhmOkzIAmzVKLz9Z/PFKXvhNBmC3NNIUTaOwnbAS8Z5/PMsA9stW25bNBuPxSI+wNHrf0lh1SqGr/l/WsN1V/50gaSyNADB9+nRMmDABAwYMwK9+9Sv06dNH2Qij7VxWrVqFsrIySxtWiLJIYK34LG6MGpPeSDuJxgOGmOUvizLBYwcmYCtbgK5gmJWtKHPvwqvSIfA2KWmiTiXV4d5XX301nnjiCcyZMweTJ0+GJJ3c8KA+aoUwYsQI/Oc//0FaWhomT56Mu+++G/Pnz0cwGKQqbZIkIS0tDbIsIxQKIRKJIBKJRLnRg0xxb9q0CZMmTUJxcTEKCgqwfXvLUYf2RUcOHA+FQgiFQlRFkfiv/lghEAgo8ZozZ47pQejqQ9BJnFjj5ZSiS/KnsrISw4YNw9y5c3HgwIGoMtG6J3kYDoeVeGjj5GacWRAvwmjUddPKIf2kjrLUTfI9EdhNXzgcNk0f+RvvOswKy4BAbwCt/ms2yBYIWJHSU1z9eIGksjQCwJQpUzBlypSoZ1rrIADs3bsXPXr0oB7xQayTNCRJQn19Pdq1a9fCTzMaGxtRUFCAcePGoaqqCr169TJ0T9b01NbWIicnx/DlQHvO2nlaPeYkljxwskOvqqpCWVkZXnjhhRbWYvUxSuqw7e6wjRduWlC8aJ2xWjeBk+nUrkEGzDeWJSJ/rKZPlmXs2bMH+fn5uukzkvVa+QsEgviSVGsa9QiFQoqVkLY2ZtOmTViyZAm2bt2KQ4cOYcWKFSgoKDBce0FG++Q3opiaTRuzTKfRftc+11vjo3WjfsayloS2HojlZaO3/ijWqXmWMM3SbhSeOs5G8olcF2R24DVNhuw6Ve/416tfaljzLRHrqYzK1kqZGimRiVwnxtJuWeq8lXrtZnqckrGDF9ITiwyP6aLJ0OpvPMrfLms6ne2q/yMObHXVfydIOksjDbWlkQaxAF5++eVYvXq1IxZAPZxqLDyNBYziov3NqY7CyfQbvcB5QFijnMHPaQP8mz7eFQ2BwE/wPReXQNQjocGDB2PatGlo3749GhsbMXjwYMO1fWSjTHZ2NiKRiLLGkfhnBssLXLtWh6x9uvjiizFnzhw0NTVR3amfqdcesqztsmIdNIuv+pn6o02PlXWU4XAYzc3NCIfDVDfq9NHSpRdvrWXGKN5OYFUhLSkpwbRp01BUVIT09PSoI6L0yM/PZ06DG2l0GnX9N1v7p67/ZN0t7XegZVnEe7BgZ1BFa2fa53ptkRdY1yIDP1vNZfnn9eTadk1r74ke+CXzelWvIqVLrn68gLA0gt6p0mhsbGxxjpjRC/rIkSPIyclRrhG0EydWaNYmo7jpXZ2nRq+ziceI3mp6tHl94sQJtGrViimslmnxRuN1A69aa6ye8cdibU1k/U9mrK5FjuWq1UQiZggEXiRp1jSGQiE89NBDqKiowP79+zFgwAB88MEHynqwzZs3o7a2FllZWRg9erTuTTDEQqH+PxwOK0qh0QvHbL2QU7CswbK7bkstF8+1MCxxI+it8dPrYIllIhwOIzU1lXq7BIs1MtHrgrT/68kYrZnTW2sUz/SYUVFRgfLy8hbnp6r9BH7eEU+gLUPRtm89WNf+qePAa/7xLkNQyxjlO61+a/2g9Xc85QEtfVoZPTfadBFY6ywPfRdrH5Vo3s8/x1X/h+3+j6v+O0HSWBoXLFiARYsW4eWXX0afPn2wbds2AD+P9goKCrBo0SI88MADGDVqlK5li1jo9u3bh44dOyItLU1RGP/73/9G7RjWYtSg9aahEtGIzJQQ3jEbwWvzOp4WYUFs+HFHvMAd9BRM2v9ex4v9tMCbJI2l8de//jU6deqE8vJyXTeRSCRKQaMpbnoY/a7+zWrDtqI0Wh2t8W5ZcmL0aSffvTZaTyYZPUs3eab2Uy8cAq1sjWStEIsMb3kebxmCkdXNKByWcHnIA7PBOc3SaGRp5SVdVmS8Zmlce2aRq/4P/fozV/13gqQZghcXF+Pf//43vvrqKwDAF1980cINqaR2KmtFRQWGDx+OefPm4eDBg4YKpizLyoHcwWDQcOE2+V/7TKCPm3lEysDJsuCxc+QRt+u+mdU/Fj95xH/1zm/pEQj4I2mmp++55x7U1taisLAQqamp6NKlC3bv3t3CndloR2vZkGVZuXJQO2Umy3LUWkf1C4ScjUdgHVkZjUytvgTsvDTiOQK0G4627Oz4o2eVcgNeR9VeQp1/ZnlptWz9Wj68pos2a8MWTxmAFFO7jydW6qyRG97T6SfENYJJpDS+/vrrWLp0KSorK9GnTx/F4uhEx1laWorS0lKlszv33HNNLQxmio0TU2Q846e0CLyAqG/xxitt3OxwfBrkOllZlpGamkrtx2kzRV7JE4FAj6RRGmfMmIF7770X11xzDQCgb9++AJyxQml/M/pO+422Xo7mf6zWMydwKly3O1DROfsPbXvRPjfmpBXKmozzJJPi4JW02rmKUn0NJQ2994NX8kRAR0oVZZc0SmNDQ4PlXZR2FbRAIIDFixdj27Zt1JErzYoYDoeRkpKijFyJO+0Cflpn5EQn5IYyqh7Bq69f1EN79I06LwhGFlzRGXuPZCuzZEuvFygpKUFJSQmAk/0Ly+H46rMhzTAyMgi8RYpQGpNHaRwzZgzmzZuHbt26oU+fPtixYwdGjRoFwHxXmtXRodlBw1r/JElCfX191HE9rJ2M05Y/J0fCZAQ/btw4VFVVmV6/qD36xkpH66eXcTJZI3hJKy/xcAu/py9RxGvds0DAC0lz5E5dXR0eeOABLFu2DAcPHsTAgQOxYcMGACctXMTKZ7T9385xHLSpZztY8YfHYxfMOlcjxZ3IsBw5YeU3o7haDY/HPBcyscloZe3I8JguMxm9GQI9mUgkonxIP0o2+tFmS9TEox1Zaa8sfYnb/QJLXMxkEl2HWGWsvGt54P/6n+eq/xfVfOqq/06QNJbGtm3b4oknnsATTzzR4jftVIPeXzV2FUiWBmG0Zsveei7+sKposjwXCASxY3WGoKmpCZmZmYhEItRbdwCxxk8g8AtJozTGA+3oSf28oqIChw4dwvjx45Gbm2u4kJpcTajelUcLQ/3XCvHsqElY2nhrocVHT8ZK3N1Ip3jJOUei85KlLSQ6jvFGvcZvxIgRpu7Jeu309HTDAS5gvtnPDeLVX/BUT3iKi5+QUpPmaGtdRA7EgaqqKtTU1ODuu+9G27ZtIUkSdu7cqdtpEoUyJSXFcuNXK2hexqkXirBK8k2iy4d1uYeADVl29uB7gT1E/gvcQlgaXUBrcSTnOAJAVlYWZFlGz549Df1QH/5t1AFYXeOnJ+MWVte7WJWjybqNmFIT+B3WGQJA/0IEs3XM8Z7t8Es4gsQhdk8LpdFV9BZ/W4FlA4k2TIFAIIgnRuu/BQKBf/DF9PSGDRswZswY5OXlQZIkLF++vIWbL7/8EpdffjmysrLQtm1bjB07VvmtoqICF110EQoLC9GnTx/Mnj0bjY2N1LBCoRDC4bAyBROJRNDc3MykHKo7VqNpZNo6Rr1n2g9N3m2cCM/JtUSBQACTJk1CcXEx5syZo1uWaiKRCILBYFTZalE/F1NwicFu2ZIzQAH9g/O1n2TGT8pfvNKSqDxL9roaT6QUydWPF/CFpfH48eMoKirCjTfeiCuvvLLF7zt37kRxcTFuvvlmzJ49G1lZWdi1axcAYOvWrbjssstw3XXXAfj5+B09tLsDtRtazBow+d3ucQVOu3UC3qZlBgwYgAEDBjC7J/HX3geuhbZbnre0+x27ZWt00oHY2SsQCARs+EJpVK8ZpHHffffhsssuw6OPPqo8O/300wEAZ511VtRLhdxEEgwGkZGR0cIv2vlS6uc0tGv1WGT0EC8yc+zu0BRKBP9o18/ZkTX6naBnjRTl7138Wn5+TBOviDWNPpmeNiISieDtt99Gr169MGrUKHTs2BHnn38+3n33XUQiETQ2NlJfEOnp6Y7FQTutqUY93WY0La6GHMkTDAYNp1LVYYspDDZIeVx88cVi+tPniDIQCAQCa/heaTx48CDq6+vxyCOP4NJLL8Xq1atxxRVX4M4774QkSWjTpg11OlptldC+XGibT7QbVrRrDGnT0bIsKwfpPvPMM5g1axYOHz5smqY9e/YgJSVFmU5lVUKceEl65UVrtoFIjfp3cgUkrTxo60qDwSAkSWpRh9TheyXPEkGsVhKWstU761TbPlnlBN5ElJ8gVqRUydWPF/DF9LQRkUgEADB27FhMmzYNANCvXz/s379f6UQikYjlA2D1di1bedFIkhR1kK4sy+jSpYtp+Pn5+czH07AcmaF2Z0XBitfGGzvh2DnfEjh5sPHgwYOVMjYrD7VFWm/6k2XXe7KSqClDoUD4i0AggMWLF2Pbtm0YMWIEZs6cqRw6TkNtEAiHw1Fn4hotUxDtNrmRDPY7JAu+Vxrbt2+PtLQ09O7dO+p5586dlf+9/AJx6sgd2jlrbsjEK27xgGXTk3YNrEAgcB4yQzBlyhScddZZOHjwILp27arrXpIkhEIhpKWlWb76UCBIZnyvNmdkZGDgwIHYsWNH1POdO3cq/xNrpBZJkhAIBDB58mTLR3ywrDmkrXEzm0qj/a83Ha71J9mUFrvrRdXrE2mYLQcwKzOz3wTWqKiowLBhwzB37lwcOHBAtz0TgsEgQqEQgsGgUtba5QTadpmodahW67D66KhQKGRpzbOXlaSSkhJMnToVRUVFSE9PZ5qxIct7aH2ommTsO81I1vwQR+74xNJYX1+Pb775Rvm+a9cubNmyBdnZ2ejWrRtmzJiBq6++GoMHD8bQoUPxzjvv4L333lPck/VoRmsOJ0+ezDSClWUZe/bsQX5+fpS/dmG1sgmrVkusWh+am5tb3J8LsB3PEguivOxTVVWFsrIyvPDCCxg0aBBqamqQnZ2NUChEPf0AQIujlVjzPxFlZLX/kSQJ9fX1aNeunfKM9bSGZK2DVtY/C04i+qzkRZK9PLz8/1m3bh2GDh3a4vnEiRPx0ksvAQCWLFmCsrIyfP/99ygoKMCCBQswatQoAC0VM72/ZO2jmfIG0NfFaBUPIzfASQvKoUOHMH78eOTm5rY4E1JNOBxGOBxGampqizhaWdfo9vS0EzJ2ZQHjl4L25UF7mZiVnV+m970iQzBrS3rhsJSxnfJxIy+spIslLK1fscSNJxkWWbO+PpHp0crakfFCOdGe8boUibBl5MWu+t9v9Qeu+u8EvrA0DhkyxFQhuummm3DTTTdRf9NOJ5r9BWJrNCxUVVWhpqYGjz76KOrq6iBJEnbu3ImePXtS/UhJSYEsy4YHk+uhfQELWsJLJ8Z7p+pVjJQ08r8X81vvRa3+neDF9DlFMqddILCCL5TGeGJnGovWWdPckHVFkiRFHVielZUFWZbRs2dPwzDNbjRhjb8W3l6YduPCOj1HszjSBg5aN04Rz/wOBAIoLy/H1q1bmXadRiIRhEIhZZBCLNtaaPkRzzpkJSyWMk4U2rpgNW56afLzwCOWNPkxPwTO4ZV1h24ilEYOcUIBMZp+0vovOsrkxc6aub179yo3KlnBq0qKV+MNCMu0QCBwFqE0OowTo1ztGjk7ftOsX3YtKawWOrdJlAUh3i/bWNbSWcXOOaFEYdTGzWgw4tbyBz3Lr1V5XhWqWKyMahle0ycQeAlxTqNPjtwpKyvDwIED0bZtW3Ts2BG/+c1vWhyxo+b2229H9+7d4xhD/onl5RKv9ZBi3SVfkOUUolwEAoEgvljVe4CTm4YlSWrx2b59O3O4vlAa169fjzvuuAMff/wx1qxZg1AohJEjR+L48eMt3C5fvhyffPIJOnXqxOx/vF+KpCDtoo0vS/yJG6EA8EM8FHg7SxWMNojp+RNrnRYIBIJEw9M5jVb0Hi07duzAjz/+qHzOPPNM5nB9MT39zjvvRH1/8cUX0bFjR3z66acYPHiw8nzfvn2488478e677+LWW2+NdzRtIV603sAP5USbCtXb1CWmPdmxutkIgHKnuXazEW3KX/1XlIdA4F1OnDiBEydORD1r1aoVWrVq1cItq95Do2PHjlFnuVrBF5ZGLbW1tQCA7Oxs5VkkEsGECRMwY8YM9OnTJ+rOYDMS2RE7Yfmj7QoWOAsveevUelVheXYOstnomWeewaxZs3D48GFTmZSUFKSmpiItLY15d7rRczVCsRQI7JGSKrn6KSsrQ1ZWVtSnrKyMKW40vUeP/v37o3Pnzhg+fDjWrl1rKQ98cbi3GlmWMXbsWBw9ehQffPDzQZllZWVYu3Yt3n33XUiShMsuuwxVVVWmfukdOMrzIap24Dk9yXBAbnV1tSVrlCzLCIVCzNYoNSzx47lseW5HZuGZnWrAuvmNdiyUmYxZ3IQMHzJaWTsyPKaL5T3K+27/bWOHuep/z3+sYrY0qtHTe7Ts2LEDGzZswHnnnYcTJ07g1VdfxaJFi7Bu3TpT6yTBF9PTau6880785z//wYcffqg8+/TTT/Hkk09i8+bNSkVkmff3KmYNj9cGmcywHn2jLjujG4KIW73novxPwnte2D09QSAQOI/b5zSyKIg0aHoPjYKCAhQUFCjfL7jgAuzduxePPfZYciqNd911F1auXIkNGzZEvXA/+OADHDx4EN26dVOesRwtwgN61iPtC6SiogLl5eUYNmwY7r77brRt21bXz0gkgnA4rBzQTA4FV7+gjAzQVkeWAnPsHH1DFEbWzStWJhW09c6tskx0/bBidWV9zhKmmayZ9YW25CTReSkQ+B0ej9zR03tYGTRoEJYuXcrs3hdKoyzLuOuuu7Bs2TKsW7cOPXr0iPp9woQJuOSSS6Ke6V0pqIaHxf4s5vqqqiqUlZXhhRdewKBBgyDLMtasWYMRI0ZQ3atv87CKeDHxhxuKQ7yXRQhawkP/IxAI+MRM72GlpqYGnTt3ZnbvC6XxjjvuQGVlJVasWIG2bdti//79AE5ev5eZmYmcnBzk5OREyVjZCEMIBAJYsmRJ1Lqz1q1bG8qorXos65MIZlYINZdddhkuu+yyqGdqhVH7YpYktisHzeLhpqxT8BAHO1ixHsbDGuVWPjrpr9rafuutt6JDhw660/fhcDjK2k5bE2pkeZcke0cIxaIIerUuWyUQCGDx4sXYtm0b825zWT55Xmg4HFYGw9r80pZhopfz27nCU1tnAf100uqsIDZ4ukbQTO8BgFmzZmHfvn145ZVXAABPPPEE8vPz0adPHzQ3N2Pp0qV444038MYbbzCH6wul8bnnngMADBkyJOr5iy++iBtuuIEqY6fDaGxsbLHuzGgasbm5Genp6airq8Npp51mKR4sFhjWToHVH6OF+6x+uUEsYfrdkuV1a5RT5aO1ttfU1CA7OxuhUAgZGRmKO/V6UK21nTUuXs1rr9DQ0IDCwkJMmTKF6WpLAIqySMpTu+Eo0QoiDTtXeNbX10cdlWK0sUorK/AXLHrPjz/+iO+++075rbm5GX/4wx+wb98+ZGZmok+fPnj77bdbGJ2M8N3uaTfR7voimFkQjWRojZ6mxBmtq6L56cddd3aIV17YCY/nPNfKxhKOkR9O7sIEnG9H2mes8Fy2XpJhUYpY+lWWvjge6TFLA2u70ZPjLV1mMnozJbwO9ndcPcpV/wtef9dV/52Av1WdSYLQ1f2F38rTS+lxMq5Gyi35CLyDn8vLLG2izgrcwBfT0/FEu2bMqEHSftdbd6Z1w4rRyJTX0RorfktPspLIMrPalvTWhIp6lzhYlwxorWws/WwiFSptvbK7ztXsHSNwDp7WNCYKYWn0EbROIhAIYNKkSSguLsbs2bPR2Nho6k8oFEI4HEYoFKKOVNXP1H8T1QGr01hQUMB0+XokEkEwGEQoFFLSSUM9Whcj9vhRUVGBYcOGYe7cuThw4AAikYiuW1KW4XAYwWAQkUiEWlbauirK1Puo2/6cOXOY+rdIJBJVR/TqSiLqidX0kLQ0NzcjHA4DoG/40fu4GTcSP9I29cIkG3xCoRA1/gK+EJZGi1gdvSV6tGd1sTWAFju9WdYVsfxuBSsjZZLGcePGoaqqCr169TL0NxgMIj09HbW1tcjJyVHSF4lElN22Rkpkoss0niQirYnY1GJkkWIl2epGPDDLT6NNMzRZsjlR+xttfaxdrNY99f9W+mtJknD48GHk5OQofZoVrNZVWtysbARtampC69at0dzcHHWA9e7du20fFxNveDynMd74YiPMc889h+eeew67d+8GAPTp0wcPPvggSktLEQwGcf/996OqqgrffvstsrKycMkll+DRRx9Fbm6upXCMFvC6RSzhaGVYjiQhMkZHkrAQ68YHu3lNWxhOU4C1cVTLsbixm65kkLGC3uJ4oOUGFtozvbIgxLJBwGq6Yu0f/CZjh1jjBrBtmjGqFzR/aPXPLD5206MXVysyajkzf+zmuTYcPTcseU2exdJO48HXv2PfZWyHMyuMrzbmAV9YGrt27YpHHnkEPXv2BAC8/PLLGDt2LGpqatC1a1ds3rwZDzzwAIqKinD06FFMnToVkyZNwvLly+Maz3h3vGrMrDdamUQfSeJU/mj90XZMRkqlwB5W6jfN4mP0uxXMXmax+s8aXrISr/7ODrQ4ORVP3tJKcLJtxQqPdYKFlFTvxdlpfGFppJGdnY0///nPuPnmm1v8FggEcNttt6GmpsaSn4mwJDgF6+iZpkQ5+fKNRx7QysnIDasMS5g8Wny8JGNk5WVte0YKgRMWeysyXshzIcM+ixHPPt8P9U7bbtXQ8p/ngQYA7Lx+tKv+n/HK26767wS+sDSqCYfD+Oc//4njx4/jggsuoLqpra1FfX19nGNmD6dfxLHEA4i/9Yb4bZYHbnQyvHZcAjZE+Qm8CM/1lue4xQOxe9pHSuPnn3+OCy64AE1NTTj11FOxbNky9O7du4W7pqYm3HvvvUwnoDsxCnVL0YlFVn191aFDh7BixQoUFBRQ3UqSpOzQIxtFZFlWriGkKaeJmP5T/6b3P8Hq9V0AlN2WkUgkKu3qcNxOv11IHHiIixl6cbQSd6fT68YUeSLxUn1wC23aecoLnuKixWrcaPnsZcVTbITxkdJYUFCALVu24L///S/eeOMNTJw4EevXr49SHIPBIK655hpEIhHMnTs3gbFNLGQX3BVXXIFVq1YZ7jYGTiramZmZiEQiUWsc1ehZM53sIJya4rG6o1yWZezZswf5+fktdlfrTb3EEmen4X3Kh3cSNbhzC1Ef+CwXgcAL+HZN4yWXXIIzzjgDzz//PICTCuNVV12Fb7/9Fu+//z5ycnJM/TBaZ8Hb2hG7MlqrmN56MLUbI3+0Vlbas3i+sFjW3JjFkZZ+I6VRz41e3HiqD16SsYLVcGht3c3whIy5jN4MgZ5MJBJBKBTSPQGCFiejNivWNDov47U1jbtvGeuq//mLV7jqvxP4xtKoRZZlnDhxAsDPCuPXX3+NtWvXMimMgJjGoaFVpLRKldth8lYWPMYpWRB5n1xYnSGQJAl79+7F6aefruuG5xkCgYBHfKE0/vGPf0RpaSl++ctfoq6uDq+99hrWrVuHd955B6FQCOPHj8fmzZvx1ltvIRwOY//+/UhJSUHHjh0THfWEQTpFFqsYzepmNmI3ep5otC8ElniyrM3Rs0oKvIeZdVqUbfwpKSlBSUkJgJNlYHSwNIEojEazJ9rffTr5JnAAsRHGJ0rjgQMHMGHCBPz444/IysrCOeecg3feeQcjRozA7t27sXLlSgBAv379FJlu3bphz549tsOMtxUy0S8pWnp5tgAKBAIBQQzmBAJn8IXSWF5ervtbfn6+KyNH3tbmWYW2XisQCGDx4sXYtm1bi13FtB3Xp59+OtLT0yHLMsLhsLJJRpIkZbcxeU7WFJEw1X+N4kcj1rxgtTJWVlYa3qCjXT9F1k6Rm3S0ijYtvXbrpnjxJQ5JkqLaQ1FREZqbm/Hll18ankag3oGfmpqKcDislCNr21CXu1F71YPEQb2hy8wKxwt2ZwjM+mraQFggoCF2T/t4I4wb+HUjjNG6RCOFh+ZO67cdaC8xu4uk7S4mtxI/MzmWPHSrPlhVLMjRSgBa7JRnmcLjrX7HIqOVpT03wii8WNqTUX0yG2yZhWfmJpHlROt/nQ7HyA9e6p3T4YmNMOx8N2mcq/53W/Smq/47gS8sjQL70Nbk0ZRIPYVB7Vb7XLsGUm/zjNXOIZ6dCetaJ1qnZ+Yni9tYaWhoQGFhIaZMmcK0eYAcr2SEGGeeRKtoGZWlkRs9ObM2wlrf9OLOOojhpbytWhljDUMg0CLWNAqlUWBCRUVFiylavY41EokoU7NkOppmwdC+hIwU1FhJ9EvAzMrk9gu5pKQEgwcPVpQEs80DaoXRbMTPo2LhReKVd2ZKbaLbCu9ol5vwiBfiKPA2QmkU6LJq1SqUlZXhhRdewKBBg1BTU4Ps7GzIsoyMjIwW02VqZZFAXkZm01+s65Os4vTL0IpfWqXLaNrdiES90NUWZPESisZsfZzR1LHeNJy2Pmj9NysD1jZEmykwUjbctoZ7Bd6nTgFvxNHLiDWNgO9yoKysDJIkYerUqcozWZbx0EMPIS8vD5mZmRgyZAi++eYbJv8CgQAmTZqE4uJizJkzB42NjYbuI5EIgsEgwuEwQqEQZFlm7nCd6pjtdBbal4YkSSgtLVWuZ5QkCeeeey7S0tKQnp4eJat+yaWlpSlKoiRJURtCaGGqFUoz7ORPZWUlhg0bhrlz5+LAgQPKej0aoVAIoVAI4XA4Kkzt1CLto0qV4lb9NxacnJZjUTxodYG29MCKv26hbp+zZ882bZ/AzxbxYDCo2z61ZWtkDWepwyzWWhpupo/2VyAQCIzwlaUxEAjgb3/7G84555yo548++igef/xxvPTSS+jVqxfmzp2LCRMmYOPGjaZ+2jlQtr6+Hu3atVOema1zUss6Aeso02xBshPxANg2jrihdFRVVVEtpaFQCBkZGS3cEyU3NmQQxdENhAUhGqvtEziZhykpKTFfCRmPsnDjyksjWVG3BAIDRPvwz+7p+vp6nHvuuXj22Wcxd+5c9OvXD0888QRkWUZeXh6mTp2Ke+65BwBw4sQJnHfeedi6daulMCKRSNQGD9ZpAKPpIFbisbOturq6xU7b1q1b674og8Eg0tLSIMty1L3UWkWUFpbanRvp0YZjNvVHC89oPadWXr2WU0/xZN14YJYmu3UnHnUoVhnW9kRza1S2RuWvpzTSlEjyTH3kzuzZszF06NCocqfFUX3kDrHI0+KpRS9etJ3xrVu3Nkyf9sgd8lzbVmh4pQ45IUN7Hq+4GcXBanixtKd4yOjFl9cBzL7fX+2q/12efN1V/53AN9PTd9xxB0aPHo1LLrkk6vmuXbuwf/9+jBw5UnnWqlUrDBo0yHIYdiqxkaKgP8WZGMhO22eeeQazZs3C4cOHDd2npaVh9+7dkCQp6pw5dVqMp3LdI9ZwiJVy3rx5uO+++7Bv3z5lyo/4r/6Ql7C6MyQvaDfgob74DSt5SiyAzzzzDIYOHYoff/zR1O89e/YgJSUFaWlpUc/Nwtd7brW9Njc3U63pLHEQCAQCwCfT06+99ho2b96MQCDQ4rf9+/cDADp16hT1vH379qb+0iwMdhRHvWkv2khW+zyeIy7aTlvtqK+6ulqxsBDrBi0t5Jn2kG+aFc4JC5wZ2jVotMPKCwsLFTelpaUoLS1V0n/uuecqa8XImkft7nDidtOmTSgvL4+yAG3dulWxCqkPgCYyWisU8TPWA6C9RqxxZ5FXu6FZk2l1lJRTMBiEJEm48MILcfHFFyt+5eXlAaArYORvfn4+c/pofUYgEMCSJUui6uzUqVOj2qta1qjtsSxBMZuu9zNebkNq/JIOXhAbYXygNO7duxe///3vsXr1amVqhoaegiaIRk/5I9DWWJGXFS1Pjxw5gpycHASDwRYbaOzExSlIOsaNG4eqqir06tXL0H1zczPS09NRW1uLnJwcJa3kJhjg5/Q3Nja2OBuRWIXGjRuHVatWKeGpFQv1OjuSX9rwWOB1aoc3rK55BRBlJQScyWtWPxobGy3VWW3bs1KHRP0RCAQ0PL+mcfny5bjiiiuijnkh1pmUlBTs2LEDPXv2xObNm9G/f3/FzS233ILFixcb+m20zoKXtTduyRit6VF/J7CuR6FZb43WotlNj16cjeJktIZNzwpMnhnlhZ5fWlkWv/XC00uLOg+8UO/iKWOW71pZrX9azOqB2do5K+nSC8cMozprJuelsvWyjFbWjgyP6WJ5j8aSX/Hgx/+5zlX/O/+l0lX/ncDzlsbhw4fj888/j3p24403orCwEPfccw9OP/105ObmYs2aNYrS2NzcjE8++cTUb1Jpeay8fsRs+p5X3Bx30fz2+DgvikS1sXjkodkyAiewkm9G8fBq2xMIBPHF80pj27ZtcfbZZ0c9a9OmDXJycpTnU6dOxfz583HmmWfizDPPxPz58w2nsrUkctRjJ1wn4mo0Ta3OD5qlzCx88rt6XSHLvcgAlDVlshx944yeBVQbX9pzq9Y8Wlq0lkW1e60bI4umNnw9a69emrz2sufRssASDz2riFWroV1LEq0tssSZyNgN3wq8lKdA4BRiTaMPlEYWZs6cicbGRkyZMgVHjx7F+eefj1dffTXR0XINt1/ATvgtyzIGDBiAAQMGMIcnSZLp2iy935zIE54UG4HAComou3bCFLM7AgHfeH5NYzyxu/5IKy9k6GtYALYz9vTcEbfq3+1YVbyUf0LGP2vL7ITHc/7ZWafmpfS4JaOVtSPDY7r8sKbxwD0TXPW/0wL+jVnC1irwJLLMz/mWAoEWOy88UZcFAgHvJMX0tNfgdZTlBrS1f2bujUal8Vjnl0zlI7CHqCMtSVR+xKssRJn7HylFlK+wNHJIMnY8VtKsVRDjnV9OhSfWbwkE7hOv9hWPAavbBAIBTJo0CcXFxZg9ezYaGxup7tTpCgaDCIVCCAaDyoHytM2ItL8C7yEsjQKuSKbROu/rdwSJxe/1wu/pA7yXRtrlDV27dm3hTp0u9aUENPQUxHA4bCjHJV6Lrwv4LgfKysogSRKmTp2qPKuvr8edd96Jrl27IjMzE2eddZaru6dZR2uESCSCYDCIcDiMUCiku1ZPvY7P6TV9WquXE/6q86GgoADbt283dB+JRBCJRNDc3ByVF2r08oA1vmKE6y2Myou3l7HTdYu39DmNX9Pn5T6mpKQE06ZNQ1FREdLT05Xbvowgip8kSVEfGurnnlMY0TKNTn+8gK8sjYFAAH/7299wzjnnRD2fNm0a1q5di6VLlyI/Px+rV6/Gn/70J0yY4M5OKNbRGkGSJNTX16Ndu3bKM6PdxFpZJ3DD6mX1qj5x7ZnACl6z4iQbomySCzsnVQi8h2+Uxvr6evzud7/DCy+8gLlz50b9tnHjRkycOBFDhgwBANx2221YsWKFa3EpKSlBSUkJgJMNyWy0JkkSTjvtNOpv2hdjItfn6LmhuZVlOSofRowYYepf+/btAUA5eF3rr/aYBjvoyfGggPAQB96IV36oD5o/dOgQVqxYgcLCQl33xCpO7h6XZVm5ylQbZ+3gJ1nK2M367JTfbsTPT+Xr5OZEmlLpNausONzbR9PTd9xxB0aPHo1LLrmkxW/FxcVYuXIl9u3bB1mWsXbtWuzatSsBsWTDqCHxdNRMvBY1q8PR/s9LXgi8/bIkVvEHH3wQpaWlplbxpqYmRUlMTU1FWlqaqbJo9lxsjIo/ou9wFlF3/Y8vLI2vvfYaNm/ejEAgQP39qaeewq233oquXbsiLS0NKSkp+Pvf/27qr90pWyesg0ajs1hxepRutGuQhKVNTyy7pd2A1W+rVx9GIhGEQqEoa5SeZVb910qceMGLllISZ6tWcVLmGRkZpm7VeWI2IBQbo9hw+gQDQUvs9NF2ZL2EOHLHB0rj3r178fvf/x6rV6/WvU/6qaeewscff4yVK1eie/fu2LBhA+6//36MGzcuzrH1F26+5HidftJbr6qn2EqShL179+L000+3HFY8lHtB7HhN0Rf1wZmlLjzip7QI+MTzSuOnn36KgwcP4rzzzlOehcNhbNiwAQsXLkRtbS3++Mc/YtmyZRg9ejQA4JxzzsF3333nWpycsjK6RTw7Fr3O2ayz1rPGWVVSnX4p6K1XNYobURitrHVzctpMWLD0odVJkldmclplkVaePK5nFPVBv19wOk/inb/JXKZxQaxp9L7SOHz4cHz++edRz2688UYUFhbinnvuQTgcRjAYbLG9n6xHMoKHETnvHUAgEMDixYuxbds2pulao3WQrEoT73mih9csUgJjeOgfeEbki1DiBP7D80pj27ZtcfbZZ0c9a9OmDXJycpTnJSUlmDFjBjIzM9G9e3esX78eb7zxBsrKyhIRZUvw3uk0NDSgsLAQU6ZMMTxeSP2CDYVCSEvTr3pmVp5E5omd9ap6uwq1boy+C9yH9xkCr8F73yUQWEWsafSB0sjCa6+9hlmzZuF3v/sdjhw5gu7du2PGjBmJjpYvKCkpweDBgxWFiOUwWKIwmr1QvHosgxqtNUq8RBOLyH+BQCCwjy+VxnXr1kV9z83NxYsvvpiYyCQBdl/EdqdrE7mIPVZrlCCxCOuXwAhRNwRGSJJY0yhyQBAzVpU/2lSsLMvKtYMXX3wx5syZE3X9ot6i9URfv8gr6isctXlJIxKJIBwOK1c4AsCmTZtaXIdplpfkO2t5kO+8UVFRgWHDhmHu3Lmoq6szjCPJt1AohGAwSE2XXr7xmHaC1ToERF+JaqU9+iluTiEUWAGP+NLS6HW8Zg2xG1etnFeuX/RC+dDy0mjpgPYKRwBobGxEr169otar0vxQ50U4HEZqaqqy0UxbHjwrSYSqqiqUlZXhhRdewKBBgyDLMtasWYMRI0ZQy117I4wVeK5HVutQc3Mz0tPTUVdXZ3jDld5zK3lhJW5kcOl23JzuF7zQzyQdYk0jJNkLvTgnqBtxLMe/JLMMK2plI5ZwYpHhMf+0MixHh9hZBmB0nIyeHzQ3ekpjLEsM3M5rI0WXVXlQo84/I3le6h3tuV4e0OoZS/7STkqw29at1E2jMqXFP9a+zc0+Ui+86upqSydaRCIRJY7kWkzaGmw9VSGWeqfXn/GqMP93wZ2u+t/unoWu+u8EwtIoiCvxUDQBcf+vm9jJS7Py5uUl4eQYWqts0/znIc1uoW7rAD0/eMCqJTGeCqCdcFhPtCBI0s8nWuhZy3ksN0Fi8MWaxoceegiSJEV9cnNzo9x8+eWXuPzyy5GVlYW2bdti7NixlsNxYp0Y7SVitI4mXrtuedjdq/dCtbpJhrZZRVs//IJeWlitg1bDon2M4qMtQ7fyX902yfpLM0j7JOsQ9da4GX2nQUsfS97xgp51SS9der8btUOtO+2zyspKZU3pgQMHEIlEWoRN4kZ+k+WTa2kjkYjpmkVaPM3qs9Pl5VadHTx4MKZOnYpzzjkH6enplk+0MKqfPNfbeCClSK5+vIBvLI19+vTBe++9p3xXj5h27tyJ4uJi3HzzzZg9ezaysrKwa9cuy2E0NjbGvE7MKvEa3cZ7FG32AooF2vSHwN9YXQ8ryzL27NmD/Px85eB/vWlTmqyoU+bYzSftmtKamhpkZ2cjFApR7/om5SdJUtRaWjdmMpz006k6C1hfEmDkjiDquICGL9Y0PvTQQ1i+fDm2bNlC/f2aa65Beno6Xn311ZjCcXqdmPqZ0Vocu2uWrMDLeiq1G73vTsASPmu4FRUVOHToEMaPH4/c3FykpKS0uIGIEA6Hlc0ielZRsybJSzmpsXozEADFIqTOK9paJxpGcTNam0abMtWTM2uvgUAA5eXl2Lp1Kw4dOoQVK1agsLCQGp46vaT8ZVlW6oE2PL00O0Gi27pZ2yMYlY1eOHrr+WhlS2TIJxKJ6J4ha3c9H2u+uVVnWd5TeusJnewj9WTshJ1Iah/7vav+Z/3hSVf9dwJfTE8DwNdff428vDz06NED11xzDb799lsAJzvqt99+G7169cKoUaPQsWNHnH/++Xj33XcTHONo9KanBXxTVVWFmpoa3HXXXWjbti0kScLOnTt1y5AolOrF5qyolUw7HaqbMmQd1TPPPINZs2bh8OHDhu6bm5shSVIL5Vqdb4lqB6zhEkvRgw8+iNLSUvTq1cvQ/ZEjR5CSkqJsNrBiLTJTsuKVVzy+yNWY1UN1/6r+m5KSoiiM2j7YC/2xk3HkvYwFicUXlsZVq1ahoaEBvXr1woEDBzB37lxs374d27ZtQzAYROfOnXHKKadg7ty5GDp0KN555x0sWrQIu3fvNvTXaKSX6NE6a3x5iZsdGSdGn25bXe2M7GmyavesVhVeyokmoxd/tVuCXr6ZWVZoFgvt/3rpMbJS6smapV0bd6sWJpZ804sv7/XBigzBinVSz1ql9YcmQwtL/SzWNq6XB6x11iwPjCx3LCRSxmuWxmOPT3XV/19Mf8JV/53AF2saS0tLlf/79u2LCy64AGeccQZefvllXHPNNQCAsWPHYtq0aQCAfv364euvv05IXAX+we3RvZ5S6Uf8nDY9jOqPn8req3Enios2/jRlkobZkg2tv5FIRPmoly1o3bpl5/FqOQniiy+URi1t2rRB37598fXXX6N9+/ZIS0tD7969o9z07NnTlt88Nyxe42Z31OhketwcudKsD6wdO80tr+VoBbM0aF+8NAsDzQ+9vNV7wVqNn12rtJUyV4dFK3terSx2sZqeWNtErHWBxTLI4reVo28kSUJjYyMyMzOV5Qs0N3p1zIrlXA+/1TtX0Fmrnkz4Umk8ceIEvvzyS1x88cXIyMjAwIEDsWPHjig3ZM2jEUYvL0H8EPkv8BviBe1dWMuupKQEgwcPVpQ9s6NviBUyPT3ddPAQD8ujQEDDF0rjH/7wB4wZMwbdunXDwYMHMXfuXBw7dgwTJ04EAMyYMQNXX301Bg8erKxp3LBhQ4JjbY14vWCM1tjE6qcVzNb72I1DvNIlFAJ72FlPmEjsxk0MSN0nlryNRdaJOqu1wNtF1C9nEfnpk93T33//Pa699loUFBRg3LhxyMjIwMcff4zu3bsDAK644gosWrQIjz76KPr27YvFixfj+eefT3CsrWFlNBnLC8nOYnw3oE0DOTHFneh0qRGKA/s0thfwUlwThdUDrSORCJqbmxEKhRAMBnUP7lYrWbTf44neFDZrXVevo6QtfaH1afGue6LvSl58YWl87bXXTN3cdNNNuOmmm+IQm8TD+w40q7ixe5qHvPFbOdmB5/LxEl7JK6sHWkuShL179+L000+3HJbbdYh1V73d9ZiJxsqO9aRBrGn0h6VRoI/b16tp/7qB2ahWncaCggJs377d0D+yQ5Fc7xgKhZgsF4m2YCQLgUAAkydPdrXO8liWTswQ8E5JSQmmTZuGoqIi5ivuiMJILGpay1plZSWGDx+OefPm4eDBg4Z5EQqFEAqFEA6HW9QHAq3Nm22+cgraVbVm4UQiEQSDQSVNept4rPZjeuFWVFTgySefxPfff49wOEy94pGgvkZXHXYoFIpyQ/phElf1X54Q1wj6xNIo0MfqyB74+bBbvcOHre7gixWzUS1J47hx41BVVcV0yLL6ekcr64eSalSdIHi8Xi0eJK31hhHtNC2gf+WgLMvUq1slqeWB8jxhte43NzcjPT0ddXV1OO2005Tn2jXhNOzUM3KZwYIFC1BfXw9JOnmZQc+ePal+paSkKO+TSCQCSZJw9OhRZGdnK26OHz+Otm3btpAVbYBPfHG4d7ygbc6wc6RBomRYXqQ0N0ZKI23NTbzSo/4NiD4XjVzrVlBQYBgOsVARN+o7y2npY00nC9p0WZnu8lK9i0XGrTrL6rc6jlaIRcbtPLeDW2Vrtb7T/gItL1wgz1jiSDCrF/EqU23cnKr7em6s9j8Ebd9YXV2tXKvJepXoJ598gkceeQTt2rXDrFmzcMYZZyhXbJK4NDQ0YMmSJfjmm28wffp0dO3aNWGKf/2z97rq/6lTHnHVfycQlkYBAHfW5zgNLWz1M3Iu2rhx47Bq1SpTiyMZpdfW1iInJ8dwSpP2XNsJCytRfLCjZCeKRIdPI5F11SxMbdy0U/Y0xceJeBgNWMxkeUIdZ6fjaZbfZlZSrcL5ww8/4Pbbb8ctt9yCKVOmYPfu3QgGg6itrVWsppFIBCtWrMCUKVMgSRK++OILyLKM48ePo02bNo6lTcCObyyN+/btwz333INVq1ahsbERvXr1Qnl5Oc4777wWbm+//Xa888472LNnDwKBAMrLy7F582bU1tYiKysLo0ePpo6SSCP85JNPUF5eji+++AIjRozAHXfcgZycHAB0y4Z6LcfmzZtRXl7e4pYAs2KQZVm5KUAdjvp3GjTFRs9/q1YBXqxR2vht2rSJmsc0GfJXlmWEw2GkpqZaHsVaHa1bSVcyyuj9T6ioqEB5eTmGDRuGW2+9FR06dKCWGSlTAs2KTNzpEYuVSFiR3ZWhWb3s5rWXy4fWXtwIz8g6SRtEE7faZ1poVmL1c5o/siwnxNpYv2iWq/6fOqnMVf+dwBeWxqNHj+Kiiy7C0KFDsWrVKnTs2BE7d+5Eu3btWrhdvnw5PvnkE3Tq1AnAz6OjgoICLFq0CA888ABGjRqlu5akubkZTU1N6Ny5M26//Xb07dsXBw8eVOKhXldCIGvoAKCxsRE9evRguiWAQJQZ8tLTm34QnKSxsZH5Jgag5RpHK/BqcfAremvYQqEQMjIyWriXJPtr2ETZ+hlRtokmFktoOBzmem2qn/GFpfHee+/F//3f/+GDDz4wdLdv3z6cf/75ePfdd3Hbbbfh//7v/6J+Jwt1tVMjNMuGXmUna+TIAmDtHaJGBAIBLFmyBFu3bo1ak0egrVFxak2e2WhTLw9oMmQ3nF4e6MXJbtxobgHzu1+17tVnwKnz0Sy+WnduWiFo8XYjHF5ltNaO6upq0zLWhkM+kUgEaWk/j5vNrCJWysipvCAzIXrrxGgypB6r08dq8XE7Pckmo5WNlwyPMwQ06yRgbmmk+UN+jzfHn7/PVf/b3D7PVf+dwBeWxpUrV2LUqFH47W9/i/Xr16NLly6YMmUKbr31VsVNJBLBhAkTMGPGDPTp04dZaaFZNnJyclooZwS1okRgbYyNjY0oKCjAFVdcEfc1eUbEMw+c7Ais3P0KRFuE1fA0ropldO51aOVgtYyJP9rTAVgGBonI+2TdSe4XRJ4K/IYvLI2tW7cGAEyfPh2//e1vsWnTJkydOhXPP/88rr/+egBAWVkZ1q5di3fffReSJOGiiy5qYWmkjYRCoRBSUlKidnTZWdtBoI2maJiNuPT80MbNzI1e2s2g5YGZW216tHEwko91JGwnPL3faP7wbO3wi4xem2ApYz1Lhlm8aOE6nS5WGZZ2q+dG/YyWHrP2wWN98KuMVtaODI/pMrJOmv3V+gMkyNL4wv2u+t/m1rmu+u8EvrA0RiIRDBgwAPPnzwcA9O/fH9u2bcNzzz2H66+/Hp9++imefPJJbN68mdpxGqGevooF0gBYw421QTg1Foil09Ni9GJPZEegBy3NVstRwAtsCimLxZEptP+/3vBen90k1rDMpuZpBINBpQzVy2JoSrT6L0/lJBDwjC+Uxs6dO6N3795Rz8466yy88cYbAIAPPvgABw8eRLdu3ZTfWW4iAE7eIKBVHGkvFqOXhPqZ3dGZnp96VjyjcJzoIK2+XPUsc07GySi8WMJgiauTSqR4idExqvO0/6ORQRRHPTesbZgFpwZbdtqJ3kCH1g9Z8dcqsabdzsUE6mUHenFyI64Cd6HNFCQCSWy+8YfSeNFFF2HHjh1Rz7766it0794dADBhwgRccsklUb+z3kPtlKWRJ9QjeNqGGxqyfHKqnnSstPWMtAbNY0ds1YIhyzKCwSB1Y4+RBUP7u0AgYKekpAQlJSUATrYploE+URhZletEKyECY8wGeIL44wuNaNq0abjwwgsxf/58XHXVVdi0aRP+9re/4W9/+xsAICcnp8UGB9oORBqxdixuV247/lu5dk+SpLhtuHETdRysWjAkScLevXuVO3CtYCf9POSXF4kl38ws4YkiViujFVk/IQZu/oKb5R6JDp8DfLERBgDeeustzJo1C19//TV69OiB6dOnR+2e1nLZZZehqqoKwMk1kcSKxLoo12sLjs3cA2xHHNCsdGQjEnGjlZNlWTmCR5Ja3vKgdqsmlrxW+2skY2VzgdYdzR/iRntE0e7du/Hiiy+2sO4S/4kVl1gyiZ/kjE71cxaM0sNCMsiwWpzVMmbH2RD3WljbYTzyQivrVjhOyhj5YXczBa91VStrR4bHdLG8R51a1uEWDS/NdtX/U274k6v+O4EvLI0A8Otf/xq//vWvmd0ThRFoOaWR6FFNPBuM1TEDzUpnNG1EDsyuq6ujHnxuFAcrSq+T7sz8IOj5RTui6Pjx4+jZs6eudVe7DOLw4cNRB47Hsn5UQMfOcTay3PK4HkB/vaValpdy4SkubpCo/tvv+SoQAD6yNLoJz5bGeI2ijSyQgLkFjvZS1T6jWR5jHX0a5Y/Z73ZGwXp5ok0PzY2RH9pnen7orVfVi3ckElE+RBGireNlsZ7x0ibsyNDqoFZWz1+1HK2ceLNK8ZLnQoZN1o4Mj+nyhaXx5Tmu+n/KxAeZ3ZaVleHNN9/E9u3bkZmZiQsvvBALFiww3Z+wfv16TJ8+Hdu2bUNeXh5mzpyJSZMmMYcrtgIxwGPljRUexgo8xIEFPQu0Ov6xpEVPlkUxJd/Jh1jPHnzwQZSWlpoeEN/U1KRMe9OuqiT+Wom3X4klL5ItrwQCgbusX78ed9xxBz7++GOsWbMGoVAII0eOxPHjx3Vldu3ahcsuuwwXX3wxampq8Mc//hF33323ctIMC8LSaIFEWBppYbsRjhUZq6NCXvPNSrytxJGgZ2mkybA8N/uNFid1XKxYi51Mj1kcE23l0P5vNbxYy8mKjJW6Eks4TsjoxZmHuPEso5W1I8NjuvxgaWx81d3DtzMn2D88/NChQ+jYsSPWr1+PwYMHU93cc889WLlyJb788kvl2aRJk/DZZ59h48aNTOEISyPnCJ3ePXjslGjEqw4YWdIEAquIuiQQWOPEiRM4duxY1OfEiRNMsrW1tQCA7OxsXTcbN27EyJEjo56NGjUK1dXVCAaDTOH4QmnMz8+HJEktPnfccQeCwSDuuece9O3bF23atEFeXh6uv/567N+/P9HRBnByDdqkSZNQXFyM2bNno7Gx0VQmEokgHA4jGAzqvui104tGbqxiZWRpxX2iUOdDIBDA7bffjuLiYsyZM4e5PMiuWoKRpchMOdObDmdFne9msixh6T3jvVzNUMefJS16Vlo7+WCn7fFqfREIkgYpxdVPWVkZsrKyoj5lZWWm0ZJlGdOnT0dxcTHOPvtsXXf79+9Hp06dop516tQJoVAIP/30E1MW+GL3dCAQQDgcVr6TIzR++9vfoqGhAZs3b8YDDzyAoqIiHD16FFOnTsUtt9yCt956K4GxPomdHZx79uxBfn6+7g5OI1nx0jGmoaEBhYWFmDJlClN5kDMs9aYnyf88Y1c5FQgEAoFzzJo1C9OnT4961qpVK1O5O++8E//5z3/w4YcfmrqNdfDrC6WxQ4cOUd8feeQRnHHGGSgpKYEkSVizZk3U708//TTGjx8fzyjqYufWA2JZZcFIcaH5UVlZGXW+YIcOHaKu5VKfP3jnnXciKysLshx9DiPxm1hEyRmYZFeuOi4sFjcnoSnO6u8lJSUYPHgwqqurcdddd2Hbtm2GZ/ilp6dH+SvLJ8/wI2crWll3aAUjf93It0QplLwOdNS71IuKitDc3Iwvv/zS8IYl9RmPqamp1BuW1DMELHFYvHixaR2lxUE94DRa5yqwDq91VuAAKe6Wa6tWrZiURDV33XUXVq5ciQ0bNphes5mbm9tilvXgwYNIS0trcQGKHr5QGtU0Nzdj6dKlmD59um7Dra2t9WWj1nZWdqY1r7vuOlx33XVUf/V+lySJevB0SkoK83WDvDFgwAAMGDAAwElFevTo0bqKNBBtrSNKtCxHXzlohNXRnngxJRZ1/WCBlJckSYZHG1kp01jiQHuu/V/rRiAQ8IMsy7jrrruwbNkyrFu3Dj169DCVueCCC/Cvf/0r6tnq1asxYMAAxQBihu+UxuXLl+O///0vbrjhBurvTU1NuPfeezF27FhTv+Kxk8uOokezlNH8UT+zEn/t7ky932m7c/Xc0X7XC9NNrFhoZVnGqlWrWhzUnZ2djVAohIyMDOoLWKsMsNYfoQS2hNc8obURszqu50b9TE9e7UY7QGEJlyUeBNoObQE7vNZZQexIEj/bQO644w5UVlZixYoVaNu2rWJBzMrKUmYcZs2ahX379uGVV14BcHKn9MKFC5Ub8zZu3Ijy8nL8/e9/Zw7Xd0fujBo1ChkZGS20aeDk7SS//e1v8d1332H9+vVo27atoV9GxwA4feSA9n+WONDcxRI3QqxKI+t0l9GLSxsHu8p7LAq/kRJsFkfWZmU3PYk4HiPZZLSyRr8D9EPXtfXBjoKnldOrd9rf9OKpFw+jMLTpYYHnsuVZRitrR4bHdLG8R+NhqImFpr8vcNX/1tfew+xWL39efPFFxWh2ww03YPfu3Vi3bp3y+/r16zFt2jTlcO977rnH0uHevrI07tmzB++99x7efPPNFr8Fg0FcddVV2LVrF95//31ThTFexGpltCJrFzPlTu9FSYublZdcoqB1WHbiafYS1v6vFwavHWgyo1cmtAGV9je7A1GzOsD6u1F71QvXZ7YFgcAeLq9ptAJLm3zppZdaPCspKcHmzZtth8uPrdUBXnzxRXTs2BGjR4+Oek4Uxq+//hrvvfce84JPo47Ujxi9qCTp56OMaL/R/mpRHy/EcpxNJBJBKBRS1gcC9LMEyTP1xyhNTmDlBU5TQFnzzAmSrR7HA235mg3mnBiEOAXP9SFeceM5DwQc4/KRO17AN5bGSCSCF198ERMnToxaUxYKhTB+/Hhs3rwZb731FsLhMPbv34+UlBR07NgxgTH2PlanEqweZ9PU1GS6G9RsXRYvVrpExoO3vEgmeMxznutDvOLGcx4IBDzjG6Xxvffew3fffYebbrop6vn333+PlStXAgD69eunPO/WrRv27NkTzygmPeQ4G9JZmx0vpFYYzTp4venfWLEzTUhzq52CjDUOAn9gtgYxHmUv6pdAwIhoK/5RGkeOHElVFvLz88V6HB+gnp4WLzmBQCAQCOKPNybRBb7BysYfslassrISw4cPx7x583Dw4EFEIpEWbghkDWQwGFTWQmrXOdLWQNLWStJQu7GzRjMSiaC5uVm5wYhlfSZr3BKJ1bwAWl6/SEujF/KhoqICw4YNw9y5c3HgwIGo+qklHA6jubkZoVAIwWAQn3zyCfXaSu0aXnXaI5FIi/qtxQv55gbxuJZV/VeQZKSkuPvxAL6xNAq8gVVLYVVVleE5iVok6efDk7UHcJth1YJJuwLSaMr9yJEjyMnJQTAYZD5I1W7c4k0yXr8I0OtnTk4O9VB7AMrtSOT3pqYmFBQUROWbXh2SJEnJt7q6Opx22mlUd2brfP28CcTqtazAyXxJSUnRvZbVLD8FgmTCd+c0uomV4zGM5IUM+3lc6g7cznE9dnZS202X1XCM/DE7fof3sjVKA3FL0B7pon1Z09arxiM9WlmjdNiNo1H3S5MzUmis5htvbd2pcNTPAf18tNKXaPOPYDWOVohFhlXW6lWUZHYA+PnqS6N6qkUbN706wqtC3vTmk67633rc71313wmEpVHALU6OZ2gWLTM38UDvpaWNH48dqBan4sjrC0OL0/WTtlRB68YsPl7INy+gpyTa8YfnMnHqRAuvzRAI7OONSXQTQqEQ7r//fvTo0QOZmZk4/fTTMWfOnKi1RbIs46GHHkJeXh4yMzNx9dVXJzDG7hLvTiqW8OzKWt3BTHsBaz+xQtZfWlnfBpysm6FQyHCtpnqtFa+dshULDC3PtdOmRm54xon2oFc/aX6z5lui8y4R4bOEqeeG9/xkwayvKCkpwdSpU1FUVIT09HTLJ1rQ/lc/80o+MZMiufvxAL5QGhcsWIBFixZh4cKF+PLLL/Hoo4/iz3/+M55++mnFzaOPPorHH38cCxcuRCAQQIcOHRIYY3dxU6mgbXgwC4/nhfuxbGIgmzjUytx1112HRx55BHv37sWVV16Jhx9+OCqP1OEdP34cQPS0olqRVG/oCYVCLeJDy7N455+XiXXThB5a5d6psiDxvfjii1FQUIDt27czxTcYDCIUCiEUCnHV9gTeRtST5MQX09MbN27E2LFjlZtg8vPz8fe//x3V1dUATlbuJ554Avfddx/GjRsHAPjLX/6SsPh6GaubP2JZuA/oWwmtQpOJdRMDibvW78bGRuqUjzY8WZaxZs0aXHLJJUhJSWlhWaSFF0t6vUA842110wRRqLSbJmjr6GiyRlZCK/EdN24cqqqq0KtXL0P3pO3V1tYiJydHV2G0El9eYIlfImZAeMNqOljzVLsmVF0een22L5RMj9za4ia+2AjzyCOPYNGiRVi9ejV69eqFzz77DCNHjsQTTzyBa6+9Ft9++y3OOOMMbN68Gf3797cdjtECXivyfpIh6Mno+ce6QSDWxdFGMnphaJ+ZxVGLWqaiogLl5eUYNmwYbr31VnTo0KHFrm4STjgcRjgcjlIWrU7D68WT5zrEg4ze2lKtrNaN9pm2TViZsteGZ+ZGG47euli9+OrFTU+Oh3LSuuMxbrHKaGXtyMQ7XbTBE60+at3q+cProKVpxUJX/W899k5X/XcCX1ga77nnHtTW1qKwsBCpqakIh8OYN28err32WgDA/v37AQCdOnWy5G+8KzCvDSVWtJ2E9rnRC9vNOKlhfUHRoI2iZVk2PS5IK8NqyWRJkx/rEc840Xbj2f69bisQ9ZsvaGs+fYlf02UBXyiNr7/+OpYuXYrKykr06dMHW7ZswdSpU5GXl4eJEycq7nivyLzHj4bZtIPRVIV6OoMmlwisToerf9MqwJdddhkuu+wy5TnNyq2VSUtLow5W9CxZAudgyU/ayzER5UQLh+bGqO05MWUu4AujPlXgAB45gNtNfKE0zpgxA/feey+uueYaAEDfvn2xZ88elJWVYeLEicjNzQVw0uLYuXPnREbVF/jVIuokTlpyzKbXjdwI9PFiPfZinJOZeJcV79O7Au/jC7W5oaGhxTqx1NRUZSdsjx49kJubizVr1ii/Nzc3m/orRm0tCQQCmDx5suF1cbSpWt52T1dXV2Py5MnMO1HJrllyBaB2N7PaqiPLctTVhwcOHMCmTZuU8AoLC1uER3a4kqvMyCG66t3TRnkSz7zzC9XV1ZauPpQkiXrlnN5aQu3fWFG3Pdbd06Ttid3TiUHkpc+QJHc/HsAXlsYxY8Zg3rx56NatG/r06YOamho8/vjjuOmmmwCc7OynTp2K+fPn48wzz8SZZ56J559/PupIHj/hppIbz93Tbo6Wre5E1V4BqFYItNOAtLWMx48fR8+ePXHFFVdg1apVLcJLS4tuiocPH6aGx4IY5LDh1O5pwHy62Im6LHZPCwSCROOL3dN1dXV44IEHsGzZMhw8eBB5eXm49tpr8eCDDyr3E8uyjNmzZ+P555/H0aNHcfnll+Mf//iHpXD8vGPPrgzBbArVaK0XbSOM+hlr3Ix27LGkw2rZauOoxSg9aje0nYdaP2gytHD04ulUfTDyg/e6aiRjtBnLaMOWWf12q5zM6pBemozqoZlcMtUHrbt4xo0WtlUZXvLPTEb7LJb8igdNVX9z1f/Wl93mqv9O4AulMV54RWm0QyKURvLci0qjkXKgRZtGVqVRT5E0w6xjZiHZlIRYlUby3CtKoxahNBq7i2fcaGFbleEl/8xkhNIYjReURl9MTwuicbvhqf21ojBpn6n9oMlZxW4Ha5YGvbC0+cuqJOgp0Eb+W31xudXh8tiR24VWfmZo3RiVkxV/Wd2p66w2HJp/Rm1Prw5ZKWM/1Qcafk+fwCJi97Q/NsIIEkuyvWTibZwXkwHJgShngUDAO0JpFFhGbxqMxT1PL0arViatWyNLIs0ipee+srIy6v5r7fSgWk5933UoFGpx/zVA30nNU747RawDkFitjE7FwwpWw/LqIM2r8fYiZv2ZQIUkJf3uaV8ojaFQCPfffz969OiBzMxMnH766ZgzZ45y5A4A1NfX484770TXrl2RmZmJYcOGJTDGzhAIBJQjQ2bPnq0cGaLXCUQiETQ3NytHu9AUDsBfR3Dw3hHKsozrrrsO//73v3HfffehY8eOhtPlZNeuJJ08CJzcWc3DdHS8Xz7xqpfxSg/PL2+/lq0Avh5YCpzHF2saFyxYgEWLFuHll19Gnz59UF1djRtvvBFZWVn4/e9/DwCYNm0a1q5di6VLlyI/Px8fffRRgmMdO3pHhuitWZIkCXv37sXpp59uOSxeFyarsbsRhgesWGPVSqXR2jnaOkiruL3xwQvEKz285J9RO7JSH/yA39MnsIjkCztbTPhCady4cSPGjh2L0aNHAwDy8/Px97//HdXV1VFuJk6ciCFDhihuvE5JSQlKSkoAnOzcyHmJRlYBojCaTTHzOqWcLDgxDamdshYIBAKBIBZ8oTYXFxfj3//+N7766isAwGeffYYPP/wQl112WZSblStXYt++fZBlGRs3bkxUdF2HZbrBD1PPWpLVIkDbta3+kGeEWMo8UXnsp3rKOzxubEtU+TudPlGPPY5Y0+gPS+M999yD2tpaFBYWIjU1FeFwGPPmzcO1116ruHnqqadw6623omvXrkhLS0P//v2xadMmQ395mS5ihXUxv9UjN7yQdq+idyyKkXujv6xheg0vxlngHH4pf7+kI2kRR+74Q2l8/fXXsXTpUlRWVqJPnz7YsmULpk6diry8PEycOBHASaXx448/xsqVK9G9e3ds2bIlsZF2AFZlVnu2G88L7gXGmCn8XhngCAQCgcB7+OJGmF/+8pe49957cccddyjP5s6di6VLl2L79u1obGxEVlYWli1bpqx7ZMHolHqvnbyfDDJOKUy8pCsQCKC8vBxbt27FoUOHsGLFChQUFOjWSbIbPhwOIzU1FbIsIzU1FQDdoqmF+BMIBLB48WJs27YNI0aMwMyZM/Hmm2+ivLwcw4YNw6233ooOHTooO7nVu/BJeKFQSAkzNTXVUUtovMpHK2tHxm0l3o28sOpXdXW1Uk9JfcnMzDT0j9SZSCSi3LtO62O1GMXX7T6FpFXbNkha9dorkY1EIlHtkeRDOBxWvpPf1ellzQe388INGb13LK+D38b3X3XV/8xhE1z13wl8YWlsaGhQXmCE1NRU5cidYDCIYDDYwo0ZwiLnfXjtfFggu+PHjRuHqqoq9OrVy9D9kSNHkJOTg2AwiPT0dEvrp9R51NDQgMLCQkyZMgVnnXUWXnrpJTz11FN44YUXMGjQINTU1CA7OxuhUAgZGRlKuGq0iiJrOXi1rJyA5xemUdzMTnGg+bVnzx7k5+crfbK2rurV3UTnjbZtkLSS32jtdfXq1Rg5cqSiEJK0kXZaW1uLnJycFs/VbrW4kQ+81TkBn/jC0njDDTfgvffew/PPP48+ffqgpqYGt912G2666SYsWLAAADBkyBD89NNPWLhwIbp3747q6mr89re/tRSOsDR6R0Yra0eGp3Spm6mVcIic1npjxZqnDlfPXyMZmh9206MNJx51yA481qF4yKjrhZ6MXt0hqOuMmZzb6TGSMWpL2teqXlq1box+08rH2+rqhoznLI1rK1z1P3Po71z13wl8sarz6aefxvjx45XR3x/+8AfcfvvtePjhhxU3r732GgYOHIjf/e536N27N5577rkExlgg+Bm3Okcj640sJ+bwdlp45Duv41de4yXwDqIOCfyCLyyN8UJYGr0jo5W1I8NDumhWmnhbzAhm08161oKKigrd9ZBat+FwGOFwGCkpKZDl6PWQNCulFrN8TMY6pIa2XjUzMzNqPZ52rR5Zf6he3iNJEnVdIit6dYnXvKblW+vWrXXbAG2dL8BWh4m7eOWFHZLW0rju7676nznkWnNHCcYXaxoFAj147HisYDf+Tr2IrdDSvYSqqiqUlZXprofUolYWrcbFL2XtZjr01uTprUtsbm5Geno6VTmirUt0Ku68KQ20fCOXKdCgrfMF2NIV73TzrqgJ+EJYGi2QTJZGFlk34xZrXtP8oX2PJY5ek7GCk5YEQH9Nliwb70ilWTojkYgiG4lElPu39cLR4pYC7VT/4EbcaDK0sqG5Jahl7DxjibMX2pFZvunVA4JR29Dzh5VYFL9E5LnXLI0N619z1f9TSq5x1X8n8MWaRoEgGQkEApg0aRKKi4sxe/ZsNDY2GrqXZRmhUAjhcBihUEiZPtO6Ic/Uf+2MLVllBgwYgEWLFuGDDz7Agw8+iNatWyu/VVZWYvjw4Zg3bx4OHDigKIzAyZetevo6EomgubkZoVAIwWBQN3ySHu2HF9zMa1a/YvGPVVZdfwsKCrB9+3ZTmUgkgmAwiFAopNRhWvhul63VtkfiReKvjpNRfbQSd6txIsf9qPNRrz+gxc1OHkQiEXz77beYP38+vvrqK+WEE6N48dQ2BWJ6WqBDokd5boWf6HQ5id6UohFqqxxgbu0gOJ1vav+0FgYCbWo7JyeHukYMSPzUthfrll6ctZZbmsVRz7prZGVTu7dypJQsy7pH1Gjd6ck7WT6NjY26bc/IcpiSktJiOp+Wx3bibnQcEI1YptDthHfixAnIsowTJ05g1qxZ+O677xAMBlFbW4vTTjtNcbd792706NFDiQuBi/YlCTubb6an6+rq8MADD2DZsmU4ePAg+vfvjyeffBIDBw5U3Hz55Ze45557sH79enTp0gVffPEFk98shyxr0Tu0Ve8FSaCtHfLCdA1vMlpZOzI8posmo7e5QU9GbTGgHa5sBMuSAqMpObU7Ixna/2bxY5maZkmHXrqs4LU6RPvdSIZF1krcjJRS2u8s6dPK0uqgE/lmNPAyagtaOb3ven6bxZFVzkocaTKs4Rn5T55ZiUMiaNjwD1f9P2XwVa767wS+UZtvueUWrFmzBq+++io+//xzjBw5Epdccgn27dsHANi5cyeKi4tRWFiIdevWYdWqVcx+kxHxAw88gNLSUtNDlpubmyFJEmpra5Gamko9VNxoVCkQWIGM+J955hnMmjULhw8fNpXZs2cPUlJSFIWRdRrISv0kVhRtZ89i1dSzTmm/q//qvWT1LGLa6TZesRO/WNJkZvlSu2F9HitmyohXylJPsXSbWMNwM49pU9TcIknufjyALyyNjY2NaNu2LVasWBF1TWC/fv3w61//GnPnzsU111yD9PR0vPqq/WuASIPRvqhoozDyO+2ZkQWG9syulc1t4m0ZiSWOdmTcznO3rESA8cYDFguGnRG/G9YbWnis4WzatAlLliyJOkaGdkwKIRKJKB+yVlJP4dRiN46JkNHK8hg3Mz+dCsepumq3TbDI2iGWdLFahu3IEIwsjVbzOJ40/N8brvp/ykVXuuq/E/jC0kgW96sX0ANAZmYmPvzwQ0QiEbz99tvo1asXRo0ahY4dO+LKK+0VTqwVN56jSith2ZGJN16IW6LiGIuV0EuWGiuQdWesFtimpiZlGYnelaN+mCHgKa7auMSrHfGUB07CW7p4i48gdnyhNLZt2xYXXHABHn74Yfzwww8Ih8NYunQpPvnkE/z44484ePAg6uvr8cgjj+DSSy/F6tWrMXz4cFN/1R2Y2srCMr2m90z9lzblxsNoKpmwuoMzEokgFAqZ7tLVKmJOdZ40y5f2O0sd0lrRtFPJtKnleNZPvaltK5SUlGDatGkoKipCenq64bl6AJR1oORcQu2mIW28RHs1Rq+u0txY9dOIeCsqsdQBHuuPnTixLlcwejd6AVmSXP14Ad/snn711Vdx0003oUuXLkhNTcW5556L6667Dps3b1bWTIwdOxbTpk0DcHLqWiCwsoMTOGmNyszMVKYwaRhZo5ywVGunb5yauqH5ycu0ULwxsygmY55YJdnrkEDgR3xhaQSAM844A+vXr0d9fT327t2LTZs2IRgMokePHmjfvj3S0tLQu3dv2/67MSKOFywjbx7ia+ecsWAwaHqmF83qR9wRa9TIkSPx17/+1TQftNYoI2uTG9YomvWGtj6IJqP3nfZckiQEAgFMnjzZ8ll0siwzl4dRvJ2AZf2m9ndJkqLOh6yrqzOcaVCfHai2PKvD1quDPE/fqdvjnDlzmM/hI23SSnt0ElZLO6vbRMFz3OIFd+1DSnH34wF8Y2kktGnTBm3atMHRo0fx7rvv4tFHH0VGRgYGDhyIHTt2WPLLCx2LU/BgDbB67qAkSaivr0e7du2UZ6yWIKcsc/EmXtYbWZZtnQMZDoeRmpqqWGG15cHdS0CDJLW8+lCWZaxZswYjRoyg5jntfEgr4fEKrfyNpvjJlYN1dXVR5+6pcdMKbwUvWEF5jlu8EfnAD77YPQ0A7777LmRZRkFBAb755hvMmDEDrVq1wocffoj09HQsW7YMV199NZ555hkMHToUH374IW644QYmv42mBJ3YfZcoGb30VFdXK+dS0s79o4UTCoWU/9W3dGh3wGnRi59a2bCzQ0+rpLDmgzZdNDmjePBQtnrpiRWW+kTQy38jJTLR+aZNh9n0v9FzNayydupdPPsUAkub1ZOhtUe7bV2bHivw1hcbydqR4TFdLO9R3pX5+o9Xuur/qYMud9V/J/CGPZSB2tpa3HHHHSgsLMT111+P4uJirF69Gunp6QCAK664AosWLcKjjz6Kvn374vXXX09wjPmFWBisnPuXkpKC1NRUpKWlMb1IzZ7bwcgvJ6fDrKTP6zg1heyV/DGrQ1bQKkba+ueVPHGCZEqrQOBnfGNpjAd+szSyyBhZ6tQyBDMrBHGjZ4Uw+p/VcqrnDyuJznOnZJwYvccSnvZ5rP7HQ8aoXvISx3jKWK1D8ewjY6nfvMkYWZGtwFu6zGQ8Z2n85F+u+n/q+WNc9d8JfLemUZBYWBRIQXwQ+S8QCAQCJxFKYxLDOppjdWM0WjRa50T7bkfh0YYl4AMvlYdRXJ2wBMUimwjri9Xw4hk/L9UrM/yUFj8je2SHs5uIHBA4hlDa+ESUh3/xW5vzW3qcwupxZACijoGKRCLU9dza9bVitZrADGFpTGJYLHuxdN7x7vh5XQfjJnppdmpdJyEWv/xKvNuG2bpip8rFyC8nra1GYcernrGum0w0do6/SklJ0b0OEzDenMhDmrlE5Av/lsYNGzZgzJgxyMvLgyRJWL58edTvsizjoYceQl5eHjIzMzFkyBBs27aN6pcsyygtLYUkSVi7dm0cYh8bVg/XJVfckYN1AVBHl1pY3HiBZOzoWF7uTlpvRB77H9aNQmZu40Us9dus3+MhfYD1KzGBn+9PlyTzay95SSf3iMO9+Vcajx8/jqKiIixcuJD6+6OPPorHH38cCxcuRCAQQG5uLkaMGIG6uroWbp944gmlcQSDQVfj7QQNDQ0oLCxkPvqmqakp6mBlPYiS6BdlkQd47nTtTD2xWGCSBd7Sylt8Eo2YWjVG9PUCJ+F+erq0tBSlpaXU32RZxhNPPIH77rsP48aNAwC8/PLL6NSpEyorK3H77bcrbj/77DM8/vjjCAQC6Ny5sycaUElJCQYPHqxM15iNLtUHcJtN8aifeyEveIfnKR2aJcbOESA0P72yocNPxFJuXsNv6TGDZbkJEL/NiYJoZJE//Fsajdi1axf279+PkSNHKs9atWqFkpISfPTRR8qzhoYGXHvttVi4cCFyc3MTEdW4I0bfAoKoCwJBciI2FgmchntLoxH79+8HAHTq1CnqeadOnbBnzx7l+7Rp03DhhRdi7Nixlvzn7aBRsziYHZiq5w9xI+CfWF4CbmxssbN2koe25Ffczlsr/vt1Da2bcbKyRtlJ/wWMeGTdoZt4WmkkGO3oW7lyJd5//33U1NQkImoCgaOYDWSMlEBeBj8C54lXudq5AcRv+DVdAgELnlabyVQzsTgSDh48qFgf33//fezcuRPt2rVDWloa0tJO6smTJk2Kb2QTiJc6OL9bPK2kT717vqCgANu3bzd0H4lEEIlE0NzcjHA4jFAoRD2XTbswnmWRfEVFBYYNG4a5c+fiwIEDiEQium5lWTY9I04bPms8kgmWvKCtS4s3ds4QjEQiCIfDCAaD1HInAyMvbdozaq96fXAkElHaCq29Ai3bLO/54GdkSK5+vICnLY09evRAbm4u1qxZg/79+wMAmpubsX79eixYsAAAcO+99+KWW26Jkuvbty8efPBBU//FehBBIiFns40bNw5VVVXo1auXofsjR44gJycHwWAQ6enpju2WrqqqQllZGV544QUMGjQINTU1yM7ORigUQkZGhuJObYEhgzPab3bjIWgJD1Yvq2cIyrKMPXv2ID8/XzkWRu/QaZpsotOrh1F7pcW7ubkZ6enpqK2tRU5OjpJm0n6JHA2e80HgbySZ82FLfX09vvnmGwBA//798fjjj2Po0KHIzs5Gt27dsGDBApSVleHFF1/EmWeeifnz52PdunXYsWMH2rZtS/VTkiS88847GDVqlKW40A5MtjNdI2ToMoFAAOXl5di6dStGjBiBmTNnIjMz01CGWLEikYiiqLDsDGfZgcpT/ml3OJrJbtq0CUuWLInKy9atW0f5oYVYf1JSUiBJEqqrq1FeXo5t27ZR/dD6FYlElDhFIhHTo5/0/DHDzbxW18FDhw5hxYoVKCwsNPSb5Bvxl6RbW05a9JbVJLreaZ9XVFTg0KFDGD9+PHJzc6MOjda6DYfDCIfDSE1NhSRFnwvIahVV5xetLUuSRO0rWrdubWjRk2VZiZv60GuWeLG0OTV67ZXmRi+NeruaaXLq33ioQ6wyeuvveVWK/1vzvqv+t+s/zFX/nYB7pXHdunUYOnRoi+cTJ07ESy+9BFmWMXv2bDz//PM4evQozj//fDzzzDM4++yzdf0USiOfMnovVj0Zs06e1Y2VOOrhpkxFRQXKy8sxbNgw3H333cpgiCZLXtopKSmQZbmFxY+VyspKJcyBAwfizTff1FUew+Gw8j9RmIgSJcuy48qjm3ltdZDBomgYoa3PPNQ7bT9nFHdZlpXBhd4ARe2e+Ek+6sEezZ1RuHq/0Yilr1D/Xl1djcWLF0e1BXLUmXbAsXz5cmXAoR1IkTQQRVY74GBBKI3xRyiNHpieHjJkiGmDf+ihh/DQQw8x+8m5npy0sCqEduTs+p1o3n777aipYVmWsWbNGowYMQJAyxcHURb1Xj5miowkSS2mo//yl79g69atePLJJ3HOOefg4MGD6NKlS1Tnrr2ujFgrCawvgUS/KEQdNN7Bq423JEm609NGaaRNT1vJH6t5yTJA1XOrhly4MGXKlBZT8drp6YKCAgDA6tWrMXLkSKVNkrDJNLTR9LSAM8Tuaf4tjTwhLI3xk9FO0xhZGvVeIOqXHOv0jtvpsiqjjas2fSx+q2HJEytWHJYyAOhLD7TWKLUf5EpMtRKs/t1OPO2Uj5Gfajd2lcZEKMl2ra7aPNfWSW3d0ss/q1ZCrZyRAhhL2bJYGrX9kV6YNL9o+cUip3VrJs9L38Ui4zlL45Z1rvrfrt8QV/13Au4tjYLkQdtR2LFM0TocmhsvEMt4jvbS1vpH81/vZUeea19YLNZEWZap1ijtDUdqeUmSsHfvXpx++ulM6dXzxw5E3kyZIPHUU66MXn6JfOFbxawuGYXLqtyr65eZQqaWUdcXq+3FSjrcItHhC6whboQRSqOAI2JVGK3IehnWF6SeZYPVuqQXDku+a2VLSkpQUlKifCdTekZKD1EYjSxL2t+dmDixWn+cqm8s5RNPaO2RJY52B3tW3SQqn1j6KZolTf2bkbKolTUKXyCIN0JpFAiSBLenfFimO1mtSFo5gp9fmDxPzRnFzSnLrlcwW07ipbQIrCGLNY38H+69YcMGjBkzBnl5eZAkCcuXL4/6/c0338SoUaPQvn17SJKELVu2RP1+5MgR3HXXXSgoKMApp5yCbt264e6778axY8fil4gYiJf1gTcrhwDKFGk8y0QvPD2FQX3od11dHVXBI3/1Dh5Xy8iy/uHFrNaoZK7DTlhareLEkhI93EqPnTrCYm1l9TdR/W0i6oevkCR3Px6Ae6Xx+PHjKCoqwsKFC3V/v+iii/DII49Qf//hhx/www8/4LHHHsPnn3+Ol156Ce+88w5mzpzpZrQdg/Zi9XI4Av9AdlnPmzcP9913H0499VSsWbNG1z053y81NdXyUUDJrAgKnMdOPyf6SIEA3to9LUkSli1bht/85jctftu9ezd69OiBmpoa9OvXz9Cff/7zn5g1a5ZyaDgrRru+rMjzKKOV5Slu8UpPvOPodRntNLPVaWS9DSRm8qIOCRk/y2hl7cjwli51uzZ6j/K8RAMADm/9yFX/c86+0FX/nYB7S6Mb1NbW4tRTT010NLjCQ2MHAQc4WV9oLwcyTW00XS0QCASC+JJ0SuPhw4fx8MMP4//9v/9n6lZMR7gH6ygyEAhg0qRJKC4uxpw5c9DY2GgqE4lEEAwGEQ6HdZUOrVJip4x5jlu8IXlx8cUXM+VFJBJRrt9TtzO9dZw85oW6/AsKCrB9+3ZTGVL+oVAoal1nRUUFnnzySXz//fcIh8PKtXe0NIfDYWVtqPp32tpQL9alWFGvsz1w4AAikYihe1IewWAwKt8JtDxOlrx0Ch6thnaQIbn68QJJtXv62LFjGD16NHr37o3f//73iY5OUsM6/WB0AwON5uZmpKeno66uDqeddho1PL3O3uqUCMvZgyxx08bBibjFG6vldOTIEeTk5LR4zns61WhvAOnVq5ehe1L+2htA3nrrLdTU1GDBggWor6+HJEnYuXMnevbsqciq84UceK69hYcF9RIAP6K9zaimpgbZ2dkIhULIyMigymjX17LWQa/UU4HASZJmTWNdXR1GjRqFU045BW+99Rb1blQtRusseF07YiRjtj7NC+kBrHXWeuHQ8ob4bTeOhFjWANLS6ETceC0nszpJ84fHtWW0sqX1Gdr0mHW/ZvmplTcKS5ueeNUHKzhR7wD99gOctBDr3R9N84NYxYGWd0OzlGU81+h5pZy8uqbx0LZPXPW/Q5/zXfXfCZLC0njs2DGMGjUKrVq1wsqVK5kURkAcQ+NFjBQN8pzHcRKvnWS8MFK6eSwvHrGiVMYT3ur2gAEDMGDAAEM36vhKkqR7lzvBjToa73zjrZwEfMK90lhfXx+1y3nXrl3YsmULsrOz0a1bNxw5cgTfffcdfvjhBwDAjh07AAC5ubnIzc1FXV0dRo4ciYaGBixduhTHjh3DsWPHkJKSgo4dOyYkTXZwojEnasrFyc7IzB8jpdBoEGA3fuq0mSmktN+1o2on45ZI7MTZyFqkhiclkpQdy2DErG7SrC3kN6v+erHOuIlWCaRZJ2m/W/lNGw5P9VTgEKJd8b8Rprq6Gv3790f//v0BANOnT0f//v3x4IMPAgBWrlyJ/v37Y/To0QCAa665Bv3798eiRYsAAJ9++ik++eQTfP755+jZsyc6d+6Mzp07Y+DAgXFPSyyWy3h1QH7r6Pz28qysrMSwYcMwb94800X+ZLOFdrOJuozJd+3HKdSbRWbPnm26QUaWZWWjENkoQouPNj1Ox9uv6G3eMionMj2rznPyPBwOIxgM6tahTZs2WSp/4r8sy6blr/4tHmVvte2Fw2FbbU/UYwELZhefaFm3bp0ywFV/WDbwqeHe0jhkyBDDRnTDDTfghhtusC0fT3hfr+EW8U6rdrRPGocap8rAqh+xhGl1kb8kSbY2SzgJbbOQ0QYZSZJQX1+Pdu3aKc+sWNzijdaCZcW9mYwb6dHbsKRXTmTzDq397NmzB/n5+Uodo7WpxsbGFuF16dLFMG3hcBipqanKlDBtvagediz1tDLUPjNre7Rytdv23OqrrJJs7ykWZI7sbOTikxtvvBFXXnkls9yOHTvwi1/8QvneoUMHS+F6aiNMojFawGtFnkcZrSxPcYtXeuIdR6sy2nWZrFOYtKk47QtY70WljlsgEEB5eTm2bt3aYgMBTSYSiSAUCiE1NbWF4m6mAJilh8WdWXpY4bkO2cGs7mjrGcFoeYXVZ1bSp6c00uqQl9oeaxytwGvfpSejfca7YeXgF9Wu+p91Rl+cOHEi6lmrVq3QqlUrQzlJ0t8kTFi3bh2GDh2Ko0ePRg3KrcKP2ixIKNpOPtFjCbfCT3S67OJkvGmdMcv0GLFGPfPMM5g1axYOHz5sGs7evXuRkpJi6+WnjZeeO16m9BIRh3hOz7ISS1yMyjNRabQarhVLqJia9hayJLn6KSsrQ1ZWVtSnrKzM0TT0798fnTt3xvDhw7F27VrL8sLSaAE/Wxr9JqOVtSPDU7poFh/WkbmdUbyZW5qVhRYOLc5G6dL6zZIuFkQdMpbR+19PxqhemFmR7KTHSv22mwdGbgmxpsdtC5pX651XLI37t9e46v9pPXq7ZmncsWMHNmzYgPPOOw8nTpzAq6++ikWLFmHdunUYPHgwcxy5X9MoEAhiJfGdr9oiZudlYKbUCAR+Q9Tz5INFQbRLQUEBCgoKlO8XXHAB9u7di8cee8yS0sj99LTRDqFgMIh77rkHffv2RZs2bZCXl4frr79eOX6HcOLECdx1111o37492rRpg8svv7yFG4GAZySp5WYe8lz9l44zx7FoJyX01mqp/deGpZXRc6P1S7w82bA7cWRUZmbuWZ6z+quFZtGON2Ztz6pfRrg98ScmFmND9tk1goMGDcLXX39tSYZ7pZHsEFq4cGGL3xoaGrB582Y88MAD2Lx5M95880189dVXuPzyy6PcTZ06FcuWLcNrr72GDz/8EPX19bj55pvjlQRBAonXy0YoNy3X17ml+Im8jg2Rb/7NA7N0+TXdAnvU1NSgc+fOlmS4n54uLS1FaWkp9besrCysWbMm6tnTTz+NX/3qV/juu+/QrVs31NbWory8HK+++iouueQSAMDSpUtx0UUXuR53QeKJ1xQP72txgNhfGDRrFKufVi1ZyYrZDnUtakU9HA5HHfOiXRPoRdyqK7y2U6M4WVlDaSVtPOYDr8gSP3Y2s4tPZs2ahX379uGVV14BADzxxBPIz89Hnz590NzcjKVLl+KNN97AG2+8YSlc7pVGq9TW1kKSJGVL+aeffopgMIiRI0cqbvLy8qLm9gWCZIDXF6Vd/JYewN65lqFQCGlpabpX3XlVYRTYx84GFYG3qK6uxtChQ5Xv06dPBwBMnDgRL730En788Ud89913yu/Nzc34wx/+gH379iEzMxN9+vTB22+/jcsuu8xSuL5SGpuamnDvvffiuuuuUw6v3L9/PzIyMnDaaadFuW3fvr2pf16wHrmN39PuxfQFAgEsXrwY27Zts22NolkKtcoF78qG18qNhZKSEpSUlCjfjRRGQnp6OpPf2t3pAm/hlvXQj+3ILWSO8mrIkCGGbfmll16K+j5z5kzMnDkz5nB9ozQGg0Fcc801iEQiePbZZ03di45T4FUGDBiAAQMGMLtXK4h+skZ5UeFnQXtkEUsazTYXCZILYWkUuAU/E/QxEAwGcdVVV2HXrl1Ys2ZN1BU5ubm5aG5uxtGjR6Nkfvrpp3hH05P4vTPxYvpi2QhC1jsZ7X71Wp5oN+D4CdpucyO3NBlamdpRRAXeRZSlM/ht97QdmC2NO3fuRDgcthxASkoKevbsaVmOFaIwfv3111i7di1ycnKifj/vvPOQnp6ONWvW4KqrrgIA/Pjjj/jqq69M/fbLLs2KigqUl5dj2LBhuPXWW9GhQwfDe1GDwaDy4iFXwAH06S29HbPxRL154NChQ1ixYgUKCwsNZSKRCMLhsBJfdTrV+M2C44WXB+t1hervoVAIKSkpSp0F9K/H81uZqvFTWliwulQDONn2ZVk2vDM70e2koqIChw4dwvjx45Gbm4uUlBTdPjscDiv3dWvbhZOboWjtsnXr1oYypJ81uhUqEokgEokAONkPJzrvBcYwK40DBw7E2WefbblA//Of/6C2ttZyxAhGO4Ty8vIwfvx4bN68GW+99RbC4TD2798PAMjOzkZGRgaysrJw880343/+53+Qk5OD7Oxs/OEPf0CfPn1sx8lLVFVVoaysDC+88AIGDRqEmpoaZGdnIxQKISMjgyqTlhZdLVinLxL1wiKbB8aNG4eqqir06tXL0H1zczPS09NRW1uLnJwcpU5HIpGoFwkNHqdyWMvG7MYWPZl4Y2czSH19fdR9qqyDGN7KUg3PceOFhoYGFBYWYsqUKUx1hbR9mpLIy5rPqqoq1NTUYMGCBaivr4ckSdi5cyd69uxJrRNksGRkCNDDShuntcsuXbrouid5XVdXh9NOOw1NTU1o3bo1mpubow6w3r17N3r06GE57omAp93TiYL5GsHTTjutxRSvm3IEcsm2lokTJ+Khhx7SrWxr167FkCFDAJzcIDNjxgxUVlaisbERw4cPx9/+9jfL5xMZXX9kRT6eMgTaiNNoVKp1q3avlecpD1jippd+8sxKvsUrXXoyLFZktUwoFGK2ImthPe7DqTywakVSWzW0FkeWbk4vznYGCl7pHxIpQ5shKCgoMJShzRAA5mVMq996bZ2lf6iurmY6GkmdB+QTiUSUgTmLhdPK+la9/oAmHwqFlP+1a51Z+gNSfnrtk9YXy7KMXbt24fXXX8f48eNxxhlnKP2VOp/UlkeWdMeLvV9/4ar/vzyzt6v+OwGzpdFuocVa2ENMdgixvAxat26Np59+Gk8//XRMcUkG9DprM4WSRwucHRJtZbCC363IelYkPWXEyOJohh/qrtdwaoaABdYBJ2s9sGoNJ/6rp5lpSpWeHEs8jfoDssNeqyTbsU4SGhsbLVl5T5w4AVmWceLECcyaNQvfffcdgsEgGhoaok43UVsezWZD4o1X1h26CbOlMTs7G0eOHLEcgF05HvGTpVFveoa2DkYvfJ7ygFY2emmlPfdT2dKe6ZWt1g+aJcbN9Bj5o/VTGyezstV2bVYsNnrxsSrDex3iRYal3rFYANV+mMmx1CWW9LDUKbO4q7/rpccsjnpthCVuRv2qNo60fLPSrozKzKhP4kFp3PPNDlf9796T//OjxQS9QJDkmL0w3LS+xiMMvXDVH4G34bUMWeLlRNxj8UM7faznv522qnar5z+ZihZ4A6E0+hxJsnfchtYtD6M8I7TTlFbcs8p4Bavlqx3da8vczbyhhUGzEgcCAUyePBnFxcWYM2cOGhsbDf0kOzKDwaDyUlJbLGhWLKFExh91mVRWVuLJJ5/E999/j3A43EKZUJdZOBxGc3OzshOa5k5blkblG2sdj6XPUf/V1k1ae7Qbt0AggEmTJqG4uBizZ89W2pCRtU+WT64vDIVCqKysxPDhwzFv3jzU1dVRlUhZlrFp0yZcddVVOPvss1FYWIiCggJs2rSpRZxkWUZzczNOnDiBjz/+GCNHjsRZZ52FwsJCTJ48WZmhJHnw7rvvYvDgwSgoKEBBQQEeeOAB1NfX28oTu8jiyB3/HO4tEAi8B+v0YGNjo6Wdm0eOHEFOTg6CwSDzjSkEPw0gvAKvO4b9hP46TP2Zhj179iA/Px/vvPMOysrK8Pzzz+PCCy+ELMtYs2YNRowY0UKusbERXbp0QYcOHfDee++htLQUAwYMwPbt29GrVy+lzA4ePIiOHTtiz549uP322xEOh3H55Zfj7LPPxvjx4/G///u/OP3001FcXIxIJIK3334bBw8exMSJE9G1a1dcd911ePrpp1FaWmp6xJrAOSztnu7bt68lz2VZjvnInQ0bNuDPf/4zPv30U/z4449YtmwZfvOb3wA4eZ7g/fffj6qqKnz77bfIysrCJZdcgkceeQR5eXmKH/v378eMGTOwZs0a1NXVoaCgAA8//DBGjx6t7ADbvHkzamtrkZWVhdGjR1N3w5EXWyQSQUVFBV588UUMHToUN998M3Jzc6NGhepsVZ9vuXnzZuqOM7NiICM+szPotPCyZineMkBsZ53pWb6M4DUv/CRjZ0c1sS6pd4iyKg526h3Ab/7xKsOyto1FVu3eaC2ck+kxWxMYax2yI2N3/aPemkuWvk8tYyZn5TfyzpUkqcWmnmAwiNTUVN2brpxm185vzB3FQI8z3DvT2imYLY0bN260dbh3rIV5/PhxFBUV4cYbb8SVV14Z9VtDQwM2b96MBx54AEVFRTh69CimTp2Kyy+/HNXV1Yq7CRMmoLa2FitXrkT79u1RWVmJO++8E6NHj1ZGXwUFBVi0aBEeeOABjBo1SncnWHNzM9asWYNHHnlEGXVt2bIFHTp0UHb1aZGkn3epNTY2olevXsw7zgAoygzJS9riZEE0wnLhT6yey0csjlpYFUaB+zjZdvQURDM3An2cLh+tf2azAWpltq6uDtnZ2cpvO3bswFlnneVY/ATmMFsaebgRRpKkKEsjjUAggF/96lfYs2cPunXrBgA49dRT8dxzz2HChAmKu6KiInz22WdRsmQ0QyopqzWPJd5GsurfjcI0Gs2qf7cySo8FHi0WahkCi+VBT5ZVnve88IOM1i1reWplCaz1Qlga3ZUxsnrZqReJTk+sMlpZOzI0WfUz2v96FlS9NmMWjpGl0sjgofe+1IYny7Lyzo7l6CArfLtzp6v+n37GGa767wTc3whjldraWkiSFHVWW3FxMV5//XWMHj0a7dq1wz/+8Q80Nze3kCWVUd0I7FydJMvRh7ia5Zn6d1mWUVlZqRzQevfdd6Nt27bUaQOg5YHG2nP4tO710mzUCZhd60aDTAvaOcg2Voi/tMNnWa69MrM4CqsiPwQCASxZssRS3SSHQ8ty9JS1UblaUVCdxso1oGSZhfqAc1q/RtIjrHACLxMvZVHwM8xKoyzL2LBhg+UA1Id2uk1TUxPuvfdeXHfddfjFL36hPH/99ddx9dVXIycnB2lpaTjllFPwr3/9i+qHdpRrdHWS3siYLB42uttUj1WrVkUd0CrLPy84Vnf+xE/t7RdWYI2T1YNs9fKABTujaz1oh8+yXHulN3IW09CJhVYvaBtkzJZ7aO/B1c4s6JVxIhQqqwe40/oD1jaVKIXRKFyhxDqHOi+t1AerZUCTo80QmP1mJENw8n3Bgizqo7cO95Yk/enpYDCI3/72t/juu++wbt26KKXxrrvuwqZNmzB//ny0b98ey5cvx//+7//i888/j/JDb2qE/P3kk0+wZMmSFpYroykz7YXt2gpuZIoncdI+U8dVC23zh17jd2vzB80qQ+sM9NxIkqRr4TXqIPTy2kzZ08tr1qlM2vQaCzxPkXlNhnWDDJFx68pBp9PFWt/0wjGqwyzpcDo9LDLV1dWWNjuprabx2CxoBbM8MLIia2VYrMgs/ZzVOLopY/bO1furTWu8FMed337rqv9nnH66q/47gS+O3AkGg7jqqquwa9cuvP/++1EK486dO7Fw4UJs3boVffr0AXByPeM331jfBdXU1KRruaJVWu2F7VpiGUHRfjPa/EH8VMPz5g8jCy8NbV5bOWqFpeOL94hWYA2zDTLa8pMk/q8cdLJ9GSkMZm7ijdXNTkB8Nws61RckgxXZDRI52yPL/slHu3je0kgUxq+//hpr165Fhw4domQ+//xznHPOOfjiiy+idllNmTIFzz77bJRbllG7FQugUcdFc2NmtmdRbLSytDiayWrjZ3WEakeGNW5mLz8jiwzNDz05py0KQsZ9GfVzgF6mVusqIRH1gbWPYe2znIxbvGRoZUmTIZj1vTxZW1mtyEZ+a9PAQ7rsyBhZFLXutMRTIf5m5y5X/e95Rg9X/XcC7i2N9fX1UVbBXbt2YcuWLcjOzkZeXh7Gjx+PzZs346233kI4HMb+/fsBnFRWMzIyUFhYiJ49e+L222/HY489hpycHCxfvhxr1qxR/GSpoE5XTKctV25bJfSUSi9gN2/sdIwCb2LW1nm0yFkhWeswr+lOViuyFqvlk+h0yOISPf6VxurqagwdOlT5Pn36dADAxIkT8dBDD2HlypUAgH79+kXJrV27FkOGDEF6ejqqqqpw7733YsyYMaivr0fPnj2xYMECxa1RxdWO+NVu9ayDerLqZ2o/aLJ2oflnZdpNLz2JaNhmea0N0yyv7cY10R2VwBza9DMLRjKJLnd1+FZPMCBXv5ltlEu05YYF1jZKm2kx6r/M+pREYbWvpdV9XpVlryN75Ko/N2FWGiORCAYPHmzJc1mWEQqFLEdKzZAhQwwbNkujP/PMM/HGG2/o/k7W9Gk7GDsKEw8IC5lAYA+jqbNEYvUEA0mSsHfvXpxuY2E9D+kVCAR8wqw0fvjhh7Y6Eu2l817EbMRqRLw6X5rFxKtKL4G3vOb9ZeqENcqrSxPMrIxmcTazSCWakpISlJSUADhZHkabwghn6BwUbFSevFneYsl7HspND6OZLat+8JxOvyEsjRaUxsGDB+Pss8+25Lksy/j888/x3//+12q8BDEgLI3JibBGOQdts4kX8YLCLxAIvIOvDvd2C693tF5+6fEE73lYUlKCwYMHKwMGFmsUURj1dnYSeE+7Fr1d0nbWOvKA3TWbxK1eH8DbOk6BgGeEpdGC0mi3MxGdUGyIKQhBPPDCTksBO35ZoiIQCPiC+/3jGzZswJgxY5CXlwdJkrB8+fKo3x966CEUFhaiTZs2OO2003DJJZfgk08+aeHPxo0bMWzYMLRp0wbt2rXD1VdfHacUxAZ5mfO21ggQLyLesGMdZFEueF3nZ4TeyQF22hEPaXX6lAWBQGAdGZKrHy/AvdJ4/PhxFBUVYeHChdTfe/XqhYULF+Lzzz/Hhx9+iPz8fIwcORKHDh1S3GzcuBGXXnopRo4ciU2bNiEQCOD666+PVxJ8C4+KbKLw4ovcSGEUFqqfUdfzQCCASZMmobi4GLNnz0ZjY6OpfDAYRCgUQjAYRCQSgSzLusqsaFMCgYBnPH8jjJZjx44hKysL7733HoYPHw4AGDRoEEaMGIGHH344pvCt3MRgJB/rDQlmsjyc8J9oGa2sHRke0yVk4l+H1H5s2LABmzdvxrBhwww3G6nDi0Qi1Ks69ayh5DfR1p2V4aH/tkIy9F167zde1+F/8c0Prvrfu2eeq/47AfeWRis0Nzfjb3/7G7KyslBUVAQAOHjwID755BN07NgRF154ITp16oSSkhIEAoEEx9bbVFRU4Mknn8T333+PcDhseLRSOBxGc3MzwuFwlJVF/bIkz2kfN1BbjAoKCrB9+3ZTmUgkoliNQqGQbtw2bdqE22+/PcoaZRZeJBJBOBxGMBhU0q0XnvpvvPLL69BeQKxWQ3WeXnTRRbj77rvRp08fpKent9hsRKvf6nNg1R/WeGqpqKjAsGHDMHfuXNTV1RmWOWl7xNLJQ9sTCATexdLuaTuHe4fDYcuRsspbb72Fa665Bg0NDejcuTPWrFmD9u3bAwC+/fZbACfXPj722GPo168fXnnlFVx//fX48ssvTePP26iHh3hUVVWhpqYGCxYsQH19PSRJws6dO9GzZ09q/Mg5gDRrixkk/52GHE8zbtw4VFVVoVevXobum5ubkZ6ejtraWuTk5ChxUluRyLPGxkYUFhZiypQpijXKLDySP8SvYDBIDY8FnuorL9DyhPWIIrVcSkpKi13ZWjdGcaD5aYWqqiqUlZXhhRdewKBBgyDLMtasWYMRI0ZQ3ZvdCGOE3+qQ39IjiD9eWXfoJszT09u3b7elAKampqKwsNCyHA296enjx4/jxx9/xE8//YQXXngB77//vmJd/Oijj3DRRRdh1qxZmD9/viJz1VVX4R//+IdheEYmc69NAzgpo335WXkZaqubdoqOJu/2FI82bL3y1sZP/UwvD4zktLJGcqx5rucXb3Uo3jIsfgLG9U9PRi1HKyfW6TeWdJnVC9Z0auNoJs9z2fpNRitrR4bHdLG8R3k01KjZ9s2Prvrfp2dnV/13AmZLo1OKnxu0adMGPXv2RM+ePTFo0CCceeaZKC8vx6xZs9C588lC6N27d5RMp06dEhFVrrDbKTnlv9mLWM8Nb7g5jaenXMQbXjtxVpx6GVl9SWplYg3bKWgWfC+2PYEgnghLo8/WNBJkWcaJEycAAPn5+cjLy8OOHTui3HzzzTem/jjR0ceKm3Gw66d2XZbeGi3yTL12bM6cOWhqajL0kyxrIPeW6+VBrGuwrEwt6rnR5gOLLC2/9L7T8tgo3/XKIlb8qECwKknq52b5rVdORn7GihW/9OJC+yQDyZJOp0nWfJPFkTvslsZEUV9fH6Xg7dq1C1u2bEF2djZycnIwb948XH755ejcuTMOHz6MZ599Ft9//z1++9vfAjhZuWfMmIE//elPKCoqQr9+/fDyyy8rax15h3dzPWBufaGtHTO6raSpqQmZmZmK0mg2DWglLomE57gJ9DErN1Gu3kWUnT1EviUv3CuN1dXVGDp0qPJ9+vTpAICJEydi0aJF2L59O15++WX89NNPyMnJwcCBA/HBBx+gT58+iszUqVPR1NSEadOm4ciRIygqKsLSpUvjnhYvodcpqJ9p3eh1IiUlJSgpKVFkjBTGiooKlJeXY9iwYbj11lvRoUOHKAukGrLjWL3Yn6Zg6ymYLHHXc88qbzUMo/DsUFFRgUOHDmH8+PHIzc2N2myjJRwOIxwOK/kYCARQXl6Obdu2YcSIEZg5cyZat25tGB6x+BJ/aOkwmxbV/ubWy4lWNiTNW7duxaFDh7BixQoUFBToypNzF0l6ST3UpoElDlbiGw/UecFa/uo2qRdfWn5YTRvPcXOqzgqljD9kWZQJ90rjkCFDDDvdN998k8mfe++9F/fee69T0RK4gHZnaE1NDXJycpSXsRbazlDWDtvpDpnHkXesu9wbGxvRo0ePqF3gegq/ellBamqqUiZGGzcSgVkZWd1Vf+TIEeTk5Ci73a2kj7f6osXqDAE5YaCurg6nnXYa1Y1TMwQ8x80peOxTBALm3dMC7xwOy6MM6+5RoOWuU9ozWnhG09hWOl8e88+OjDY/aPnDIkvDbv4bKZGJzjcjC7XV+kNkrOY5b3VILUMws9IZlS2tPVpp416Mm93lRbHI8FqHtDJ67wVeFeYtXx8ydxQD/c7s4Kr/TsC9pVGQHLBuSmDBTKGJ1X8v4ORYkLY0wGp+8voScBqzqXY1yZAfBJ7Ln+e4CQS84cvd0wL+sNsp0+SM/KIpOJKUnDtDAeN1mHb80vuYhaf+62ScnIRmcZIkCZWVlcoNLAcOHDC9/Yh89Oohgaz/JB8esWIpNWqrZuVvpw7wHDerOFX+2jS5hdPh0MqCR8TuaQ8ojRs2bMCYMWOQl5cHSZKwfPlyXbe33347JEnCE088Qf1dlmWUlpZCkiS8++677kSYAR4bBO+N1S5+S48d/Fq28UCWZVx33XX497//jfvuuw8dO3akDkyIW7Kek2wm4nGgwnN9iLfS4zdIvXR7EOJ0OFr/eB1ECTygNB4/fhxFRUVYuHChobvly5fjk08+QV6e/oXfTzzxBBedRaIaBEu4duLmRJ468XKlvXCStfNR56W2I2bNYyMLotW4eAk9q5RZ/aS5MbIkJkqptPJithqvWNMRr7jx1i94rY0kK7IsufrxAtyvaSwtLUVpaamhm3379uHOO+/Eu+++i9GjR1PdfPbZZ3j88ccRCASUW2KSDaOOKZZ1PWJNkP86fZ6tUbFCO64lMzOzhTua9UOreBgpInpKOrFU8qa4CAQCgRncK41mRCIRTJgwATNmzIg6m1FNQ0MDrr32WixcuBC5ubkA/PkyFCQOvynOvO9ijAXacS1du3bVdS9JEkKhENLS9LtLMwVQm48sm7UE8cOP9ZxnvJrfXll36CbcT0+bsWDBAqSlpeHuu+/WdTNt2jRceOGFGDt2rPIsPT3d1O9kW1+hvu5v9uzZaGxspP5WUFCA7du3A/h5+i0UCrWwnkQiEQSDQYRCIeV3IkPQbgbgJa+N8kIPEndy4LPeujetBcqLnaeXKSkpwbRp01BUVIT09HTDsydJ2RCFkTyrrq7G5MmTlfrR1NSEyspKDB8+HPPmzUNdXV2UX7IsIxgMKtdjhsNhNDc3IxQKIRgMUq2aRh8C2aQzb9480006pB2qN+lo/TMLz6+INhhfRH57F09bGj/99FM8+eST2Lx5s24lXLlyJd5//33U1NREPQ8Gg/GIIhO8jLoaGhpQWFgYdZgzscDoHXq8evVqjBw5ssVhzuSw49raWuTk5OgqUDR4yA+jvNBDfZsKYHw2XDIfv+JlSLlprZUvvfQSnnrqKeVgelmWsWbNGowYMQLAyfKtr69Hu3btFL/27duHX/7yl5bjQOoK7TD87OxshEIhZGRkUOX0bgMSCATmeGXdoZt46nBvSZKwbNky/OY3vwFwcmPL9OnTozpCck3UL3/5S+zevRtTp07FU0891cJNfn4+du3aZRie0YGjXjtE1cgdTcZo04Teon6938jvRr9p/SbPAoEAFi9eHHWVHW39GUF7rRspd9ZF8LTyNttAondVH01pJFeZqXfXavPBrEkmog6ZyVotJ+DnslK3TW0YZvUp1jxg8UNbD0j4mzZtwpIlS5QrB5cvX46zzjqrRbyJ/7QrBzdv3qz4wXIdniRJLa7DU1uszeq2UXrU8aaVA099l55MdXU183pV9dpS0jbVFmWtey0s8YtlmUcsMryXk9F7h+dlMYEd/3XV/4EF7Vz13wk8bWmcMGECLrnkkqhno0aNwoQJE3DjjTcCOHl94C233BLlpm/fvnjwwQdN/RfTh4nHqsVPe60bgaUTslPORlf10cLWXtVnBTOlIJFYLSdyrZueckL+5wnty6yxsREFBQW44oorsGrVKt07qgm0KweJH05eh6eHXhvw0/pKq+tVASjtkbRJoxkCrRyv74Z4xYvX9Avcg3tLY319Pb755hsAQP/+/fH4449j6NChyM7ORrdu3Vq4z8/Px9SpUzF16lRdPyVJwjvvvINRo0ZZiovfLI1amerqakNrEc1KQ/5GIpGoO6CNwjez3Bmlx4qcOiy1nJk/LHlB818bX3W4saSB5jevdYg1jdpyYS0n9TO306OV1T6rrKxEeXk57r//flx88cVIS0vT7R/Igd9k0KB2q/VXjZEbsgtcXUfVVkptvqk/6raqdstqhWch0XXVSj00kmOpq06mRytrR4bHcmLxh3dL46btta76/6vCLFf9dwLuF7hUV1ejf//+6N+/PwBg+vTp6N+/P5OlUGANYi165plnMGvWLBw+fNhU5t///jckSWqxplGN9oXFC0bxYskLrUw4HAag/wJWfzcKW/sbb/nmNl5IK1lPOG/ePAwdOhRffPEFpk6dihMnTlDdE0tWamqqMgX6448/RpWtUdtRfyc0NjZaaq9HjhxR4pDssNQxL9RDL2FUzwXegXtLI0/43dJoxyKmNyrXjtz1Ru1upScWWda80Fq9CKyWs3jlRbysHU7ltRvWGzcsjQTWuq8nT2RY64NeP2Qmp5VnTYNWhre6amRpNJpNMJsNSVQesMSfNTye2hNNxmuWxo9dtjQO8oCl0dNrGgX8wmujTwRezAsvxjle2FH2eQyDNXyBQCAgCKVRoIveSFv7nWZlU7vT+qOViQexvnhp8kbpYrVIs+RFMilwXk2nXrkboddWzGT1frOTd6wWTi8qkbHUJa/WQy1+SQcviCN3PLCmUSAQCAQCgUCQeITSKKDCYlmzIhsP3LKGOGGxcMvqoZdm2nMnysUoj+NV7k7kaazhk4/2mRU/1H9pv1mNk11ikfWiBdIKPKdP75YugF6mkUgEoVCIehORGrL5Sv0RnESG5OrHC3CvNG7YsAFjxoxBXl4eJEnC8uXLo36/4YYbojpxSZIwaNAg5fcjR47grrvuQkFBAU455RR069YNd999N44dOxbnlDiH1SvuIpFIVEehd82deneb6Cys4YVpICvlaUcxi1d9UddTP2InXU7mhV/z1W8MGDAAzz33HD744ANs374dBQUFpssaUlNTkZ6eHrWLX42VQaggOeF+TePx48dRVFSEG2+8EVdeeSXVzaWXXooXX3xR+a6+QuuHH37ADz/8gMceewy9e/fGnj17MGnSJASDQTz33HOux98NrB5gK0kS9u7di9NPP91yWE6vp2PdyeeG327CGu945WesYeitxfSCcuwXErWW1Y7F1A5eWKvLc/y068vJX6vrYs1mJYTC+DNiTaMHlMbS0lKUlpYaumnVqhVyc3Opv5199tl44403lO9nnHEG5s2bhz/+8Y+OxjOelJSUoKSkBMDJBm10gwSBKIwsnaD2+A0zd1bwwotCIIgVUccFPCMUQYFduJ+eZmHdunXo2LEjevXqhVtvvRUHDx40dF9bW4t27drFJ3Kc4eTUsxsdj99ftjylzy8vDh6nq52IC+vgza3w3c5PntqCV1Hv2jdb+6xdckKTYX2WrIg1jT5QGktLS1FRUYH3338ff/nLXxAIBDBs2DDdmxkOHz6Mhx9+GDfccIOp3zy+jGhYsR6q/9IOGNZ+93pn4fX4a3FyE4jTeZOoDSqJ3hjjNvHeGEPzw+95bAWe8sDq5kRRjrERkd39eAHup6fNuPrqq5X/zz77bAwYMADdu3fH22+/jXHjxkW5PXbsGEaPHo3evXvjtttui3dUHcPqFG+iOgwepqJ5iIOTJOrGBLdulvAysaQzXjeEOEWyla0RIg8EyYznLY1aOnfujO7du+Prr7+Oel5XV4dLL70Up556KpYtW4b09PQExTB2nNqY4HbHJzpW/2DFmp0sON32zAZ1dmS8hlfT4+aJFuL4G34Q09M+sDRqOXz4MPbu3YvOnTsrz44dO4ZRo0ahVatWWLlyJVq3bs3kl1c7MIHAbYzu5hXYg9WaZ3R3r9fxanq8fKKFQGAF7i2N9fX12LJlC7Zs2QIA2LVrF7Zs2YLvvvsO9fX1+MMf/oCNGzdi9+7dWLduHcaMGYP27dvjiiuuAHDSwjhy5EgcP34c5eXlOHbsGPbv32+6WUbgLm50em4daK2H2rowZ84cU+sCcNLCEAwGEQ6HdS0HVqwLbp2rppdv2uvy1FRWVmLYsGGYN28eDhw4gEgkout/KBRCKBRS8oH4rY43LR94srSwHK6szSdS/iT9elf4GaXZ7bWGVvKYp/IArFv8ACj1kJSHXh0k39XPCSUlJZg2bRqKioqQnp5u+UQL9UeL0W80t+q/AmeRZcnVjxfg3tJYXV2NoUOHKt+nT58OAJg4cSKee+45fP7553jllVfw3//+F507d8bQoUPx+uuvo23btgCATz/9FJ988gkAoGfPnoo/3bp1w549e+KYEoGaeI2W3QyHZl0welk0NzcjPT0ddXV1OO2003Tjq/ectzWMaqqqqlBWVoYXXngBgwYNQk1NDbKzsxEKhaLOTSVIkoSUFO7HrIaQ8h83bhyqqqrQq1cv5Tda/pHyr62tRU5OjlLWkUhEyQur5e9Vy5xbWLX4AUBKSgr1XEKz/HQqv/UGDnb9EvVB4CaSzNtQkWNinRYSMvGT0crakbEaRwKrlY4mQ3thqZ/xludqd6xx1GtHBDc23Lhdh2hlqw3PrPz1lBVWJYaVeORfIBDA4sWLsW3bNowYMQIzZ85EZmamrvtIJKJYplNTU6N+o7UFLbS4GeWbUXpYysloaQbLe8JK27CClbbHKmMnHFYZ7TPeld61n5tbrmNhaF/9NsIL3FsaBYJkgNdOkgWj6VOr6L3A/Y7f0tnQ0IDCwkJMmTKFyeLX1NRkqFQC8ckjszDU09NO1nOBwCsIpVHgOjwoRG7FQe2v1mKmhfa7dlStt6bJCdxIPy1NZnlNfqNZo1q3bh0lq/6fWKMk6eQdumYW3XhA0mpW9kDLvNKz0NLkvEZJSQkGDx6spMtsjZ9aYTSzNLFYHmluWdGzfBtZwfTqrJ2wnYAl75wOMxmIeGSHs5sIpVHga4yUMTMZv2FHcXYzLwYMGIABAwZYiot66tJoet+LqNNQXV2N8vJybN26lWl6V5Z/3rARiUSQlpYW5afanfYvSxnHsx05Zc2LBa/3AVamea3ms9WlB8DJAV84HIYkSQiFQkhPT48a+Kn/hkIhpKamcmFsELREKI0C10lkw3d7HSRPSpgbsK5pJP+rMbIEqq1zRuv5zHDCGhULVi1MRhYqdV7a2dAhyzJSUlJabKoxU6itKBZ21rCp46CH2Ro3PT+MLLx2rYyJxGlFyWjtpV2sLj0gG8AOHTqEzp07o6GhAa1atcKxY8fwi1/8QonX5s2b0a9fvxYDw0SXiRqv7HB2E+63L27YsAFjxoxBXl4eJEnC8uXLW7j58ssvcfnllyMrKwtt27bFoEGD8N133ym/nzhxAnfddRfat2+PNm3a4PLLL8cPP/zAFL7dY1XC4TCCwaAy+teitgyQD+0ID6MOk4TT3NwcdWSEWTh6cWLJA9ZjLOzmgZPYjTc5aJfEMd7xFrAjyzIqKiowbNgwzJ07FwcPHjQtD3Lkjd6hyrSyjUc5q9s4OcKlubkZd911F6655hrTOpySkgJZlhEKhaI2lrDEW9RjASslJSWYOnUq8/FC5CKNzp07Q5Ik5WQT8pdw7rnnttjJLuAP7i2Nx48fR1FREW688UZceeWVLX7fuXMniouLcfPNN2P27NnIysrCl19+GXWA99SpU/Gvf/0Lr732GnJycvA///M/uO222/DWW2+Zhm91xC/LMvbs2YP8/HzTYzT0wlIf4WE0wj9y5AhycnIQDAaRnp5uqdO30jDjmQdOjiztjoiNRud+erE6kddOWnOsWKMIq1atMj3qR2uNIlO3BKtTtG5Bi4dZHdbKSJKE+vp6tGvXLsoNC25YpWIl0eHHG7fSq7bu8wIP9csqPur+beOpI3ckScKyZcvwm9/8Rnl2zTXXID09Ha+++ipVpra2Fh06dMCrr76q3FP9ww8/YMyYMfj0008thU8W4etNnwDGR6aQ50ZuaC9Gq9NCWpycfqIt3HcrD5zAyvSnXhzVcmbxtjN9J2TsyxDUsps2bUJ5eTm2bduGQ4cOYcWKFSgoKNCtq7IsK5a51NTUFke+qN1qMZuCd+LFaFSH7eYfgeeyTVYZrawdGd7Spe0j1dDeJU62HydZ/Vmzq/6PLGp5pi1vcD89bUQkEsHbb7+NXr16YdSoUejYsSPOP//8qCnsTz/9FMFgECNHjlSe5eXlITc313J4PFVeGtopVb3pNQ+NEwC0TFci4yDgn8bGRhQWFuLBBx9EaWlp1KHbNJqamhRFUW0ZZ1mC4GS9sFPPWQaDZvEXU9MCnhB1kW88rTQePHgQ9fX1eOSRR3DppZdi9erVuOKKKzBu3DisX78eALB//35kZGS0uIFD70YONbQO1c40Fm1aQO+7ekRmVUnVrn8kfqg/rGlQo05/IBDA5MmTTdd4kjDUaxu1a6zUaVaH4daLjGX6U69ctHlq5C9tLaXRlXMAfQ2o3pVz2oEAi3KTTAwePBjTpk3DyJEj8de//tWw3CsrK/HrX/8a8+bNw9GjR1u0P62s9so5ANS6q37OWi609qm1tNCmkFn81fpB6yMEArdgeSfqueWJiOzuxwtwv6bRCKKEjB07FtOmTQMA9OvXDx999BEWLVqEkpISXVm/vVzVLxc3TfuNjY2Wrs7Trru0As+dhxG0NaBGV84BLXe+kvzSXjnHAm9TOrxidPUhbW0r4O56SFq7daocaf2DQAB4t58VJAZPK43t27dHWloaevfuHfX8rLPOwocffggAyM3NRXNzM44ePRplXfzpp5+YwqCtwTBzbxe3ZGN9SahlS0pKFGVclumH9qrdt2/fHgCUjUlGeelm52XFbzNLohm0POratavybMSIES1ktLsGiYJN8k8bHz3rt98GQ2bQykateGkt+GpKS0tRWlqquD/33HN1848Wjlm7sjpwM1IU9fxhsZ6zuBMkLzwNIniJhx48HbmzYcMG/PnPf8ann36KH3/8scV+Dxrr16/H9OnTsW3bNuTl5WHmzJmYNGmSpXA9PT2dkZGBgQMHYseOHVHPv/rqK3Tv3h0AcN555yE9PR1r1qxRfv/xxx/x1VdfmfovOtz4wss0K0+KFy0uPMXPq9AW49tFT9bqGkUe1u4KBInEyXbpd8jJMgsXLmRyv2vXLlx22WW4+OKLUVNTgz/+8Y+4++678cYbb1gKl3tLY319Pb755hvl+65du7BlyxZkZ2ejW7dumDFjBq6++moMHjwYQ4cOxTvvvIN//etfWLduHQAgKysLN998M/7nf/4HOTk5yM7Oxh/+8AcUFBQwx4GnkZgdWONudNJ/IBBQbqhQ70gFTuZPOBxWNhNo1zOS77RdqbQXZLzy2qhc7dx6QNJCdtkTaNYuo3Vq5BltmtJs6YGX66mTxMPiX1lZifLycvTp0wf19fX4+uuvdXdrh8NhhMNh5SzFlJQUpc3Isqy0DSPFUbsW2GodVZ9JqQ4vmS3VAoEVeGoeZKaElUWLFqFbt2544oknAJycka2ursZjjz1GPc5QD+6VxurqagwdOlT5Pn36dADAxIkT8dJLL+GKK67AokWLUFZWhrvvvhsFBQV44403UFxcrMj89a9/RVpaGq666io0NjZi+PDhWLRoUdzTwjtGZ8LprclbvXo1Ro4c2eKlx7Imz2hHZ6KVH6tnPAJQlACWtWNiRO1t1Oshm5ub8Y9//AOtW7fW3a1NlMXU1FQcPnwYOTk5+PHHH9GlSxfbiprVOkrWF2vhob0JBIKTF5GcOHEi6lmrVq3QqlWrmP3euHFj1CkyADBq1CiUl5db2nPgqXMaE02sm028JKO1amjdajFaO0Z+Z1kvRrPGxbKxh2ZJMfPDSl6YxZvlu5nfRnHzuwztebziZuYfYLye1CxdWnc0rNRVFvc0GbWcnj+81AcvyMT6nqD5Y1WGl7wwk6H1tU62U6d5a3PIVf+rV87F7Nmzo5796U9/wkMPPWQoJ0ktz7DW0qtXL9xwww344x//qDz76KOPcNFFF+GHH35A586dmeLIvaVRIPA6vHaAAjbcnMI1UhhpYcVz6YZAIIgvs2bNUmZTCU5YGQm0wSDtuRFCafQxsb5g9KwNtFGiWkZvtMgyknT7pWjXfxY5K3mhJyOIxivKtpEVWQ8zq4tV7MqwWLxFHWXHK3VWYB23m4FTU9E0cnNzsX///qhnBw8eRFpaGnXZih6e3j0tMEZ09AI1fnmZkXT4JT1arKTL73mR7NAuCjCCbHSSZRmhUChq85MeYuCaHFxwwQVRp8gAJ/ckDBgwwNIZysLSKKASixXQby+yWNLBU174ZZo80euenLCQG9WLeK/V9EOd8Cu0iwKMNjtJkoRQKIS0tDTdO9QBYVCwC0/nNJqdLDNr1izs27cPr7zyCgBg0qRJWLhwIaZPn45bb70VGzduRHl5Of7+979bCpd7S+OGDRswZswY5OXlQZKkqHulCV9++SUuv/xyZGVloW3bthg0aBC+++475ff9+/djwoQJyM3NRZs2bXDuuee20Lj9hB1FhSflRo94TdnFAo/5F6+y5THtiYKX9lRRUYFhw4Zh7ty5OHDggHKLFo1wOIzm5maEQiHl2k+alUptzVJ/nICXfHMau+kpKSnBtGnTUFRUhPT0dMPbtwjk1iKylEi9pEgbJ7/ls9vwdI1gdXU1+vfvj/79+wM4ebJM//798eCDDwI4eR61Wg/q0aMHqqqqsG7dOvTr1w8PP/wwnnrqKUvH7QAesDSSAyxvvPFGauJ27tyJ4uJi3HzzzZg9ezaysrLw5ZdfKjeQAMCECRNQW1uLlStXon379qisrMQdd9zBdMC3F+Fhd6kbWNm1yCrjNLyE5+RaOZbwzH5LNnhoT7RrEnNycnQtUOpjgQis8XcqjTzkGy0+vPhjNUwCD3kpcJYhQ4YYDtZeeumlFs9KSkqwefPmmML11JE7tG3l11xzDdLT0/Hqq6/qyp166ql47rnnMGHCBOVZUVERPvvsM0vhx/oiFjLxV2iT4dgKuzIsCqAX0sPih5frkNV0Ge24Vm/YYRmEaf2xstvSS3WIJxmtrN53lvDcahtOydiJbyJZtinsqv9X/Ep/SQEvcD89bUQkEsHbb7+NXr16YdSoUejYsSPOP//8FlPYxcXFeP3113HkyBFEIhG89tpraG5uTkykBQKBwEPoWbKdnpoWOA+PipfA23haaTx48CDq6+vxyCOP4NJLL8Xq1atxxRVXYNy4cVi/fr3i7vXXX0coFEJOTg5atWqF22+/Hc8//7yp/6QzFJ2iwI/45YWS6HVwbvcPVtNl5N4Jv7Rr5fxSj3gnlnwWZeQMMiRXP16A+zWNRpBF3WPHjsW0adMAAP369cNHH32ERYsWoaSkBABw//334+jRo3jvvffQvn17LF++HHfccQc+//zzhMVdEB/81lkmWkEygue4CWJDlO3PiDwQJDOetjS2b98eaWlp6N27d9Tzs846S9k1tHPnTixcuBBLlizB8OHDUVRUhD/96U/o27dvIqIscBGaxcdPVuJAIIDbb7896sw2vfSR55FIRNkFq36udcs63cjym9qN1XPmSJzD4TCCwaBufNRhJXqKlEclQs8KqC6POXPmRJWHdqe1LMtKWYRCoai8NtpprS6PTZs2tSh/szpEPuow9dy4PRtE/LVbj51se27BY/3lFZ52TycKT1saMzIyMHDgQOzYsSPq+VdffYXu3bsDOHnOFXByZ6Aa7XcaYnQt4ImGhgYUFhZiypQpypltRkdwNDc3Iz09vUX9Ndo04UacrZwzJ8sy9uzZg/z8fKWNsm6+4HXxPE/Q6lDXrl2pO62zs7Nx+PBhdOrUqYU/rDutGxsbqeHpIUkSIpEIUlJSFL+tbL5xC7180yOWthfveizajcAK3O+eVh9g2b9/fzz++OMYOnSocoDlsmXLcPXVV+OZZ57B0KFD8c4772Dq1KlYt24diouLEQwG0bt3b3Tu3BmPPfYYcnJysHz5cjz99NPYvXu3pbgY7fqyIh8vGSt4IT1e3Pnqpgxg/CJVN221jFqO5o+VHbZW0qMXlnbXpFF8aOnS89tK3FjSZUfGK3WItR4Y5b3Rcyt1lrbDluZvIvtilnQQ4tH2aHHkud55dff0Pz/WP+fUCX47iP/JX+5jaHaA5RVXXIFFixbh0UcfRd++fbF48WK88cYbKC4uBgCkp6ejqqoKHTp0wJgxY3DOOefglVdewV/+8peEpSle8DgeENZbPnCjbrhdtjzWZy+iLZ+KigoMHz4c8+bNMz0AHABCoZCyfIDmVm9KmRY2a3z1BiFuT0+7gVFcxcYiAe9wb2nkCa9ZGpNZRitrR4bHdDkhwyLrtCVB+7/VuCWi7dHCtirDe31YtWoVZs6cGTUt3bdvX8iyHDW9SrM0av3T+12N1XrHkj6v5LVdWSt4KS9oz3i3NP5jo7uWxqsu4N6Ox7+lUSAQnCQQCGDy5MnUTQx6xHMxPs3SaLTxwijOXtoI4zUqKysxbNgwzJs3D+eddx7+85//IDU1FZMmTcLdd9+N+fPnIxw+eYhxKBRSLIvqPJblkxtVSFkZlUFlZSWzJVO9oYaHjTAEu/XYCxthBAIreHojjECghtfRqVPQNpV06dJF1zIXDAapi/G17qxg1eLT2NhoaSMMAOpGGJZy9Xv5O4Hehpdjx45RN3pIkkTdNChJEurr69GuXTvDctILLxQKISMjo4W/xC9JkqI2wiSqXEnYTm2E0fptFKaAPyKyKBcxPW2BREyRCRkxtagnA/C1GJ9lipkWlnZayig+tHTp+W0lbizpsiPDWx3Slq0aIl9RUYHy8nIMGzYMt956Kzp06ECdfpYkCeFwGOFwOGo3tZW8YlmuoHVLqw9+aXuxEq/pXbenp3nl7//nrrp07UX8pp0gLI0CQRISj87Z6TB4f6HwDot9gGYZzMnJiTpaB/hZ0WE9eoeGVnFgcauXHi/ZPozianfAFmt8eFPYeIqLIBru1zRu2LABY8aMQV5eHiRJanGvNBkBaz9//vOfo9xt3LgRw4YNQ5s2bdCuXTtcffXVrsZbvQamoKAA27dvN3QfiUQQCoWiDsz18xoY0SHEDsuLVutGu+7QyI0T8dOzZpnJ0b6rn9PciDplHW2elZaW4vPPP8cFF1wASZJw7rnnUhVGtXKTlpam/E+ms/XqFUtZ0uKotdSpPyx+OE2sbU8bfyM5t0lUHnoRWXb34wW4VxqPHz+OoqIiLFy4kPr7jz/+GPVZsmQJJEnClVdeqbjZuHEjLr30UowcORKbNm1CIBDA9ddf72q8yfqzBx98EKWlpejVq5eh+6amJqVzTk1NVTpiNUZrYLyGF+MsEPgZJ9qk0eYUo99aIhSYeMFeJgKBx9Y0SpKEZcuW4Te/+Y2um9/85jeoq6vDv//9b+XZoEGDMGLECDz88MMxhW93TSNtfQttLY/ajTZMo9+NZHhaT2UmEwgEUF5ejq1bt2LEiBGYOXMmMjMzDWXI7sRIJBJl8VCHR8NsPVKi84JVVg+9vDSC7IRVW4oqKyup69vUkO/qtW3kVg+tFcOou+Ehr9X5dujQIaxYsQIFBQWGMiTfiBsy+NOu1dLihGXHybzQ9jG0OtS6dWvdcEhbDIfDSE1NjdpAw1r+VtNTUVGBQ4cOYfz48cjNzUVKSorubV9k/SVZd0mzdpq9Dp2ud0Z+JWotLes7jcWt2TtT793I6/T00g/cVZf+38X8pVkL95ZGKxw4cABvv/02br75ZuXZwYMH8cknn6Bjx4648MIL0alTJ5SUlCAQCCQwpsZ4ferZLsQ6+8wzz2DWrFk4fPiwoXtZPnnlXEpKiqIwsuabV/OWNd60vDSSbW5uhiRJqKurU5S9qqoqzJ8/H/PmzcN9992Hffv2KUfh0OKk3fmqpyzxjNUZApJvtbW1LRQlAusMAW8WH6vt8ciRI0hJSVEGDFawoyBUVVWhpqYGd911F9q2bQtJkrBz507d/CMKpXow42b8nJQXCHjBV5bGRx99FI888gh++OEHtG7dGgDw8ccf44ILLkB2djYee+wx9OvXD6+88gr+9a9/KdcT6mE0AmMZEdFG8iyjTrsWTdZweJTRs3oYjT5p1let5VXPEqt9ZiVdepZinvOcYJRm9e9GeUt7Risn1vLxU75pn+n5oSfHS16wtDWWcLQysbY9I/+N8prWXtWwxC9e/YKRf6wyPNQhFhm9dyyvlsZXN7jr/4TB7vrvBL7aPb1kyRL87ne/UxRGAMpBsrfffjtuvPFGACfvsP7iiy8SEkcjeGwkXoXXTseLxJqXei9Y2v9+wq/pcgI9pc2ufCyY1U89N36BpM3PaRQ4h2+mpz/44APs2LEDt9xyS9Tzzp07AwB69+4d9fyMM85wPU52G2EyNl7aVCZLPkhSy7VJtGdGMnbi6gWsWIeMfte+VGj5bSSrjYe2jGItD6dRW5vs5Bstv5yug25jZF226o9e+6S1VbvxNIojeaa91aWpqUk3vqTehsNhhEKhKH9o1kT10gKnFNp41Q8xuGFHFrun/WNpLC8vx3nnnYeioqKo5/n5+cjLy8OOHTuinu/evdvUTzEC4wOr5SCsjInGet6LMvMvVsrWTp/L6nbAgAEYMGCAJX+1509qwzRarxprfY53m4hXeEYDegH/cK801tfXR6093LVrF7Zs2YLs7Gx069YNAHDs2DH885//xF/+8pcW8pIkYcaMGfjTn/6EoqIi9OvXDy+//LLpekZBfFB3VHqdid46F70OR3REJ7GaDzSrjb1pOhlA4s7Qi5VY881POJk21nWKLGvajNYssvQLdq1rtDbhlN/JAm1No1eIiKLlX2msrq7G0KFDle/Tp08HAEycOBEvvfQSAOC1116DLMu49tprqX5MnToVTU1NmDZtGo4cOYKioiJUVFS4HneBQGCMVxVLwUloVw7q7ZxWH+kky9FXDtKULh7XFcaibAoEfsBTu6cTjdGuLyvyQibx5xmy7BzlMV3JIsND+bDExcnwvCZTVVWFmTNnRl052LdvX8iyjIyMDOqOWHJOotZfs+leo3haSY/VXbosO32thGeFRNeHeLRBvTzl1QL54lp3/b9xqLmbROObjTCC2OCxgbqJF9Jr9SpK4KQ1JxgMIhQKIRQKUV/CZLG++qMX7pw5c/Dhhx8q32fPno3GxkZquORwZ/KdnOlIC0+7eUD9F/BG+SQ75MrBtLQ0TJ48GXfffTfmz5+PcDgMgL4hKC0tDbIsIxQKKXUGMLfg6dVVqzix9KCyshLDhg3DvHnzcODAASUNhIqKCgwbNgxz585FbW2tEm+SZnU6wuFwi6tjaek0aj9uIdpgS2SxEYb/6WlBfOBlZMdLPHiAHK48btw4VFVVMR00nZ6ejtraWuTk5OgqjDTU+d7Q0IDCwkJMmTIFZ511Fl5//XUUFBRg8uTJOOuss3Dw4EF07dq1RbhaK4X6dg7WchXl/zM854W6HpF6qq0felYjSZJQX1+Pdu3aUf0zItH5UVVVhbKysijranZ2NkKhEDIyMlr8LssyVq9ejZEjRyoWVnV+kMPQ1dbXhoYGnHLKKaZxSXReCJITMT1tASOzuhV5ISOmFq3KaK1wNBniRqu8qWXUfpjJqWW004c0eaNw9PwxixsNUYcSL6Mtr02bNmHJkiXU6xf1wjG7flEPWr2PVx7Q4kBrY1bquV4b1fOTNV3V1dVMV4mqZchH71pWbZpo+cESN69OTy/+t7mbWLhluLv+O4GwNAoESUCixobazp/Xl4EgNhobG1FQUIArrrgCq1atcsQqrgevdSjWNsYyRa/GLA/0LMBmYWhnCNRhscxUCPwN92saN2zYgDFjxiAvLw+SJGH58uVRv9fX1+POO+9E165dkZmZibPOOgvPPfcc1S9ZllFaWgpJkvDuu+/GIfYCQWyoO22zTpn2O21dmdYdi99G4ej5afY31ngI4ode+RGr0ODBgzFt2jSMHDkSf/3rX03LMT09HQDQvn17ADC8q1odFq8YxU2vbai/q9OnTav6dyt5UVJSgmnTpqGoqAjp6eno0qWLqQy5l5slHJ7Lwy1iXbNo9vEC3CuNx48fR1FRERYuXEj9fdq0aXjnnXewdOlSfPnll5g2bRruuusurFixooXbJ554IikrukAQD0Tbig2tYsYTZPASD4u1WDHFH6JMBATup6dLS0tRWlqq+/vGjRsxceJEDBkyBABw22234fnnn0d1dTXGjh2ruPvss8/w+OOPIxAIKFcLCgS8Y1WBcEPh0Fo99KBteHA7bn4hEAhg8eLF2LZtm+H6M4J657F6gwUQ/4OmtevTWMKmrdVjqR+JrkN66w7Vf83kjdy6lT5Wf2ltVl2uems1kwXNRvmkhHtLoxnFxcVYuXIl9u3bB1mWsXbtWnz11VcYNWqU4qahoQHXXnstFi5ciNzc3ATGViDwL8n08nAasmP9mWeewaxZs3D48GFD901NTUhNTY3adavFy9YhL8edR0R+CpzC80rjU089hd69e6Nr167IyMjApZdeimeffRbFxcWKm2nTpuHCCy+MsjyyEM8pGd5IdJrdCj/R6XIKO2sQ3VbqaHmrPvNR74xHLbQzHvXCUu/4TDSxxKGkpARTp05lXn+mtkIarRNVP5ckqcUZnOryUJ8vSM4fJGURDoej8lx7vqD6vEV1edDKiWU3cCxrbN3AKN+0kPRpzy7Vq8e0vHGaWPJIDAZ/Rqxp9MD0tBlPPfUUPv74Y6xcuRLdu3fHhg0bMGXKFHTu3BmXXHIJVq5ciffffx81NTWJjqpA4BjxOjomVgYMGIABAwYwuydxlCQpanME67FEyfiCUyslZukfOHAgBg4cSPXjuuuuw3XXXdfiN5qf6h22LAQCAZSXlzNPv8vyycOwyfSo2ZWD6u9u1IFY6rFe2o12IpOPOo9Zjr4RCNzG00pjY2Mj/vjHP2LZsmUYPXo0AOCcc87Bli1b8Nhjj+GSSy7B+++/j507d0YdJAsAkyZNwq5duxIQa2+Q6Jdvotf2CNgwe0nTXvAsL3Yra+PM3MQLJ+sWa/4YnW+nZ3WknbVIC1d7fqBWRi/vac8bGxujDozXO/5FHX8zxTSex7+Y5RvNLcEs7do8DwaDLQ7LJ7/FWt+TdWDlFBx0MwnH00pjMBhEMBhs0bGkpqYq0yX33nsvbrnllqjf+/btiwcffNDUf553MwpOQkbxZgfYEiKRCEKhEFJSUpgsGG4u+Bb1yxrihecOTivcNP8GDx6MwYMHKwoV6/EvAPsGEx4GDgR1nEtKSjB48GDlu1nayXFEan/00hbvTU/acEV7TD64Vxrr6+vxzTffKN937dqFLVu2IDs7G926dUNJSQlmzJiBzMxMdO/eHevXr8crr7yCxx9/HACQm5tL3fzC0mkJ+MfqAbaSJGHv3r04/fTTLYfltNLi5O0HRn64ecC21SljOztkaXmkZ/VKduzstmex5BrJ6Fk6na7fevFJBKzx0Fp+zeqsWXnEmpdO5Z+TZeslIvyMSxIG99cIrlu3DkOHDm3xfOLEiXjppZewf/9+zJo1C6tXr8aRI0fQvXt33HbbbZg2bZrhsQbvv/8+1V8jYu0QhYy7MiwvFhZFw2gKLtFKY7w6a97KliZjhVjyLV7hJYsMywDHaNrdibixxMWJcGIZsCVDvbNT3onkmVXu+n+H/umC3MC9pXHIkCGGI6/c3Fy8+OKLlvzkXE8WOIjZy4bglKWPx86O57jFgt/SI2iJdhrUqTKPd91xezYhHiQ6fB5wX3fgP389f+SOILnRNmIr059600Y0fyTJ/WvMrHZI8TjOxk68nMLN9Gk/AvegtSUgunwLCgqwfft2Q3/IETbNzc0Ih8MIhUItyo5Wtm6VMe2IIiOCwaBSN8lRPOp40eKr/t+s/7FyLBDByrFA2v8BoLKykpoHNH/IMU3btm3Dww8/jK1btyr7EtRhacuXpzYqiyN3+Lc0CgROQlMYvYrV9ZzAyU5ZvStV+0LS65wTYWWwmj5ZlrFnzx7k5+e3SJ8ZworiHrTpSODn8h03bhyqqqrQq1cvQ3+OHDmCnJwcZXexFUXC6bKtqqpCWVkZXnjhBQwaNAg1NTXIzs5GKBRCRkYGVSYtLfp1y1rnWONODog326FOaG5ujmmXtlEeqDfzqElPT0fPnj1x//33o6mpCZFIBIcPH0ZeXp6S1u+//x49evSwlQcC9+F+TSNPGK3FsCIvZOK3js9v64KM/ALonas6HK0bI6VRa4E1i6ObeUDiUVlZiUOHDmH8+PHIzc2lKsAkruRQarJDXu0/y0sxlilRnutQouuqFtoViq1bt9YNh1jGSNlaOS8S0N+4Y4a2HRG/1L/Tnumt69T6QWuLRu2ZJY5qd7STJrT5rJWLRCKK3BdffIFDhw5h+fLlKCws1M2nTZs24S9/+Qu++OILxYr4xhtvoE+fPlFxIydZhMNhfPbZZ/jTn/6E7777DrIsY+jQoZg/fz6ys7MVf999913MmzcPBw4cAABcddVVuOeee3DqqafqxsVpnvyXu+rS78fwrxwLS6NA4CHcenl7wdJWVVWF/6+9M4+rqlr//+dwAEEkURCBEsEroqkXTbwqIigqRZqaN9O8qX2vlgPiQJNmZlpXs7yaqdm1zAaHvOUAFal4VYxrKiCmOec8gOIQIiBwzlm/P/ztfffZ7PGMe8N6v17npey9njXvtZ/9rLWeVVhYiIULF+LevXswGAw4e/YsWrduLZh3xq2SWqUCoJYNR6C0rwpZyKS8W/AtjmrQWruKKYXc/zsqz0KW+4cfflhQGWb+f/HiRdy/fx/t2rXD0KFDsW3bNkRHR0umc/nyZYSGhqJp06bYvXs3UlJS8Oijj6KyshINGzZkwx06dAixsbG4dOkSxo8fD7PZjEGDBqF9+/YYNmwYvvvuO7Rs2RIJCQkghODHH3/EjRs3MGbMGDzyyCMYOXIkli1bhpSUFEklluJYNG9p3Lt3Lz744AMUFBSgqKgIW7ZswZAhQ9j7169fx+uvv44dO3bgjz/+QEJCApYtW4aoqCgADwaYOXPmYMeOHbh8+TKCgoIwZMgQvPvuu3jooYdqpSfl90/M0sj8LBYLOwXhqM0XWrYkaFmGL2uLjBbLJWW5UGJ90Et5uPeY/3MRsvjI1QFfnntdzspZH/qQO2S494QsjlI+V4H/WR35J6dw21XsFecMS6OScvLjUiqjNo9KLPdCVlclMxN8lMxUiF0Tiosfj8VigcFgsJr6JuSBI3S5M9gdyYeZzlWXpg3S1keNEJrfCFNeXo6YmBgsX7681j1CCIYMGYJz584hIyMDhYWFaNmyJfr164fy8nIAwLVr13Dt2jUsWrQIR48exRdffIFt27bhtddeE0yP+RpbsWIFZs6ciVu3bsnmkRm0GIWRUSK59/nXmOsUirupb/1QzLoj9pxSnAe3LRiLo5Kxl5AHGyYMBkMtS7KQxcxduDt9bWL9/HE3wgjBVYDLysqs7p06dYrWsYvRvKWRi8FgsLI0nj59GtHR0fjtt9/Qvn17AA92aAUHB2PhwoW1ToJh+PbbbzFz5kwrp+FCMF83fOviunXrRNdVMeGY/Eqtq2LCKP3ykvpyVLJeRah8ZrMZHh4eVnmT+7IUsrY6y2IhV9dcHL2GTQuWGCrzv3CAuGUkPz9f8mQgoXQYK5XUDIFU/3dEuahM7bD5+fmKLI5M23BlLRYLa3VSMrYy4RxdHiUycqdZCcnI9VmlVlWh/4tZQ4XyIlUuIUulkMVRLL9i7SZUBuYdbcsSFFtYnOFcdSl9sPYtjbpe01hVVQUA8PHxYa8ZjUZ4e3sjNzdXVGksLS1VtHhWaGCwWCwYOXJkretiA4nc+ali6aglNjYWsbGxisMz+eXv6OOHkZK1B6UvXn5dS8nJ1bXYgnupeGtqatjBSumRg46oH4o6bO3//BcO/6UkJUuRxtY6UtuW3GeSP54pVRzdgTP6rJRsXcRVyiLlf+i6xtu2bYuWLVti5syZuHPnDqqrq/Hee++huLgYRUVFgjK3bt3CO++8g+eff15RGlKDjtiDyrdwCVkYueGkpsSUPuxS6SiRc0bcUqgdxJR+TUvlVWj6S65cHh4eMBqN8PT0FAxnb7tpCS3nWaqd5NpdSbxKnwGKctT0J6FZATVtwh8fuGOq0HisJH5n4+o+K/Z/KXk14flhhepa6j3Iv6eknVw9ZhHqp1HflkYvLy9s2rQJY8eORdOmTWE0GtGvXz+kpAifxXP37l0MGDAAjz76KKZOnWpTmo4aaMQUEL5Z3x1owZLiyMGAKUtiYiISEhLY68zOQW4YPsyXrNJBVsuKF0UZWuj/FOegpfWOjqS+9tn6WGZ3o2ulEQC6dOmCw4cPo7S0FNXV1WjWrBm6detWy+xfVlaGJ554Ao0aNcKWLVtUu2rgwlcOpNZsyMkKhbX1QeAOHHIKjFg++HEIybkSexQxJWtplJaH3951ET2Xi//SVGtJYf4W6v/1oe21jC11LjRGc+F/oLsDZ/RZ7jU18eoJt7aZxdlpa7+tdD09zaVx48Zo1qwZzpw5g/z8fAwePJi9d/fuXSQnJ8Pb2xuZmZlWayCVoOTB1sKDqTYP+fn5qo6dYpyxMsc8iU2rc6eHpKbepZCa2nCmtVcsjJgyLjRNRKcyXY+9L181shTXYavCqFTWne1N+6yyJQhy4fRcfj2ieUvjvXv3rHY5nz9/HocPH0bTpk0RHh6Ob7/9Fs2aNUN4eDiOHj2KqVOnYsiQIUhOTgbwwMKYnJyMiooKrF27Fnfv3sXdu3fh4eGB4OBgwTSldpG5AlelJ+bsVYz79+/D19cXFotFdAGyrZsHpO7L7VpUWl+21qtW1kBRKBT1KBlbnJ0ORRi1debu+nW6oVEHaF5pzM/PR58+fdi/09PTAQBjxozBF198gaKiIqSnp+P69esIDQ3F6NGjMXv2bDZ8QUEBDhw4AABo3bo1ez08PBwXL150USnUYatypZbExEQkJiay8UopjABYdxDMeaVSUzx8pVsOe8qkVNbWhd22pkepW1ClQJ9wlxk4s/1o36j71KFlsDajKz+N7kaJbysl8vbIKHWvYEs6DFJf5WJIrRXk++tyRb3xZW2RcXXbUhltyeghj1RGuYwWxm811Ie+KvZ+0+oH2sLvLE6N//VntL9iUPs5rOcIrZdzJGoWTHOnacWmbLn3pK4JkZeXx66xnDt3rugaS26eGQflNTU1Tl1jWd8wGAzIy8vDxIkTZduDgVu/JpNJsK6F2kFL7cHtg9HR0Th58iR7T6wPWywW1NTUwGQyseXmo+c+uG7dOixduhRXrlyB2WyGxSL+4jSbzYJrnrnlFaoLJXVib50JzTTw21RP7UJxPRYLcepPD2h+erq+o7UvLmfmRWiN5SOPPCIanhCCixcvIiIigl1jKWTpFJPVUr1qDUKI6vYwGAzselfmVA6l7aEVmDIPHToUWVlZaNOmjWT46upqeHl5obS0FIGBgaIKoxB66INZWVkoLCzEwoULce/ePRgMBpw9exatW7cWzLuHhwcIITY5XeYudRGK25F1pXWLFoWiVej0tAr0Mr3BlZE7Xk1Ihvm583g17stDTIb/glm3bh1Wr16NpKQkvPjii2jWrFktiwIjwz1CkRDr01646YohVB9a7w9qEJouApStt+U/J1w5ob/5MkqPV+PK2HO8mlR55MKJ9VPuNSVpiZVLDmfL8POvtjxcxPqArXnjy7gKJWOXPe8Jpek4CqV5lHsu+TDPJHO0K/dDgvvcmkwm9h7zsckNoyXmbzQ7Nf43hhvlA7kZamnUOGLTKEpRay0CwFoKxKx3WrScZGVlYcGCBfj000/RvXt3FBYWIjAw0GoQ4sJVFhmU5l+Lg5kWkVK6+dOVXLRmcba1vZWUX8t9yZH2BDEFUS6Ms9CDcmoLzrSginnbEEvn9u3bCAwMRE1NDby8vFBRUYGGDRvCZDKxfpINBgOuXLmCyMhIh+aV4jw0b2lcsGABNm/ejJMnT8LX1xdxcXFYuHAhoqOj2TCEEMydOxerVq3CnTt30K1bN6xYsQLt27dnw1RVVeGVV17Bhg0bUFlZiREjRmD16tWq8uJMS6PYV5yYDPcLTa2lTO4Lnx9GqZVJLoxcHQjVr5K4+OmrsY7x889gi7wWrURSllpHy4rJSMnKhRHrj3LtLPWhI2fxErJo22up1aOM2DNla39wdHkc0b/V4Oy6luuH7s4jX4bBHhkhS7azlF5H8I9vnGtpnDVC+5ZGzW+EycnJQWpqKvbv34/s7GyYTCYkJyejvLycDfP+++9j8eLFWL58OfLy8hASEoL+/fujrKyMDTNt2jRs2bIF33zzDXJzc1FRUeGO4ogSGxuLlStX4ueff8Zbb70l64DcYDDAy8tL8lxkMWz9TuBbh5i/bYlPaX7z8vIwfvx4yc0Y/PQJIaipqWGnR4Tyx1zn3xdTXITCcjdNKHGODvxv04SznaO7g/Xr1yMpKQn/+Mc/cP36dcl8M5tGmHoAUKusQv1NbV04s+6kNs2Iwd80c/DgQcE+JNb+zLIKZrMJE5aLUP/RUz9SitCzqnQzHQMhhO2Hcpu3mP/zr1Ecgz3vE4rr0Pz09LZt26z+XrNmDYKDg1FQUICEhAQQQvDhhx9i1qxZGDp0KADgyy+/RPPmzbF+/XqMHz8epaWlWL16Nb7++mv069cPALBkyRKXl0UKISuJLbJC8nLTynLxCX358S2Zjlivw48feDAl0rZtW0yaNKnWVKVQmszGhLKyMjRp0kQwPTX1K1Uutc7RxfLGRSxvtnx5O/JLXS4uoeUBTZs2hclkgre3t2B8Ug7i7S2rPX1WadpqNs0wHzL8TTOVlZVo06aNbB9iysKf8lODI/uPFq1AgG3LcTw8PFTPrCi5rwZ7Lduugps3qdksW+IRmj3QGhaN5suVaH56ms/vv/+OqKgoHD16FB06dMC5c+fwpz/9CYcOHULnzp3ZcIMHD0ZAQAC+/PJL7Nq1C3379sXt27dFX9ZK4Cse7pgW4ofhIzVtp+SFKZY3qekTV0yFSE0XC9WF2mlJJXUul0ehvEmFcVXebEFtOmJ5ZK4pSYdBbvpYLA6p6/bUmxIZobYVe16l2lYujFi6/LTFwnPz5srn1h19VW3/E6s/fh3rod6clZ6QcqdmfOAipixqeXr6nQ0mp8Y/+znN2/G0b2nkQghBeno64uPj0aFDBwBAcXExAKB58+ZWYZs3b86e+FJcXAxvb2/VCqMrO7CQVUQOJQ+Z0jjlyqilB1iqrqS+gdzRjs6WUxO3s9Jw5HennGLEDceUx16LoTPh5lGubzojbS5aqA85XK1oMggpiGJyQvKOxJ54nV1vtsatB4VQCcS5vr11ga6UxsmTJ+PIkSPIzc2tdU/MCqYXbFEYhf51RPpaRi6fQi9n/kAlp1zbkzepl46UNUntNbX5ckQ89qRtS1gpS5nWkGt7uTBCFlruPTXx6uVZdhT2PNdiMtyNiSUlJcjIyEB0dLSo0mOxWNgfszmRcf3ERWpWRE+4c0yhuBfdKI1paWnIzMzE3r17rdaohISEAHhgTQwNDWWv37hxg7U+hoSEoLq6Gnfu3LFrerou4wpLV13EEVNIWptqdid6yCMXveW3LuKMpQdqnbzfv38fvr6+sFgsom6+xD4YnNGHXDWjUt/6vxY/WF2N5ndPE0IwefJkbN68Gbt27arlzykyMhIhISHIzs5mr1VXVyMnJwdxcXEAgC5dusDLy8sqzPXr12XTpl9TjsHV9ecsS6LStBwZXg6pF5EzELOUOKrO9fasic0QiI0dQn9rrcxq+o4WXqJK61pNXImJiZg+fTr69++PJUuWyMbVsGFDGAwGeHt7s20qtWxCSbvbO01NcTwWi3N/ekDzlsbU1FSsX78eGRkZ8Pf3Z9cwNm7cGL6+vjAYDJg2bRrmz5+PqKgoREVFYf78+WjYsCFGjhzJhh07dixefvllBAYGomnTpnj//fexYcMGdxatzqCVl159++qlaJf6aolxB1qoa2etSaV9h6I1NK80rly5EgDQu3dvq+tr1qzBCy+8AAB47bXXUFlZiUmTJrHOvXfs2AF/f382/JIlS+Dp6Ylnn32Wde5NcQxaGdy0vIDcWXlw9SYQvcVLUY9YW+Tl5eGzzz7DsWPHFB0jx900whzZKWQV5CtcShQwoTXKYpuo7F0+wkfsmtRObCk5MbQwQ6MUoZ3rdfGZphZcHbrccSf8wUDt1y2Vcb0LCjUuIdzdtra4vHBV3uqjDF/WFhktlstWmb179+LQoUNISkpSfLyjyWQS3BAipFSJhZEqg1x5xMqopA7EFCHa7+THLqm2kGo7LViNpXjry2qnxj9vTG2ftlpD85ZGCkUMRw4sWhigtJAHe9GSNYriWBITE5GQkMC+1KUc2TModUCuRIkUCm9vODWWPimLpjPQk6VRrK7qGhY67FClkaJf6urAJIU9Ly5nyxgMBskTfMRkGGuU2l2ndQ1XKyVqsCdv/PbTYvm0iKutbVq17lG0BVUaKfUSvQ6Q7pruUhrWFmsUM30pZVnk3q+rSqSWp+aE8qb2g0JpHEwYZ8BNW61V3GKxsHnjutaR67f2lsUV/YHrl5KpCx8fH8mpe+Zfs9nssrpwN4SaGrWvNC5YsACbN2/GyZMn4evri7i4OCxcuBDR0dFsGEII5s6di1WrVrEbYVasWIH27duzYYqLi/Hqq68iOzsbZWVlSExMRFZWlkPzKvTgyQ1CJpPJyhmskJmf+4Byr+sZveef4nj0/kKhWKPFKUtuHmJjYxEbG6ta1mAwCK7T1DP21AV3hkCLHzwUx6J5P405OTlITU3F/v37kZ2dDZPJhOTkZJSXl7Nh3n//fSxevBjLly9HXl4eQkJC0L9/f5SVlbFhRo0ahVOnTiEzMxNHjx7FE0884fC8Mg5hV6xYgZkzZ+LWrVuS4Q0GAy5fvgyj0Wg1CHFfnmIvUr2/YPWef4oylLxA+FYnIWVDiwpIfUZJ/Wv9GbfVYspACGF/QuG0vNyAjz11IfR88sPpqS6kIMS5Pz2gu93TJSUlCA4ORk5ODhISEkAIQVhYGKZNm4bXX38dAFBVVYXmzZtj4cKFGD9+PACgUaNGWLlyJUaNGmVz2mp22DLVKrX7TonlUCiMUNxC+dTqLkxbZWxBbveko/NIZRyzG1wL7aMkL45Mr77IaKFtxcZipcqw2Pgt9TrVSx9S+l6Sk1FSF2JjhlYtlm+srnJq/PPHNnBq/I5A85ZGPqWlpQCApk2bAgDOnz+P4uJiJCcns2EaNGiAxMRE7Nu3j70WHx+PjRs34vbt27BYLPj+++9dm3EZxL5YKf/DkfWjxQGprmFPHdP2qbtotW2VfODww4lZxR1pXXNGfcmNpXLWQzEZoTjqkqXRYiFO/ekBXSmNhBCkp6cjPj4eHTp0AAD2hBjmnGmG5s2bs/cAYOPGjTCZTAgMDESDBg3w/vvvK0qP+69abJmaE0pPyXQdRb/QtqRQXAt/3FXzDPKVRz0+v3LTybbGp8e60DMff/wxIiMj4ePjgy5duuDnn38WDbtnz55airzBYMDJkydVpamr1byTJ0/GkSNHkJubW+sev7Pyzdtvvvkm7ty5g507dyIoKAh79uxxeP7UmtTtGbjqGs6YgnYWjk5Pq1MxFEpdQOj5csY0KH2O6z5amg3cuHEjpk2bho8//hg9e/bEv/71L6SkpOD48eMIDw8XlTt16hQeeugh9u9mzZqpSlc3SmNaWhoyMzOxd+9eK79vISEhAB5YHENDQ9nrN27cYK2PZ8+exfLly/Hbb7+xO6pjYmIcnkepaQtb43EXrh4AXVVfjiiXo+qFfizIo6cXsZ7yWl8Q82gh9uwxHi2MRqPkjI7Y5hcl1+oC9XXsIhbnxl9VVYWqKut1kw0aNECDBrXXOi5evBhjx47FuHHjAAAffvghtm/fjpUrV2LBggWiaQQHByMgIMDmPGp+epoQgsmTJ2Pz5s3YtWsXIiMjre5HRkYiJCQE2dnZ7LXq6mrk5OQgLi4OwINdzQDg4aGuuPX1wdACWvqicxb2Ln+gUCjSiHm0EHv2DIYHHi24JxMpRSjOuvps07HLOSxYsACNGze2+gkpgNXV1SgoKLDaywEAycnJVns5hOjcuTNCQ0PRt29f7N69W3UeNW9pTE1Nxfr165GRkQF/f392nWLjxo3ZL8Zp06Zh/vz5iIqKQlRUFObPn4+GDRti5MiRAIC2bduidevWGD9+PBYtWoTAwED85z//wUsvveTOoinCXQqruxVlZ6Xv7nJRa5Q6HG3VdSaOTkPvfUULeU9MTERiYiKAB/WpxNl8q1atAKg7glDJjmOK/rE4WUmeOXMm0tPTra4JWRlv3rwJs9ksu5eDS2hoKFatWoUuXbqgqqoKX3/9Nfr27Ys9e/YgISFBcR41rzSuXLkSANC7d2+r62vWrMELL7wAAHjttddQWVmJSZMmsc69d+zYAX9/fwAPzj/NysrCjBkz8NRTT+HevXvo1auXLpRGvb84KBQtQJ8j16OHOpfKH1UEKa5GbCpaDLm9HFyio6OtDkXp0aMHLl++jEWLFqlSGnUxPS30YxRG4EHFvf322ygqKsL9+/eRk5PD7q5miIqKwqZNm3D9+nWUl5dj27ZtLi4JRcu4appF6UtKiLy8PEyYMAHx8fGYN28eKisrJcNbLBaYzWaYzWbR6SSx58uRrFu3DklJSXj33Xdx/fp1WCziC4PMZjOqq6thMplQU1MDi8UimCdueZyVb3fjDEVFrA9xr8+dO9eqb1ksFrYdgP/VPdO/ampqRPvQwYMHReMVg5E3mUyi7cpPR6zt5TxRKFmbaDAYkJeXh/Hjx7P1dv/+/VphuHL2PHt67Mdiz2ddQ6rNHPFTSlBQEIxGYy2rIncvhxK6d++OM2fOKA4P6MDSSHENdXX9Zl0qT0VFBdq2bYtJkyahXbt2uHHjhtWmMD63b99GYGBgreuutABlZWVhwYIF+PTTT9G9e3cUFhaiadOmMJlM8Pb2rhWeOVLTlqPJ6lJbOwuxPsSs/Zs4caLV9erqanh5eQkqWxcvXkRERAS7VlyonSorK2ul9/DDD0u2FXOWMdMH+C5uHKGQiKXPHQe55XHls0f7MUUOb29vdOnSBdnZ2Xj66afZ69nZ2Rg8eLDieAoLC602ECtBdyfCuBPuA2+LywYq49hTQ9SkZ4uMu+uPu/OzpKQEGRkZiI6OZmUA4d2fZrOZvc68eLkyQki5JMnPz1d1pjqTD0IILBYLe0Smkpe+lCVISN6Z7cOXtUXG3X1ITgYQr3N+nxHqQ8y19evXY/Xq1UhKSsKLL76IZs2a1dp4yKTLWOC4Hwi2KEqu7g9y9Sb1vArBPCOMksyvC7lXs1C8eul3Yu9Re55TVzB9+T2nxr9kciPFYTdu3IhRo0bhk08+QY8ePbBq1Sp8+umnOHbsGFq2bImZM2fi6tWr+OqrrwA82F0dERGB9u3bo7q6GmvXrsV7772HTZs2YejQoYrTpZZGCkWjxMbGIjY2VvR+Xl4ePv/8cytlzsfHx+occz7MNAhXmWOuC4VVkg8hOWa6jqs4qPlAYMr32Wef4dixY6LKKl9hkFJWpcqqhReUnq39I0eOZDceMvDbhvshwyhJAKwUzilTprBr0YVgPooYhVOor6v5KHEkUs+JUN9nysB9RrhhpJRHR5bD1n6n5PnkwijIv//+OzIyMjBs2DBERETU2lBUU1PD1gl3xkGPz4UzGT58OG7duoV58+ahqKgIHTp0QFZWFlq2bAkAKCoqwqVLl9jw1dXVeOWVV3D16lX4+vqiffv2+PHHH/Hkk0+qSpdaGlVALY36keHL2iKjpXIJWXz27t2LQ4cOISkpSXTqj281slgsooqcEmukmJVFLK9ScYhdY+LOycmpVb5HHnlEtN4IIbhw4QIiIiJsLp9YeepCH5KSEfu/mAw/PIBa94Wu8ePNysrCa6+9xi5fIIRg586d6N+/PyvPT5+xzAkh1cb8vuKK51Xt+0LKyuvoPIrJKg0rNP5ITdlfunQJoaGhuHfvHgICAnD69Gm0bNkSZrMZDRs2ZPN9/vz5Wq71tKIwTlvmXEvjh2nKLY3uQvNK44IFC7B582acPHkSvr6+iIuLw8KFC612AW3evBn/+te/UFBQgFu3bqGwsBCdOnWqFdcvv/yCWbNm4cCBA2jVqhWOHj2qKi91WWnky2opb1RpFFYahZRD5r7QdS5CVgyhF5VcunJ5VYLUC1KNEiKk0IqVR06OX5660IekZGxVGsX6h1KlUaq/CSElz4/DWYoGvxzr1q2rNTXPvc8tu6On5sXy5uqxX+nHpNgHByNri6LsSqjSqIPd0zk5OUhNTcX+/fuRnZ0Nk8mE5ORklJeXs2HKy8vRs2dPvPfee6Lx/PLLL3jiiSeQnJyMgwcPIiMjwxXZ1w0a/3aot3AHY3teLvyXmDMQGvCZfAv9zc+H2DWK81HbP8TCCPU3MRk5BYHfV6TyJXSPEPGdqczfasY9obDMRq9//OMfmDVrFq5evcruKBfKk4eHBzw8PGA0GuHp6alKyVVSD3pCrg3E2s6dEAtx6k8PaN7SyKekpATBwcHIycmp5VvowoULiIyMFLQ0du/eHf3798c777xjc9p12dJY12T4srbIaLFcUlY2W60PSixLri6PLTL21IGS9GyREZOV2+TEl2Hc3nCXFyhdz6fVtnWUVcndFlpA3HJtazoMehm7lMhKWakB4fqy57l1BlOXljk1/qVTxdfzagXNWxr5lJaWAgCaNm2qWObGjRs4cOAAgoODERcXh+bNm+O5555zVhYpFIqD0dm3rSyMi5u33noLKSkpaNOmjWT4+/fvs+v4hNzRSFlk9FR3tlgA3YUj8ihnSXOHtc2RadWFPsnFQohTf3pAV7unCSFIT09HfHx8LefdUpw7dw4A8Pbbb2PRokXo1KkTvv/+e0Xpaekrh6KMutJeYtYoIZjyirnc4SI3YPN3Rfbt2xdr166VdLnD7FpmrGBCu5jF4CoKctOeUuipzbnH2zGbPqRg6lvIZyIfIauXsxFa18f0BX5+hdb1CSGWd3e2s9COYR8fH/Ye/3lt1aoVvLy8QIj1Bh6DwSDqckcMIcucM7BnGYza62JWR3vy4kz0MoXsTHSlNE6ePBlHjhxBbm6uKjnmBIrx48fj//7v/wA8OLSbQtEyjDVq6NChyMrKkrVGMY6YS0tLERgYKDgQS335MwM035Hxxo0bBR0/89PlKyvM+i1+/FLUFYXfmShdA+cqhBy4BwYGsuvv+G2qZwfuQk6+mfOsmef16aefxk8//cQ+r+fOnUOrVq1qWYcZB+A1NTWsYglAcnc4g7vrwVXQ8UB76GZNY1paGrZu3Yq9e/fW2o7PILam8fz582jVqhW+/vprPP/884rTlFpXoZV1QVRGmawtMloqF3+dk9g6ROa+0DWxtVJiclwZsTVcYuuS+OmI5UMub0LUlT7Ev6dkDaDcOGTL2jJ7y6N0XZ9U3Fy03h+4cXDzK2bhlVsHKfURoPYDQWkfUiNjSzpKZcT6ry392hWkLvrDqfGveCXAqfE7As1bGgkhSEtLw5YtW7Bnzx5RhVGKiIgIhIWF4dSpU6rkmE6qhc5KodiDu74NpV6mFHVocTxyZHuKTVOqUSCdib3LJ5g4bLmvdGpai33EEdS18ugZzSuNqampWL9+PTIyMuDv788e0N24cWN2nc/t27dx6dIlXLt2DQBY5TAkJAQhISEwGAx49dVXMWfOHMTExKBTp07IyMjAyy+/7J5CaRC9PJTcdUNKTyFg/mV2ngoNrPxBWSvKDfOiErPscZGy4kl9ratte254pdYVNVaq+oIzyqyVehSyUsuFV3LN0ahJQ0kelTyvcpZUe8ptz7Oulb7DRWt5oksadbB7euXKlSgtLUXv3r0RGhrK/jZu3MiGyczMROfOnTFgwAAAwIgRI9C5c2d88sknbJhp06Zh5syZmD59OmJiYvDf//7X5WXRMlpRkuRg1g2tWLECM2fOxK1btyTDGwwGmEwmGAwGUUe6eim7K3H1YF3X2kBrLzt3ouW2VZM37geoK/Lh6nrTcjtRtINu1jRqAam1GGrkqYzj/PIB8qcQMEitLePet2U6zN11UZ9k+LJaSsee9OqyTH5+vqIZAq4M83PGOeKurIP8/HxVZzQz/jiB2t4PlCz10EN/ULqmUWtMWHjHqfF/8noTp8bvCDQ/PU2hOAqtrI2i2IdWXygUcWJjYxEbG6tKhpnmFXPdI4bW+ofasjOzIsz/haC2Hoq7oEojRdcoXS8l9SUrtvaHDszuQ2svfop9KLGQyckxsmIzAu6YzrVl/HFkOrbWq73p1lfoO0EHaxopFEcgpSBSKBT9UBde3OvWrUNSUhLeffddXL9+nZ2OFqOmpgYmkwk1NTWCYbl1wl17qaW6ktqYQ8dl/UAtjRTdYs9AQwcpbUPbp25j645q7nV71iA7AlvT+umnn2o5Q2/atClMJhO8vb0Fy8U/VUmNldNWHF2XYt4TtLyGkY+Fbp/WvqVxwYIF6Nq1K/z9/REcHIwhQ4ZY+VusqanB66+/jo4dO8LPzw9hYWEYPXo0636HDyEEKSkpNp0Ik5eXhwkTJiA+Ph7z5s1DZWWlrIzFYkFNTQ3MZrPolx930betX4dq88YcN1ddXQ2z2czmQy5frvp6tbeumSO6+LijLBTXoMZ6w/R9rvWG3x+4f9M+4zjklAPusz937lyrZ19qjR8hBCaTSdE4y/ztagwGA1JSUnD06FF4enpi4sSJmDJlCubPn8+Ow0IyjGJlMpmsNsrIlYH2WYqj0bylMScnB6mpqejatStMJhNmzZqF5ORkHD9+HH5+fqioqMChQ4cwe/ZsxMTE4M6dO5g2bRoGDRqE/Pz8WvF9+OGHMBgMKCsrU52XysrKWsepMUdICcEcr1ZWVoYmTYR3RYk9zGq/vBhXNErzxj/CSg2u+CJUWx5+XQuVy1F1TdEeQkfZca03fPR8lF1dR+ioPu6xlUIwR+/xj+oTstrZiz3tz82H0BgnVU6DwYB79+4hICBAMD5n5ZnyP6jyrUOXOyUlJQgODkZOTg4SEhIEw+Tl5eEvf/kLLl68iPDwcPb6r7/+ioEDByIvLw/dunXDxYsXVaUttQhbLDz3vpAMPwz/mq2uDeTypkRGKG98GVe5auDnTSyMWD0qDWNPHqmMe113ALY/R1L9nx+vK8tV32XkxiBGhkGsPygZi51RHiV5FcofNz2pfPDjclW5HDWOi5VFqx/yY98pcWr8q2c3c2r8jkDz09N8SktLAQBNmzaVDGMwGKy+yCoqKvDcc89h+fLlCAkJcXY2ZdGTrq73qTk95pkijtC6KGfFzU1Dr/2/PuPu9nLnVDjtsxRnoPnpaS6EEKSnpyM+Ph4dOnQQDHP//n3MmDEDI0eOxEMPPcRenz59OuLi4jB48GBV6QlZNLgLsaUeSKH7/PikdpSpRU3elOTX3V96Wq5rivtQY4VQ075i/UWrVo/6gtIlA1LWKrFn39UKlZQl0ZY+rWTc0xJSde6O9lALoRth9KU0Tp48GUeOHEFubq7g/ZqaGowYMQIWiwUff/wxez0zMxO7du1CYWGhq7JKoVAoFAqFUqfQzfR0WloaMjMzsXv3bsHFwjU1NXj22Wdx/vx5ZGdnW1kZd+3ahbNnzyIgIACenp613BeoQe3Xm6O/9uSsbXykdiKKYTKZYDabRXcicq9x/3X0V6LSumPK2KtXL0RHR+PkyZOyMsxOa5PJxJZTCL1PzesVNTuhubvmuTuh+fD7KrdNpSxSfHklKImPIo899Se1jEEL7WOLlVFIXigeZ5TLWd5DGE8eJpMJgPuXFEhhIcSpPz2geUsjIQRpaWnYsmUL9uzZg8jIyFphGIXxzJkz2L17NwIDA63uz5gxA+PGjbO6NmDAANm0tTCw2IvaHXrAg52lcou0hXBXPTFlHDp0KLKystCmTRvJ8MxO69LSUgQGBrLls1gs7JFlUkqknvuDHqgLO6EdsWmC4nzqWvs4s9/Z69Hi/v378PHxQXV1NRo0aMCGu3DhguB7naJNNL97etKkSVi/fj0yMjIQHR3NXm/cuDF8fX1hMpnw17/+FYcOHcIPP/yA5s2bs2GaNm0q+JIBgJYtW9q9e1ovOxC51wHhF6XQrkWlOw+dUR5b61oor7bUAbd8amX10h+0KsOtZ36biPVRfpwMYn1WDUrL447xwRb01h+cIcOXdWQ69vYDhnXr1mH16tVISkrCiy++iGbNmrEftXwYKx0Aq48nQJnbIVvqnB+/WBipZ1DoXSIXrzsZ81axU+P/cp77N+nKoXlL48qVKwEAvXv3trq+Zs0avPDCC7hy5QoyMzMBAJ06dbIKs3v37lpyesPd1k65bwrulJ8WH3IxxJRJjX9D1XkcWf9yLzOpMHqBWjTrJmqt7QaDQVSh1CK0v+oXzVsatURdsDRKvUilZIRk3V0eoTBS/xcLLyajhXI5UkYN7iqPlJXXXuulK6xRYnlQaiV3RN7y8vKwevVq/Pbbb+jfvz9ee+01+Pr6iubNYrHAZDJZTfPzP1bFXhNKLG5afiZsaR9X5I1B6MNWqZVTLF4lY6hc/LZaXbkIxaP1j6DRs4ucGv9X74Q6NX5HoHlLY33H3ZZGSt2BOxDLKRZ8lCgWTBrcfx3Vb5V+yDgDVz97asolFC42NhaxsbGK0zMYDKpPhWJwdBvQ8c4xCLUL96NMSIFTAm0XClUaNY4jX5C2yDtiEHfFy50bvxLLotCAWp+w5Qizy5cvo1WrVqrTslcJUoujFQ93PXtKEBofhJR4pSjJp5z10R7cZWnSy/NvryXaEX1EDWKKq17qm4+F+mmkSiPlAXX1C7+ulksNQnWQmJiIxMREAA9eGlK7IBkYhVGNYuEMaJvah1T9uXu9p6va1lVlc3VftSedulLnFOdClUYKAOd+4btzsNDzV62jsGWNq1x8DO6oW72tg7IXteXiLw3gy4pZJ+Xqkzu9KRSvI3BVW7qqr9hSHqk1gErTE4pL6XpFZ1EXnk96IowOnHsvWLAAXbt2hb+/P4KDgzFkyBCcOnXKKszbb7+Ntm3bws/PD02aNEG/fv1w4MAB9v7t27eRlpaG6OhoNGzYEOHh4Vi4cKGri0KhaAq5KX2x8HxFQyxONXHbQ123PKotl8FgUFUnQu1qS7ruQm/5dQe0bhwDIcSpPz2geaUxJycHqamp2L9/P7Kzs2EymZCcnIzy8nI2TJs2bbB8+XIcPXoUubm5iIiIQHJyMkpKSgAA165dw7Vr17Bo0SIcPXoUX3zxBbKzs91VJArF7di61s1Ra1wpjsWZ6wzdiZLyiG3u0CNiZXBU2epCHVHci+5c7pSUlCA4OBg5OTlISEgQDHP37l00btwYO3fuRN++fQXD/PDDDxg4cKCqtPnuA9ROO1AZ1zvvtQUt59GevLkqnbpSb3xZtTL5+fn47LPPcOzYMcU71Jk8MqcTKdm4UJfaVo8yUnG4o9/Z+hy5oy70ttTkudcuOTX+De+HOzV+R6C7NY2lpaUAHpz2IkR1dTVWrVqFxo0bIyYmRjSee/fuOSV/FG2h1cEHcJ8yrHe0/mJhqKioQNu2bTFp0iTFO9RNJhM8PT1rnerBIGWJ0nJdUFyLXH+g/YViK7pSGgkhSE9PR3x8PDp06GB174cffsCIESNQUVGB0NBQZGdnIygoSDCeW7du4aOPPsKIESNk09PDy0kpUuWoC+WrT6hpL7rmyz0kJiYiISGBHUOU7FD39HwwJMu1lZ7a0lVrWt2Fs9JXG++6detQUlKCZ555BiEhIfDw8BA9JcZiscBsNrP+VoU2zNgyCenutnA21OWODtY0cpk8eTKOHDmCDRs21LrXp08fHD58GPv27cMTTzyBZ599Fjdu3KgV7u7duxgwYABat27tiizrBp2tUqj3qGmvurTmq76gdHG8HtrU2XnUQx3YgppyZWVlobCwEFOmTIG/vz8MBgPOnj0rGgejUHKXQCilriuGFGl0ozSmpaUhMzMTu3fvFpzi8fPzQ+vWrdG9e3esXr0anp6eWL16tVWYsrIyPPHEE2jUqBFWrVrlqqxrBvqw1y3y8vIwYcIExMfHY+7cuaisrJSVMZlMMJvNMJlMgkoJ9xr3X3e9mNetW4ekpCS8++67uH79OiwWS60wTN7MZjOqq6thMplQU1MDi8UiWUZX71x0xA51rlVICkeUx9l1wu2/0dHROHnypKyMxWJBTU0NTCYT24f5uKNt3QVTtpSUFHzwwQcwGAxo3LgxPDw8rAwjQutgPT092f7E/fERum8wGGwafywWC86dO4f58+fj9OnTgs8zYwUVG6PcidjY4aifHtD89DQhBGlpadiyZQv27NmDyMhIxXJVVVXs33fv3sXjjz+OBg0aIDMzEz4+PrJx0Gk9ipZRe6oLgFqWBWag0uJ0aFZWFhYsWIBPP/0U3bt3R2FhIQIDA0XX+3GPOGRgptu0WD45tJgnR8L036FDhyIrKwtt2rSRDF9dXQ0vLy+UlpYiMDBQVGEUwpFLjLTWLkqUDbn1jVLhhBROQojqNbtVVVXse3nmzJm4dOkSampqUFpaiiZNmrDhLly4wL7n5fJGcT2a3z09adIkrF+/HhkZGYiOjmavN27cGL6+vigvL8c//vEPDBo0CKGhobh16xY+/vhjrF27FgUFBWjfvj3KysrQv39/VFRUYMuWLfDz84OHhweCg4NV5UVq15caeSojLsM9E7mkpIRtdzEZi8XC/hilgbsuTCwdtbv87C2Xs2WklD++jNC6JaF1TK4sj9g9fpm41+TSEXvZ8eO0J49yMnrqQ2pklDxXauNnkOrfzH3+dSGLrFAYsfI4C1ekI/RMOOoZlJPh/svkQS6f3HBC7S4WRgtK47Dp550a/7dLlBnF3InmLY0rV64EAPTu3dvq+po1a/DCCy/AaDTi5MmT+PLLL3Hz5k0EBgaia9eu+Pnnn9G+fXsAQEFBAevsmzHZh4eH4+LFi64rCEURsbGxiI2NVRzeYDDAaDSKWp8YxKwSWhiI3IXc9yJz3xX1JKUk2Av/ZSQUf33uB45A7GMPEO8/zDQkc0/oGVbaD1xh+3CVoslPz1a4baLE5RN3mtRisYhuyuKOC9x/KfUDzSuNch3Sx8cHmzdvlgzTu3dv2rF1gj2Kg5opyLrYH5S+YLjhpL7uXfmCVIqQAigXXsk1ijr4deiuqWYl1jUtt7cjni8xeVuWrxBCrHZdS81EKMmHLeUTGpO0Ml7TYwR1MD2tJej0tGtllEyVinVfuXZSM53r6HI5Q0ZumldKRqxe3V0eBlvzZu/zKhSPWhk99SFHyah9boWmIaWmv7kyriiPM2WUPLe2YOt4xyCVJ24YORn+M8gPK9ZX1IxvruSZqeecGv93S1s5NX5HoHlLI6X+wQwQSr9u+QONUHi9WSDUYktZuNYaLeIsCwzFvShRNLj/12o7OtNKaGs6jswTfxy1Z4zh/i0Ur15sVxZSe7d3fYMqjRTNYct0htC/jkyD4lr47SO0PkvOAwKzZk7KF53SD4z6ji3PlpJZAEB67Z2Q0gFoo21d1U/UpCPVTmrHRak4pBR//vrWwMBAPPnkk3jxxRfRrFmzWmns2LEDGzZsQH5+PqqrqwEAXbt2xYwZMxAVFYU7d+7g2rVreO6556zSSU5OxtSpUxEeHg5PT09RR+aOhE5PU6WRQqHoAKH1WVInrDBr5srKyqzceXBRsmaO8gAlU7O21ptY24rFRdtWHGesRVYbF9OeLVu2xBdffIH09HT89a9/RVZWFlavXo2vvvoKjRo1YuPOy8tDXFwcXnrpJXh7e+OXX37B3//+d9y+fRujRo3C+PHj0adPHwwaNAgdOnTA0qVLsXnzZrRs2RLl5eW4d++e6LHCFMej+TWNCxYswObNm3Hy5En4+voiLi4OCxcutHK/w2X8+PFYtWoVlixZgmnTprHXq6qq8Morr2DDhg2orKzEiBEjajn/loOuadSPDF/WFhktlovKyE9vcsMoWU8lt/aL9iHttK2SdXHca2Jta0951ODqscsWlOZRbG2qEksvfwpa7D0qtcQIAOsMXM5bhrMYMum0U+Pf+rH0BjItoPkTYXJycpCamor9+/cjOzsbJpMJycnJKC8vrxV269atOHDgAMLCwmrdmzZtGrZs2YJvvvkGubm5qKiocEX2KRSKxtD4dzLFDup72zLld3c92JO+1FrKCxcuwMPDAzU1NSCE4Ny5c24va31D89PT27Zts/p7zZo1CA4ORkFBARISEtjrV69exeTJk7F9+3YMGDDASqa0tBSrV6/G119/jX79+gEAlixZ4vzM13G0vpGCUrfgWjXkFs+LLbiX+lvsmi3QZ0MdSttWapMGPw4xWe7/bWkfrUxxC+VDbbnWrVuHkpISPPPMMwgJCbFyt8NPi1lHajQaYTAYalkLmf+L5VVJeaQwGAxo1erB7mLGh2RERAQIeeBb0iVrGqmCqn1LI5/S0lIAsFrDYLFYMGrUKLz66qusQ28uBQUFqKmpQXJyMnstJCTE+Zmt42jlq5ZCUYqr+ip9NrRNXW0fqXLxr2VlZaGwsBBpaWnw9/eHwWDA2bNnReuEUSilNh+JYa+SzVWGuXEx09U1NTV2xU9RjuYtjVwIIUhPT0d8fDw6dOjAXl+4cCE8PT0xZcoUQbni4mJ4e3uLLpqWSs/Ri4rtRUt5oTwgLy8Pn332GY4dO6bo5AXm2EPgf2tzpNZgUR7g6hcVH/rsOQ9XtK072s+Z6dlbZykpKUhJSQHw4FheQgh7YpqYLGPhszVPclZkpe9cJgwzfnp7e0um6yiYcbs+oyulcfLkyThy5Ahyc3PZawUFBVi6dCkOHTpEB3SKW6ioqEDbtm0xadIkRScv3L9/X1KpBKjCSKHUBbT6oeGI8UXNh64zPuC4/9diHddVdDM9nZaWhszMTOzevdvqhfzzzz/jxo0brK8mT09PXLx4ES+//DIiIiIAPJiKrq6uxp07d9yUe8fhroeDKjHiJCYmYtq0aYiJiYGXl5ekKxgAVgqj2LQL977YPYo0ju6z9rQBfX7cD7/98vLyMGHCBMTHx2Pu3LmorKyUlGfWzhFCYDKZ2P+LhdHDFDhTJ3l5eZg4cSJ69eqFefPmydYFY3Hj14XYeGVvXXDl5dYqOxNiIU796QHNu9whhCAtLQ1btmzBnj17EBUVZXX/1q1bKCoqsrr2+OOPY9SoUfi///s/REdHo7S0FM2aNcPatWvx7LPPAgCuX7+O5s2bq86LmKsANfJ6lJFytyB0T8xhr5iMxWKByWSCh4cHO+3AX7huy1esq9xk8F1F2JsnpS5D1OatPsnY+7wKxaNWRk26Wqu/ui4jhFx/EkOJnDv6nZI8Ocs6qKS+xJ4Tfv7546uj86qUgS8ed2r8P3z6qFPjdwSan55OTU3F+vXrkZGRAX9/fxQXFwN4sAbD19cXgYGBCAwMtJLx8vJCSEgI68uxcePGGDt2LF5++WUEBgaiadOmeP/997FhwwaXl0evqH0ohRz2Sk3ZGgwGXL58md0dpwZHTE84copDyboebppCA6WY1VHj33iaQkvWWS3lpS7giOeV/+yplWHyIZU3tfG7Cn7dqa0LobGLHxc/LXvqQmpcdCWEHiOofaVx5cqVAIDevXtbXV+zZg1eeOEFxfEsWbIEnp6eePbZZ1nn3npAKw+LWhITE5GYmAjgwWAhN2ULgFUYxaY2GJSsoaHYh177nTOgdUARQmrs4a+50yPr1q3D6tWrkZSUxB4BKObWhnusI3+mSKgumLD8+xTto3ml0ZYOdeHChVrXfHx8sGzZMixbtswBuXId9kyr6RWhr1Rno4W61UIeGOpjvxOD1oH2cEZ72BKnkOVMzbSqu5Dr0z/99BMWLFiATz/9FN27d0dhYSGaNm0Kk8kkuFOZqyzy0xBSHj08PGCxWFj3PVqrHzH0su7QmWheaaTUDZQMyErWgAlNq1AoFIo92Kow2irrLJTmSeq+wWBg3fEw4R577DFZxY7vjkcuDaPRqBtlkYEqjTraPU3RF2oHA/5g5+qBWAuDlxbyQKHoAb08K+4ax+ypH0fULSG1dzmruU/RLlRppDgF7mCppS9xMbSQR3flQU+D97p167B06VJcuXIFZrNZ0tmu2WxGdXU1zGaz1UuKv8ZK7EdxDVzXN0rdvVgsFrZtgdp9WCvtKpeeFvqZGuukWBju9by8PIwYMQJ//vOfER0djejoaJw8edJKnvk/83deXh4ef/xxtGvXjt1Aefv2bas0tm/fjoSEBDbO2bNn4969ezaX2xYsxOLUnx6g09MUCkUXMMeeLVy4EPfu3YPB8ODYs9atWwu+zJh1VracSauFj4j6gpCnBamNc7dv30ZgYCBqamrg5eWlKi3arrWxxYWVFBUVFQgLC0Pz5s2xfft2pKSkoE2bNoLyhBBcuXIF48aNg9lsxqBBg9ChQwc888wz+O6779CqVSvEx8fDYrHgxx9/xI0bNzBmzBg88sgjGDlyJJYtW4aUlBS0bdvW7nqgKEPzfhoXLFiAzZs34+TJk/D19UVcXBwWLlzIutPhM378eKxatQpLlizBtGnT2OvFxcV49dVXkZ2djbKyMiQmJiIrK0tVXuz1++YOmfz8fEF/iVIyzM9isbDrVOR2NAuFcUZ5pGSEdvuJfR2bzWbR3X5MWdT4ZdNLf9CDjJSPTwZ++9jaZ7nxyeVRDXqrcy3JMCiRETvCk4mHH4fFYmH7CnfThlKPDLZuDlMqI9b3peDuXOZb87jpC5WFmze5PArlzcfHR1SGqWuz2Qyj0Wj18cbIcP3zArBqE25Yi8UCg8EAk8nEfigQQlBTUwOj0Sgo5wySRxU6Nf4dX3d2avyOQPOWxpycHKSmpqJr164wmUyYNWsWkpOTcfz4cfj5+VmF3bp1Kw4cOICwsLBa8YwaNQqlpaXIzMxEUFAQfvjhB1cVwa2o9ZcIgLXO8F0iCO2C48u560s+Kyur1m6/wMBA0cFEarefHNRa4VzUWp4A4OLFi4iIiFDdZxmkdsBStIvcEZ78dmWslHycNXapjVfteF1dXQ0vLy+UlZWhSZMmonlQkje5fNprEa6oqEDDhg2tFD+DwYArV64gMjJSNB5uPsvKytC0aVP23qlTp9CuXTvJfFMci+YtjXxKSkoQHByMnJwcJCQksNevXr2Kbt26Yfv27RgwYACmTZtmZWls1KgRVq5ciVGjRtmcth4tjUJfm0LxcGX4YaRewHyLj1wenVUHSvKtFjXy7m7buiojpMzxZWzps0Jh5PKoBjpDoE5GqE1tSQdQXx4GuX7hqmeCKyuUD/59obwrvaa0zvkzAULWXaEyM2kw1lCutVDsg95kMolaHPnvYMYCacsSFFvo/7cCp8afva6LU+N3BLrbCFNaWgoAVl8bFosFo0aNwquvvor27dsLysXHx2Pjxo24ffs2LBYLMjIyXJLfuoRWvy8ckS/mhSsUH/eFrNU6oAij1/ZirDorVqzAzJkzcevWLVkZZoaAURj5/VXK4kSpjd6fe6V5FprOloKx7irtm9XV1TAYDCgpKYHRaER1dTU8PDxw9+5dAP+bxi4oKGCnspWWiTn3muI6dKU0EkKQnp6O+Ph4dOjQgb2+cOFCeHp6YsqUKaKyGzduhMlkQmBgIBo0aIBZs2YpSo/7b13AlulX/qDCXTfDDaO1KT21+REKz5SL+9MK3F2nc+fOld11Cvxv/VNNTY3oy5Df713d/5n0mPL16tUL8+bNw/3790VluGukzGYzTCaTaBilypQjUZtGYmIipk+fjpiYGHh5eSk6UYm7pk2ur2qpH69btw59+/bFu+++i7KyMsm6YnbEm0wmtg8DtS1nYj8hxMYyqbq05dkzmUxs3xTKj1hepfLNzZfY8yJUBuZv5nmRqiNuHRNCkJCQgGnTpqG6uhppaWkYMWKEVR3w3xPMVHRISAjMZjOuXbuG999/Hzdv3mQdfAMPfEEaDAbU1NSwa86FyszF29vbZVZGACAW4tSfHtD8mkYukydPxpEjR5Cbm8teKygowNKlS3Ho0CHJgfDNN9/EnTt3sHPnTgQFBWHXrl2uyDKF4jRiY2MRGxurSoYZyLkDLX/KRwhbptfsRW35mDwKlU8KLSlQzsAdbacUQghGjhyJkSNHKgrPXWutBGeV25ZnT8iCpqRtlJbBnudFKk9iSxy46a1fvx4DBgyQPHLQYHjg0LtRo0b49ddf8fnnn6Nr166YMWMG2rRpwyqYXl5eIISwynVpaSksFguaNGlSy4E4xfXoZk1jWloatm7dir1791otmv3www+Rnp5u1UGZnWQtWrTAhQsXWLccv/32m+j0tRBS62y0suaLyiiTtUVGi+USk5Fa/yS15ot/jb/+SWg9lCvKIxSOmyeldSBWHqV1x82jGhzdtnJ5EwsjdN3dfZUfBpBefyeXBhexNrYlb0pllD57YsoXv+z8d4wapJ51obAMUs+6VHkJIfjpp5/w2muvWW1C7NixIwgh7JGDXJnq6mp4enrijz/+QJMmTVBWVgZvb2/cunXLahPrmTNnEBUVpajcrvoo6jvioFPj/883f3Fq/I5A89PThBBMnjwZmzdvxq5du2rtsho1ahSOHDmCw4cPs7+wsDC8+uqr2L59O4AHazAAqDZjc83sFO3DnTJiHMrKYbFYUFNTA5PJJLo+Ruk0l56pi2WiaBtH9jkxhU2rz6xUfrj5dVe+1aSbkpKCo0ePwmg0YuLEiZgyZQrmz59vNb3MtWgyU8pNmjTBwYMHMWbMGEycOBHl5eVWzvoZhbGyshIrVqzA9OnTcfnyZcEPBYrr0LytNzU1FevXr0dGRgb8/f1RXFwMAGjcuDF8fX0RGBhYy4WCl5cXQkJCWF+Obdu2RevWrTF+/HgsWrQIgYGB2L17N8aNG+fy8lCcB7N5YOjQocjKykKbNm0kwzPuKkpLSxEYGCi5nkfoutYGKyX5EVq/xS2LrRYfZyA3TSaEUPmYuOTCaBV78qf1sgnBt3qpDeuOMtuSppwl394xRum0t5DFUeq5E2ufyspKSXdBfKtrUVERxo8fj7FjxyI1NRUXLlxATU0NKioqWPdBhBBkZGRg0qRJMBgMOH78OAghqKysrOVyzxVYdLLu0JlofnparOOvWbMGL7zwguC9iIiIWi53zpw5gxkzZiA3Nxf37t3DwIEDsXHjRlV5odPT+pJhUDo1xr8mNV0jdE3LdaF3GbWOhQH7nR7z8+hstFbnrpJxR9tqrQ6kZB2FlNN8oTwydXzo0CGsXr0ax48fR0lJCTIyMhAdHS2qPDLpHDlyBLdv34a3tzeGDx9e68AF5jjIHTt24N///jcKCgpQXV0NAOxax6ioKNy5cwfXrl3Dc889Z5VOcnIypk6divDwcHh6erpkQ0zvZ35xavx7vuvh1PgdgeYtjbbotBcuXKh1LSoqCps2bXJAjij1AY1/S9U71DoWdqTTY1uwR1mob+itbbWC2DpJsX6n1nG4wWDAvXv3cP/+fbRr1w5Dhw7Ftm3bZGdw7t69i9atW8PPzw8//vgjZs+ejWHDdRWT3wAAIwdJREFUhmHr1q34+uuv8dVXX8HPzw+XL19GeHg4CgoK0LNnT4wfPx7e3t7Yt28fxo4di9u3b+P555/HhAkT0KdPHwwaNAjt27fHRx99hK+//hqPPvooysvLce/ePSsXfBTnonmlkUJRAzNYCk298BGb2pJ74deVl46eaNiwIU6dOoVNmzaxVhIpmB2Y/v7+rAPg+thuelCSaNvahtTYJHQvMTERV65cwfTp09ldzkz9CWGxWODn54cGDRrgxIkT2LRpE0pKSvDSSy9JnsLSt29fHDhwANnZ2QCA2bNnIygoCEOGDMGQIUPYcC1atIDZbMbdu3exdu1aqzgOHDiA2bNn49///jcAoLCwEJmZmcjMzAQADB06lLU0/ulPf5KoJceiF7c4zoQqjRQKRfOotUYxR5cJWaP0oEjVJ2jbOg6pD15bj1qtqqpCu3bt8Ne//hU//fQT2rZtK5mHjIwMrF27FjExMUhJSUFhYSF69eqFy5cvo0WLFmw4xtI4Y8YMNGjQAIGBgXjssccQEBCANm3a4Ntvv8Uff/yBbt26ISYmBgMGDMDOnTuxfv16AEC7du2wefNmAEDr1q3tqTaKCjS/plFL0DWN+pHhy9oio8Vy1WcZblgGpetVhWTk1rTy01WDFutPyzKublst1oGYrJr7UumJlZ9/TSwtPnx5obDM+sZjx46hpKQEy5cvR79+/UTjAYCDBw/ivffew++//47g4GCMGzcOw4cPh8FgYD8Ytm/fjo8++giXLl1CgwYNkJ6ejr/97W+ideUoEp7OlQ9kB3u3xDs1fkdALY0UCqXOw30x1XdrVF2Dtq08zrYNicVfWVmJtm3bYujQoZIntjFcuXIF48ePx7Bhw/DBBx/g0KFDmDdvHvz8/PDUU0/h4sWLKCsrQ3p6OtLS0hAUFISbN2/i3XffRYcOHRATE+PoolF4aN5PI4VCofCRUwzELDDce0Jr4err+jgtQdvW+UitfZSTE6tXsXWU06dPR3JysqJ4vvnmG4SGhuL1119Hq1at8Mwzz2Do0KFYu3YtDAYDQkND8cUXXyAuLg59+vRBw4YN0bt3b7Rv3x5ffvmlqrLYgtaOEfz4448RGRkJHx8fdOnSBT///LNk+JycHHTp0gU+Pj5o1aoVPvnkE9VpUqWRQqFQKBSK2yksLETPnj3ZDToeHh6Ij4/HsWPHUFNTAz8/Pxw5cgQ9e/bE5cuX0blzZ+zYsQNJSUkoLCx0d/ZdysaNGzFt2jTMmjWLXTeakpKCS5cuCYY/f/48nnzySfTq1QuFhYV44403MGXKFNVeZajSSKFQdIEtVhGKPqBtqx41dSBlDVQjryYd/vp/oTybTCarv2/evImgoCB4eXmxYYOCgmAymXD79m0AQElJCQIDAxEUFISgoCAUFxfjkUceQUlJiU1lUwOxWJz6U8PixYsxduxYjBs3Du3atcOHH36IFi1aYOXKlYLhP/nkE4SHh+PDDz9Eu3btMG7cOPz973/HokWLVKVLlUYKhUKhUCguR2jnttgGHv6xhH5+fiCEoFOnToJyeqSqqgp37961+lVVVdUKV11djYKCglrT/snJydi3b59g3L/88kut8I8//jjy8/NRU1OjOI90I4xK+LvF1OyiY/5V+4WoVpabNyW7Efky/DyLyUrVgZisXL0JySmtaylZORwhWx/aVkrOVW3L/VtoF6hQeKk4hODHQ9u2brWtlttH6p4j2lYKqV3XYnlREjc/LH/XNnMtKCiItRgy12/fvg1PT0/cv38fhBA0bdoUV65cwZNPPony8nL06dMHWVlZCAoKUlRGe8j9PtGp8b/99tuYO3eu1bU5c+bg7bfftrp28+ZNmM1mNG/e3Op68+bN2aOW+RQXFwuGN5lMuHnzJkJDQxXlkVoaKRRKnUPsZSz0AaNUlqIN6nPb2jvNLBanGM7cdW3hTcdaLBZ06tQJ//3vf9njBAEgNzcXjz76KAICAlBcXIyOHTti+/btuHTpEvz8/BAcHIzc3Fx07tzZaXl1FTNnzkRpaanVb+bMmaLhpTaFKQ0vdF0KqjSqxNmuCygUijRC66akdnWqiUvvSoXeoW0rDSGE/dmKWJ2I1bOUrNyvvLwcJ06cwIkTJwAAV69excmTJ1FUVASj0YjFixfj9ddfh8FggNFoxHPPPYeioiIsXrwY586dw6ZNm7Bp0yaMGzcOTZs2RVhYGMaNG4fTp08jOzsbFy5cwKpVq/DLL79gzJgxNteJVmjQoAEeeughq1+DBg1qhQsKCoLRaKxlVbxx40YtayJDSEiIYHhPT08EBgYqziNVGikUCoVCoTic3377DU8//TSefvppAMCCBQswZMgQfPTRRwAebGopKipiw7do0QKrVq3CwYMHMXjwYHz88ceYNWsWHn/8cTbMY489hsWLF2Pz5s0YPHgwtmzZgiVLltQrH43e3t7o0qULe1QjQ3Z2NuLi4gRlevToUSv8jh07EBsbCy8vL+WJE4piSktLCQBSWlpKZZ0kq7f81jdZveWXymo7TSrrGlm95ZcizzfffEO8vLzI6tWryfHjx8m0adOIn58fuXDhAiGEkBkzZpBRo0ax4c+dO0caNmxIpk+fTo4fP05Wr15NvLy8yHfffacqXboRRgUNGjTAnDlzBM3FVNYxsnrLb32T1Vt+qay206SyrpHVW34p8gwfPhy3bt3CvHnzUFRUhA4dOiArKwstW7YEABQVFVn5bIyMjERWVhamT5+OFStWICwsDB999BH++te/qkqXnj1NoVAoFAqFQpGFrmmkUCgUCoVCochClUYKhUKhUCgUiixUaaRQKBQKhUKhyEKVRgqFQqFQKBSKLFRppFAoFAqFQqHIQl3uSHDlyhWsXLkS+/btQ3FxMQwGA5o3b464uDhMmDABLVq0cHcWKRQKhUKhUFwCdbkjQm5uLlJSUtCiRQskJyejefPmIITgxo0byM7OxuXLl/HTTz+hZ8+egvLLli1Dfn4+BgwYgGeffRZff/01FixYAIvFgqFDh2LevHnw9HSNzt6qVSts374dUVFRTom/vLwc69evr6Vc9+zZE8899xz8/PxEZb///nvk5+fjiSeeQI8ePbBr1y4sWrSIraeXXnrJKXkWwtn1BLi+rq5cuQIfHx8EBQUBAH7++Wd88sknuHTpElq2bInU1FT06NHDaeWlULQCIQQ7d+4UfPb69u0reczgr7/+ikOHDqF3796IjIzEsWPHsGLFClgsFjz99NNWJ5ZwqaqqgoeHB3vixtmzZ/H555+zz9/YsWMRGRmpqhxJSUlYs2YN649PDIvFAg+P2pOJFosFV65cQXh4uKCcO+qJoh+o0ihC165dER8fjyVLlgjenz59OnJzc5GXl1fr3jvvvIMPPvgAycnJ+O9//4tp06bhgw8+wPTp0+Hh4YElS5Zg4sSJmDt3bi1Ze17yzNFMfNLT0/Haa68hJCQEADBlyhTRcl+5cgUBAQFo1KiR1fWamhr88ssvSEhIsLp+/Phx9O/fHxUVFUhMTLRSrnNycuDn54cdO3bg0UcfrZXWJ598grS0NMTExODMmTP4+OOPMXHiRAwfPhxGoxFfffUVFixYgKlTp+q+ntxVV3FxcZg9ezZSUlKQkZGBoUOHYuDAgWjXrh1Onz6NH374AZs3b8bAgQNFyyqE0hcXoK+Xl6Nf8rSelOPMurp69SoGDhyIo0ePokOHDlbP3m+//YaYmBhkZmbi4YcfrhXnpk2bMHz4cAQEBKC6uhpbtmzBM888g9jYWBiNRuzcuRNfffUVRo4cKVimyZMnY+jQofjvf/+Lvn37Ijo6mn3+Tp06hZ07dwqOVZmZmYJlHzp0KJYuXcrOdA0aNMjq/t27dzFu3Dh8//33eOihhzBhwgS89dZbMBqNAIDr168jLCwMZrO5VtzuqieKjrDvIJu6i4+PDzl58qTo/RMnThAfHx/Be61atSKbNm0ihBBy+PBhYjQaydq1a9n7mzdvJq1btxaU7dGjB8nKyiKEELJ161bi4eFBBg0aRF5//XXy9NNPEy8vL/L9998LyhoMBvLII4+QiIgIq5/BYCAPP/wwiYiIIJGRkYKy165dI127diUeHh7EaDSS0aNHk7KyMvZ+cXEx8fDwqCXXu3dvMmLECFJVVVXrXlVVFXnuuedI7969BdNs164dWbVqFSGEkF27dhEfHx+yYsUK9v6aNWtIu3bt6kQ9EeKeuvL39yfnz58nhBDSrVs38t5771ndX7ZsGencubNgmoQQkpGRIfgzGo1k+fLl7N9ClJaWkmHDhhEfHx8SHBxM3nrrLWIymdj7UnV15coV0qlTJ2I0GklMTAxJTk4m/fv3JzExMcRoNJLHHnuMXLlyRVD2u+++I0ajkQQGBhJ/f3+yc+dOEhAQQPr160cef/xxYjQaybp16wRl+/Tpwz67ubm5pEGDBuTPf/4zGT58OOncuTNp2LAh2bdvH60nG+vJXXU1aNAgkpSURK5du1br3rVr10hSUhIZPHiwYJqPPfYYeffddwkhhGzYsIEEBASQefPmsfcXLVpEOnXqJCgbEBBAfv/9d0IIIYmJiWT69OlW9998803Ss2dPQVmDwUA8PDyIwWAQ/QmVdcqUKaRNmzbk22+/JZ9++ilp2bIlGTBgADvuFBcXE4PBIJimu+qJoh+o0ihCZGQk+fzzz0Xvf/7556KKha+vL7l48SL7t5eXF/ntt9/Yvy9cuEAaNmwoKGvPS/6ll14inTp1IsePH7e67unpSY4dOyZaFkIIGT16NOnevTvJy8sj2dnZJDY2lnTp0oXcvn2bECI+0Pj6+krGffToUeLr6yt4T6iejh49yv59/vz5OlNPTHldXVeNGzcmv/76KyGEkODgYPb/DL///rtoHRNi+4uLEH2+vGx9ydN6cr4yRIjtdeXn50cOHz4sGCchhBw6dIj4+fkJ3vPz82PHGovFQry8vMiRI0fY+2fPniWNGjUSlT1x4gQhhJDmzZvXysPvv/8uKvvEE0+QAQMGkOvXr1tdlxunwsPDye7du9m/b968Sbp160aSk5PJ/fv3JT9C3FVPFP1AlUYRVqxYQby9vUlqairZunUr+eWXX8j+/fvJ1q1bSWpqKmnQoAFZuXKloGxkZCT56aefCCGEnD59mnh4eJB///vf7P0ff/yRRERECMra+5LfsmULadGiBVm2bBl7TYkyFBYWRg4cOMD+ff/+fTJ48GDSqVMncuvWLdGBJiwsjGzdulUyP2FhYYL3HnnkEbJ3715CCCFXr14lBoOB/Pjjj+z9PXv2kEceeURQVm/1xMi6uq4GDRpEZsyYQQgh5PHHHydLly61uv/pp5+SqKgo0TzZ+uIiRJ8vL1tf8rSenK8MEWJ7XQUFBZFdu3aJxvuf//yHBAUFCd4LCQkh+fn5hBBCbt++TQwGg1UeDh48SEJCQgRlk5KSyPvvv08IISQuLo58+eWXVve/++47Eh4eLpqvxYsXk/DwcKtZE7l6atiwITl37pzVtbt375IePXqQpKQkcu7cOdH+5K56ougHqjRK8M0335Bu3boRT09P9gvY09OTdOvWjWzcuFFUbtasWaRZs2Zk3LhxJDIyksycOZOEh4eTlStXkk8++YS0aNGi1pc5g70veUIeTFklJSWRJ554ghQVFSkajP38/Mjp06etrtXU1JAhQ4aQP//5z+TIkSOCA82cOXNI48aNyQcffEAOHz5MioqKSHFxMTl8+DD54IMPSJMmTcjcuXMF00xNTSVRUVHk3XffJX/5y1/ImDFjSNu2bclPP/1Etm3bRjp27Ej+/ve/C8rqrZ4IcU9dHT9+nAQGBpLRo0eTd955hzRq1Ig8//zz5B//+AcZPXo0adCgAVmzZo1kmW15cRGiz5eXPS95Wk//wxnKECG219XkyZNJixYtyLfffkv++OMP9voff/xBvv32WxIeHk6mTJkimObzzz9PunXrRtauXUueeuop8sQTT5Du3buTEydOkJMnT5LExETyzDPPCMru27ePNG7cmMyZM4csW7aMBAUFkTfffJOsW7eOvPXWWyQgIIAsXLhQssyHDx8mjz76KHnppZdIeXm5bD1FR0dbfVAylJWVkR49epCYmBjR/uSueqLoB6o0KqC6uppcu3aNXLt2jVRXV8uGN5lM5N133yUDBw5kp003bNhAWrRoQQIDA8kLL7xA7t27JyjriJc8IQ8sB/PnzychISHEaDTKDsYdO3Yk3333Xa3rjEIUHh4uOtC89957JDQ0lJ1WYqaeQkNDJQfEe/fukXHjxpEOHTqQCRMmkOrqavLBBx8Qb29vYjAYSO/evWtZIxj0WE+EuKeufv/9dzJ8+HDi7+/Pfvx4eXmRuLg4smXLFsnyMqh9cRGiz5eXvS95Wk/OU4YIsb2uqqqqyIQJE4i3tzfx8PAgPj4+xMfHh3h4eBBvb28yceJEwbXGhDyY8u7Xrx9p1KgRSUlJIaWlpWTy5MnsMxwVFcVO1YvVVffu3WtNwT/88MPkww8/lCwvQ0VFBRk/fjyJioqSHafS0tJE2+3u3bukW7duov3JnfVE0QdUadQgv//+OxkxYoRdL3mG/Px88uGHH7Jr7sR47bXXSHJysuC9mpoaMmjQIElliBBCzp07R/bt20f27dtHzp49qyqfXMrLy8ndu3dlw+m1ngixriu+5UQNlZWViuqKkAcKcnFxseKPHz5qXlyE6PflZe9LntaTc5QhQuyrK0IebKT5z3/+Q9avX0/Wr19Pdu3aRUpLSxXllc/Zs2fJ0aNHSU1NjaLwN27cIPv37yf79u1jlwWoJSMjg0ybNk30A5GQB1Zj7hp6PmVlZWTPnj2S6ZSWlpJdu3Y5pJ5+//13VfVE0TbU5Y6GIf/f1YHFYkFQUBDr3sIZmEwmVFRU4KGHHhK8bzabceXKFUXuMADA29sbv/76K9q1a6c6L2pl9VxPrqCoqAgrV65Ebm4uioqKYDQaERkZiSFDhuCFF15gXXEoJTMzE7t378bMmTMRHBwsGu7OnTu4du0a2rdvL3j/3r17KCgoQGJiomgcd+/eRUFBAYqLiwEAISEh6NKli2j9S3Hu3DlUVFSgbdu2inyklpSU4Ny5c7BYLAgNDUVERISq9PRaT2fPnkVlZaXL6glwbV1RKBTboUqjzrh8+TLmzJmDzz//XPB+ZWUlCgoK0LRp01r+/u7fv49///vfGD16tKDsiRMnsH//fvTo0QNt27bFyZMnsXTpUlRVVeH5559HUlJSLZn09HTBuJYuXYrnn38egYGBAIDFixc7VJbPnTt38OWXX+LMmTMICwvD6NGjRU/sKSwsREBAAOtHbu3atVi5ciXrY27y5MkYMWKEoGxaWhqeffZZ9OrVSzZPQtjj9N0W2fz8fPTr1w+RkZHw9fXFgQMH8Le//Q3V1dXYvn072rVrh+3bt8Pf39+m8lAoesEex/r2yEpx/fp1/Otf/8Jbb70lGsYWn7C3bt3CkSNHEBMTg6ZNm+LmzZtYvXo1qqqqMGzYMNUf8/YcfOCKQxMoroMqjTrj119/xWOPPSbomPX06dNITk7GpUuXYDAY0KtXL2zYsAGhoaEApJ26btu2DYMHD0ajRo1QUVGBLVu2YPTo0YiJiQEhBDk5Odi+fXstxdHDwwMxMTEICAiwup6Tk4PY2Fj4+fnBYDBg165dtdK0RzYsLAxHjx5FYGAgzp8/j7i4OABAx44dceLECZSVlWH//v1o27ZtLdnHHnsM//znP9GnTx989tlnmDJlCl588UW0a9cOp06dwmeffYalS5fi73//u2CeDQYD/vSnP2Hs2LEYM2YM6wxcDnucvtsqGx8fj/79+2POnDkAHijIy5cvx/79+3Hnzh0kJSUhISEBS5cuFc233l6Y9so68oVrywuzpqYGP/74I86cOYPQ0FA8/fTTonVsj5N7e2T/+c9/4plnnrHZom7PSVCVlZXYsGGDoOW8b9++gjL2ONa3R1YOqfG8qKgIgwcPRkFBAQwGA/72t79hxYoVbH8WG88PHjyI5ORk3L17FwEBAcjOzsawYcPg6ekJQgiuXr2K3NxcPPbYY7XStOfgA0ccmkDRAe6ZFaeIIeb4lvktWbJEdM3OkCFDyMCBA0lJSQk5c+YMeeqpp0hkZCTr30/KdUePHj3IrFmzCCEPNu00adKEvPHGG+z9N954g/Tv37+W3Pz580lkZCT5z3/+Y3VdyaJ2e2QNBgO7rmfEiBGkd+/epLy8nBDywA3OwIEDRdc+NWzYkK2Tzp07k3/9619W99etW0ceffRR0XR37txJpk6dSoKCgoiXlxcZNGgQ+f7774nZbJbMsz1O322V9fX1tVpfajabiZeXFykuLiaEELJjxw5RNz+EEHLs2DESFhZGAgICyODBg8lLL71EXnzxRTJ48GASEBBAHn74Ydm2EuPw4cOi/dEeJ+r2yB44cIA0btyYGAwG0qRJE5Kfn08iIyNJVFQUad26NfH19SUFBQW15JYuXSr4MxqNZObMmezfQvTo0YPcuXOHEPJg3VvHjh2Jt7c3iYqKIj4+PiQ8PFzUQbc9Tu7tdZBvNBpJv379yDfffCO6blKIlStXEk9PT9KlSxfy0EMPkbVr1xJ/f38ybtw4Mn78eOLr6yu6JvLMmTOkZcuWJDAwkN1QNmDAANKtWzdiNBrJsGHDBNfO2eNY3x7ZX3/9VfK3ceNG0b5oq0/Yfv36kXHjxpG7d++SDz74gDzyyCNk3Lhx7P2xY8eSIUOGCKZpz8EH9shS9ANVGjWGPY5vg4ODrfysEULIpEmTSHh4ODl79qzky/Khhx4iZ86cIYQ8UCw8PT2tXo5Hjx4lzZs3F5Q9ePAgadOmDXn55ZfZDRZKFD97ZLlKo5DiuX//flEfj4GBgayrkeDgYEEfc2JOtrnpVldXk40bN7KnZ4SFhZE33niDrUc+9jh9t1W2ZcuWJDc3l/372rVrxGAwkIqKCkLIA6fgYicbEaK/F6a9sra+cO192TJ96sUXXySdOnUiRUVFhJAHfgjj4uJEXU/Z4+TeHlmDwUDWrFlDBg8eTLy8vEhgYCCZOnWqlcN5Mew5CSolJYWMHz+e/UBbsGABSUlJIYQ88IkbERFB5syZU0vOXsf6tspKjefMdSm/rrb4hG3SpAl7cEF1dTXx8PCwiufQoUPk4YcfFkzTnoMP7JGl6AeqNGqMsLAwyZ2/hYWFooOMv79/rQeWkAduORjn0EqURkIIadSokZWF6sKFC5LKRVlZGRk9ejTrq9DLy0vxQGGLrMFgIDdu3CCEPKgz/m7B8+fPkwYNGgjKPv/882Ts2LGEEEKGDRtG3nzzTav78+fPJx07dhRNV2jn4sWLF8mcOXNIy5YtRevYHqfvtspOnTqVdOjQgfz0009k165dpE+fPlZK3rZt28if/vQnwTQJ0d8L015ZW1+49rwwuX2qTZs25IcffrC6v3v3bqccBmCPLDfP169fJwsXLiRt27YlHh4epGvXrmTVqlWiu/rtOQmqYcOGVn5Sq6qqiJeXF7l58yYh5IHFVKiu7HGsb49sUFAQWb16Nblw4YLg78cff5R03G6LT1iu03ZCao/lFy9elBzLbT34wF5Zij6gSqPGeOqpp8js2bNF7x8+fFjUStK1a1fy1VdfCd5LTU0lAQEBogPUn//8Z1YpIYTUcpHw888/K5pa2LBhA2nevDnx8PBQPVCokTUYDKRjx46kc+fOpFGjRmTz5s1W93NyckS/pq9evUoiIiJIQkICSU9PJ76+viQ+Pp68+OKLJCEhgXh7ewv6gmPSlXJ3YbFYyI4dOwTv2eP03VbZsrIy8uyzz7IO6uPi4qxc/Gzfvt1K+eSjtxemI2RtfeHa+sLkfgAFBwfXCn/hwgXRDyB7nNzbIyv2HOzdu5eMGTOG+Pn5iZ5EY89JUGFhYVYzIHfu3CEGg4FVUM+dOydYV/Y41rdH9vHHHyfvvPOO4D1CpMdzW33Ctm3b1mrm5YcffmBnFgiRnoVhsOXgA0fIUrQPVRo1xt69e62UNz737t0T9bE1f/58dqpGiIkTJ4oOUCtXrqxl4eDyxhtvsNY5OS5fvky2bt0q6sDcEbJvv/221W/btm1W91955RUyYsQIUfk7d+6Q119/nTz66KPEx8eHeHt7k5YtW5KRI0eSvLw8UbmIiAjWqqEWe5y+2yNLyAN/jtx1fUrR2wvTXll7X7i2vDANBgN58sknydNPP02aNGnCrjNk+OWXX0SXhtjj5N4eWQ8PD8mPp9LSUnYKmo89J0GNGTOGJCYmkhMnTpBz586R4cOHW02h79mzh7Ro0UJQ1lbH+vbIbt68mXz99dei92/fvk2++OILwXu2+oR9++23yYYNG0TTfOONN8jQoUNF7zOoPfjAUbIUbUOVRgqFIolWX5hiCqc9so544ap9Yb7wwgtWP77l95VXXiGPP/64qLw9Tu5tlZWzuEthz+lG169fZx2Ke3h4kIiICHLo0CH2/rfffks++ugjyfTtcazvKKf8SqipqZF0qG0ymciFCxdUx1teXk7u37+vOLzSgw8cLUvRJtTlDoVCUcT58+etnEczfi6dgT1O1J3pgL2iogJGoxENGjSQDVtQUIDc3FyMHj0aTZo0UZ0WQ3l5OYxGI3x8fCTDETuc3Nsj6yju37+PmpoaRT5Dz5w5g6qqKsUOyCkUimPwcHcGKBSKPoiMjESPHj3Qo0cPVmG8fPmyoD9LJUjJenp6Sp5mcu3aNUF/lvbKynHr1i1MnDhRUdguXbpg6tSpaNKkiV31dPv2bUyaNEk2HOM/MzQ0lFX6lKZrj6wQtsj6+PjA399fkWxUVBQ6dOhQS2GUkq2srERubi6OHz9e6979+/fx1VdfiaanN1m95ZeiI9xr6KRQKHpGytcilXVvmlT2f5w6dYq0bNmSndZOTEwk165dY+9L7ajXm6ze8kvRF9SuT6FQRMnMzJS8f+7cOSrrpjSprHLZ119/HR07dkR+fj7++OMPpKeno2fPntizZw/Cw8Ml49SbrN7yS9EZ7tZaKRSKdrHH2Xx9ktVbfuubrD0HH+hNVm/5pegLuqaRQqGIEhoaik2bNsFisQj+Dh06RGV1mN/6JltZWVlr/eOKFSswaNAgJCYm4vTp06Jp6k1Wb/ml6AuqNFIoFFG6dOki+RI3GAwgIg4Y6pOs3vJb32Tbtm2L/Pz8WteXLVuGwYMHY9CgQaJx6k1Wb/ml6Az3GDgpFIoesMfZfH2S1Vt+65usPQcf6E1Wb/ml6Avqp5FCoVAoFAqFIgudnqZQKBQKhUKhyEKVRgqFQqFQKBSKLFRppFAoFAqFQqHIQpVGCoVCoVAoFIosVGmkUCgUCoVCochClUYKhUKhUCgUiixUaaRQKBQKhUKhyEKVRgqFQqFQKBSKLFRppFAoFAqFQqHIQpVGCoVCoVAoFIosVGmkUCgUCoVCochClUYKhUKhUCgUiiye7s4AhUKhKOHEiRPo0aOHZJiff/4ZvXr1kgyTl5cHk8kkG1deXh6ioqJU55NCoVDqKlRppFAousBsNqNDhw7Izc0VvB8fHw+DwSAbxmw2K4rLbDY7LO8UCoVSF6DT0xQKhUKhUCgUWajSSKFQKBQKhUKRhSqNFAqFQqFQKBRZqNJIoVAoFAqFQpGFKo0UCoVCoVAoFFmo0kihUCgUCoVCkYUqjRQKhUKhUCgUWajSSKFQKBQKhUKRhSqNFAqFQqFQKBRZqNJIoVAoFAqFQpGFKo0UCoVCoVAoFFmo0kihUCgUCoVCkcXT3RmgUCgUJRiNRvz6668ICAgQvG82m2E2m2XDeHh4KIrLw4N+U1MoFAoXAyGEuDsTFAqFQqFQKBRtQz+lKRQKhUKhUCiyUKWRQqFQKBQKhSILVRopFAqFQqFQKLJQpZFCoVAoFAqFIgtVGikUCoVCoVAoslClkUKhUCgUCoUiC1UaKRQKhUKhUCiyUKWRQqFQKBQKhSLL/wNPQi5RSKVNegAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def plot_matrix(matrix, title=\"矩阵热图\", cmap=\"viridis\", annot=True, figsize=(8, 6)):\n", "    \"\"\"\n", "    可视化矩阵，用颜色深浅表示元素值差异\n", "    \n", "    参数:\n", "        matrix: 待可视化的NumPy矩阵\n", "        title: 图表标题\n", "        cmap: 颜色映射（如\"viridis\", \"coolwarm\", \"RdBu\"）\n", "        annot: 是否在单元格中显示数值\n", "        figsize: 图表尺寸\n", "    \"\"\"\n", "    # 设置绘图风格\n", "    plt.style.use(\"default\")\n", "    \n", "    # 创建画布\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # 绘制热图\n", "    sns.heatmap(\n", "        matrix,\n", "        annot=annot,          # 显示数值\n", "        fmt=\".2f\",            # 数值格式（保留2位小数）\n", "        cmap=cmap,            # 颜色映射\n", "        cbar=True,            # 显示颜色条\n", "        square=True,          # 单元格为正方形\n", "        ax=ax,                # 子图对象\n", "        linewidths=0.5,       # 单元格边框宽度\n", "        linecolor=\"gray\"      # 单元格边框颜色\n", "    )\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=14, pad=10)\n", "    ax.set_xlabel(\"列索引\", fontsize=12, labelpad=10)\n", "    ax.set_ylabel(\"行索引\", fontsize=12, labelpad=10)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig\n", "\n", "fig = plot_matrix(\n", "        #np.abs(full_vecs @ full_vecs_eig ),\n", "        #np.abs((full_vecs @ full_vecs_eig ) **4),\n", "        #np.abs(V),\n", "        #np.abs(Hk_blockfull),\n", "        np.abs(U_transform @ U_transform.T.conj()),\n", "        #np.abs(full_vecs_eig),\n", "        #np.abs(full_vecs.T.conj() @ V),\n", "        #np.abs(V @ np.linalg.inv(V)),\n", "        #np.abs((full_vecs @ full_vecs_eig)  @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() ),\n", "        #np.abs(H_F),\n", "        #np.abs(V ** 4),\n", "        #np.abs((full_vecs @ full_vecs_eig) @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(H_F),\n", "        #np.abs(np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) @ (V @ np.diag(E)) @ np.linalg.inv(V) @ full_vecs @ full_vecs_eig ),\n", "        #np.abs(Hk_full),\n", "        #np.abs(V @ np.diag(E) @ np.linalg.inv(V) - H_F),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ Hk_full @ np.linalg.inv(full_vecs) @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(v_block_full) @ (U_transform).T.conj() @ H_tot @ U_transform @ v_block_full  -np.diag(E_full)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V - np.diag(E)),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig),\n", "        #np.abs(full_vecs @ full_vecs_eig- np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data - E )),\n", "        #np.abs(np.linalg.inv(V) @ full_vecs @ full_vecs_eig @ np.linalg.inv(full_vecs_eig) @ full_vecs.T.conj() @ V),\n", "        #np.abs(full_vecs @ full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) @ np.linalg.inv(full_vecs) - V @ np.diag(E) @ np.linalg.inv(V)),\n", "        #np.abs(np.diag(E_blocks_data) - np.diag(E)),\n", "        #np.abs(full_vecs.T.conj() @ H_F @ full_vecs - Hk_full),\n", "        #np.abs( full_vecs @ Hk_full @ full_vecs.T.conj() - H_F),\n", "        #np.abs(full_vecs @ (Hk_full @ np.linalg.inv(full_vecs)) - H_F),\n", "        #np.abs(V @np.linalg.inv(full_vecs_eig)@full_vecs.T.conj() @ H_F @ full_vecs @full_vecs_eig @ np.linalg.inv(V) - H_F),\n", "        #np.abs(full_vecs_eig @ np.diag(E_blocks_data) @ np.linalg.inv(full_vecs_eig) - Hk_full),\n", "        title=\"示例矩阵热图（颜色表示元素值大小）\",\n", "        cmap=\"coolwarm\"  # 冷暖色映射（正值暖色，负值冷色）\n", "    )\n", "    \n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.00000000e+00+0.j,  0.00000000e+00+0.j,  0.00000000e+00+0.j,\n", "        ...,  0.00000000e+00+0.j,  0.00000000e+00+0.j,\n", "        -4.26642159e-17+0.j],\n", "       [ 0.00000000e+00+0.j,  1.25000000e+00+0.j, -7.50000000e-01+0.j,\n", "        ..., -2.73236122e-18+0.j,  1.72338832e-18+0.j,\n", "         0.00000000e+00+0.j],\n", "       [ 0.00000000e+00+0.j, -7.50000000e-01+0.j,  1.25000000e+00+0.j,\n", "        ..., -3.19755979e-18+0.j, -2.73236122e-18+0.j,\n", "         0.00000000e+00+0.j],\n", "       ...,\n", "       [ 0.00000000e+00+0.j, -2.73236122e-18+0.j, -3.19755979e-18+0.j,\n", "        ...,  1.25000000e+00+0.j, -7.50000000e-01+0.j,\n", "         0.00000000e+00+0.j],\n", "       [ 0.00000000e+00+0.j,  1.72338832e-18+0.j, -2.73236122e-18+0.j,\n", "        ..., -7.50000000e-01+0.j,  1.25000000e+00+0.j,\n", "         0.00000000e+00+0.j],\n", "       [-4.26642159e-17+0.j,  0.00000000e+00+0.j,  0.00000000e+00+0.j,\n", "        ...,  0.00000000e+00+0.j,  0.00000000e+00+0.j,\n", "         1.00000000e+00+0.j]])"]}, "execution_count": 241, "metadata": {}, "output_type": "execute_result"}], "source": ["U_transform @ U_transform.T.conj()"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["投影算符重构误差: 117.82508221936448\n"]}], "source": ["# 验证投影算符的正确性\n", "def verify_projection_matrix(V, H_full, H_block):\n", "    \"\"\"\n", "    验证投影算符的正确性\n", "    \n", "    参数:\n", "        V: 投影矩阵\n", "        H_full: 完整哈密顿量\n", "        H_block: 分块对角化的哈密顿量\n", "    返回:\n", "        error: 重构误差\n", "    \"\"\"\n", "    # 计算重构的哈密顿量\n", "    H_reconstructed = V @ H_block @ V.conj().T\n", "    \n", "    # 计算误差\n", "    error = np.linalg.norm(H_reconstructed - H_full, ord='fro')\n", "    \n", "    return error\n", "\n", "# 在您的代码中添加验证步骤\n", "error = verify_projection_matrix(U_transform, H_tot, Hk_blockfull)\n", "print(f\"投影算符重构误差: {error}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "nikola": {"category": "Strongly Correlated Systems", "date": "2020-12-20 13:18:31 UTC+08:00", "description": "", "link": "", "slug": "exact-diagonalization", "tags": "Correlated Electronic Systems, Numerical Method, Exact Diagonalization", "title": "Exact Diagonalization", "type": "text"}, "toc-autonumbering": true}, "nbformat": 4, "nbformat_minor": 4}