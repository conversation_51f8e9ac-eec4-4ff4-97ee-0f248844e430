import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import expm
from math import * 
import pandas as pd
import cmath
import os #导入os库
import random
import time
import seaborn as sns
import cmath
import scipy
import functools
import matplotlib
import matplotlib.pyplot as plt
%matplotlib inline

def get_hamiltonian_sparse(col,row, Jz, hx):
    '''
    Creates the Hamiltonian of the Transverse Field Ising model
    on a linear chain lattice with periodic boundary conditions.

    The Hamiltonian is given by:
    H = -Jz \sum_{i=1}^{L} S_i^z S_{i+1}^z - Jx\sum_{i=1}^{L} S_i^x S_{i+1}^x - hx \sum_{i=1}^{L} S_i^x - hz \sum_{i=1}^{L} S_i^z
    Args:
        L (int): length of chain
        J (float): coupling constant for Ising term
        hx (float): coupling constant for transverse field

    Returns:
        (hamiltonian_rows, hamiltonian_cols, hamiltonian_data) where:
        hamiltonian_rows (list of ints): row index of non-zero elements
        hamiltonian_cols (list of ints): column index of non-zero elements
        hamiltonian_data (list of floats): value of non-zero elements
    '''
    L = col * row
    def get_site_value(state, site):
        ''' Function to get local value at a given site '''
        return (state >> site) & 1
        #返回值为state的第site位上的二进制值

    def hilbertspace_dimension(L):
        ''' return dimension of hilbertspace '''
        return 2**L

    def flip_state(state: int, index: int) -> int:
        """翻转一个整数某位置处的二进制值"""
        mask = 1 << index
        return state ^ mask
    # Define chain lattice
    #ising_bonds_col = [(site, (site+1)%col) for site in range(col-1)]#开放边条件
    #ising_bonds_row = [(site, (site+1)%row) for site in range(row-1)]#开放边条件
    #ising_bonds_col = [(site, (site+1)%col) for site in range(col)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0
    #ising_bonds_row = [(site, (site+1)%row) for site in range(row)]#周期边条件：site+1%L，当site+1=L时，site+1%L=0
    ising_bonds = []

    #  周期边条件
    for i in range(row):          # 遍历每一行
        for j in range(col):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 水平方向链接（同一行内相邻列）
            # 右侧相邻格点，列索引取模实现周期边界
            right = i*col+ (j + 1) % col
            ising_bonds.append((current, right))
            # 垂直方向链接（同一列内相邻行）
            # 下方相邻格点，行索引取模实现周期边界
            down = ((i + 1) % row) *col + j
            ising_bonds.append((current, down))
    #非周期边条件
    '''
    for i in range(row):          # 遍历每一行
        for j in range(col-1):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 水平方向链接（同一行内相邻列）
            # 右侧相邻格点，列索引取模实现非周期边界
            right = i*col+ (j + 1) % col
            ising_bonds.append((current, right))

    for i in range(row-1):          # 遍历每一行
        for j in range(col):      # 遍历每一列
            # 当前格点坐标 (i,j)
            current = i* col + j
            # 垂直方向链接（同一列内相邻行）
            # 下方相邻格点，行索引取模实现非周期边界
            down = ((i + 1) % row) *col + j
            ising_bonds.append((current, down))'''
    print(ising_bonds)
    # Empty lists for sparse matrix
    hamiltonian_rows = []
    hamiltonian_cols = []
    hamiltonian_data = []
    
    # Run through all spin configurations
    for state in range(hilbertspace_dimension(L)):

        # Apply Ising bonds
        ising_diagonal = 0
        for bond in ising_bonds:
            if get_site_value(state, bond[0]) == get_site_value(state, bond[1]):
                ising_diagonal += Jz#mid1[state]#J+random.random()-0.5
            else:
                ising_diagonal -= Jz#mid1[state]#J+random.random()-0.5
        hamiltonian_rows.append(state)
        hamiltonian_cols.append(state)
        hamiltonian_data.append(ising_diagonal)

        # Apply transverse field
        for site in range(L):
            # Flip spin at site
            new_state = flip_state(state,site)#state ^ (1 << site)
            hamiltonian_rows.append(new_state)
            hamiltonian_cols.append(state)
            hamiltonian_data.append(hx)#(mid3[state])#hx
    return hamiltonian_rows, hamiltonian_cols, hamiltonian_data

#测试函数
Hr11,Hc11,Hd11 = get_hamiltonian_sparse(2, 2, 1, 1)
#矩阵形式输出
H11=np.zeros((2**4,2**4))  
H11[Hr11,Hc11] = Hd11
print(H11)


def phasecrate(lam_h,lam_J):
    col = 4
    row = 2
    L = col * row
    #T=t1+t2
    lam_h = 0.01
    lam_J = 0.01
    hx=np.pi/2 -lam_h#hx*t1#标准选的是 1.5
    J=np.pi/4 - lam_J#J*t2# 0.75
    Hr1,Hc1,Hd1 = get_hamiltonian_sparse(col,row,0 * J,hx)
    Hr2,Hc2,Hd2 = get_hamiltonian_sparse(col,row,J,0 * hx)
    #创建3X3的0矩阵
    H1=np.zeros((2**L,2**L))  
    H2=np.zeros((2**L,2**L))
    H1[Hr1,Hc1] = Hd1
    H2[Hr2,Hc2] = Hd2
    H_F = expm(-1j*H1) @ expm(-1j*H2)
    
    E,V=np.linalg.eig(H_F)
    
    quasienergy = [0 for index in range(2**L)]
    for i in range(0,2**L,1):
        quasienergy[i] = (cmath.phase(E[i]))
    quasienergy1 = quasienergy.copy()

    IPR1 = np.multiply(np.multiply(abs(V),abs(V)),np.multiply(abs(V),abs(V)))
    IPR = np.sum((IPR1),axis=0)
    
    return quasienergy1, IPR


import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
import cmath
from scipy.linalg import expm
import time

# 假设 get_hamiltonian_sparse 已定义
# def get_hamiltonian_sparse(col, row, J, hx):
#     # 实现略
#     pass

col = 3
row = 3
L = col * row
def phasecrate(lam_h, lam_J):

    # 注意：原函数中这两行覆盖了输入参数，建议修改
    # lam_h = 0.01
    # lam_J = 0.01
    hx = np.pi/2 - lam_h
    J_val = np.pi/4 - lam_J
    Hr1, Hc1, Hd1 = get_hamiltonian_sparse(col, row, 0 * J_val, hx)
    Hr2, Hc2, Hd2 = get_hamiltonian_sparse(col, row, J_val, 0 * hx)
    
    # 创建哈密顿量矩阵
    H1 = np.zeros((2**L, 2**L), dtype=complex)  
    H2 = np.zeros((2**L, 2**L), dtype=complex)
    H1[Hr1, Hc1] = Hd1
    H2[Hr2, Hc2] = Hd2
    
    # 计算演化算符
    H_F = expm(-1j * H1) @ expm(-1j * H2)
    
    # 求解本征值和本征向量
    E, V = np.linalg.eig(H_F)
    
    # 计算准能（本征值的相位）
    quasienergy = [cmath.phase(e) for e in E]
    
    # 计算IPR
    abs4 = np.power(np.abs(V), 4)
    IPR = np.sum(abs4, axis=0)
    
    return quasienergy, IPR

def test_phasecrate_scan(n_points=6):
    """
    遍历 lam_h 和 lam_J 从 0 到 π，绘制 IPR 随准能变化的关系图
    n_points: 每个参数的采样点数
    """
    # 设置参数扫描范围
    lam_h_vals = np.linspace(0, np.pi, n_points)
    lam_J_vals = np.linspace(0, np.pi, n_points)
    
    # 存储结果的数组
    all_quasienergies = []
    all_iprs = []
    params_list = []
    
    print(f"开始扫描 {n_points}x{n_points} 个参数点...")
    start_time = time.time()
    
    # 遍历参数空间
    for i, lam_h in enumerate(lam_h_vals):
        for j, lam_J in enumerate(lam_J_vals):
            # 显示进度
            if (i * n_points + j) % 10 == 0:
                progress = (i * n_points + j) / (n_points * n_points) * 100
                print(f"进度: {progress:.1f}%")
            
            # 计算准能和IPR
            quasienergy, ipr = phasecrate(lam_h, lam_J)
            
            # 存储结果
            all_quasienergies.extend(quasienergy)
            all_iprs.extend(ipr)
            params_list.extend([(lam_h, lam_J)] * len(quasienergy))
    
    end_time = time.time()
    print(f"扫描完成，耗时 {end_time - start_time:.2f} 秒")
    
    # 转换为numpy数组
    all_quasienergies = np.array(all_quasienergies)
    all_iprs = np.array(all_iprs)
    params_array = np.array(params_list)
    
    # 绘制IPR随准能变化的散点图
    plt.figure(figsize=(12, 6))
    
    # 主图：IPR vs 准能，颜色表示lam_h，大小表示IPR值
    scatter = plt.scatter(
        all_quasienergies, all_iprs,
        c=params_array[:, 0],  # 用lam_h编码颜色
        s=all_iprs * 50,       # 用IPR值编码大小
        cmap='viridis', 
        alpha=0.6,
        edgecolors='black', 
        linewidth=0.5
    )
    plt.colorbar(scatter, label='λ_h')
    plt.xlabel('准能 (Quasienergy)')
    plt.ylabel('逆参与率 (IPR)')
    plt.title('IPR随准能的变化关系')
    plt.grid(True, alpha=0.3)
    
    # 添加IPR分布的直方图
    plt.figure(figsize=(12, 5))
    plt.subplot(121)
    plt.hist(all_iprs, bins=30, color='skyblue', edgecolor='black')
    plt.xlabel('IPR值')
    plt.ylabel('出现频率')
    plt.title('IPR分布直方图')
    plt.grid(True, alpha=0.3)
    
    # 添加准能分布的直方图
    plt.subplot(122)
    plt.hist(all_quasienergies, bins=30, color='lightgreen', edgecolor='black')
    plt.xlabel('准能值')
    plt.ylabel('出现频率')
    plt.title('准能分布直方图')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 绘制参数空间中的IPR最大值分布
    max_ipr = np.zeros((n_points, n_points))
    for i, lam_h in enumerate(lam_h_vals):
        for j, lam_J in enumerate(lam_J_vals):
            idx = i * n_points + j
            start = idx * (2**L)  # 假设系统大小是8个格点
            end = start + (2**L)
            max_ipr[i, j] = np.max(all_iprs[start:end])
    
    plt.figure(figsize=(8, 6))
    im = plt.imshow(
        max_ipr, 
        extent=[0, np.pi, 0, np.pi],
        origin='lower',
        cmap='plasma',
        norm=LogNorm()
    )
    plt.colorbar(im, label='最大IPR值')
    plt.xlabel('λ_J')
    plt.ylabel('λ_h')
    plt.title('参数空间中最大IPR的分布')
    plt.show()
    
    return all_quasienergies, all_iprs, params_array

# 运行测试函数
if __name__ == "__main__":
    # 注意：对于8个格点系统(2^8=256维)，15x15的网格会有3375个点，计算量较大
    # 可先使用较小的n_points进行测试
    quasi, ipr, params = test_phasecrate_scan(n_points=64)
    print(ipr)

#selected_vecs
#把这个结果导出为一个可以编辑的表格
import pandas as pd
# 创建一个DataFrame
df = pd.DataFrame((ipr))

# 将DataFrame保存为Excel文件
df.to_excel('selected_ipr.xlsx', index=False)



# 输出准能量为 txt 文件
#import numpy as np
#np.savetxt('quasienergy_output.txt', quasienergy)
#print('准能量已保存到 quasienergy_output.txt')

'''
#plt.plot(np.arange(len(E)),quasienergy,
#					marker='x',color='r',markersize=2,label='real space ED')
plt.scatter(np.arange(len(E)),quasienergy,
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('state number',fontsize=16)
plt.ylabel('energy',fontsize=16)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
#plt.savefig('example5a.pdf', bbox_inches='tight')
plt.show()
'''

'''c=0
number=[]
for i in IPR:    
    if i>0.3:
        number.append([i,c])
        print(quasienergy1[c])
    c= 1+c
number'''

'''plt.scatter(quasienergy1,list(IPR),
            s=3
					#marker='x',color='r',markersize=2,
     #label='IPR-quasienergy'
     )
plt.xlabel('quasienergy',fontsize=16)
plt.ylabel('IPR',fontsize=16)
# 设置纵坐标范围为0到0.5
plt.ylim(0, 0.7)
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)
plt.legend(fontsize=16)
#plt.grid()
plt.tight_layout()
plt.savefig('example5a.eps', bbox_inches='tight')
plt.show()'''

'''# 绘制直方图并获取统计信息
n, bins, patches = plt.hist(quasienergy, bins=500, range=(-np.pi, np.pi))

# 设置坐标轴
plt.xlabel('Quasienergy', fontsize=16)
plt.ylabel('Count', fontsize=16)
plt.xticks([-np.pi, -np.pi/2, 0, np.pi/2, np.pi],
           [r'$-\pi$', r'$-\pi/2$', '0', r'$\pi/2$', r'$\pi$'],
           fontsize=14)
plt.yticks(fontsize=14)

# 在每个柱子上方标记数量
bin_width = bins[1] - bins[0]  # 计算柱子宽度
for i in range(len(n)):
    count = n[i]
    if count > 0:  # 只标记有数据的柱子
        # 计算柱子中心位置
        x_pos = bins[i] + bin_width / 2
        # 放置文本标签
        plt.text(x_pos, count, f'{int(count)}',
                 ha='center', va='bottom',
                 fontsize=8, color='darkred')

plt.tight_layout()
plt.show()'''

import numpy as np
import matplotlib.pyplot as plt


def plot_selected_vectors(IPR, ipr_threshold):
    """
    筛选IPR大于阈值的列向量并绘制
    
    参数:
    V: 输入矩阵，每行代表一个基矢，每列代表一个向量
    ipr_threshold: IPR筛选阈值，默认0.5
    """
    # 计算IPR
    ipr = IPR
    print(len(ipr))
    # 筛选IPR大于阈值的列索引
    selected_indices = np.where(ipr > ipr_threshold)[0]
    if len(selected_indices) == 0:
        print(f"没有找到IPR大于{ipr_threshold}的向量")
        return
    
    print(f"找到{len(selected_indices)}个IPR大于{ipr_threshold}的向量，索引为: {selected_indices}")
    
    # 提取对应的列向量
    selected_vectors = V[:, selected_indices]
    
    
    # 设置绘图风格
    plt.style.use('seaborn-v0_8-ticks')
    
    # 计算子图布局（最多5列）
    n_cols = min(5, len(selected_indices))
    n_rows = (len(selected_indices) + n_cols - 1) // n_cols
    
    # 创建画布
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))
    axes = np.ravel(axes)  # 转换为一维数组便于索引
    for i, idx in enumerate(selected_indices):
        E_slect = quasienergy1[idx]
        print(E_slect)
    # 绘制每个选中的向量
    for i, idx in enumerate(selected_indices):
        ax = axes[i]
        # 绘制向量的绝对值点线图
        ax.plot(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]), marker='o')
        #ax.bar(range(len(selected_vectors[:, i])), np.abs(selected_vectors[:, i]))
        ax.set_title(f'向量索引: {idx}\nIPR = {ipr[idx]:.4f}', fontsize=10)
        ax.set_xlabel('基矢索引', fontsize=8)
        ax.set_ylabel('|振幅|', fontsize=8)
        ax.tick_params(axis='both', which='major', labelsize=6)
    
    # 隐藏未使用的子图
    for i in range(len(selected_indices), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return selected_vectors, selected_indices

# 示例使用
#if __name__ == "__main__":
    # 筛选并绘图
    #selected_vecs, selected_idx = plot_selected_vectors(IPR, ipr_threshold=0.1)


print(ipr)


